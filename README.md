# 股权管理系统

这是一个股权管理系统，用于管理公司信息、股东信息、任职档案等。

## 功能特点

- 公司信息管理：添加、编辑、查询公司信息
- 股东信息管理：记录公司股东及持股比例
- 任职档案管理：记录人员任职情况
- 业务板块管理：管理公司业务板块
- 基础数据管理：地区、代理机构等基础数据维护
- **公司信息变更确认**：管理和确认公司信息变更申请
  - 统计展示：基础信息、高管信息、股东信息变更数量统计
  - 变更记录管理：查看、筛选、确认变更记录
  - 状态管理：待确认、已确认状态切换
  - 详情确认：弹窗显示变更详情，支持确认、取消、退回操作

## 技术栈

- 前端：React、TypeScript、Ant Design、React Router
- 后端：Node.js、Express
- 数据库：MySQL

## 🚀 快速部署（Windows用户）

### 📋 部署文件说明
本项目为Windows用户提供了完整的一键部署方案：

| 文件名 | 用途 | 使用时机 |
|--------|------|----------|
| `快速入门指南.md` | 5分钟快速部署指南 | 🔥 **首次安装必读** |
| `Windows用户部署指南.md` | 详细部署说明文档 | 需要详细了解时 |
| `系统检查脚本.bat` | 检查系统环境 | 安装前或故障排除 |
| `数据库初始化脚本.bat` | 一键初始化数据库 | 首次安装时 |
| `一键启动脚本.bat` | 一键启动所有服务 | 每次使用系统时 |

### ⚡ 三步快速部署
1. **环境检查**：双击运行 `系统检查脚本.bat`
2. **数据库初始化**：双击运行 `数据库初始化脚本.bat`
3. **启动系统**：双击运行 `一键启动脚本.bat`

### 📖 详细部署指南
- **新手用户**：请阅读 `快速入门指南.md`
- **技术用户**：请阅读 `Windows用户部署指南.md`

## 💻 开发环境安装

### 前提条件
- Node.js (v18+)
- MySQL (v8+)

### 安装依赖
```bash
npm install
```

### 数据库配置
```bash
# 创建数据库
mysql -u root -p
CREATE DATABASE stake_management_v2;

# 导入数据库结构
mysql -u root -p stake_management_v2 < database-schema-complete.sql
```

### 启动服务
```bash
# 启动后端服务
npm run server

# 启动前端服务（新终端窗口）
npm run dev
```

### 访问地址
- 前端页面：http://localhost:5173
- 后端API：http://localhost:8080

## 项目结构

```
├── public/            # 静态资源
├── server/            # 后端服务
│   ├── index.js       # 服务器入口
│   └── init-db.sql    # 数据库初始化脚本
├── src/               # 前端源代码
│   ├── assets/        # 资源文件
│   ├── components/    # 公共组件
│   ├── layouts/       # 布局组件
│   ├── pages/         # 页面组件
│   ├── main.tsx       # 应用入口
│   └── routes.tsx     # 路由配置
└── package.json       # 项目配置
```

## 功能说明

### 业务板块管理

**问题修复**：修复了业务板块页面公司数量显示为0的问题
- **问题原因**：前端代码重复调用不存在的API方法获取公司数量
- **解决方案**：直接使用后端已经计算好的 `companyCount` 字段
- **修复内容**：
  - 移除了对 `getCompaniesBySegment` 的重复调用
  - 直接使用API返回的 `companyCount` 数据
  - 优化了数据处理逻辑，提高页面加载性能

### 新增公司

在左侧菜单中点击「公司信息」>「新增公司」，可以添加新的公司信息。系统会自动检查：

1. 是否填写了所有必填字段
2. 公司名称是否重复（中英文名称同时重复才视为重复）

满足条件后，系统会将新公司信息保存到数据库中。
