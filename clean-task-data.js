import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function cleanTaskData() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 1. 删除2022和2023年度的所有待办任务
    console.log('\n🗑️ 删除2022和2023年度的待办任务...');
    
    const [oldTasks] = await connection.query(`
      SELECT COUNT(*) as count FROM pending_tasks 
      WHERE year IN ('2022', '2023')
    `);
    console.log(`找到 ${oldTasks[0].count} 个2022-2023年度的任务`);
    
    if (oldTasks[0].count > 0) {
      const [deleteResult] = await connection.query(`
        DELETE FROM pending_tasks 
        WHERE year IN ('2022', '2023')
      `);
      console.log(`✅ 已删除 ${deleteResult.affectedRows} 个2022-2023年度的任务`);
    }

    // 2. 删除不管年审公司的任务
    console.log('\n🗑️ 删除不管年审公司的任务...');
    
    const [unmanagedTasks] = await connection.query(`
      SELECT pt.id, pt.task_type, pt.company_name
      FROM pending_tasks pt
      JOIN companies c ON pt.company_id = c.id
      JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      WHERE aus.name = '不管年审'
    `);
    
    console.log(`找到 ${unmanagedTasks.length} 个不管年审公司的任务:`);
    unmanagedTasks.forEach(task => {
      console.log(`  ID: ${task.id}, 类型: ${task.task_type}, 公司: ${task.company_name}`);
    });
    
    if (unmanagedTasks.length > 0) {
      const [deleteUnmanagedResult] = await connection.query(`
        DELETE pt FROM pending_tasks pt
        JOIN companies c ON pt.company_id = c.id
        JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
        WHERE aus.name = '不管年审'
      `);
      console.log(`✅ 已删除 ${deleteUnmanagedResult.affectedRows} 个不管年审公司的任务`);
    }

    // 3. 查找并删除重复任务
    console.log('\n🔍 查找重复任务...');
    
    // 查找重复的年审年报任务
    const [duplicateAnnualTasks] = await connection.query(`
      SELECT 
        company_id, 
        task_type, 
        year,
        COUNT(*) as count,
        GROUP_CONCAT(id) as task_ids
      FROM pending_tasks 
      WHERE task_type = '年审年报'
      GROUP BY company_id, task_type, year
      HAVING COUNT(*) > 1
    `);
    
    console.log(`找到 ${duplicateAnnualTasks.length} 组重复的年审年报任务:`);
    for (const duplicate of duplicateAnnualTasks) {
      console.log(`  公司ID: ${duplicate.company_id}, 年度: ${duplicate.year}, 数量: ${duplicate.count}, 任务IDs: ${duplicate.task_ids}`);
      
      // 保留最早创建的任务，删除其他的
      const taskIds = duplicate.task_ids.split(',').map(id => parseInt(id));
      const keepId = Math.min(...taskIds);
      const deleteIds = taskIds.filter(id => id !== keepId);
      
      if (deleteIds.length > 0) {
        const [deleteResult] = await connection.query(`
          DELETE FROM pending_tasks WHERE id IN (${deleteIds.join(',')})
        `);
        console.log(`    ✅ 保留任务ID ${keepId}，删除了 ${deleteResult.affectedRows} 个重复任务`);
      }
    }
    
    // 查找重复的地址维护任务
    const [duplicateAddressTasks] = await connection.query(`
      SELECT 
        company_id, 
        task_type, 
        year,
        COUNT(*) as count,
        GROUP_CONCAT(id) as task_ids
      FROM pending_tasks 
      WHERE task_type = '地址维护'
      GROUP BY company_id, task_type, year
      HAVING COUNT(*) > 1
    `);
    
    console.log(`找到 ${duplicateAddressTasks.length} 组重复的地址维护任务:`);
    for (const duplicate of duplicateAddressTasks) {
      console.log(`  公司ID: ${duplicate.company_id}, 年度: ${duplicate.year}, 数量: ${duplicate.count}, 任务IDs: ${duplicate.task_ids}`);
      
      // 保留最早创建的任务，删除其他的
      const taskIds = duplicate.task_ids.split(',').map(id => parseInt(id));
      const keepId = Math.min(...taskIds);
      const deleteIds = taskIds.filter(id => id !== keepId);
      
      if (deleteIds.length > 0) {
        const [deleteResult] = await connection.query(`
          DELETE FROM pending_tasks WHERE id IN (${deleteIds.join(',')})
        `);
        console.log(`    ✅ 保留任务ID ${keepId}，删除了 ${deleteResult.affectedRows} 个重复任务`);
      }
    }

    // 4. 验证清理结果
    console.log('\n📊 清理后的数据统计:');
    
    const [finalStats] = await connection.query(`
      SELECT 
        year,
        task_type,
        COUNT(*) as count
      FROM pending_tasks
      GROUP BY year, task_type
      ORDER BY year, task_type
    `);
    
    finalStats.forEach(stat => {
      console.log(`  ${stat.year}年 ${stat.task_type}: ${stat.count}个`);
    });

    // 验证2025年数据
    console.log('\n🔍 验证2025年数据:');
    const [tasks2025] = await connection.query(`
      SELECT 
        pt.task_type,
        COUNT(*) as count
      FROM pending_tasks pt
      JOIN companies c ON pt.company_id = c.id
      JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      WHERE pt.year = '2025' AND aus.name IN ('固定周期', '滚动周期')
      GROUP BY pt.task_type
    `);
    
    tasks2025.forEach(stat => {
      console.log(`  2025年管年审公司的${stat.task_type}: ${stat.count}个`);
    });

  } catch (error) {
    console.error('❌ 清理失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行清理
cleanTaskData();
