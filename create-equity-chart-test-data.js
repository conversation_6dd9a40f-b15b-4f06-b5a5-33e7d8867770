import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function createEquityChartTestData() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 1. 创建测试公司
    const companies = [
      { name: '深圳科技有限公司', code: 'SZTECH001' },
      { name: '北京投资控股有限公司', code: 'BJINV001' },
      { name: '上海金融服务有限公司', code: 'SHFIN001' },
      { name: '广州制造有限公司', code: 'GZMFG001' },
      { name: '杭州互联网科技有限公司', code: 'HZINT001' },
      { name: '成都新能源有限公司', code: 'CDENE001' },
      { name: '西安软件开发有限公司', code: 'XASOFT001' }
    ];

    console.log('🏢 创建测试公司...');
    const companyIds = [];
    for (const company of companies) {
      const [result] = await connection.query(
        `INSERT INTO companies (
          company_name_cn, company_name_en, registered_capital,
          establish_date, business_segment_id, region_id, agency_id,
          operation_status_id, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
        ON DUPLICATE KEY UPDATE id = LAST_INSERT_ID(id)`,
        [
          company.name,
          company.code,
          Math.floor(Math.random() * 5000) + 1000, // 1000-6000万注册资本
          '2020-01-01',
          1, // 业务板块ID
          1, // 地区ID
          1, // 代理机构ID
          1  // 存续状况ID
        ]
      );
      companyIds.push(result.insertId);
    }

    // 2. 创建测试人员（股东）
    const persons = [
      { name: '张三', type: 'individual', idNumber: '110101199001011234' },
      { name: '李四', type: 'individual', idNumber: '110101199002021234' },
      { name: '王五', type: 'individual', idNumber: '110101199003031234' },
      { name: '赵六', type: 'individual', idNumber: '110101199004041234' },
      { name: '钱七', type: 'individual', idNumber: '110101199005051234' },
      { name: '孙八', type: 'individual', idNumber: '110101199006061234' },
      { name: '周九', type: 'individual', idNumber: '110101199007071234' },
      { name: '吴十', type: 'individual', idNumber: '110101199008081234' }
    ];

    console.log('👥 创建测试人员...');
    const personIds = [];
    for (const person of persons) {
      const [result] = await connection.query(
        `INSERT INTO persons (
          name, id_type, id_number, created_by
        ) VALUES (?, ?, ?, 1)
        ON DUPLICATE KEY UPDATE id = LAST_INSERT_ID(id)`,
        [person.name, '身份证', person.idNumber]
      );
      personIds.push(result.insertId);
    }

    // 3. 创建股东关系 - 多层级结构
    console.log('📊 创建股东关系...');
    
    // 主体公司：深圳科技有限公司 (companyIds[0])
    const mainCompanyId = companyIds[0];
    
    // 第一层股东（直接持股主体公司）
    const level1Holdings = [
      { personId: personIds[0], percentage: 35.0, isProxy: false }, // 张三 35%
      { personId: personIds[1], percentage: 25.0, isProxy: true, actualId: personIds[2] }, // 李四代持王五 25%
      { personId: companyIds[1], percentage: 40.0, isProxy: false, isCompany: true } // 北京投资控股 40%
    ];

    for (const holding of level1Holdings) {
      if (holding.isCompany) {
        // 企业股东，使用investments表创建投资关系
        await connection.query(
          `INSERT INTO investments (
            investor_company_id, investee_company_id, investee_company_name,
            investment_amount, percentage, start_date, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, 1)`,
          [
            holding.personId, // 这里是companyIds[1]，即北京投资控股的ID
            mainCompanyId,    // 被投资公司：深圳科技有限公司
            companies[0].name, // 被投资公司名称
            holding.percentage * 100, // 投资金额
            holding.percentage,
            '2023-01-01'
          ]
        );
      } else {
        await connection.query(
          `INSERT INTO shareholdings (
            person_id, company_id, investment_amount, percentage, 
            is_proxy, actual_shareholder_id, start_date, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, 1)`,
          [
            holding.personId,
            mainCompanyId,
            holding.percentage * 100, // 投资金额
            holding.percentage,
            holding.isProxy || false,
            holding.actualId || null,
            '2023-01-01'
          ]
        );
      }
    }

    // 第二层股东（持股北京投资控股）
    const level2Holdings = [
      { personId: personIds[3], percentage: 60.0 }, // 赵六 60%
      { personId: personIds[4], percentage: 30.0 }, // 钱七 30%
      { personId: companyIds[2], percentage: 10.0, isCompany: true } // 上海金融服务 10%
    ];

    for (const holding of level2Holdings) {
      if (holding.isCompany) {
        // 企业股东，使用investments表创建投资关系
        await connection.query(
          `INSERT INTO investments (
            investor_company_id, investee_company_id, investee_company_name,
            investment_amount, percentage, start_date, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, 1)`,
          [
            holding.personId, // 这里是companyIds[2]，即上海金融服务的ID
            companyIds[1],    // 被投资公司：北京投资控股
            companies[1].name, // 被投资公司名称
            holding.percentage * 50, // 投资金额
            holding.percentage,
            '2023-01-01'
          ]
        );
      } else {
        await connection.query(
          `INSERT INTO shareholdings (
            person_id, company_id, investment_amount, percentage,
            start_date, created_by
          ) VALUES (?, ?, ?, ?, ?, 1)`,
          [holding.personId, companyIds[1], holding.percentage * 50, holding.percentage, '2023-01-01']
        );
      }
    }

    // 第三层股东（持股上海金融服务）
    const level3Holdings = [
      { personId: personIds[5], percentage: 70.0 }, // 孙八 70%
      { personId: personIds[6], percentage: 30.0 }  // 周九 30%
    ];

    for (const holding of level3Holdings) {
      await connection.query(
        `INSERT INTO shareholdings (
          person_id, company_id, investment_amount, percentage, 
          start_date, created_by
        ) VALUES (?, ?, ?, ?, ?, 1)`,
        [holding.personId, companyIds[2], holding.percentage * 20, holding.percentage, '2023-01-01']
      );
    }

    // 4. 创建对外投资关系
    console.log('💰 创建对外投资关系...');
    
    // 主体公司对外投资
    const investments = [
      { investeeId: companyIds[3], investeeName: companies[3].name, percentage: 51.0 }, // 广州制造 51%
      { investeeId: companyIds[4], investeeName: companies[4].name, percentage: 30.0 }  // 杭州互联网 30%
    ];

    for (const investment of investments) {
      await connection.query(
        `INSERT INTO investments (
          investor_company_id, investee_company_id, investee_company_name,
          investment_amount, percentage, start_date, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, 1)`,
        [
          mainCompanyId,
          investment.investeeId,
          investment.investeeName,
          investment.percentage * 100,
          investment.percentage,
          '2023-06-01'
        ]
      );
    }

    // 下游公司的进一步投资
    const level2Investments = [
      { investorId: companyIds[3], investeeName: '深圳制造子公司', percentage: 80.0 },
      { investorId: companyIds[4], investeeName: '杭州技术子公司', percentage: 100.0 }
    ];

    for (const investment of level2Investments) {
      await connection.query(
        `INSERT INTO investments (
          investor_company_id, investee_company_name,
          investment_amount, percentage, start_date, created_by
        ) VALUES (?, ?, ?, ?, ?, 1)`,
        [
          investment.investorId,
          investment.investeeName,
          investment.percentage * 50,
          investment.percentage,
          '2023-08-01'
        ]
      );
    }

    console.log('✅ 股权图测试数据创建完成！');
    console.log(`📊 创建了 ${companies.length} 个公司`);
    console.log(`👥 创建了 ${persons.length} 个人员`);
    console.log('🔗 创建了多层级的股权关系和投资关系');
    console.log(`🎯 主体公司: ${companies[0].name} (ID: ${mainCompanyId})`);

  } catch (error) {
    console.error('❌ 创建测试数据失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
createEquityChartTestData();
