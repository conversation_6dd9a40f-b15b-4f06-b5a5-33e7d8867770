import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function updateUserNames() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 查看当前用户信息
    console.log('\n📊 当前用户信息:');
    const [currentUsers] = await connection.query(`
      SELECT 
        u.id,
        u.username,
        u.person_id,
        p.name as person_name,
        ur.name as role_name
      FROM users u
      LEFT JOIN persons p ON u.person_id = p.id
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      WHERE u.username IN ('admin', 'manager', 'operator', 'viewer')
      ORDER BY u.id
    `);

    currentUsers.forEach(user => {
      console.log(`  ${user.username} - Person ID: ${user.person_id} - Name: ${user.person_name} - Role: ${user.role_name}`);
    });

    // 更新用户对应的person记录的姓名
    const userUpdates = [
      { username: 'admin', realName: '系统管理员' },
      { username: 'manager', realName: '管理员' },
      { username: 'operator', realName: '操作员' },
      { username: 'viewer', realName: '查看员' }
    ];

    console.log('\n🔄 更新用户真实姓名...');
    for (const update of userUpdates) {
      // 获取用户的person_id
      const [userInfo] = await connection.query(
        'SELECT person_id FROM users WHERE username = ?',
        [update.username]
      );

      if (userInfo.length > 0 && userInfo[0].person_id) {
        // 更新person表中的姓名
        await connection.query(
          'UPDATE persons SET name = ? WHERE id = ?',
          [update.realName, userInfo[0].person_id]
        );
        console.log(`✅ 更新 ${update.username} 的姓名为: ${update.realName}`);
      } else {
        console.log(`⚠️ 用户 ${update.username} 没有关联的person记录`);
      }
    }

    // 验证更新结果
    console.log('\n📊 更新后的用户信息:');
    const [updatedUsers] = await connection.query(`
      SELECT 
        u.id,
        u.username,
        COALESCE(p.name, u.username) as realName,
        ur.name as roleName
      FROM users u
      LEFT JOIN persons p ON u.person_id = p.id
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      WHERE u.username IN ('admin', 'manager', 'operator', 'viewer')
      ORDER BY u.id
    `);

    updatedUsers.forEach(user => {
      console.log(`  ${user.username} - 真实姓名: ${user.realName} - 角色: ${user.roleName}`);
    });

  } catch (error) {
    console.error('❌ 更新用户姓名失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行更新
updateUserNames().catch(console.error);
