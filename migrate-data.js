import mysql from 'mysql2/promise';

const oldDbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management',
  socketPath: '/tmp/mysql.sock'
};

const newDbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function migrateData() {
  console.log('🔄 开始数据迁移...');
  
  let oldConnection, newConnection;
  
  try {
    // 连接到两个数据库
    oldConnection = await mysql.createConnection(oldDbConfig);
    newConnection = await mysql.createConnection(newDbConfig);
    console.log('✅ 数据库连接成功');
    
    // 创建临时用户用于外键约束
    console.log('🔧 创建临时用户...');
    await newConnection.query(`
      INSERT INTO user_roles (name, description) VALUES ('临时角色', '数据迁移临时角色')
      ON DUPLICATE KEY UPDATE description = '数据迁移临时角色'
    `);
    
    await newConnection.query(`
      INSERT INTO users (username, password_hash, role_id) VALUES ('migration_user', 'temp_hash', 1)
      ON DUPLICATE KEY UPDATE password_hash = 'temp_hash'
    `);
    
    // 阶段1: 迁移基础数据
    console.log('\n📋 阶段1: 迁移基础数据');
    
    // 迁移业务板块
    console.log('📦 迁移业务板块...');
    const [oldSegments] = await oldConnection.query('SELECT * FROM business_segments');
    for (const segment of oldSegments) {
      await newConnection.query(`
        INSERT INTO business_segments (name, description, created_at) 
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE description = VALUES(description)
      `, [segment.name, segment.description || '', segment.created_at]);
    }
    console.log(`✅ 迁移了 ${oldSegments.length} 个业务板块`);
    
    // 迁移地区数据
    console.log('📦 迁移地区数据...');
    const [oldRegions] = await oldConnection.query('SELECT * FROM regions');
    for (const region of oldRegions) {
      await newConnection.query(`
        INSERT INTO regions (type, name, created_at) 
        VALUES (?, ?, ?)
        ON DUPLICATE KEY UPDATE name = VALUES(name)
      `, ['国内', region.name, region.created_at]);
    }
    
    // 检查是否有region表（单数形式）
    try {
      const [oldRegionSingle] = await oldConnection.query('SELECT * FROM region');
      for (const region of oldRegionSingle) {
        const type = region.type === '海外公司对外投资' ? '海外' : '国内';
        await newConnection.query(`
          INSERT INTO regions (type, name, created_at) 
          VALUES (?, ?, ?)
          ON DUPLICATE KEY UPDATE name = VALUES(name)
        `, [type, region.region, region.create_time]);
      }
      console.log(`✅ 迁移了 ${oldRegions.length + oldRegionSingle.length} 个地区`);
    } catch (error) {
      console.log(`✅ 迁移了 ${oldRegions.length} 个地区`);
    }
    
    // 迁移代理机构
    console.log('📦 迁移代理机构...');
    const [oldAgencies] = await oldConnection.query('SELECT * FROM agencies');
    for (const agency of oldAgencies) {
      await newConnection.query(`
        INSERT INTO agencies (name, contact_person, contact_method, created_at) 
        VALUES (?, ?, ?, ?)
        ON DUPLICATE KEY UPDATE contact_person = VALUES(contact_person)
      `, [
        agency.agency_name || agency.name, 
        agency.contact_person, 
        agency.contact_method, 
        agency.create_time || agency.created_at
      ]);
    }
    console.log(`✅ 迁移了 ${oldAgencies.length} 个代理机构`);
    
    // 阶段2: 迁移人员数据
    console.log('\n👥 阶段2: 迁移人员数据');
    
    // 收集所有人员数据
    const allPersons = new Map();
    
    // 从persons表收集
    try {
      const [oldPersons] = await oldConnection.query('SELECT * FROM persons');
      for (const person of oldPersons) {
        allPersons.set(person.id_number, {
          name: person.name,
          id_type: person.id_type || '身份证',
          id_number: person.id_number,
          created_at: person.created_at
        });
      }
      console.log(`📝 从persons表收集了 ${oldPersons.length} 个人员`);
    } catch (error) {
      console.log('⚠️ persons表不存在或为空');
    }
    
    // 从shareholder_entities表收集
    try {
      const [oldShareholders] = await oldConnection.query('SELECT * FROM shareholder_entities');
      for (const shareholder of oldShareholders) {
        const idNumber = `SE_${shareholder.id}`;
        if (!allPersons.has(idNumber)) {
          allPersons.set(idNumber, {
            name: shareholder.name,
            id_type: '身份证',
            id_number: idNumber,
            created_at: shareholder.created_at
          });
        }
      }
      console.log(`📝 从shareholder_entities表收集了 ${oldShareholders.length} 个股东`);
    } catch (error) {
      console.log('⚠️ shareholder_entities表不存在或为空');
    }
    
    // 从shareholders表收集
    try {
      const [oldShareholdersOld] = await oldConnection.query('SELECT DISTINCT name, created_at FROM shareholders WHERE name IS NOT NULL');
      let counter = 1;
      for (const shareholder of oldShareholdersOld) {
        const idNumber = `SH_${counter++}`;
        if (!allPersons.has(idNumber)) {
          allPersons.set(idNumber, {
            name: shareholder.name,
            id_type: '身份证',
            id_number: idNumber,
            created_at: shareholder.created_at
          });
        }
      }
      console.log(`📝 从shareholders表收集了 ${oldShareholdersOld.length} 个股东`);
    } catch (error) {
      console.log('⚠️ shareholders表不存在或为空');
    }
    
    // 从executives表收集
    try {
      const [oldExecutives] = await oldConnection.query('SELECT DISTINCT person, created_at FROM executives WHERE person IS NOT NULL');
      let counter = 1;
      for (const executive of oldExecutives) {
        const idNumber = `EX_${counter++}`;
        if (!allPersons.has(idNumber)) {
          allPersons.set(idNumber, {
            name: executive.person,
            id_type: '身份证',
            id_number: idNumber,
            created_at: executive.created_at
          });
        }
      }
      console.log(`📝 从executives表收集了 ${oldExecutives.length} 个高管`);
    } catch (error) {
      console.log('⚠️ executives表不存在或为空');
    }
    
    // 插入所有人员到新数据库
    console.log('📝 插入人员数据到新数据库...');
    for (const person of allPersons.values()) {
      await newConnection.query(`
        INSERT INTO persons (name, id_type, id_number, created_at, created_by) 
        VALUES (?, ?, ?, ?, 1)
        ON DUPLICATE KEY UPDATE name = VALUES(name)
      `, [person.name, person.id_type, person.id_number, person.created_at]);
    }
    console.log(`✅ 插入了 ${allPersons.size} 个人员记录`);
    
    // 阶段3: 迁移公司数据
    console.log('\n🏢 阶段3: 迁移公司数据');
    
    const [oldCompanies] = await oldConnection.query('SELECT * FROM companies');
    for (const company of oldCompanies) {
      // 查找关联的基础数据ID
      let businessSegmentId = null, regionId = null, agencyId = null;
      
      if (company.business_segment) {
        const [segments] = await newConnection.query('SELECT id FROM business_segments WHERE name = ?', [company.business_segment]);
        businessSegmentId = segments[0]?.id || null;
      }
      
      if (company.region) {
        const [regions] = await newConnection.query('SELECT id FROM regions WHERE name = ?', [company.region]);
        regionId = regions[0]?.id || null;
      }
      
      if (company.agency) {
        const [agencies] = await newConnection.query('SELECT id FROM agencies WHERE name = ?', [company.agency]);
        agencyId = agencies[0]?.id || null;
      }
      
      // 处理注册资本字段，提取数字部分
      let registeredCapital = null;
      if (company.registered_capital) {
        const match = company.registered_capital.toString().match(/[\d.]+/);
        registeredCapital = match ? parseFloat(match[0]) : null;
      }

      await newConnection.query(`
        INSERT INTO companies (
          company_name_cn, company_name_en, registered_capital, establish_date,
          business_segment_id, region_id, agency_id, registered_address,
          created_at, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
        ON DUPLICATE KEY UPDATE company_name_cn = VALUES(company_name_cn)
      `, [
        company.company_name_cn,
        company.company_name_en,
        registeredCapital,
        company.establish_date,
        businessSegmentId,
        regionId,
        agencyId,
        company.registered_address,
        company.created_at
      ]);
    }
    console.log(`✅ 迁移了 ${oldCompanies.length} 个公司`);
    
    console.log('\n🎉 数据迁移完成！');
    
    // 验证迁移结果
    const [newPersonsCount] = await newConnection.query('SELECT COUNT(*) as count FROM persons');
    const [newCompaniesCount] = await newConnection.query('SELECT COUNT(*) as count FROM companies');
    const [newSegmentsCount] = await newConnection.query('SELECT COUNT(*) as count FROM business_segments');
    
    console.log('\n📊 迁移结果统计:');
    console.log(`  👥 人员: ${newPersonsCount[0].count}`);
    console.log(`  🏢 公司: ${newCompaniesCount[0].count}`);
    console.log(`  📋 业务板块: ${newSegmentsCount[0].count}`);
    
  } catch (error) {
    console.error('❌ 数据迁移失败:', error.message);
    throw error;
  } finally {
    if (oldConnection) await oldConnection.end();
    if (newConnection) await newConnection.end();
  }
}

migrateData()
  .then(() => console.log('🎉 数据迁移成功完成'))
  .catch(err => console.error('💥 迁移失败:', err));
