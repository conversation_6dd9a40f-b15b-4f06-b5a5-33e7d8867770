import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function addCorrectChangeLogs() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 首先查看现有公司的ID
    const [companies] = await connection.query(`
      SELECT id, company_name_cn
      FROM companies
      ORDER BY id
    `);
    
    console.log('\n🏢 现有公司列表:');
    companies.forEach(company => {
      console.log(`  ID: ${company.id}, 名称: ${company.company_name_cn}`);
    });

    // 使用正确的company_id添加变更记录
    const changeLogsData = [
      // 深圳科技有限公司 (ID: 43)
      {
        company_id: 43,
        change_type: 'basic',
        change_content: '公司注册资本变更：【1000万元】→【1500万元】',
        old_value: '1000万元',
        new_value: '1500万元',
        change_date: '2024-03-15',
        status: 'confirmed',
        operator: '张三',
        operate_date: '2024-03-15 10:30:00'
      },
      {
        company_id: 43,
        change_type: 'executive',
        change_content: '新增副总经理：【无】→【王五】',
        old_value: '无',
        new_value: '王五',
        change_date: '2024-06-01',
        status: 'pending',
        operator: '李四',
        operate_date: '2024-06-01 14:20:00'
      },
      {
        company_id: 43,
        change_type: 'basic',
        change_content: '公司注册地址变更：【深圳市南山区科技园南区】→【深圳市南山区科技园北区创新大厦】',
        old_value: '深圳市南山区科技园南区',
        new_value: '深圳市南山区科技园北区创新大厦',
        change_date: '2024-08-20',
        status: 'confirmed',
        operator: '张三',
        operate_date: '2024-08-20 09:15:00'
      },

      // 北京投资控股有限公司 (ID: 44)
      {
        company_id: 44,
        change_type: 'shareholder',
        change_content: '股东出资比例变更：【赵六：60%，钱七：40%】→【赵六：55%，钱七：35%，新增股东孙八：10%】',
        old_value: '赵六：60%，钱七：40%',
        new_value: '赵六：55%，钱七：35%，孙八：10%',
        change_date: '2024-05-10',
        status: 'confirmed',
        operator: '赵六',
        operate_date: '2024-05-10 16:45:00'
      },
      {
        company_id: 44,
        change_type: 'basic',
        change_content: '公司经营范围变更：【投资管理】→【投资管理；资产管理；投资咨询】',
        old_value: '投资管理',
        new_value: '投资管理；资产管理；投资咨询',
        change_date: '2024-07-25',
        status: 'pending',
        operator: '钱七',
        operate_date: '2024-07-25 11:30:00'
      },

      // 上海金融服务有限公司 (ID: 45)
      {
        company_id: 45,
        change_type: 'executive',
        change_content: '财务负责人变更：【孙八】→【吴十】',
        old_value: '孙八',
        new_value: '吴十',
        change_date: '2024-04-12',
        status: 'confirmed',
        operator: '周九',
        operate_date: '2024-04-12 13:20:00'
      },
      {
        company_id: 45,
        change_type: 'basic',
        change_content: '公司注册资本变更：【2000万元】→【3000万元】',
        old_value: '2000万元',
        new_value: '3000万元',
        change_date: '2024-09-05',
        status: 'pending',
        operator: '周九',
        operate_date: '2024-09-05 15:10:00'
      },

      // 广州制造有限公司 (ID: 46)
      {
        company_id: 46,
        change_type: 'executive',
        change_content: '监事变更：【赵六】→【王五】',
        old_value: '赵六',
        new_value: '王五',
        change_date: '2024-02-28',
        status: 'confirmed',
        operator: '李四',
        operate_date: '2024-02-28 10:45:00'
      },
      {
        company_id: 46,
        change_type: 'basic',
        change_content: '公司名称变更：【广州制造有限公司】→【广州智能制造有限公司】',
        old_value: '广州制造有限公司',
        new_value: '广州智能制造有限公司',
        change_date: '2024-10-15',
        status: 'pending',
        operator: '李四',
        operate_date: '2024-10-15 09:30:00'
      },

      // 杭州互联网科技有限公司 (ID: 47)
      {
        company_id: 47,
        change_type: 'shareholder',
        change_content: '新增股东：【原股东：钱七100%】→【钱七：80%，新增股东：周九20%】',
        old_value: '钱七：100%',
        new_value: '钱七：80%，周九：20%',
        change_date: '2024-06-20',
        status: 'confirmed',
        operator: '钱七',
        operate_date: '2024-06-20 14:15:00'
      },
      {
        company_id: 47,
        change_type: 'executive',
        change_content: '新增董事：【无】→【周九】',
        old_value: '无',
        new_value: '周九',
        change_date: '2024-06-20',
        status: 'confirmed',
        operator: '钱七',
        operate_date: '2024-06-20 14:20:00'
      },

      // 成都新能源有限公司 (ID: 48)
      {
        company_id: 48,
        change_type: 'basic',
        change_content: '公司注册资本变更：【500万元】→【800万元】',
        old_value: '500万元',
        new_value: '800万元',
        change_date: '2024-08-01',
        status: 'pending',
        operator: '吴十',
        operate_date: '2024-08-01 11:00:00'
      },

      // 西安软件开发有限公司 (ID: 49)
      {
        company_id: 49,
        change_type: 'executive',
        change_content: '总经理变更：【李四】→【王五】',
        old_value: '李四',
        new_value: '王五',
        change_date: '2024-01-20',
        status: 'confirmed',
        operator: '李四',
        operate_date: '2024-01-20 16:30:00'
      },
      {
        company_id: 49,
        change_type: 'basic',
        change_content: '公司经营范围变更：【软件开发】→【软件开发；技术咨询；系统集成】',
        old_value: '软件开发',
        new_value: '软件开发；技术咨询；系统集成',
        change_date: '2024-11-10',
        status: 'pending',
        operator: '王五',
        operate_date: '2024-11-10 13:45:00'
      }
    ];

    console.log('\n📝 开始添加公司变更记录...');
    
    let successCount = 0;
    for (const changeLog of changeLogsData) {
      try {
        const [result] = await connection.query(`
          INSERT INTO company_change_logs (
            company_id, change_type, change_content, old_value, new_value, 
            change_date, status, operator, operate_date
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          changeLog.company_id,
          changeLog.change_type,
          changeLog.change_content,
          changeLog.old_value,
          changeLog.new_value,
          changeLog.change_date,
          changeLog.status,
          changeLog.operator,
          changeLog.operate_date
        ]);
        
        console.log(`✅ 添加变更记录成功 - ID: ${result.insertId}`);
        successCount++;
      } catch (error) {
        console.error(`❌ 添加变更记录失败:`, error.message);
      }
    }

    console.log(`\n📊 变更记录添加完成，成功添加 ${successCount} 条记录`);

    // 验证添加结果
    const [changeLogs] = await connection.query(`
      SELECT cl.id, c.company_name_cn, cl.change_type, cl.change_content, 
             cl.change_date, cl.status, cl.operator
      FROM company_change_logs cl
      LEFT JOIN companies c ON cl.company_id = c.id
      ORDER BY cl.change_date DESC, c.company_name_cn
    `);
    
    console.log(`\n📋 当前变更记录 (共${changeLogs.length}条):`);
    changeLogs.forEach(log => {
      const statusText = {
        'pending': '待确认',
        'confirmed': '已确认'
      }[log.status] || log.status;
      
      console.log(`${log.change_date.toISOString().split('T')[0]} | ${log.company_name_cn} | ${log.change_type} | ${statusText} | ${log.operator}`);
    });

    // 统计各状态的数量
    const [statusStats] = await connection.query(`
      SELECT status, COUNT(*) as count
      FROM company_change_logs
      GROUP BY status
      ORDER BY status
    `);
    
    console.log('\n📊 变更记录状态统计:');
    statusStats.forEach(stat => {
      const statusText = {
        'pending': '待确认',
        'confirmed': '已确认'
      }[stat.status] || stat.status;
      console.log(`  ${statusText}: ${stat.count} 条`);
    });

  } catch (error) {
    console.error('❌ 添加失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行添加
addCorrectChangeLogs();
