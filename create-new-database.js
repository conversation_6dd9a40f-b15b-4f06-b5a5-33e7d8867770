import mysql from 'mysql2/promise';
import fs from 'fs';

const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  socketPath: '/tmp/mysql.sock'
};

async function createNewDatabase() {
  console.log('🔄 开始创建新数据库结构...');
  
  try {
    // 连接到MySQL服务器（不指定数据库）
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ MySQL服务器连接成功');
    
    // 创建新数据库
    await connection.query('CREATE DATABASE IF NOT EXISTS stake_management_v2 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    console.log('✅ 新数据库 stake_management_v2 创建成功');

    // 切换到新数据库
    await connection.query('USE stake_management_v2');
    console.log('✅ 已切换到新数据库');
    
    // 读取并执行SQL文件
    const sqlContent = fs.readFileSync('database-schema-complete.sql', 'utf8');
    console.log('📄 读取数据库结构文件成功');
    
    // 分割SQL语句并执行
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => {
        // 过滤掉空语句、注释和USE语句
        return stmt.length > 0 &&
               !stmt.startsWith('--') &&
               !stmt.startsWith('/*') &&
               !stmt.toLowerCase().startsWith('use ') &&
               !stmt.toLowerCase().startsWith('create database') &&
               !stmt.toLowerCase().startsWith('set foreign_key_checks');
      });
    
    console.log(`📋 准备执行 ${statements.length} 条SQL语句`);
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement.trim()) {
        try {
          await connection.query(statement);
          if (statement.toLowerCase().includes('create table')) {
            const tableName = statement.match(/create table (?:if not exists )?`?(\w+)`?/i)?.[1];
            console.log(`✅ 创建表: ${tableName}`);
          }
        } catch (error) {
          console.error(`❌ 执行SQL失败 (${i + 1}/${statements.length}):`, error.message);
          console.error('SQL语句:', statement.substring(0, 100) + '...');
        }
      }
    }
    
    // 验证表创建
    const [tables] = await connection.query('SHOW TABLES');
    console.log(`✅ 新数据库创建完成，共 ${tables.length} 个表:`);
    tables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });
    
    await connection.end();
    console.log('🎉 新数据库结构创建成功！');
    
  } catch (error) {
    console.error('❌ 创建新数据库失败:', error.message);
    throw error;
  }
}

createNewDatabase()
  .then(() => console.log('🎉 数据库迁移第一阶段完成'))
  .catch(err => console.error('💥 迁移失败:', err));
