import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function checkPersonsTable() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 检查persons表结构
    console.log('\n📋 检查persons表结构:');
    const [columns] = await connection.execute('DESCRIBE persons');
    console.log('persons表字段:');
    columns.forEach(col => {
      console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(可空)' : '(非空)'} ${col.Key ? `[${col.Key}]` : ''}`);
    });

    // 检查users表结构
    console.log('\n📋 检查users表结构:');
    const [userColumns] = await connection.execute('DESCRIBE users');
    console.log('users表字段:');
    userColumns.forEach(col => {
      console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(可空)' : '(非空)'} ${col.Key ? `[${col.Key}]` : ''}`);
    });

    // 检查现有用户数据
    console.log('\n👥 检查现有用户数据:');
    const [users] = await connection.execute(`
      SELECT 
        u.id,
        u.username,
        u.role_id,
        ur.name as role_name,
        u.account_status,
        u.person_id,
        p.name as person_name
      FROM users u
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      LEFT JOIN persons p ON u.person_id = p.id
      ORDER BY u.id
    `);
    
    console.log('现有用户:');
    users.forEach(user => {
      console.log(`  ID:${user.id} 用户名:${user.username} 角色:${user.role_name} 状态:${user.account_status} 关联人员:${user.person_name || '无'}`);
    });

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行检查
checkPersonsTable().catch(console.error);
