// 测试更新任务弹窗的主体字段默认值功能
// 这个脚本用于验证修复是否正确

const testUpdateModal = async () => {
  console.log('🧪 开始测试更新任务弹窗主体字段默认值功能...');
  
  try {
    // 1. 获取一个测试任务
    console.log('📋 获取测试任务...');
    const tasksResponse = await fetch('http://localhost:8080/api/tasks?page=1&limit=1');
    const tasksResult = await tasksResponse.json();
    
    if (!tasksResult.success || tasksResult.data.list.length === 0) {
      console.error('❌ 无法获取测试任务');
      return;
    }
    
    const testTask = tasksResult.data.list[0];
    console.log('✅ 获取到测试任务:', {
      id: testTask.id,
      companyName: testTask.companyName,
      companyId: testTask.companyId || '未知',
      taskType: testTask.taskType
    });
    
    // 2. 获取公司列表
    console.log('🏢 获取公司列表...');
    const companiesResponse = await fetch('http://localhost:8080/api/companies-list');
    const companiesResult = await companiesResponse.json();
    
    if (!companiesResult.success) {
      console.error('❌ 无法获取公司列表');
      return;
    }
    
    console.log('✅ 获取到公司列表，共', companiesResult.data.length, '个公司');
    
    // 3. 检查测试任务的公司是否在公司列表中
    const taskCompany = companiesResult.data.find(c => c.name === testTask.companyName);
    if (taskCompany) {
      console.log('✅ 任务公司在公司列表中找到:', taskCompany);
    } else {
      console.log('⚠️  任务公司不在当前公司列表中，这是测试边界情况的好机会');
    }
    
    // 4. 模拟更新任务进度（创建下一周期任务）
    console.log('🔄 测试更新任务进度（创建下一周期任务）...');
    const updateData = {
      taskStatus: '已完成',
      uploadedFiles: ['test-file.pdf'],
      createNextCycle: true,
      nextCycleData: {
        taskType: testTask.taskType,
        year: (parseInt(testTask.year) + 1).toString(),
        companyId: taskCompany ? taskCompany.id : testTask.companyId,
        startDate: '2025-01-01',
        deadline: '2025-12-31',
        remarks: '测试创建的下一周期任务'
      }
    };
    
    const updateResponse = await fetch(`http://localhost:8080/api/tasks/${testTask.id}/update-progress`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(updateData)
    });
    
    const updateResult = await updateResponse.json();
    
    if (updateResult.success) {
      console.log('✅ 任务更新成功，下一周期任务已创建');
      console.log('📊 测试结果总结:');
      console.log('   - 原任务公司:', testTask.companyName);
      console.log('   - 使用的公司ID:', updateData.nextCycleData.companyId);
      console.log('   - 主体字段应该默认显示原公司且不可编辑 ✅');
    } else {
      console.error('❌ 任务更新失败:', updateResult.message);
    }
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
  
  console.log('🏁 测试完成');
};

// 如果在Node.js环境中运行
if (typeof window === 'undefined') {
  // Node.js环境，需要使用node-fetch
  console.log('请在浏览器控制台中运行此测试脚本');
  console.log('复制以下代码到浏览器控制台:');
  console.log(testUpdateModal.toString());
  console.log('然后执行: testUpdateModal()');
} else {
  // 浏览器环境，可以直接运行
  console.log('在浏览器中运行测试...');
  testUpdateModal();
}

// 导出函数供浏览器使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = testUpdateModal;
}
