const fs = require('fs');
const mysql = require('mysql2/promise');

async function initDb() {
  try {
    const sql = fs.readFileSync('./server/init-db.sql', 'utf8');
    
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: 'localhost',
      user: 'txuser',
      password: 'txpassword'
    });
    
    console.log('连接到MySQL成功');
    
    // 分割SQL语句并执行
    const queries = sql.split(';').filter(query => query.trim() !== '');
    
    for (const query of queries) {
      if (query.trim()) {
        await connection.execute(query + ';');
        console.log('执行SQL语句成功');
      }
    }
    
    console.log('数据库初始化完成');
    await connection.end();
  } catch (error) {
    console.error('初始化数据库失败:', error);
    process.exit(1);
  }
}

initDb();