# 股权图-主体公司页面布局优化

## 🎯 优化目标

根据用户需求，对股权图-主体公司页面进行以下布局优化：

1. **垂直平均分布**：当某个N级主体公司的股东存在多个时，所有该公司的股东与该主体公司保持垂直平均分布的布局，但各自的水平坐标保持不变
2. **缩小股权比例显示**：显示股权比例的字体和框可以再缩小一些
3. **股权比例位置优化**：每个显示股权比例的框应该位于相应的线条上方，且股权比例不应该重叠

## 🔧 技术实现

### 1. 垂直分布算法优化

#### **上游股东布局改进**
```typescript
// 按目标公司分组，每组内部垂直平均分布
const shareholdersByTarget = new Map<number, typeof level>();
level.forEach(shareholder => {
  const targetCompanyId = shareholder.companyId;
  if (!shareholdersByTarget.has(targetCompanyId)) {
    shareholdersByTarget.set(targetCompanyId, []);
  }
  shareholdersByTarget.get(targetCompanyId)!.push(shareholder);
});

// 为每个目标公司的股东组分配垂直位置
let currentGroupStartY = centerY - ((shareholdersByTarget.size - 1) * verticalSpacing * 1.5) / 2;

shareholdersByTarget.forEach((shareholders, targetCompanyId) => {
  // 每组内部的垂直分布
  const groupHeight = (shareholders.length - 1) * (verticalSpacing * 0.8);
  const groupStartY = currentGroupStartY - groupHeight / 2;

  shareholders.forEach((shareholder, index) => {
    const y = shareholders.length === 1 ? currentGroupStartY : groupStartY + index * (verticalSpacing * 0.8);
    // ... 节点位置计算
  });

  // 移动到下一组的位置
  currentGroupStartY += verticalSpacing * 1.5;
});
```

#### **下游投资布局改进**
```typescript
// 按投资方分组，每组内部垂直平均分布
const investmentsBySource = new Map<number, typeof level>();
level.forEach(investment => {
  const sourceInvestorId = investment.investorId;
  if (!investmentsBySource.has(sourceInvestorId)) {
    investmentsBySource.set(sourceInvestorId, []);
  }
  investmentsBySource.get(sourceInvestorId)!.push(investment);
});
```

### 2. 股权比例显示优化

#### **尺寸缩小**
```typescript
// 统一的持股份额显示框尺寸 - 缩小尺寸
const percentageBoxWidth = 70;  // 从90缩小到70
const percentageBoxHeight = 18; // 从24缩小到18
```

#### **字体大小调整**
```typescript
<text
  fontSize="9"  // 从11缩小到9
  // ... 其他属性
>
  {percentage}%
</text>
```

### 3. 股权比例位置算法

#### **防重叠位置计算**
```typescript
// 根据线条的高度动态调整Y位置，确保框始终在线条上方
const lineTopY = Math.min(startY, endY);
const baseOffsetY = 25; // 基础向上偏移距离

// 为避免重叠，根据股东在同一目标公司组中的索引调整位置
const shareholdersToSameTarget = equityData.upstreamShareholders.filter(s => 
  s.companyId === shareholder.companyId
);
const shareholderIndex = shareholdersToSameTarget.findIndex(s => 
  s.shareholderId === shareholder.shareholderId
);

// 如果有多个股东指向同一公司，错开显示位置避免重叠
const verticalOffset = shareholderIndex * (percentageBoxHeight + 5);
const boxY = lineTopY - baseOffsetY - verticalOffset;
```

## 📊 优化效果

### 修改前问题
❌ **布局问题**：
- 多个股东指向同一公司时布局混乱
- 股权比例框过大，占用过多空间
- 股权比例框位置不准确，可能重叠

### 修改后效果
✅ **布局优化**：
- 同一目标公司的股东垂直平均分布，布局清晰
- 股权比例框尺寸适中，不占用过多空间
- 股权比例框精确位于线条上方，避免重叠

## 🔍 关键改进点

### 1. 分组布局算法
- **上游股东**：按目标公司ID分组
- **下游投资**：按投资方ID分组
- **组内分布**：每组内部使用0.8倍垂直间距
- **组间分布**：组之间使用1.5倍垂直间距

### 2. 尺寸优化
- **框宽度**：90px → 70px (减少22%)
- **框高度**：24px → 18px (减少25%)
- **字体大小**：11px → 9px (减少18%)

### 3. 位置算法
- **基础偏移**：25px向上偏移
- **重叠避免**：根据索引错开显示
- **动态调整**：根据线条高度自适应

## 🧪 测试验证

### 测试场景
1. **单股东场景**：验证单个股东的正常显示
2. **多股东场景**：验证多个股东的垂直分布
3. **复杂层级**：验证多层级的布局效果
4. **股权比例**：验证比例框的位置和大小

### 预期结果
- 布局清晰，无重叠
- 股权比例显示准确
- 视觉效果美观
- 响应式适配良好

## 🔧 第二轮修复（垂直分布和连接线问题）

### 问题分析
根据用户反馈，发现以下问题：
1. **垂直分布不准确**：孙八、周九与上海金融服务有限公司的框在垂直方向上并不是垂直平均分布
2. **股权比例位置错误**：周九的30%持股比例显示在孙八的连接线上，而不是周九的连接线上
3. **代持关系显示不完整**：李四与深圳科技有限公司的代持关系缺少实际股东王五的显示

### 修复内容

#### **1. 垂直分布算法优化**
```typescript
// 计算所有组的总高度，确保垂直居中
const totalGroups = shareholdersByTarget.size;
const totalGroupHeight = (totalGroups - 1) * verticalSpacing * 1.2;
let currentGroupStartY = centerY - totalGroupHeight / 2;

// 每组内部真正的平均分布
if (shareholders.length === 1) {
  // 单个股东，直接居中
  const y = currentGroupStartY;
} else {
  // 多个股东，垂直平均分布
  const groupHeight = (shareholders.length - 1) * verticalSpacing * 0.7;
  const groupStartY = currentGroupStartY - groupHeight / 2;
  const y = groupStartY + index * verticalSpacing * 0.7;
}
```

#### **2. 代持关系完整显示**
```typescript
// 添加实际股东节点（在代持人左侧）
if (shareholder.isProxy && shareholder.actualShareholderName) {
  positions.push({
    id: `actual-${shareholder.actualShareholderId}-${shareholder.level || 0}`,
    x: x - horizontalSpacing / 2 - nodeWidth / 2,
    y: y - nodeHeight / 2,
    name: shareholder.actualShareholderName!,
    type: 'person',
    level: nodeLevel + 1,
    isProxy: false
  });
}
```

#### **3. 连接线精确绘制**
```typescript
// 处理代持关系：先绘制实际股东到代持人的连接线
if (shareholder.isProxy && shareholder.actualShareholderName) {
  // 实际股东到代持人的连接线（实线）
  const actualPathData = `M ${actualStartX} ${actualStartY} L ${proxyEndX} ${proxyEndY}`;

  connections.push(
    <path
      d={actualPathData}
      stroke="#666666"
      strokeWidth="2"
      fill="none"
      markerEnd="url(#arrowhead)"
    />
  );
}

// 代持关系使用虚线，普通关系使用实线
const strokeDashArray = shareholder.isProxy ? "5,5" : "none";
const strokeColor = shareholder.isProxy ? "#ff7875" : "#1890ff";
```

#### **4. 股权比例位置修复**
```typescript
// 精确计算当前连接线的持股份额框位置
const midX = (startX + endX) / 2;
const lineTopY = Math.min(startY, endY);

// 为同一目标公司的股东错开显示位置
const shareholdersToSameTarget = equityData.upstreamShareholders.filter(s =>
  s.companyId === shareholder.companyId
);
const shareholderIndex = shareholdersToSameTarget.findIndex(s =>
  s.shareholderId === shareholder.shareholderId
);

const verticalOffset = shareholderIndex * (percentageBoxHeight + 5);
const boxY = lineTopY - baseOffsetY - verticalOffset;
```

### 修复效果

#### **垂直分布优化**
- ✅ 同一目标公司的股东真正垂直平均分布
- ✅ 组间距离合理，整体布局居中
- ✅ 单个股东和多个股东都有正确的布局

#### **代持关系完整显示**
- ✅ 实际股东节点显示在代持人左侧
- ✅ 实际股东到代持人的连接线（实线）
- ✅ 代持人到目标公司的连接线（虚线，红色）

#### **股权比例位置精确**
- ✅ 每个股权比例框精确位于对应的连接线上方
- ✅ 多个股东的比例框错开显示，避免重叠
- ✅ 代持关系的比例框使用红色标识

## 📊 测试数据验证

通过 `test-equity-chart-data.cjs` 验证了数据结构：

**代持关系**：
- 代持人：李四
- 实际股东：王五
- 持股公司：深圳科技有限公司
- 持股比例：25.00%

**分组情况**：
- 深圳科技有限公司：3个股东（张三35%、李四25%代持、北京投资40%）
- 北京投资控股有限公司：3个股东（赵六60%、钱七30%、上海金融10%）
- 上海金融服务有限公司：2个股东（孙八70%、周九30%）

## 📝 备份信息

**Checkpoint文件**：`src/pages/company/EquityChart.tsx.checkpoint-before-layout-optimization`

如需回滚修改，可以使用以下命令：
```bash
cp src/pages/company/EquityChart.tsx.checkpoint-before-layout-optimization src/pages/company/EquityChart.tsx
```
