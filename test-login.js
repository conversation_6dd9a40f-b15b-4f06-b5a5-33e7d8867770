import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function testLogin() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    const username = 'admin';
    const password = '123456';

    console.log('🔐 测试登录查询...');
    
    // 查询用户信息
    const [userResult] = await connection.query(`
      SELECT 
        u.id,
        u.username,
        u.password_hash,
        u.account_status,
        COALESCE(p.name, u.username) as realName,
        COALESCE(p.email, '') as email,
        COALESCE(p.phone, '') as phone,
        '' as department,
        '' as position,
        u.role_id as roleId,
        ur.name as roleName
      FROM users u
      LEFT JOIN persons p ON u.person_id = p.id
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      WHERE u.username = ? AND u.account_status = 'active'
    `, [username]);

    console.log('用户查询结果:', userResult);

    if (userResult.length === 0) {
      console.log('❌ 用户不存在或状态不正确');
      return;
    }

    const user = userResult[0];
    console.log('找到用户:', user);

    // 验证密码
    const expectedPassword = Buffer.from(password).toString('base64');
    console.log('期望密码hash:', expectedPassword);
    console.log('数据库密码hash:', user.password_hash);
    
    if (user.password_hash !== expectedPassword) {
      console.log('❌ 密码不匹配');
      return;
    }

    console.log('✅ 密码验证成功');

    // 获取用户权限
    const [permissionsResult] = await connection.query(`
      SELECT 
        p.id,
        p.module,
        p.action,
        p.description
      FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      WHERE rp.role_id = ?
    `, [user.roleId]);

    console.log('用户权限:', permissionsResult);

    console.log('🎉 登录测试成功！');

  } catch (error) {
    console.error('❌ 登录测试失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行测试
testLogin().catch(console.error);
