{"name": "stake-equity-management", "version": "1.0.0", "description": "Secure stake and equity management system for local deployment", "private": true, "type": "module", "workspaces": ["client", "server", "shared"], "scripts": {"dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "build": "npm run build:shared && npm run build:client && npm run build:server", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "build:shared": "cd shared && npm run build", "test": "npm run test:client && npm run test:server", "test:client": "cd client && npm run test", "test:server": "cd server && npm run test", "lint": "npm run lint:client && npm run lint:server", "lint:client": "cd client && npm run lint", "lint:server": "cd server && npm run lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "db:migrate": "cd server && npm run db:migrate", "db:seed": "cd server && npm run db:seed", "db:reset": "cd server && npm run db:reset", "start": "cd server && npm start", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down"}, "devDependencies": {"@types/node": "^20.10.0", "concurrently": "^8.2.2", "prettier": "^3.1.0", "husky": "^8.0.3", "lint-staged": "^15.2.0"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}, "keywords": ["equity-management", "stake-management", "corporate-governance", "react", "typescript", "nodejs", "mysql"], "author": "Your Organization", "license": "PROPRIETARY"}