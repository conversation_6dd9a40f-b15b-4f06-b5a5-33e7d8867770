import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function checkConfirmedAtField() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 查看company_change_logs表结构
    const [columns] = await connection.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'stake_management_v2' AND TABLE_NAME = 'company_change_logs'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('\n📋 company_change_logs表字段:');
    let hasConfirmedAt = false;
    columns.forEach(col => {
      console.log(`  ${col.COLUMN_NAME} (${col.DATA_TYPE}) - ${col.COLUMN_COMMENT || '无注释'}`);
      if (col.COLUMN_NAME === 'confirmed_at') {
        hasConfirmedAt = true;
      }
    });

    if (!hasConfirmedAt) {
      console.log('\n⚠️ 缺少confirmed_at字段，正在添加...');
      
      // 添加confirmed_at字段
      await connection.query(`
        ALTER TABLE company_change_logs 
        ADD COLUMN confirmed_at DATETIME NULL COMMENT '确认时间'
      `);
      
      console.log('✅ confirmed_at字段添加成功');
    } else {
      console.log('\n✅ confirmed_at字段已存在');
    }

    // 查看当前变更记录
    const [records] = await connection.query(`
      SELECT id, company_id, change_type, status, confirmed_at
      FROM company_change_logs
      ORDER BY id
    `);
    
    console.log('\n📋 当前变更记录:');
    records.forEach(record => {
      console.log(`  ID: ${record.id}, 公司ID: ${record.company_id}, 类型: ${record.change_type}, 状态: ${record.status}, 确认时间: ${record.confirmed_at || '无'}`);
    });

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行检查
checkConfirmedAtField();
