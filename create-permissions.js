import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function createPermissions() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    await connection.beginTransaction();
    console.log('🔄 开始创建权限数据...');

    // 权限数据
    const permissions = [
      // 系统设置权限
      { module: 'system', action: 'user_add', description: '新增用户' },
      { module: 'system', action: 'user_management', description: '用户管理' },
      { module: 'system', action: 'database_config', description: '数据库配置' },
      
      // 任务管理权限
      { module: 'task', action: 'view', description: '查看任务' },
      { module: 'task', action: 'add', description: '新增任务' },
      { module: 'task', action: 'edit', description: '编辑任务' },
      { module: 'task', action: 'verify', description: '核实任务' },
      { module: 'task', action: 'investigation', description: '任务排查' },
      
      // 公司信息权限
      { module: 'company', action: 'view', description: '查看公司信息' },
      { module: 'company', action: 'add', description: '新增公司' },
      { module: 'company', action: 'edit', description: '编辑公司信息' },
      { module: 'company', action: 'change_confirm', description: '公司变更确认' },
      
      // 任职档案权限
      { module: 'employment', action: 'view', description: '查看任职档案' },
      { module: 'employment', action: 'add', description: '新增任职档案' },
      { module: 'employment', action: 'edit', description: '编辑任职档案' },
      
      // 业务板块权限
      { module: 'business', action: 'view', description: '查看业务板块' },
      { module: 'business', action: 'edit', description: '编辑业务板块' },
      
      // 股东信息权限
      { module: 'shareholder', action: 'view', description: '查看股东信息' },
      { module: 'shareholder', action: 'add', description: '新增股东信息' },
      { module: 'shareholder', action: 'edit', description: '编辑股东信息' },
      
      // 基础数据权限
      { module: 'basic_data', action: 'view', description: '查看基础数据' },
      { module: 'basic_data', action: 'edit', description: '编辑基础数据' },
      
      // 档案规范权限
      { module: 'archive', action: 'view', description: '查看档案规范' },
      { module: 'archive', action: 'add', description: '新增档案规范' },
      { module: 'archive', action: 'edit', description: '编辑档案规范' }
    ];

    // 清空现有权限数据
    await connection.query('DELETE FROM role_permissions');
    await connection.query('DELETE FROM permissions');
    console.log('🗑️ 已清空现有权限数据');

    // 插入权限数据
    for (const permission of permissions) {
      await connection.query(
        'INSERT INTO permissions (module, action, description) VALUES (?, ?, ?)',
        [permission.module, permission.action, permission.description]
      );
    }
    console.log(`✅ 成功插入 ${permissions.length} 条权限数据`);

    // 获取所有权限ID
    const [allPermissions] = await connection.query('SELECT id, module, action FROM permissions');
    
    // 角色权限分配
    const rolePermissions = {
      1: allPermissions.filter(p => p.module === 'system'), // 系统管理员：只有系统设置权限
      2: allPermissions.filter(p => p.module !== 'system'), // 管理员：除系统设置外的所有权限
      3: allPermissions.filter(p => p.module !== 'system'), // 操作员：除系统设置外的所有权限
      4: allPermissions.filter(p => p.module !== 'system' && p.action === 'view') // 查看员：只有查看权限
    };

    // 插入角色权限关联
    for (const [roleId, permissions] of Object.entries(rolePermissions)) {
      for (const permission of permissions) {
        await connection.query(
          'INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)',
          [parseInt(roleId), permission.id]
        );
      }
      console.log(`✅ 为角色 ${roleId} 分配了 ${permissions.length} 个权限`);
    }

    await connection.commit();
    console.log('🎉 权限数据创建完成！');

    // 验证创建结果
    console.log('\n📊 验证创建结果:');
    const [roleStats] = await connection.query(`
      SELECT 
        ur.id,
        ur.name as role_name,
        COUNT(rp.permission_id) as permission_count
      FROM user_roles ur
      LEFT JOIN role_permissions rp ON ur.id = rp.role_id
      GROUP BY ur.id, ur.name
      ORDER BY ur.id
    `);

    console.log('角色权限统计:');
    roleStats.forEach(stat => {
      console.log(`  ${stat.role_name}: ${stat.permission_count} 个权限`);
    });

  } catch (error) {
    await connection.rollback();
    console.error('❌ 创建权限数据失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行创建
createPermissions().catch(console.error);
