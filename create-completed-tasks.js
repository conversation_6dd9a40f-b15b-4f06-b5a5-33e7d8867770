import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function createCompletedTasks() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 将一些"未开始"或"进行中"的任务改为"已完成"状态
    const [tasks] = await connection.query(
      'SELECT id, company_name, task_type, task_status FROM pending_tasks WHERE task_status IN ("未开始", "进行中") LIMIT 5'
    );
    
    console.log(`\n📊 找到 ${tasks.length} 个可以改为已完成的任务:`);
    tasks.forEach(task => {
      console.log(`  ID: ${task.id}, 公司: ${task.company_name}, 类型: ${task.task_type}, 当前状态: ${task.task_status}`);
    });

    if (tasks.length === 0) {
      console.log('没有找到可以修改的任务');
      return;
    }

    // 将这些任务改为"已完成"状态
    let updatedCount = 0;

    for (const task of tasks) {
      await connection.query(
        'UPDATE pending_tasks SET task_status = ? WHERE id = ?',
        ['已完成', task.id]
      );
      
      console.log(`  ✅ 任务 ${task.id} (${task.company_name} - ${task.task_type}) 状态从"${task.task_status}"改为"已完成"`);
      updatedCount++;
    }

    console.log(`\n🎉 成功创建 ${updatedCount} 个已完成任务`);

    // 验证结果
    const [completedTasks] = await connection.query(
      'SELECT COUNT(*) as count FROM pending_tasks WHERE task_status = "已完成"'
    );
    
    console.log(`\n📋 当前已完成任务总数: ${completedTasks[0].count} 个`);

    // 显示当前任务状态统计
    const [statusStats] = await connection.query(
      'SELECT task_status, COUNT(*) as count FROM pending_tasks GROUP BY task_status ORDER BY task_status'
    );
    
    console.log('\n📊 当前任务状态统计:');
    statusStats.forEach(stat => {
      console.log(`  ${stat.task_status}: ${stat.count} 个`);
    });

  } catch (error) {
    console.error('❌ 创建失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
createCompletedTasks();
