import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function testConfirmedRecord() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 查询ID为15的记录
    const [records] = await connection.query(`
      SELECT id, company_id, change_type, status, confirmed_at,
             DATE_FORMAT(confirmed_at, '%Y-%m-%d %H:%i:%s') as confirmedAtFormatted
      FROM company_change_logs
      WHERE id = 15
    `);
    
    if (records.length > 0) {
      const record = records[0];
      console.log('\n📋 ID为15的变更记录:');
      console.log(`  ID: ${record.id}`);
      console.log(`  公司ID: ${record.company_id}`);
      console.log(`  变更类型: ${record.change_type}`);
      console.log(`  状态: ${record.status}`);
      console.log(`  确认时间: ${record.confirmed_at}`);
      console.log(`  格式化确认时间: ${record.confirmedAtFormatted}`);
    } else {
      console.log('❌ 未找到ID为15的记录');
    }

    // 查询所有已确认的记录
    const [confirmedRecords] = await connection.query(`
      SELECT id, status, confirmed_at,
             DATE_FORMAT(confirmed_at, '%Y-%m-%d %H:%i:%s') as confirmedAtFormatted
      FROM company_change_logs
      WHERE status = 'confirmed'
      ORDER BY id
    `);
    
    console.log('\n📋 所有已确认的记录:');
    confirmedRecords.forEach(record => {
      console.log(`  ID: ${record.id}, 状态: ${record.status}, 确认时间: ${record.confirmedAtFormatted || '无'}`);
    });

  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行查询
testConfirmedRecord();
