import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function updateAdminPermissions() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    await connection.beginTransaction();
    console.log('🔄 开始更新系统管理员权限...');

    // 获取系统管理员角色ID (通常是1)
    const [adminRole] = await connection.query(`
      SELECT id FROM user_roles WHERE name = '超级管理员' OR name = '系统管理员'
    `);
    
    if (adminRole.length === 0) {
      console.log('❌ 未找到系统管理员角色');
      return;
    }
    
    const adminRoleId = adminRole[0].id;
    console.log(`📋 系统管理员角色ID: ${adminRoleId}`);

    // 清除系统管理员现有权限
    await connection.query('DELETE FROM role_permissions WHERE role_id = ?', [adminRoleId]);
    console.log('🗑️ 已清除系统管理员现有权限');

    // 获取所有权限
    const [allPermissions] = await connection.query('SELECT id FROM permissions');
    console.log(`📊 找到 ${allPermissions.length} 个权限`);

    // 为系统管理员分配所有权限
    for (const permission of allPermissions) {
      await connection.query(
        'INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)',
        [adminRoleId, permission.id]
      );
    }

    await connection.commit();
    console.log(`✅ 成功为系统管理员分配了 ${allPermissions.length} 个权限`);

    // 验证更新结果
    console.log('\n📊 验证更新结果:');
    const [roleStats] = await connection.query(`
      SELECT 
        ur.id,
        ur.name as role_name,
        COUNT(rp.permission_id) as permission_count
      FROM user_roles ur
      LEFT JOIN role_permissions rp ON ur.id = rp.role_id
      GROUP BY ur.id, ur.name
      ORDER BY ur.id
    `);

    console.log('角色权限统计:');
    roleStats.forEach(stat => {
      console.log(`  ${stat.role_name}: ${stat.permission_count} 个权限`);
    });

  } catch (error) {
    await connection.rollback();
    console.error('❌ 更新系统管理员权限失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行更新
updateAdminPermissions().catch(console.error);
