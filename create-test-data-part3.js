import mysql from 'mysql2/promise';

const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function createTestDataPart3() {
  console.log('🔄 开始创建第三部分测试数据...');
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 获取基础数据ID
    const [companies] = await connection.query('SELECT id FROM companies ORDER BY id');
    const [users] = await connection.query('SELECT id FROM users ORDER BY id');
    const [changeTypes] = await connection.query('SELECT id FROM change_types ORDER BY id');
    const [archiveRules] = await connection.query('SELECT id FROM archive_update_rules ORDER BY id');
    
    // 1. 创建档案规范参考文件测试数据
    console.log('\n📁 创建档案规范参考文件测试数据...');
    
    const archiveFiles = [];
    const fileTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt'];
    
    for (let i = 0; i < 15; i++) {
      const ruleId = archiveRules[i % archiveRules.length].id;
      const fileName = `规范文件_${i + 1}`;
      const originalName = `${fileName}.${fileTypes[i % fileTypes.length]}`;
      const filePath = `/uploads/archive_rules/${fileName}_${Date.now()}.${fileTypes[i % fileTypes.length]}`;
      const fileSize = Math.floor(Math.random() * 1024 * 1024) + 1024; // 1KB - 1MB
      const fileType = fileTypes[i % fileTypes.length];
      const uploadedBy = users[i % users.length].id;
      
      archiveFiles.push([ruleId, fileName, originalName, filePath, fileSize, fileType, uploadedBy]);
    }
    
    for (const [ruleId, fileName, originalName, filePath, fileSize, fileType, uploadedBy] of archiveFiles) {
      await connection.query(
        `INSERT INTO archive_rule_files (
          rule_id, file_name, original_name, file_path, file_size, file_type, uploaded_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [ruleId, fileName, originalName, filePath, fileSize, fileType, uploadedBy]
      );
    }
    console.log(`✅ 创建了 ${archiveFiles.length} 条档案规范参考文件记录`);
    
    // 2. 创建公司信息变更记录测试数据
    console.log('\n📝 创建公司信息变更记录测试数据...');
    
    const changeFields = [
      'company_name_cn', 'company_name_en', 'registered_capital', 'registered_address',
      'business_segment_id', 'region_id', 'agency_id', 'annual_update_status_id', 'operation_status_id'
    ];
    
    const changeLogs = [];
    for (let i = 0; i < 25; i++) {
      const companyId = companies[i % companies.length].id;
      const changeTypeId = changeTypes[i % changeTypes.length].id;
      const fieldName = changeFields[i % changeFields.length];
      const oldValue = `旧值_${i + 1}`;
      const newValue = `新值_${i + 1}`;
      const changeReason = `变更原因_${i + 1}：业务发展需要`;
      const changedBy = users[i % users.length].id;
      const changedAt = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000); // 过去一年内的随机时间
      
      changeLogs.push([companyId, changeTypeId, fieldName, oldValue, newValue, changeReason, changedAt, changedBy]);
    }
    
    for (const [companyId, changeTypeId, fieldName, oldValue, newValue, changeReason, changedAt, changedBy] of changeLogs) {
      await connection.query(
        `INSERT INTO company_change_logs (
          company_id, change_type_id, field_name, old_value, new_value, 
          change_reason, changed_at, changed_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [companyId, changeTypeId, fieldName, oldValue, newValue, changeReason, changedAt, changedBy]
      );
    }
    console.log(`✅ 创建了 ${changeLogs.length} 条公司信息变更记录`);
    
    // 3. 创建系统操作日志测试数据
    console.log('\n🔍 创建系统操作日志测试数据...');
    
    const modules = ['company', 'shareholder', 'employment', 'finance', 'archive', 'user', 'system'];
    const actions = ['view', 'create', 'edit', 'delete', 'export', 'import', 'login', 'logout'];
    const targetTypes = ['company', 'person', 'shareholding', 'employment', 'finance', 'archive_rule', 'user'];
    const ipAddresses = ['*************', '*************', '*************', '*********', '*********'];
    const userAgents = [
      'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
      'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36'
    ];
    
    const operationLogs = [];
    for (let i = 0; i < 50; i++) {
      const userId = users[i % users.length].id;
      const module = modules[i % modules.length];
      const action = actions[i % actions.length];
      const targetType = targetTypes[i % targetTypes.length];
      const targetId = Math.floor(Math.random() * 20) + 1;
      const description = `用户执行了${action}操作在${module}模块`;
      const ipAddress = ipAddresses[i % ipAddresses.length];
      const userAgent = userAgents[i % userAgents.length];
      const operatedAt = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000); // 过去30天内的随机时间
      
      operationLogs.push([userId, module, action, targetType, targetId, description, ipAddress, userAgent, operatedAt]);
    }
    
    for (const [userId, module, action, targetType, targetId, description, ipAddress, userAgent, operatedAt] of operationLogs) {
      await connection.query(
        `INSERT INTO system_operation_logs (
          user_id, module, action, target_type, target_id, description, 
          ip_address, user_agent, operated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [userId, module, action, targetType, targetId, description, ipAddress, userAgent, operatedAt]
      );
    }
    console.log(`✅ 创建了 ${operationLogs.length} 条系统操作日志记录`);
    
    // 4. 创建角色权限关联测试数据
    console.log('\n🔐 创建角色权限关联测试数据...');
    
    const [roles] = await connection.query('SELECT id FROM user_roles ORDER BY id');
    const [permissions] = await connection.query('SELECT id FROM permissions ORDER BY id');
    
    const rolePermissions = [];
    
    // 超级管理员拥有所有权限
    for (const permission of permissions) {
      rolePermissions.push([1, permission.id]); // 角色ID 1 是超级管理员
    }
    
    // 普通管理员拥有大部分权限（除了用户管理和系统配置）
    for (let i = 0; i < permissions.length; i++) {
      if (i % 4 !== 0) { // 排除25%的权限
        rolePermissions.push([2, permissions[i].id]); // 角色ID 2 是普通管理员
      }
    }
    
    // 只读用户只有查看权限
    for (let i = 0; i < permissions.length; i++) {
      if (i % 5 === 0) { // 只有20%的权限（主要是查看权限）
        rolePermissions.push([3, permissions[i].id]); // 角色ID 3 是只读用户
      }
    }
    
    // 财务专员有财务相关权限
    for (let i = 0; i < permissions.length; i++) {
      if (i % 3 === 0) { // 约33%的权限
        rolePermissions.push([4, permissions[i].id]); // 角色ID 4 是财务专员
      }
    }
    
    for (const [roleId, permissionId] of rolePermissions) {
      await connection.query(
        `INSERT INTO role_permissions (role_id, permission_id) VALUES (?, ?)
         ON DUPLICATE KEY UPDATE role_id = VALUES(role_id)`,
        [roleId, permissionId]
      );
    }
    console.log(`✅ 创建了 ${rolePermissions.length} 条角色权限关联记录`);
    
    console.log('\n🎉 第三部分测试数据创建完成！');
    
    // 5. 统计所有测试数据
    console.log('\n📊 测试数据统计汇总：');
    
    const tables = [
      'users', 'persons', 'companies', 'shareholdings', 'employments', 'investments',
      'company_finances', 'shareholder_contributions', 'archive_update_rules',
      'archive_rule_segments', 'archive_rule_regions', 'archive_rule_files',
      'company_change_logs', 'system_operation_logs', 'role_permissions'
    ];
    
    for (const table of tables) {
      const [count] = await connection.query(`SELECT COUNT(*) as count FROM ${table}`);
      console.log(`  📋 ${table}: ${count[0].count} 条记录`);
    }
    
  } catch (error) {
    console.error('❌ 创建第三部分测试数据失败:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

createTestDataPart3()
  .then(() => console.log('🎉 第三部分测试数据创建成功完成'))
  .catch(err => console.error('💥 创建失败:', err));
