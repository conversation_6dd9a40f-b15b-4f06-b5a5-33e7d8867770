import axios from 'axios';

async function testCompletedTasksAPI() {
  try {
    console.log('🧪 测试获取已完成任务API...');
    
    const response = await axios.get('http://localhost:8080/api/tasks', {
      params: {
        status: '已完成',
        page: 1,
        limit: 1000
      }
    });

    console.log('📊 API响应状态:', response.status);
    console.log('📋 API响应数据:', JSON.stringify(response.data, null, 2));

    if (response.data.success) {
      const tasks = response.data.data.list || [];
      console.log(`\n✅ 成功获取 ${tasks.length} 个已完成任务:`);

      tasks.forEach((task, index) => {
        console.log(`  ${index + 1}. ID: ${task.id}, 公司: ${task.companyName}, 类型: ${task.taskType}, 状态: ${task.taskStatus}`);
      });
    } else {
      console.log('❌ API返回失败:', response.data.message);
    }

  } catch (error) {
    console.error('❌ API调用失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行测试
testCompletedTasksAPI();
