# 新增任务页面功能说明

## 功能概述

新增任务页面采用标签页设计，包含三个标签页，分别用于创建不同类型的任务：
1. 新增年审年报任务
2. 新增地址维护任务  
3. 新增自定义任务

## 页面设计

### 标签页样式
- 采用与数据字典页面一致的标签页样式
- 激活标签：蓝色背景，白色文字
- 非激活标签：白色背景，蓝色文字
- 标签文字居中显示

## 功能详情

### 1. 新增年审年报任务标签页

#### 表单字段
- **任务类型**：下拉选择（固定为"年审年报"，不可编辑）
- **年度**：下拉选择（必填），提供当前年份前后2年的选项
- **备注**：多行文本输入框（可选），最大500字符

#### 功能说明
系统将按公司信息中的年审要求批量自动生成对应的待办任务：

1. **不管年审**：不需要创建待办任务
2. **管年审（固定周期）**：
   - 开始日期：当年1月1日
   - 结束日期：当年6月30日
3. **管年审（滚动周期）**：
   - 开始日期：按公司的设立日期对应今年的日期 - 3个月
   - 结束日期：按公司的设立日期对应今年的日期 + 2个月

#### 重复检查
- 若该年度该主体公司已生成过年审年报任务，则不会再重复生成

### 2. 新增地址维护任务标签页

#### 表单字段
- **任务类型**：下拉选择（固定为"地址维护"，不可编辑）
- **年度**：下拉选择（必填）
- **主体**：多选下拉框（必填），支持搜索，读取数据库中的公司信息
- **开始日期**：日期选择器（必填），格式YYYY-MM-DD
- **结束日期**：日期选择器（必填），格式YYYY-MM-DD
- **重复周期（年）**：数字输入框（必填），默认值为1年
- **备注**：多行文本输入框（可选），最大500字符

#### 功能说明
1. 按要求填写信息后，批量在数据库中生成地址维护任务
2. 创建第一条数据，按重复周期，到时间后生成第二条数据
3. 第二条数据的年度、开始日期、结束日期，在原年度基础上增加重复周期时间

### 3. 新增自定义任务标签页

#### 表单字段
- **任务名称**：文本输入框（必填）
- **任务描述**：文本输入框（可选）
- **任务年度**：下拉选择（必填）
- **任务主体**：单选下拉框（必填），支持搜索，从数据库导入所有非注销的公司
- **开始日期**：日期选择器（可选），格式YYYY-MM-DD
- **结束日期**：日期选择器（必填），格式YYYY-MM-DD
- **备注**：多行文本输入框（可选），最大500字符

#### 功能说明
按要求填写信息后，在数据库中创建单个自定义任务记录。

## 操作按钮

### 保存按钮
- 样式：黑色背景的主要按钮
- 功能：提交表单数据，创建相应类型的任务
- 成功后：显示成功消息，自动返回待办任务提醒页面

### 取消按钮
- 样式：默认样式按钮
- 功能：不保存数据，直接返回待办任务提醒页面

## 技术实现

### 前端技术
- React + TypeScript
- Ant Design UI组件库
- 表单验证和数据处理
- 响应式设计

### 后端API
1. **GET /api/companies-list** - 获取公司列表（用于下拉选择）
2. **POST /api/task/create-annual-tasks** - 批量创建年审年报任务
3. **POST /api/task/create-address-tasks** - 批量创建地址维护任务
4. **POST /api/task/create-custom-task** - 创建自定义任务

### 数据验证
- 前端表单验证：必填字段检查、数据格式验证
- 后端数据验证：参数完整性检查、业务逻辑验证
- 重复任务检查：防止创建重复的年审年报任务

## 用户体验

### 响应式设计
- 支持桌面端和移动端访问
- 在不同屏幕尺寸下自适应布局

### 交互反馈
- 加载状态显示
- 成功/错误消息提示
- 表单验证错误提示

### 数据持久化
- 所有创建的任务数据保存到MySQL数据库
- 支持事务处理，确保数据一致性

## 注意事项

1. 年审年报任务的生成规则基于公司的年审要求设置
2. 地址维护任务支持重复周期设置，便于定期任务管理
3. 自定义任务提供最大的灵活性，适用于特殊需求
4. 所有任务创建都会记录创建人和创建时间
5. 系统会自动检查并避免创建重复的年审年报任务
