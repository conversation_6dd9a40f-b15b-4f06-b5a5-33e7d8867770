# 股权管理系统 - 快速入门指南

## 🚀 5分钟快速部署

### 第一步：下载必需软件
1. **Node.js**: 访问 https://nodejs.org/zh-cn/ 下载并安装LTS版本
2. **MySQL**: 访问 https://dev.mysql.com/downloads/mysql/ 下载并安装
   - 安装时设置root密码为：`Yiwill@2025`

### 第二步：准备项目文件
1. **获取项目文件**：
   - 如果您收到的是压缩包（.zip/.rar文件），请解压到：`C:\股权管理系统\`
   - 如果您已经有项目文件夹，请将整个文件夹复制到：`C:\股权管理系统\`

2. **验证文件完整性**，确保 `C:\股权管理系统\` 文件夹包含：
   - `package.json` （项目配置文件）
   - `server/` 文件夹 （后端代码）
   - `src/` 文件夹 （前端代码）
   - `database-schema-complete.sql` （数据库文件）
   - `系统检查脚本.bat` （检查脚本）
   - `数据库初始化脚本.bat` （数据库脚本）
   - `一键启动脚本.bat` （启动脚本）

### 第三步：一键部署
1. 双击运行：`系统检查脚本.bat`
   - 检查系统环境是否就绪
   - 如有错误，按提示解决

2. 双击运行：`数据库初始化脚本.bat`
   - 自动创建数据库
   - 导入数据结构

3. 双击运行：`一键启动脚本.bat`
   - 自动启动前后端服务
   - 自动打开浏览器

### 第四步：开始使用
- 系统地址：http://localhost:5173
- 开始添加公司信息和股东数据

---

## 📁 文件说明

| 文件名 | 用途 | 何时使用 |
|--------|------|----------|
| `系统检查脚本.bat` | 检查系统环境 | 首次安装或遇到问题时 |
| `数据库初始化脚本.bat` | 初始化数据库 | 首次安装时 |
| `一键启动脚本.bat` | 启动系统服务 | 每次使用系统时 |
| `Windows用户部署指南.md` | 详细部署说明 | 需要详细了解时 |

---

## ⚡ 常用操作

### 启动系统
```
双击：一键启动脚本.bat
```

### 停止系统
```
关闭两个命令行窗口
```

### 重启系统
```
1. 关闭命令行窗口
2. 重新运行：一键启动脚本.bat
```

### 备份数据
```
mysqldump -u root -p stake_management_v2 > 备份文件名.sql
```

---

## 🔧 故障排除

### 问题1：无法启动
**解决**：运行 `系统检查脚本.bat` 检查环境

### 问题2：数据库连接失败
**解决**：
1. 确认MySQL服务正在运行
2. 检查密码是否为 `Yiwill@2025`
3. 重新运行 `数据库初始化脚本.bat`

### 问题3：端口被占用
**解决**：
1. 关闭其他占用端口的程序
2. 或重启电脑

### 问题4：页面打不开
**解决**：
1. 确认两个服务都已启动
2. 手动访问：http://localhost:5173
3. 检查防火墙设置

---

## 📞 获取帮助

1. **查看详细文档**：`Windows用户部署指南.md`
2. **系统检查**：运行 `系统检查脚本.bat`
3. **联系技术支持**：提供错误信息截图

---

## 🎯 系统功能概览

### 主要模块
- **公司信息管理**：添加、编辑公司基本信息
- **股东信息管理**：管理股东及持股比例
- **任职档案管理**：记录人员任职情况
- **业务板块管理**：管理业务分类
- **变更确认**：处理信息变更申请

### 操作流程
1. 添加公司基本信息
2. 录入股东信息
3. 添加高管任职信息
4. 设置业务板块
5. 处理变更申请

---

**🎉 恭喜！您已完成股权管理系统的快速部署！**
