@echo off
chcp 65001 >nul
title 股权管理系统 - 数据库初始化

echo ========================================
echo         股权管理系统
echo       数据库初始化脚本 v1.0
echo ========================================
echo.

:: 检查MySQL是否安装
echo [检查] 正在检查MySQL安装状态...
mysql --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到MySQL！
    echo.
    echo 请先安装MySQL数据库:
    echo 1. 访问 https://dev.mysql.com/downloads/mysql/
    echo 2. 下载MySQL Community Server
    echo 3. 安装时设置root密码为: Yiwill@2025
    echo 4. 确保MySQL服务正在运行
    echo.
    pause
    exit /b 1
) else (
    echo [成功] MySQL已安装
)

:: 检查数据库结构文件
if not exist "database-schema-complete.sql" (
    echo [错误] 未找到数据库结构文件: database-schema-complete.sql
    echo 请确保该文件在当前目录下
    pause
    exit /b 1
)

echo.
echo ========================================
echo           数据库配置向导
echo ========================================
echo.

:: 获取MySQL root密码
set /p MYSQL_PASSWORD="请输入MySQL root密码 (默认: Yiwill@2025): "
if "%MYSQL_PASSWORD%"=="" set MYSQL_PASSWORD=Yiwill@2025

echo.
echo [信息] 即将执行以下操作:
echo 1. 创建数据库: stake_management_v2
echo 2. 导入数据库结构和基础数据
echo 3. 创建专用数据库用户 (可选)
echo.

set /p CONFIRM="确认继续? (Y/N): "
if /i not "%CONFIRM%"=="Y" (
    echo 操作已取消
    pause
    exit /b 0
)

echo.
echo [执行] 正在创建数据库...

:: 创建数据库
echo CREATE DATABASE IF NOT EXISTS stake_management_v2 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; | mysql -u root -p%MYSQL_PASSWORD%
if errorlevel 1 (
    echo [错误] 数据库创建失败！
    echo 请检查:
    echo 1. MySQL服务是否正在运行
    echo 2. root密码是否正确
    echo 3. 是否有足够的权限
    pause
    exit /b 1
)

echo [成功] 数据库创建完成

echo [执行] 正在导入数据库结构...
mysql -u root -p%MYSQL_PASSWORD% stake_management_v2 < database-schema-complete.sql
if errorlevel 1 (
    echo [错误] 数据库结构导入失败！
    echo 请检查 database-schema-complete.sql 文件是否完整
    pause
    exit /b 1
)

echo [成功] 数据库结构导入完成

:: 询问是否创建专用用户
echo.
set /p CREATE_USER="是否创建专用数据库用户? (推荐) (Y/N): "
if /i "%CREATE_USER%"=="Y" (
    echo.
    echo [执行] 正在创建专用数据库用户...
    
    :: 创建用户的SQL命令
    (
        echo CREATE USER IF NOT EXISTS 'stake_user'@'localhost' IDENTIFIED BY 'stake_password_2025';
        echo GRANT ALL PRIVILEGES ON stake_management_v2.* TO 'stake_user'@'localhost';
        echo FLUSH PRIVILEGES;
    ) | mysql -u root -p%MYSQL_PASSWORD%
    
    if errorlevel 1 (
        echo [警告] 专用用户创建失败，将使用root用户
    ) else (
        echo [成功] 专用用户创建完成
        echo 用户名: stake_user
        echo 密码: stake_password_2025
        echo.
        echo [重要] 请修改 server/index.js 中的数据库配置:
        echo   user: 'stake_user'
        echo   password: 'stake_password_2025'
    )
)

:: 测试数据库连接
echo.
echo [测试] 正在测试数据库连接...
echo SELECT 'Database connection test successful' as result; | mysql -u root -p%MYSQL_PASSWORD% stake_management_v2 >nul 2>&1
if errorlevel 1 (
    echo [警告] 数据库连接测试失败
) else (
    echo [成功] 数据库连接测试通过
)

:: 显示数据库信息
echo.
echo ========================================
echo           初始化完成！
echo ========================================
echo.
echo 数据库信息:
echo   数据库名: stake_management_v2
echo   主机地址: localhost
echo   端口号: 3306
echo   字符集: utf8mb4
echo.

if /i "%CREATE_USER%"=="Y" (
    echo 推荐连接配置:
    echo   用户名: stake_user
    echo   密码: stake_password_2025
    echo.
    echo 备用连接配置:
    echo   用户名: root
    echo   密码: %MYSQL_PASSWORD%
) else (
    echo 连接配置:
    echo   用户名: root
    echo   密码: %MYSQL_PASSWORD%
)

echo.
echo 下一步操作:
echo 1. 运行 "一键启动脚本.bat" 启动系统
echo 2. 或手动执行: npm run server 和 npm run dev
echo 3. 在浏览器访问: http://localhost:5173
echo.

:: 询问是否立即启动系统
set /p START_SYSTEM="是否立即启动系统? (Y/N): "
if /i "%START_SYSTEM%"=="Y" (
    echo.
    echo [启动] 正在启动股权管理系统...
    call "一键启动脚本.bat"
) else (
    echo.
    echo 数据库初始化完成！
    echo 您可以随时运行 "一键启动脚本.bat" 来启动系统
)

echo.
pause
