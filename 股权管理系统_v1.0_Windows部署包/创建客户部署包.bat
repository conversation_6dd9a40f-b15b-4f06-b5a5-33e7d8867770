@echo off
chcp 65001 >nul
title 创建客户部署包

echo ========================================
echo        股权管理系统
echo      创建客户部署包脚本
echo ========================================
echo.

:: 设置变量
set PACKAGE_NAME=股权管理系统_v1.0_Windows部署包
set PACKAGE_DIR=%PACKAGE_NAME%
set CURRENT_DIR=%CD%

echo [信息] 当前目录: %CURRENT_DIR%
echo [信息] 部署包名称: %PACKAGE_NAME%
echo.

:: 检查必需文件
echo [检查] 验证必需文件是否存在...

if not exist "package.json" (
    echo [错误] 缺少 package.json 文件
    goto :error
)

if not exist "src" (
    echo [错误] 缺少 src 目录
    goto :error
)

if not exist "server" (
    echo [错误] 缺少 server 目录
    goto :error
)

if not exist "database-schema-complete.sql" (
    echo [错误] 缺少 database-schema-complete.sql 文件
    goto :error
)

if not exist "快速入门指南.md" (
    echo [错误] 缺少 快速入门指南.md 文件
    goto :error
)

echo [成功] 必需文件检查通过
echo.

:: 清理旧的部署包
if exist "%PACKAGE_DIR%" (
    echo [清理] 删除旧的部署包目录...
    rmdir /s /q "%PACKAGE_DIR%"
)

if exist "%PACKAGE_NAME%.zip" (
    echo [清理] 删除旧的压缩包...
    del "%PACKAGE_NAME%.zip"
)

:: 创建部署包目录
echo [创建] 创建部署包目录...
mkdir "%PACKAGE_DIR%"

:: 复制核心文件
echo [复制] 复制核心项目文件...

:: 复制配置文件
copy "package.json" "%PACKAGE_DIR%\" >nul
if exist "package-lock.json" copy "package-lock.json" "%PACKAGE_DIR%\" >nul
copy "index.html" "%PACKAGE_DIR%\" >nul
if exist "vite.config.ts" copy "vite.config.ts" "%PACKAGE_DIR%\" >nul
if exist "tsconfig.json" copy "tsconfig.json" "%PACKAGE_DIR%\" >nul
if exist "tsconfig.app.json" copy "tsconfig.app.json" "%PACKAGE_DIR%\" >nul
if exist "tsconfig.node.json" copy "tsconfig.node.json" "%PACKAGE_DIR%\" >nul
if exist "tailwind.config.js" copy "tailwind.config.js" "%PACKAGE_DIR%\" >nul
if exist "eslint.config.js" copy "eslint.config.js" "%PACKAGE_DIR%\" >nul

:: 复制源代码目录
echo [复制] 复制前端源代码...
xcopy "src" "%PACKAGE_DIR%\src\" /E /I /Q >nul

echo [复制] 复制后端代码...
xcopy "server" "%PACKAGE_DIR%\server\" /E /I /Q >nul

echo [复制] 复制静态资源...
if exist "public" xcopy "public" "%PACKAGE_DIR%\public\" /E /I /Q >nul

:: 复制数据库文件
echo [复制] 复制数据库文件...
copy "database-schema-complete.sql" "%PACKAGE_DIR%\" >nul

:: 复制部署脚本
echo [复制] 复制部署脚本...
copy "系统检查脚本.bat" "%PACKAGE_DIR%\" >nul 2>&1
copy "数据库初始化脚本.bat" "%PACKAGE_DIR%\" >nul 2>&1
copy "一键启动脚本.bat" "%PACKAGE_DIR%\" >nul 2>&1

:: 复制说明文档
echo [复制] 复制说明文档...
copy "快速入门指南.md" "%PACKAGE_DIR%\" >nul
copy "Windows用户部署指南.md" "%PACKAGE_DIR%\" >nul 2>&1
copy "Windows数据库配置说明.md" "%PACKAGE_DIR%\" >nul 2>&1
copy "部署包说明.md" "%PACKAGE_DIR%\" >nul 2>&1

:: 复制README（如果存在）
if exist "README.md" copy "README.md" "%PACKAGE_DIR%\" >nul

:: 创建客户专用README
echo [创建] 创建客户专用说明文件...
(
echo # 股权管理系统 - 客户部署包
echo.
echo ## 🚀 快速开始
echo.
echo 1. **首先阅读**: 快速入门指南.md
echo 2. **详细说明**: Windows用户部署指南.md
echo 3. **开始部署**: 双击运行"系统检查脚本.bat"
echo.
echo ## 📁 文件说明
echo.
echo - `快速入门指南.md` - 5分钟快速部署指南
echo - `Windows用户部署指南.md` - 详细部署说明
echo - `系统检查脚本.bat` - 检查系统环境
echo - `数据库初始化脚本.bat` - 初始化数据库
echo - `一键启动脚本.bat` - 启动系统服务
echo.
echo ## 📞 技术支持
echo.
echo 如遇问题，请联系技术支持并提供详细的错误信息。
echo.
echo **祝您使用愉快！**
) > "%PACKAGE_DIR%\客户使用说明.md"

:: 验证部署包内容
echo.
echo [验证] 验证部署包内容...
set ERROR_COUNT=0

if not exist "%PACKAGE_DIR%\package.json" (
    echo [错误] package.json 复制失败
    set /a ERROR_COUNT+=1
)

if not exist "%PACKAGE_DIR%\src" (
    echo [错误] src 目录复制失败
    set /a ERROR_COUNT+=1
)

if not exist "%PACKAGE_DIR%\server" (
    echo [错误] server 目录复制失败
    set /a ERROR_COUNT+=1
)

if not exist "%PACKAGE_DIR%\快速入门指南.md" (
    echo [错误] 快速入门指南.md 复制失败
    set /a ERROR_COUNT+=1
)

if %ERROR_COUNT% GTR 0 (
    echo [错误] 部署包创建失败，发现 %ERROR_COUNT% 个错误
    goto :error
)

echo [成功] 部署包内容验证通过

:: 显示部署包信息
echo.
echo ========================================
echo           部署包创建成功！
echo ========================================
echo.
echo 部署包位置: %CURRENT_DIR%\%PACKAGE_DIR%
echo.
echo 包含文件:
dir "%PACKAGE_DIR%" /B | findstr /V /C:"." >nul || (
    for /f %%f in ('dir "%PACKAGE_DIR%" /B') do echo   - %%f
)
echo.

:: 询问是否创建压缩包
set /p CREATE_ZIP="是否创建ZIP压缩包? (Y/N): "
if /i "%CREATE_ZIP%"=="Y" (
    echo.
    echo [创建] 正在创建ZIP压缩包...
    
    :: 使用PowerShell创建ZIP文件
    powershell -command "Compress-Archive -Path '%PACKAGE_DIR%' -DestinationPath '%PACKAGE_NAME%.zip' -Force"
    
    if exist "%PACKAGE_NAME%.zip" (
        echo [成功] ZIP压缩包创建完成: %PACKAGE_NAME%.zip
        
        :: 获取文件大小
        for %%A in ("%PACKAGE_NAME%.zip") do set FILE_SIZE=%%~zA
        set /a FILE_SIZE_MB=%FILE_SIZE%/1024/1024
        echo [信息] 压缩包大小: %FILE_SIZE_MB% MB
    ) else (
        echo [错误] ZIP压缩包创建失败
        goto :error
    )
)

echo.
echo ========================================
echo           任务完成！
echo ========================================
echo.
echo 客户部署包已准备就绪：
echo.
echo 📁 文件夹: %PACKAGE_DIR%
if exist "%PACKAGE_NAME%.zip" echo 📦 压缩包: %PACKAGE_NAME%.zip
echo.
echo 交付给客户：
echo 1. 发送压缩包文件
echo 2. 告知客户阅读"快速入门指南.md"
echo 3. 提供技术支持联系方式
echo.
echo 🎉 准备完成，可以交付给客户了！

goto :end

:error
echo.
echo [错误] 部署包创建失败！
echo 请检查文件完整性后重试。
pause
exit /b 1

:end
echo.
pause
