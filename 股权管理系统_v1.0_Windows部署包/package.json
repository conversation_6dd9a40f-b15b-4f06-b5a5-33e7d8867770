{"name": "stake-share-management", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "server": "node --experimental-modules --es-module-specifier-resolution=node server/index.js"}, "dependencies": {"@ant-design/icons": "^6.0.0", "antd": "^5.12.2", "axios": "^1.10.0", "body-parser": "^1.20.2", "cors": "^2.8.5", "dayjs": "^1.11.13", "express": "^4.18.2", "moment": "^2.30.1", "mysql2": "^3.6.5", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0"}}