@echo off
chcp 65001 >nul
title 股权管理系统 - 系统检查工具

echo ========================================
echo         股权管理系统
echo       系统检查工具 v1.0
echo ========================================
echo.

set ERROR_COUNT=0
set WARNING_COUNT=0

echo [开始] 系统环境检查...
echo.

:: 检查1: 操作系统
echo [检查1] 操作系统信息
echo 系统版本: %OS%
for /f "tokens=4-5 delims=. " %%i in ('ver') do set VERSION=%%i.%%j
echo 版本号: %VERSION%
echo.

:: 检查2: Node.js
echo [检查2] Node.js环境
node --version >nul 2>&1
if errorlevel 1 (
    echo [❌错误] Node.js未安装或未添加到PATH
    echo 解决方案: 
    echo   1. 下载安装Node.js: https://nodejs.org/zh-cn/
    echo   2. 安装时确保勾选"Add to PATH"选项
    set /a ERROR_COUNT+=1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo [✅正常] Node.js版本: %NODE_VERSION%
)

npm --version >nul 2>&1
if errorlevel 1 (
    echo [❌错误] npm未安装或未添加到PATH
    set /a ERROR_COUNT+=1
) else (
    for /f "tokens=*" %%i in ('npm --version') do set NPM_VERSION=%%i
    echo [✅正常] npm版本: %NPM_VERSION%
)
echo.

:: 检查3: MySQL
echo [检查3] MySQL数据库
mysql --version >nul 2>&1
if errorlevel 1 (
    echo [❌错误] MySQL未安装或未添加到PATH
    echo 解决方案:
    echo   1. 下载安装MySQL: https://dev.mysql.com/downloads/mysql/
    echo   2. 安装后将MySQL的bin目录添加到系统PATH
    set /a ERROR_COUNT+=1
) else (
    for /f "tokens=*" %%i in ('mysql --version') do set MYSQL_VERSION=%%i
    echo [✅正常] MySQL版本: %MYSQL_VERSION%
)

:: 检查MySQL服务状态
sc query mysql >nul 2>&1
if errorlevel 1 (
    echo [⚠️警告] MySQL服务未运行或未安装
    echo 解决方案: 启动MySQL服务
    set /a WARNING_COUNT+=1
) else (
    echo [✅正常] MySQL服务正在运行
)
echo.

:: 检查4: 项目文件
echo [检查4] 项目文件完整性
if not exist "package.json" (
    echo [❌错误] 缺少package.json文件
    echo 当前目录: %CD%
    echo 解决方案: 确保在正确的项目目录下运行此脚本
    set /a ERROR_COUNT+=1
) else (
    echo [✅正常] package.json文件存在
)

if not exist "server\index.js" (
    echo [❌错误] 缺少后端服务文件: server\index.js
    set /a ERROR_COUNT+=1
) else (
    echo [✅正常] 后端服务文件存在
)

if not exist "src" (
    echo [❌错误] 缺少前端源码目录: src
    set /a ERROR_COUNT+=1
) else (
    echo [✅正常] 前端源码目录存在
)

if not exist "database-schema-complete.sql" (
    echo [⚠️警告] 缺少数据库结构文件: database-schema-complete.sql
    echo 影响: 无法自动初始化数据库
    set /a WARNING_COUNT+=1
) else (
    echo [✅正常] 数据库结构文件存在
)
echo.

:: 检查5: 项目依赖
echo [检查5] 项目依赖
if not exist "node_modules" (
    echo [⚠️警告] 项目依赖未安装
    echo 解决方案: 运行 npm install 安装依赖
    set /a WARNING_COUNT+=1
) else (
    echo [✅正常] 项目依赖已安装
    
    :: 检查关键依赖包
    if exist "node_modules\express" (
        echo [✅正常] Express框架已安装
    ) else (
        echo [❌错误] Express框架缺失
        set /a ERROR_COUNT+=1
    )
    
    if exist "node_modules\mysql2" (
        echo [✅正常] MySQL驱动已安装
    ) else (
        echo [❌错误] MySQL驱动缺失
        set /a ERROR_COUNT+=1
    )
    
    if exist "node_modules\react" (
        echo [✅正常] React框架已安装
    ) else (
        echo [❌错误] React框架缺失
        set /a ERROR_COUNT+=1
    )
)
echo.

:: 检查6: 端口占用
echo [检查6] 端口占用情况
netstat -an | findstr ":5173" >nul
if not errorlevel 1 (
    echo [⚠️警告] 端口5173已被占用 (前端端口)
    echo 解决方案: 关闭占用端口的程序或修改端口配置
    set /a WARNING_COUNT+=1
) else (
    echo [✅正常] 端口5173可用 (前端端口)
)

netstat -an | findstr ":8080" >nul
if not errorlevel 1 (
    echo [⚠️警告] 端口8080已被占用 (后端端口)
    echo 解决方案: 关闭占用端口的程序或修改端口配置
    set /a WARNING_COUNT+=1
) else (
    echo [✅正常] 端口8080可用 (后端端口)
)

netstat -an | findstr ":3306" >nul
if not errorlevel 1 (
    echo [✅正常] 端口3306已占用 (MySQL端口，正常)
) else (
    echo [⚠️警告] 端口3306未被占用 (MySQL可能未运行)
    set /a WARNING_COUNT+=1
)
echo.

:: 检查7: 磁盘空间
echo [检查7] 磁盘空间
for /f "tokens=3" %%a in ('dir /-c %SystemDrive%\ ^| findstr /i "bytes free"') do set FREE_SPACE=%%a
echo 系统盘可用空间: %FREE_SPACE% 字节
echo.

:: 检查8: 内存使用
echo [检查8] 系统内存
for /f "skip=1" %%p in ('wmic computersystem get TotalPhysicalMemory') do (
    set TOTAL_MEMORY=%%p
    goto :memory_done
)
:memory_done
if defined TOTAL_MEMORY (
    set /a TOTAL_MEMORY_GB=%TOTAL_MEMORY:~0,-9%
    echo 总内存: %TOTAL_MEMORY_GB% GB
    if %TOTAL_MEMORY_GB% LSS 8 (
        echo [⚠️警告] 内存不足8GB，可能影响系统性能
        set /a WARNING_COUNT+=1
    ) else (
        echo [✅正常] 内存充足
    )
) else (
    echo [⚠️警告] 无法获取内存信息
    set /a WARNING_COUNT+=1
)
echo.

:: 生成检查报告
echo ========================================
echo           检查报告
echo ========================================
echo.
echo 检查完成时间: %date% %time%
echo 错误数量: %ERROR_COUNT%
echo 警告数量: %WARNING_COUNT%
echo.

if %ERROR_COUNT% EQU 0 (
    if %WARNING_COUNT% EQU 0 (
        echo [🎉状态] 系统环境完美，可以正常运行！
        echo.
        echo 建议操作:
        echo 1. 运行 "数据库初始化脚本.bat" 初始化数据库
        echo 2. 运行 "一键启动脚本.bat" 启动系统
    ) else (
        echo [⚠️状态] 系统基本正常，但有一些警告需要注意
        echo.
        echo 建议操作:
        echo 1. 解决上述警告问题
        echo 2. 运行 "数据库初始化脚本.bat" 初始化数据库
        echo 3. 运行 "一键启动脚本.bat" 启动系统
    )
) else (
    echo [❌状态] 系统存在严重问题，需要先解决错误才能运行
    echo.
    echo 必须操作:
    echo 1. 解决上述所有错误问题
    echo 2. 重新运行此检查脚本确认
    echo 3. 然后再进行数据库初始化和系统启动
)

echo.
echo ========================================
echo.

:: 询问是否查看详细帮助
set /p SHOW_HELP="是否查看详细帮助信息? (Y/N): "
if /i "%SHOW_HELP%"=="Y" (
    echo.
    echo ========================================
    echo           详细帮助信息
    echo ========================================
    echo.
    echo 常见问题解决方案:
    echo.
    echo 1. Node.js安装问题:
    echo    - 访问 https://nodejs.org/zh-cn/
    echo    - 下载LTS版本
    echo    - 安装时勾选"Add to PATH"
    echo    - 重启命令提示符
    echo.
    echo 2. MySQL安装问题:
    echo    - 访问 https://dev.mysql.com/downloads/mysql/
    echo    - 下载MySQL Community Server
    echo    - 安装时设置root密码
    echo    - 启动MySQL服务
    echo.
    echo 3. 端口占用问题:
    echo    - 查看占用进程: netstat -ano ^| findstr :端口号
    echo    - 结束进程: taskkill /PID 进程ID /F
    echo    - 或修改配置文件中的端口号
    echo.
    echo 4. 依赖安装问题:
    echo    - 确保网络连接正常
    echo    - 运行: npm cache clean --force
    echo    - 重新运行: npm install
    echo.
    echo 5. 权限问题:
    echo    - 以管理员身份运行命令提示符
    echo    - 或修改文件夹权限
)

echo.
pause
