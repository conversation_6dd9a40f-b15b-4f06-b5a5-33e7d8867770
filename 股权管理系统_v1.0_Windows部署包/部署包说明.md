# 股权管理系统 - 部署包说明

## 📦 部署包内容

本部署包为Windows用户提供了完整的股权管理系统安装方案，包含以下文件：

### 📋 核心文件
- `package.json` - 项目配置文件
- `src/` - 前端源代码目录
- `server/` - 后端服务代码目录
- `public/` - 静态资源文件
- `database-schema-complete.sql` - 完整数据库结构文件

### 📖 用户指南
- `快速入门指南.md` - **5分钟快速部署指南**（推荐首读）
- `Windows用户部署指南.md` - 详细部署说明文档
- `Windows数据库配置说明.md` - 数据库配置专项说明

### 🔧 自动化脚本
- `系统检查脚本.bat` - 检查系统环境和依赖
- `数据库初始化脚本.bat` - 一键创建和初始化数据库
- `一键启动脚本.bat` - 一键启动前后端服务

### 🛠️ 配置文件
- `server/index-windows.js` - Windows优化的服务器配置文件

## 🚀 快速开始

### 新手用户（推荐）
1. 阅读 `快速入门指南.md`
2. 按照指南三步完成部署

### 有经验用户
1. 运行 `系统检查脚本.bat` 检查环境
2. 运行 `数据库初始化脚本.bat` 初始化数据库
3. 运行 `一键启动脚本.bat` 启动系统

## 📋 系统要求

### 硬件要求
- **操作系统**: Windows 10/11 (64位)
- **内存**: 最少8GB，推荐16GB
- **硬盘**: 最少10GB可用空间
- **处理器**: 双核以上

### 软件要求
- **Node.js**: v18+ LTS版本
- **MySQL**: v8.0+
- **浏览器**: Chrome、Edge、Firefox等现代浏览器

## 🔧 安装步骤概览

### 第一步：环境准备
1. 安装Node.js（https://nodejs.org/zh-cn/）
2. 安装MySQL（https://dev.mysql.com/downloads/mysql/）
3. 解压部署包到 `C:\股权管理系统\`

### 第二步：系统检查
运行 `系统检查脚本.bat` 确保环境就绪

### 第三步：数据库初始化
运行 `数据库初始化脚本.bat` 创建数据库

### 第四步：启动系统
运行 `一键启动脚本.bat` 启动服务

### 第五步：访问系统
浏览器访问 http://localhost:5173

## 🎯 功能特性

### 核心功能
- **公司信息管理**: 完整的公司基础信息管理
- **股东信息管理**: 股东持股比例和投资关系管理
- **任职档案管理**: 人员任职情况记录和查询
- **业务板块管理**: 公司业务分类和统计
- **变更确认系统**: 信息变更申请和审批流程

### 技术特性
- **响应式设计**: 支持不同屏幕尺寸
- **数据安全**: 本地部署，数据完全可控
- **用户友好**: 直观的操作界面
- **高性能**: 优化的数据库查询和前端渲染
- **可扩展**: 模块化设计，便于功能扩展

## 🔒 安全说明

### 数据安全
- 所有数据存储在本地MySQL数据库
- 不涉及任何云服务或外部数据传输
- 支持数据备份和恢复

### 访问控制
- 系统默认只允许本机访问
- 可配置局域网访问（需要额外设置）
- 建议在可信网络环境中使用

### 密码安全
- 默认MySQL密码：`Yiwill@2025`
- 建议部署后立即修改为强密码
- 支持创建专用数据库用户

## 📞 技术支持

### 自助解决
1. 查看对应的说明文档
2. 运行系统检查脚本诊断问题
3. 查看命令行窗口的错误信息

### 常见问题
- **端口占用**: 关闭占用程序或修改端口配置
- **数据库连接失败**: 检查MySQL服务和密码
- **依赖安装失败**: 检查网络连接，清除npm缓存
- **页面无法访问**: 确认服务启动，检查防火墙

### 联系支持
如遇到无法解决的问题，请联系技术支持并提供：
- 具体错误信息截图
- 系统检查脚本的输出结果
- 操作步骤描述

## 📊 性能优化建议

### 系统优化
- 使用SSD硬盘提升性能
- 确保充足的内存（推荐16GB+）
- 关闭不必要的后台程序

### 数据库优化
- 定期清理无用数据
- 定期备份重要数据
- 监控数据库性能

### 网络优化
- 使用有线网络连接
- 确保网络稳定性
- 避免网络拥堵时段使用

## 🔄 维护说明

### 日常维护
- 定期重启服务（建议每周一次）
- 定期备份数据库数据
- 监控系统运行状态

### 数据备份
```bash
# 备份命令
mysqldump -u root -p stake_management_v2 > backup_日期.sql

# 恢复命令
mysql -u root -p stake_management_v2 < backup_日期.sql
```

### 系统更新
1. 备份当前数据
2. 停止所有服务
3. 替换新版本文件
4. 重新启动服务

## 📈 版本信息

- **当前版本**: v1.0
- **发布日期**: 2025年6月30日
- **兼容系统**: Windows 10/11
- **技术栈**: React + Node.js + MySQL

## 🎉 部署成功标志

当您看到以下情况时，说明部署成功：
- ✅ 系统检查脚本无错误提示
- ✅ 数据库初始化脚本执行成功
- ✅ 一键启动脚本成功启动两个服务
- ✅ 浏览器能正常访问 http://localhost:5173
- ✅ 能够正常添加和查看公司信息

---

**恭喜！您已获得完整的股权管理系统部署包！**

请从 `快速入门指南.md` 开始您的部署之旅。
