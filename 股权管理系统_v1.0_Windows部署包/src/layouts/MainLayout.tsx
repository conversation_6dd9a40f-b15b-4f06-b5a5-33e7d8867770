import React, { useState, useRef, useEffect } from 'react';
import { Layout, Avatar, theme } from 'antd';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  UserOutlined,
  HomeOutlined,
  FileTextOutlined,
  SettingOutlined,
  TeamOutlined,
  BankOutlined,
  DatabaseOutlined,
  DownOutlined,
  RightOutlined,
  BellOutlined,
  UsergroupAddOutlined,
  CheckCircleOutlined,
} from '@ant-design/icons';

const { Header, Sider, Content } = Layout;

interface MenuItemType {
  key: string;
  label: string;
  icon: React.ReactNode;
  path: string;
  children?: MenuItemType[];
}

interface MenuState {
  [key: string]: boolean;
}

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [siderWidth, setSiderWidth] = useState(200);
  const [isResizing, setIsResizing] = useState(false);
  const [openMenus, setOpenMenus] = useState<MenuState>({});
  const resizingRef = useRef(false);
  const startXRef = useRef(0);
  const startWidthRef = useRef(0);

  const navigate = useNavigate();
  const location = useLocation();
  
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  useEffect(() => {
    // 添加全局类以禁用文本选择当正在调整大小时
    if (isResizing) {
      document.body.classList.add('resizing');
    } else {
      document.body.classList.remove('resizing');
    }

    return () => {
      document.body.classList.remove('resizing');
    };
  }, [isResizing]);

  // 根据当前路径自动打开对应的菜单
  useEffect(() => {
    const currentPath = location.pathname;
    const newOpenMenus: MenuState = {};

    // 查找当前路径对应的菜单项并打开其父级菜单
    const findAndOpenParentMenus = (items: MenuItemType[], parentKey?: string) => {
      for (const item of items) {
        if (currentPath.startsWith(item.path) && item.path !== '') {
          if (parentKey) {
            newOpenMenus[parentKey] = true;
          }
          newOpenMenus[item.key] = true;
        }
        if (item.children) {
          findAndOpenParentMenus(item.children, item.key);
        }
      }
    };

    findAndOpenParentMenus(menuItems);
    setOpenMenus(newOpenMenus);
  }, [location.pathname]);

  const handleResizeStart = (e: React.MouseEvent) => {
    e.preventDefault();
    resizingRef.current = true;
    setIsResizing(true);
    startXRef.current = e.clientX;
    startWidthRef.current = siderWidth;
    document.addEventListener('mousemove', handleResize);
    document.addEventListener('mouseup', handleResizeEnd);
  };

  const handleResize = (e: MouseEvent) => {
    if (!resizingRef.current) return;
    const newWidth = startWidthRef.current + (e.clientX - startXRef.current);
    if (newWidth >= 150 && newWidth <= 400) {
      setSiderWidth(newWidth);
    }
  };

  const handleResizeEnd = () => {
    resizingRef.current = false;
    setIsResizing(false);
    document.removeEventListener('mousemove', handleResize);
    document.removeEventListener('mouseup', handleResizeEnd);
  };

  const menuItems: MenuItemType[] = [
    {
      key: 'task',
      label: '待办任务提醒',
      icon: <BellOutlined />,
      path: '/task',
      children: [
        { key: 'task-add', label: '新增任务', icon: <RightOutlined />, path: '/task/add' },
        { key: 'task-annual-report', label: '新增任务-年审年报', icon: <RightOutlined />, path: '/task/annual-report' },
        { key: 'task-address-maintenance', label: '新增任务-地址维护', icon: <RightOutlined />, path: '/task/address-maintenance' },
        { key: 'task-custom', label: '新增任务-自定义任务', icon: <RightOutlined />, path: '/task/custom' },
        { key: 'task-edit', label: '编辑任务', icon: <RightOutlined />, path: '/task/edit' },
        { key: 'task-update-progress', label: '更新进度', icon: <RightOutlined />, path: '/task/update-progress' },
        { key: 'task-verify', label: '核实', icon: <RightOutlined />, path: '/task/verify' },
        { key: 'task-investigation', label: '任务排查', icon: <RightOutlined />, path: '/task/investigation' }
      ]
    },
    {
      key: 'company-change-confirmation',
      label: '公司信息变更确认',
      icon: <CheckCircleOutlined />,
      path: '/company-change-confirmation'
    },
    {
      key: 'company',
      label: '公司信息',
      icon: <TeamOutlined />,
      path: '/company/info',
      children: [
        { key: 'company-add', label: '新增公司', icon: <RightOutlined />, path: '/company/add' },
        { key: 'company-detail', label: '公司详情', icon: <RightOutlined />, path: '/company/detail' },
        { key: 'company-finance', label: '添加财务信息', icon: <RightOutlined />, path: '/company/finance' },
        {
          key: 'company-change',
          label: '变更信息',
          icon: <RightOutlined />,
          path: '/company/change',
          children: [
            { key: 'company-change-basic-info', label: '变更基础信息', icon: <RightOutlined />, path: '/company/change/basic-info' },
            { key: 'company-change-executive-info', label: '变更高管信息', icon: <RightOutlined />, path: '/company/change/executive-info' },
            { key: 'company-change-shareholder-info', label: '变更股东信息', icon: <RightOutlined />, path: '/company/change/shareholder-info' },
            { key: 'company-change-investment-info', label: '变更对外投资信息', icon: <RightOutlined />, path: '/company/change/investment-info' },
          ]
        },
        { key: 'company-equity-chart', label: '股权图-主体公司', icon: <RightOutlined />, path: '/company/equity-chart' }
      ]
    },
    {
      key: 'employment',
      label: '任职档案',
      icon: <FileTextOutlined />,
      path: '/employment',
      children: [
        { key: 'employment-add', label: '新增人员', icon: <RightOutlined />, path: '/employment/add-person' }
      ]
    },
    {
      key: 'business',
      label: '业务板块',
      icon: <SettingOutlined />,
      path: '/business',
      children: [
        { key: 'business-add', label: '新增业务板块', icon: <RightOutlined />, path: '/business/add' },
        { key: 'business-equity', label: '股权图-业务板块', icon: <RightOutlined />, path: '/business/equity-chart' }
      ]
    },
    {
      key: 'shareholder',
      label: '股东信息',
      icon: <UsergroupAddOutlined />,
      path: '/shareholder',
      children: [
        { key: 'shareholder-add', label: '新增股东', icon: <RightOutlined />, path: '/shareholder/add' },
        { key: 'shareholder-register', label: '股东登记', icon: <RightOutlined />, path: '/shareholder/register' }
      ]
    },
    {
      key: 'basic-data',
      label: '基础数据',
      icon: <DatabaseOutlined />,
      path: '', // 基础数据不关联页面
      children: [
        { key: 'basic-data-region', label: '地区管理', icon: <RightOutlined />, path: '/basic-data/region' },
        { key: 'basic-data-agency', label: '代理机构', icon: <RightOutlined />, path: '/basic-data/agency' },
        { key: 'basic-data-dictionary', label: '数据字典', icon: <RightOutlined />, path: '/basic-data/dictionary' }
      ]
    },
    {
      key: 'archive',
      label: '档案更新规范',
      icon: <FileTextOutlined />,
      path: '/archive',
      children: [
        { key: 'archive-add', label: '新增档案规范', icon: <RightOutlined />, path: '/archive/update-rule/add' }
      ]
    },
    {
      key: 'system',
      label: '系统设置',
      icon: <SettingOutlined />,
      path: '/system',
      children: [
        { key: 'system-user-add', label: '新增用户', icon: <RightOutlined />, path: '/system/user/add' },
        { key: 'system-user-management', label: '用户管理', icon: <RightOutlined />, path: '/system/user/management' },
        { key: 'system-database-config', label: '数据库配置', icon: <RightOutlined />, path: '/system/database/config' }
      ]
    }
  ];

  // 处理一级菜单点击：展开菜单 + 跳转页面
  const handleMainMenuClick = (menuKey: string, path: string) => {
    // 如果是基础数据菜单，只展开/折叠，不跳转
    if (menuKey === 'basic-data') {
      setOpenMenus(prev => ({
        ...prev,
        [menuKey]: !prev[menuKey]
      }));
      return;
    }

    // 保持当前页面所属的菜单打开状态，只切换点击的菜单
    setOpenMenus(prev => ({
      ...prev,
      [menuKey]: !prev[menuKey]
    }));

    // 跳转到对应页面
    if (path) {
      navigate(path);
    }
  };

  // 处理二级菜单点击：展开菜单 + 跳转页面
  const handleSubMenuClick = (menuKey: string, path: string, hasChildren: boolean) => {
    if (hasChildren) {
      // 如果有子菜单，展开/折叠子菜单并跳转
      setOpenMenus(prev => ({
        ...prev,
        [menuKey]: !prev[menuKey]
      }));
    }
    // 跳转到对应页面
    navigate(path);
  };

  // 处理箭头点击：只展开/折叠菜单
  const handleArrowClick = (menuKey: string, e: React.MouseEvent) => {
    e.stopPropagation();
    setOpenMenus(prev => ({
      ...prev,
      [menuKey]: !prev[menuKey]
    }));
  };

  // 处理三级菜单点击
  const handleThirdLevelClick = (path: string) => {
    navigate(path);
  };

  // 渲染菜单项
  const renderMenuItem = (item: MenuItemType, level: number = 0) => {
    const hasChildren = item.children && item.children.length > 0;
    const isOpen = openMenus[item.key];
    const isActive = location.pathname === item.path;

    // 调试信息
    if (item.key === 'company-change') {
      console.log('渲染变更信息菜单:', {
        key: item.key,
        hasChildren,
        childrenCount: item.children?.length,
        isOpen,
        level
      });
    }

    const handleClick = () => {
      if (level === 0) {
        handleMainMenuClick(item.key, item.path);
      } else if (level === 1) {
        handleSubMenuClick(item.key, item.path, hasChildren);
      } else {
        handleThirdLevelClick(item.path);
      }
    };

    return (
      <div key={item.key}>
        <div
          className={`menu-item level-${level} ${isActive ? 'active' : ''}`}
          style={{
            display: 'flex',
            alignItems: 'center',
            padding: `8px ${16 + level * 24}px`,
            cursor: 'pointer',
            color: isActive ? '#1890ff' : 'rgba(255, 255, 255, 0.85)',
            backgroundColor: isActive ? 'rgba(24, 144, 255, 0.1)' : 'transparent',
            borderLeft: isActive ? '3px solid #1890ff' : '3px solid transparent',
            fontSize: level > 0 ? '13px' : '14px'
          }}
          onClick={handleClick}
        >
          {item.icon && (
            <span style={{
              marginRight: 8,
              fontSize: level > 0 ? '10px' : '14px',
              opacity: level > 0 ? 0.7 : 1
            }}>
              {item.icon}
            </span>
          )}
          <span style={{ flex: 1 }}>{item.label}</span>
          {hasChildren && level <= 1 && (
            <span
              onClick={(e) => handleArrowClick(item.key, e)}
              style={{ marginLeft: 8, transition: 'transform 0.2s' }}
            >
              {isOpen ? <DownOutlined /> : <RightOutlined />}
            </span>
          )}
        </div>
        {hasChildren && isOpen && (
          <div>
            {item.children!.map(child => renderMenuItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={siderWidth}
        style={{
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
          height: '100vh',
          zIndex: 1000
        }}
      >
        <div className="logo" style={{ height: 64, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <img src="/vite.svg" alt="Logo" style={{ height: 32 }} />
          {!collapsed && <span style={{ color: 'white', marginLeft: 10, fontSize: 18 }}>BLUETTI</span>}
        </div>
        <div
          style={{
            height: 'calc(100vh - 64px)',
            overflowY: 'auto',
            backgroundColor: '#001529'
          }}
        >
          {menuItems.map(item => renderMenuItem(item))}
        </div>
        {!collapsed && (
          <div
            className={`sider-resizer ${isResizing ? 'active' : ''}`}
            onMouseDown={handleResizeStart}
          />
        )}
      </Sider>
      <Layout style={{ marginLeft: collapsed ? 80 : siderWidth }}>
        <Header style={{
          padding: 0,
          background: colorBgContainer,
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          position: 'fixed',
          top: 0,
          right: 0,
          left: collapsed ? 80 : siderWidth,
          zIndex: 999,
          borderBottom: '1px solid #f0f0f0'
        }}>
          <div style={{ paddingLeft: 16 }}>
            {React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {
              className: 'trigger',
              onClick: () => setCollapsed(!collapsed),
              style: { fontSize: '18px' }
            })}
            <span style={{ marginLeft: 16, fontSize: 16 }}>股权管理</span>
          </div>
          <div style={{ paddingRight: 24, display: 'flex', alignItems: 'center' }}>
            <Avatar icon={<UserOutlined />} />
            <span style={{ marginLeft: 8 }}>test_account</span>
          </div>
        </Header>
        <Content
          style={{
            marginTop: 64,
            padding: 24,
            minHeight: 'calc(100vh - 64px)',
            background: '#f5f5f5',
            overflowY: 'auto'
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;