import { createBrowserRouter } from 'react-router-dom';
import MainLayout from './layouts/MainLayout';

// Index Pages
import TaskIndex from './pages/task/TaskIndex';
import CompanyIndex from './pages/company/CompanyIndex';

import BusinessIndex from './pages/business/BusinessIndex';
import BusinessSegmentPage from './pages/business/BusinessSegmentPage';
import ShareholderIndex from './pages/shareholder/ShareholderIndex';

import ArchiveIndex from './pages/archive/ArchiveIndex';
import SystemIndex from './pages/system/SystemIndex';

// Task Pages
import TaskAdd from './pages/task/TaskAdd';
import AnnualReport from './pages/task/AnnualReport';
import AddressMaintenance from './pages/task/AddressMaintenance';
import CustomTask from './pages/task/CustomTask';
import EditTask from './pages/task/EditTask';
import UpdateProgress from './pages/task/UpdateProgress';
import Verify from './pages/task/Verify';
import Investigation from './pages/task/Investigation';

// Company Pages
import CompanyAdd from './pages/company/CompanyAdd';
import CompanyAddWorking from './pages/company/CompanyAddWorking';
import CompanyAddBasic from './pages/company/CompanyAddBasic';
import CompanyAddSimple from './pages/company/CompanyAddSimple';
import CompanyAddTest from './pages/company/CompanyAddTest';
import CompanyInfo from './pages/company/CompanyInfo';
import CompanyDetail from './pages/company/CompanyDetail';
import EquityChart from './pages/company/EquityChart';
import CompanyFinanceAdd from './pages/company/CompanyFinanceAdd';
import RegionManagement from './pages/basic-data/RegionManagement';
import AgencyManagement from './pages/basic-data/AgencyManagement';
import DataDictionary from './pages/basic-data/DataDictionary';
import BasicDataIndexPage from './pages/basic-data/index';
import EmploymentArchive from './pages/employment/EmploymentArchive';
import AddPerson from './pages/employment/AddPerson';

import BasicChange from './pages/company/change/BasicChange';
import ExecutiveChange from './pages/company/change/ExecutiveChange';
import ShareholderChange from './pages/company/change/ShareholderChange';
import ChangeIndex from './pages/company/change/ChangeIndex';
import ChangeBasicInfo from './pages/company/change/ChangeBasicInfo';
import ChangeExecutiveInfo from './pages/company/change/ChangeExecutiveInfo';
import ChangeShareholderInfo from './pages/company/change/ChangeShareholderInfo';
import ChangeInvestmentInfo from './pages/company/change/ChangeInvestmentInfo';

// Company Change Confirmation Page
import CompanyChangeConfirmation from './pages/company-change-confirmation';

// Company Change Basic Page
import CompanyChangeBasic from './pages/company-change-basic';

// Company Change Executive Page
import CompanyChangeExecutive from './pages/company-change-executive';

// Company Change Shareholder Page
import CompanyChangeShareholder from './pages/company-change-shareholder';

// Shareholder Pages
import ShareholderAdd from './pages/shareholder/ShareholderAdd';
import ShareholderRegister from './pages/shareholder/ShareholderRegister';

// Archive Pages
import ArchiveUpdateRuleAdd from './pages/archive/ArchiveUpdateRuleAdd';
import ArchiveUpdateRuleList from './pages/archive/ArchiveUpdateRuleList';
import ArchiveUpdateRuleEdit from './pages/archive/ArchiveUpdateRuleEdit';

// System Pages
import UserAdd from './pages/system/UserAdd';
import UserManagement from './pages/system/UserManagement';
import DatabaseConfig from './pages/system/DatabaseConfig';

const router = createBrowserRouter([
  // Direct test route (bypasses layout)
  {
    path: '/test',
    element: <CompanyAddBasic />,
  },
  {
    path: '/',
    element: <MainLayout />,
    children: [
      {
        index: true,
        element: <TaskIndex />, // Default to task reminder
      },
      // Task Routes
      {
        path: 'task',
        children: [
          {
            index: true,
            element: <TaskIndex />,
          },
          {
            path: 'add',
            element: <TaskAdd />,
          },
          {
            path: 'annual-report',
            element: <AnnualReport />,
          },
          {
            path: 'address-maintenance',
            element: <AddressMaintenance />,
          },
          {
            path: 'custom',
            element: <CustomTask />,
          },
          {
            path: 'edit',
            element: <EditTask />,
          },
          {
            path: 'update-progress',
            element: <UpdateProgress />,
          },
          {
            path: 'verify',
            element: <Verify />,
          },
          {
            path: 'investigation',
            element: <Investigation />,
          },
        ],
      },
      // Company Change Confirmation Route
      {
        path: 'company-change-confirmation',
        element: <CompanyChangeConfirmation />,
      },

      // Companies Route (plural form)
      {
        path: 'companies/:id',
        element: <CompanyDetail />,
      },
      // Company Routes
      {
        path: 'company',
        children: [
          {
            index: true,
            element: <CompanyInfo />,
          },

          {
            path: 'info',
            element: <CompanyInfo />,
          },
          {
            path: 'add',
            element: <CompanyAddWorking />,
          },
          {
            path: 'detail',
            element: <CompanyDetail />,
          },
          {
            path: ':id',
            element: <CompanyDetail />,
          },
          {
            path: 'equity-chart',
            element: <EquityChart />,
          },
          {
            path: 'finance',
            element: <CompanyFinanceAdd />,
          },

          {
            path: 'change',
            children: [
              {
                index: true,
                element: <ChangeIndex />,
              },
              {
                path: 'basic-info',
                element: <CompanyChangeBasic />,
              },
              {
                path: 'executive-info',
                element: <CompanyChangeExecutive />,
              },
              {
                path: 'shareholder-info',
                element: <CompanyChangeShareholder />,
              },
              {
                path: 'shareholder-info',
                element: <ChangeShareholderInfo />,
              },
              {
                path: 'investment-info',
                element: <ChangeInvestmentInfo />,
              },
              // Legacy routes
              {
                path: 'basic',
                element: <BasicChange />,
              },
              {
                path: 'executive',
                element: <ExecutiveChange />,
              },
              {
                path: 'shareholder',
                element: <ShareholderChange />,
              },
            ],
          },
        ],
      },

      // Business Routes
      {
        path: 'business',
        children: [
          {
            index: true,
            element: <BusinessSegmentPage />,
          },
          {
            path: 'add',
            element: <BusinessIndex />,
          },
          {
            path: 'equity-chart',
            element: <BusinessIndex />,
          },
        ],
      },
      // Shareholder Routes
      {
        path: 'shareholder',
        children: [
          {
            index: true,
            element: <ShareholderIndex />,
          },
          {
            path: 'add',
            element: <ShareholderAdd />,
          },
          {
            path: 'register',
            element: <ShareholderRegister />,
          },
        ],
      },
      // Basic Data Routes
      {
        path: 'basic-data',
        children: [
          {
            index: true,
            element: <BasicDataIndexPage />,
          },
          {
            path: 'region',
            element: <RegionManagement />,
          },
          {
            path: 'agency',
            element: <AgencyManagement />,
          },
          {
            path: 'dictionary',
            element: <DataDictionary />,
          },
        ],
      },
      // Archive Routes
      {
        path: 'archive',
        children: [
          {
            index: true,
            element: <ArchiveIndex />,
          },
          {
            path: 'update-rules',
            element: <ArchiveUpdateRuleList />,
          },
          {
            path: 'update-rule/add',
            element: <ArchiveUpdateRuleAdd />,
          },
          {
            path: 'edit/:id',
            element: <ArchiveUpdateRuleEdit />,
          },
        ],
      },

      // Employment Routes
      {
        path: 'employment',
        children: [
          {
            index: true,
            element: <EmploymentArchive />,
          },
          {
            path: 'add-person',
            element: <AddPerson />,
          },
        ],
      },
      // Employment Archive Route (alternative path)
      {
        path: 'employment-archive',
        element: <EmploymentArchive />,
      },

      // System Routes
      {
        path: 'system',
        children: [
          {
            index: true,
            element: <SystemIndex />,
          },
          {
            path: 'user/add',
            element: <UserAdd />,
          },
          {
            path: 'user/management',
            element: <UserManagement />,
          },
          {
            path: 'database/config',
            element: <DatabaseConfig />,
          },
        ],
      },
      // Legacy routes for backward compatibility
      {
        path: 'company/info',
        element: <CompanyInfo />,
      },
    ],
  },
]);

export default router;