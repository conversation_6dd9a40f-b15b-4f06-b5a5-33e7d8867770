import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Table, Modal, Form, message, Space, Select, Pagination } from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
import axios from 'axios';
import './EmploymentArchive.css';

// 配置axios默认URL
axios.defaults.baseURL = 'http://localhost:8080';

interface PersonItem {
  id: string;
  name: string;
  idType: string;
  idNumber: string;
  maskedIdNumber: string;
  createTime: string;
}

interface EmploymentRecord {
  id: string;
  personId: string;
  companyName: string;
  position: string;
  startDate: string;
  endDate: string;
  createTime: string;
}

const EmploymentArchive: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [personData, setPersonData] = useState<PersonItem[]>([]);
  const [employmentData, setEmploymentData] = useState<EmploymentRecord[]>([]);
  const [selectedPerson, setSelectedPerson] = useState<PersonItem | null>(null);
  const [hoveredPersonId, setHoveredPersonId] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingPerson, setEditingPerson] = useState<PersonItem | null>(null);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  }>({
    key: 'position',
    direction: 'asc'
  });
  const [form] = Form.useForm();

  // 模拟人员数据
  const mockPersonData: PersonItem[] = [
    {
      id: '1',
      name: '李明',
      idType: '身份证',
      idNumber: '110101199001012341',
      maskedIdNumber: '110101********2341',
      createTime: '2023-12-01 10:00:00'
    },
    {
      id: '2',
      name: '张华',
      idType: '身份证',
      idNumber: '110101199002022342',
      maskedIdNumber: '110101********2342',
      createTime: '2023-11-15 14:30:00'
    },
    {
      id: '3',
      name: 'John Smith',
      idType: '护照',
      idNumber: 'P123456789',
      maskedIdNumber: 'P1234*****',
      createTime: '2023-10-20 09:15:00'
    },
    {
      id: '4',
      name: '王芳',
      idType: '身份证',
      idNumber: '110101199003032343',
      maskedIdNumber: '110101********2343',
      createTime: '2023-09-10 16:45:00'
    },
    {
      id: '5',
      name: '刘强',
      idType: '身份证',
      idNumber: '110101199004042344',
      maskedIdNumber: '110101********2344',
      createTime: '2023-08-05 11:20:00'
    }
  ];

  // 模拟任职记录数据
  const mockEmploymentData: EmploymentRecord[] = [
    {
      id: '1',
      personId: '1',
      companyName: '深圳市A公司',
      position: '董事',
      startDate: '2023-11-10',
      endDate: '2024-11-10',
      createTime: '2023-11-10 10:00:00'
    },
    {
      id: '2',
      personId: '1',
      companyName: '深圳市A公司',
      position: '监事',
      startDate: '2023-11-10',
      endDate: '',
      createTime: '2023-11-10 10:00:00'
    },
    {
      id: '3',
      personId: '2',
      companyName: '深圳市B公司',
      position: '董事',
      startDate: '2023-11-10',
      endDate: '2024-11-10',
      createTime: '2023-11-10 10:00:00'
    },
    {
      id: '4',
      personId: '2',
      companyName: '深圳市C公司',
      position: '总经理',
      startDate: '2023-11-10',
      endDate: '',
      createTime: '2023-11-10 10:00:00'
    }
  ];

  // 获取数据
  useEffect(() => {
    fetchPersonData();
    fetchEmploymentData();
  }, []);

  const fetchPersonData = async () => {
    try {
      setLoading(true);
      const response = await axios.get('/api/persons');
      if (response.data.success) {
        setPersonData(response.data.data);
      } else {
        console.warn('API获取失败，使用模拟数据');
        setPersonData(mockPersonData);
      }
      setLoading(false);
    } catch (error) {
      console.error('获取人员数据失败:', error);
      setPersonData(mockPersonData);
      setLoading(false);
      message.warning('连接数据库失败，显示模拟数据');
    }
  };

  const fetchEmploymentData = async () => {
    try {
      // 获取个人列表（只包含有任职记录的个人）
      const personsResponse = await axios.get('/api/persons');
      if (personsResponse.data.success) {
        setPersonData(personsResponse.data.data);
      }

      // 获取任职记录
      const response = await axios.get('/api/employment-records');
      if (response.data.success) {
        setEmploymentData(response.data.data);
      } else {
        console.warn('API获取失败，使用模拟数据');
        setEmploymentData(mockEmploymentData);
      }
    } catch (error) {
      console.error('获取任职记录失败:', error);
      setEmploymentData(mockEmploymentData);
      message.warning('连接数据库失败，显示模拟数据');
    }
  };

  // 处理新增人员
  const handleAddPerson = () => {
    setEditingPerson(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 处理编辑人员
  const handleEditPerson = (person: PersonItem) => {
    setEditingPerson(person);
    form.setFieldsValue({
      name: person.name,
      idType: person.idType,
      idNumber: person.idNumber
    });
    setIsModalVisible(true);
  };

  // 处理删除人员
  const handleDeletePerson = (person: PersonItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除人员"${person.name}"吗？此操作不可逆。`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      async onOk() {
        try {
          const response = await axios.delete(`/api/persons/${person.id}`);
          if (response.data.success) {
            message.success('删除成功');
            fetchPersonData();
            if (selectedPerson?.id === person.id) {
              setSelectedPerson(null);
            }
          } else {
            message.error('删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          const newData = personData.filter(item => item.id !== person.id);
          setPersonData(newData);
          if (selectedPerson?.id === person.id) {
            setSelectedPerson(null);
          }
          message.warning('数据库删除失败，仅本地删除');
        }
      },
    });
  };

  // 处理模态框确认
  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();

      // 检查是否重复
      const isDuplicate = personData.some(item =>
        item.name === values.name &&
        item.idType === values.idType &&
        item.idNumber === values.idNumber &&
        (!editingPerson || item.id !== editingPerson.id)
      );

      if (isDuplicate) {
        message.error('已存在该人员');
        return;
      }

      if (editingPerson) {
        // 编辑模式
        try {
          const response = await axios.put(`/api/persons/${editingPerson.id}`, values);
          if (response.data.success) {
            message.success('更新成功');
            fetchPersonData();
          } else {
            message.error('更新失败');
            return;
          }
        } catch (error) {
          console.error('更新失败:', error);
          const newData = personData.map(item =>
            item.id === editingPerson.id
              ? { 
                  ...item, 
                  ...values, 
                  maskedIdNumber: maskIdNumber(values.idNumber, values.idType)
                }
              : item
          );
          setPersonData(newData);
          message.warning('数据库更新失败，仅本地更新');
        }
      } else {
        // 新增模式
        try {
          const response = await axios.post('/api/persons', values);
          if (response.data.success) {
            message.success('创建成功');
            fetchPersonData();
          } else {
            message.error('创建失败');
            return;
          }
        } catch (error) {
          console.error('创建失败:', error);
          const newPerson: PersonItem = {
            id: Date.now().toString(),
            ...values,
            maskedIdNumber: maskIdNumber(values.idNumber, values.idType),
            createTime: new Date().toLocaleString()
          };
          setPersonData([newPerson, ...personData]);
          message.warning('数据库保存失败，仅本地添加');
        }
      }

      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('操作失败:', error);
    }
  };

  // 处理模态框取消
  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  // 掩码处理证件号码
  const maskIdNumber = (idNumber: string, idType: string): string => {
    if (idType === '身份证' && idNumber.length >= 8) {
      return idNumber.substring(0, 6) + '********' + idNumber.substring(idNumber.length - 4);
    } else if (idType === '护照' && idNumber.length >= 6) {
      return idNumber.substring(0, 5) + '*****';
    }
    return idNumber;
  };

  // 处理人员选择
  const handlePersonSelect = (person: PersonItem) => {
    setSelectedPerson(person);
  };

  // 处理排序
  const handleSort = (key: string) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // 过滤人员数据
  const filteredPersonData = personData.filter(person =>
    person.name.toLowerCase().includes(searchKeyword.toLowerCase())
  );

  // 获取选中人员的任职记录
  const selectedPersonEmployment = selectedPerson
    ? employmentData.filter(record => record.personId === selectedPerson.id)
    : [];

  // 排序任职记录
  const sortedEmploymentData = [...selectedPersonEmployment].sort((a, b) => {
    const { key, direction } = sortConfig;
    let aValue = a[key as keyof EmploymentRecord];
    let bValue = b[key as keyof EmploymentRecord];

    // 特殊处理日期字段
    if (key === 'startDate' || key === 'endDate') {
      aValue = aValue || '9999-12-31'; // 空日期排在最后
      bValue = bValue || '9999-12-31';
    }

    if (aValue < bValue) return direction === 'asc' ? -1 : 1;
    if (aValue > bValue) return direction === 'asc' ? 1 : -1;
    
    // 如果主要字段相同，按职位排序
    if (key !== 'position') {
      if (a.position < b.position) return -1;
      if (a.position > b.position) return 1;
    }
    
    // 如果职位也相同，按开始日期排序
    if (key !== 'startDate') {
      const aStart = a.startDate || '9999-12-31';
      const bStart = b.startDate || '9999-12-31';
      if (aStart < bStart) return -1;
      if (aStart > bStart) return 1;
    }
    
    return 0;
  });

  return (
    <div className="employment-archive-container">
      <div className="archive-layout">
        {/* 左侧人员列表 */}
        <div className="person-list-panel">
          <Card className="person-list-card">
            <div className="person-list-header">
              <Input
                placeholder="搜索人员"
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                style={{ marginBottom: 16 }}
              />
              <Button 
                type="primary" 
                onClick={handleAddPerson}
                style={{ width: '100%' }}
              >
                新增
              </Button>
            </div>
            
            <div className="person-list">
              {filteredPersonData.map(person => (
                <div
                  key={person.id}
                  className={`person-item ${selectedPerson?.id === person.id ? 'selected' : ''}`}
                  onClick={() => handlePersonSelect(person)}
                  onMouseEnter={() => setHoveredPersonId(person.id)}
                  onMouseLeave={() => setHoveredPersonId(null)}
                >
                  <div className="person-name">
                    {person.name}({person.maskedIdNumber})
                  </div>
                  {hoveredPersonId === person.id && (
                    <div className="person-actions">
                      <EditOutlined 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditPerson(person);
                        }}
                        style={{ marginRight: 8, color: '#1890ff' }}
                      />
                      <DeleteOutlined 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeletePerson(person);
                        }}
                        style={{ color: '#ff4d4f' }}
                      />
                    </div>
                  )}
                </div>
              ))}
            </div>
          </Card>
        </div>

        {/* 右侧任职记录 */}
        <div className="employment-records-panel">
          <Card className="employment-records-card">
            {selectedPerson ? (
              <>
                <div className="employment-header">
                  <h3>{selectedPerson.name} 的任职记录</h3>
                </div>
                <Table
                  columns={[
                    {
                      title: (
                        <span
                          className="sortable-header"
                          onClick={() => handleSort('companyName')}
                        >
                          任职公司 {sortConfig.key === 'companyName' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                        </span>
                      ),
                      dataIndex: 'companyName',
                      key: 'companyName',
                      align: 'center',
                      render: (text: string) => (
                        <span style={{ color: '#1890ff', cursor: 'pointer' }}>
                          {text}
                        </span>
                      )
                    },
                    {
                      title: (
                        <span
                          className="sortable-header"
                          onClick={() => handleSort('position')}
                        >
                          任职职位 {sortConfig.key === 'position' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                        </span>
                      ),
                      dataIndex: 'position',
                      key: 'position',
                      align: 'center'
                    },
                    {
                      title: (
                        <span
                          className="sortable-header"
                          onClick={() => handleSort('startDate')}
                        >
                          开始日期 {sortConfig.key === 'startDate' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                        </span>
                      ),
                      dataIndex: 'startDate',
                      key: 'startDate',
                      align: 'center'
                    },
                    {
                      title: (
                        <span
                          className="sortable-header"
                          onClick={() => handleSort('endDate')}
                        >
                          结束日期 {sortConfig.key === 'endDate' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                        </span>
                      ),
                      dataIndex: 'endDate',
                      key: 'endDate',
                      align: 'center',
                      render: (text: string) => text || '至今'
                    }
                  ]}
                  dataSource={sortedEmploymentData}
                  rowKey="id"
                  pagination={{
                    pageSize: 10,
                    showTotal: (total) => `共 ${total} 条记录`,
                  }}
                  locale={{ emptyText: '暂无任职记录' }}
                />
              </>
            ) : (
              <div className="empty-selection">
                <p>请选择左侧人员查看任职记录</p>
              </div>
            )}
          </Card>
        </div>
      </div>

      {/* 新增/编辑人员模态框 */}
      <Modal
        title={editingPerson ? '编辑人员' : '新增人员'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          style={{ marginTop: 16 }}
        >
          <Form.Item
            label="姓名"
            name="name"
            rules={[
              { required: true, message: '请输入姓名' },
              { pattern: /^[\u4e00-\u9fa5a-zA-Z\s]+$/, message: '姓名只能包含中英文字符' }
            ]}
          >
            <Input placeholder="请输入姓名" />
          </Form.Item>
          
          <Form.Item
            label="证件类型"
            name="idType"
            rules={[{ required: true, message: '请选择证件类型' }]}
          >
            <Select placeholder="请选择证件类型">
              <Select.Option value="身份证">身份证</Select.Option>
              <Select.Option value="护照">护照</Select.Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            label="证件号码"
            name="idNumber"
            rules={[{ required: true, message: '请输入证件号码' }]}
          >
            <Input placeholder="请输入证件号码" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default EmploymentArchive;
