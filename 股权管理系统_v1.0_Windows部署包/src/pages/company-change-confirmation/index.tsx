import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Modal, message, Tabs, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';

// 统计数据接口
interface StatisticsData {
  basicInfo: number;
  executiveInfo: number;
  shareholderInfo: number;
  investmentInfo: number;
}

// 变更记录接口
interface ChangeRecord {
  id: number;
  changeType: 'basic' | 'executive' | 'shareholder' | 'investment';
  changeTypeText: string;
  content: string;
  changeDate: string;
  operator: string;
  operateDate: string;
  status: 'pending' | 'confirmed';
  statusText: string;
  details?: any;
}

// 添加样式
const styles = `
  .pending-row {
    background-color: #fff2f0 !important;
  }
  .pending-row:hover {
    background-color: #ffe7e0 !important;
  }
  .ant-table-thead > tr > th {
    font-size: 13px !important;
    font-weight: bold !important;
    padding: 8px 6px !important;
  }
  .ant-table-tbody > tr > td {
    padding: 6px 6px !important;
    line-height: 1.3 !important;
  }
  .ant-table-cell {
    word-break: break-word !important;
  }
`;

const CompanyChangeConfirmation: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState<StatisticsData>({
    basicInfo: 0,
    executiveInfo: 0,
    shareholderInfo: 0,
    investmentInfo: 0
  });
  const [changeRecords, setChangeRecords] = useState<ChangeRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<ChangeRecord[]>([]);
  const [activeTab, setActiveTab] = useState('all');
  const [searchText, setSearchText] = useState('');
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<ChangeRecord | null>(null);

  // 获取统计数据
  const fetchStatistics = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/company-change-confirmation/statistics');
      const result = await response.json();
      if (result.success) {
        setStatistics(result.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取变更记录
  const fetchChangeRecords = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/company-change-confirmation/records');
      const result = await response.json();
      if (result.success) {
        setChangeRecords(result.data);
        setFilteredRecords(result.data);
      }
    } catch (error) {
      console.error('获取变更记录失败:', error);
      message.error('获取变更记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 过滤数据
  const filterRecords = (status: string, search: string = searchText) => {
    let filtered = changeRecords;
    
    // 按状态过滤
    if (status === 'pending') {
      filtered = filtered.filter(record => record.status === 'pending');
    } else if (status === 'confirmed') {
      filtered = filtered.filter(record => record.status === 'confirmed');
    }
    
    // 按搜索文本过滤
    if (search) {
      filtered = filtered.filter(record => 
        record.content.toLowerCase().includes(search.toLowerCase()) ||
        record.operator.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    setFilteredRecords(filtered);
  };

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    filterRecords(key);
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
    filterRecords(activeTab, value);
  };

  // 确认变更
  const handleConfirm = async (record: ChangeRecord) => {
    try {
      const response = await fetch(`/api/company-change-confirmation/confirm/${record.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      const result = await response.json();
      if (result.success) {
        message.success('确认成功');
        setDetailModalVisible(false);
        fetchChangeRecords();
        fetchStatistics();
      } else {
        message.error(result.message || '确认失败');
      }
    } catch (error) {
      console.error('确认失败:', error);
      message.error('确认失败');
    }
  };

  // 显示详情
  const showDetails = (record: ChangeRecord) => {
    setSelectedRecord(record);
    setDetailModalVisible(true);
  };

  useEffect(() => {
    fetchStatistics();
    fetchChangeRecords();
  }, []);

  // 表格列定义
  const columns: ColumnsType<ChangeRecord> = [
    {
      title: '任务状态',
      dataIndex: 'statusText',
      key: 'status',
      width: 80,
      align: 'center',
      render: (text: string, record: ChangeRecord) => (
        <span style={{
          color: record.status === 'pending' ? '#ff4d4f' : '#52c41a',
          fontWeight: 'bold',
          fontSize: '12px'
        }}>
          {text}
        </span>
      ),
    },
    {
      title: '公司名称',
      dataIndex: 'companyName',
      key: 'companyName',
      width: 140,
      ellipsis: true,
      align: 'center',
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '变更类型',
      dataIndex: 'changeTypeText',
      key: 'changeType',
      width: 80,
      align: 'center',
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '变更内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      align: 'center',
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '档案文件',
      key: 'files',
      width: 70,
      align: 'center',
      render: () => (
        <span style={{ fontSize: '12px' }}>3</span>
      ),
    },
    {
      title: '变更时间',
      dataIndex: 'changeDate',
      key: 'changeDate',
      width: 90,
      align: 'center',
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
      width: 70,
      align: 'center',
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '操作时间',
      dataIndex: 'operateDate',
      key: 'operateDate',
      width: 130,
      align: 'center',
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 60,
      align: 'center',
      render: (_, record: ChangeRecord) => (
        record.status === 'pending' ? (
          <Button
            type="primary"
            size="small"
            style={{ fontSize: '12px', padding: '2px 8px' }}
            onClick={() => showDetails(record)}
          >
            确认
          </Button>
        ) : null
      ),
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <style>{styles}</style>
      {/* 统计卡片 */}
      <div style={{ display: 'flex', gap: '12px', marginBottom: '24px' }}>
        <div style={{
          width: '23%',
          textAlign: 'center',
          backgroundColor: '#8c8c8c',
          padding: '20px',
          borderRadius: '4px'
        }}>
          <div style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>基础信息</div>
          <div style={{ color: 'white', fontSize: '32px', fontWeight: 'bold', marginTop: '8px' }}>
            {statistics.basicInfo}
          </div>
        </div>
        <div style={{
          width: '23%',
          textAlign: 'center',
          backgroundColor: '#bfbfbf',
          padding: '20px',
          borderRadius: '4px'
        }}>
          <div style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>高管信息</div>
          <div style={{ color: 'white', fontSize: '32px', fontWeight: 'bold', marginTop: '8px' }}>
            {statistics.executiveInfo}
          </div>
        </div>
        <div style={{
          width: '23%',
          textAlign: 'center',
          backgroundColor: '#bfbfbf',
          padding: '20px',
          borderRadius: '4px'
        }}>
          <div style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>股东信息</div>
          <div style={{ color: 'white', fontSize: '32px', fontWeight: 'bold', marginTop: '8px' }}>
            {statistics.shareholderInfo}
          </div>
        </div>
        <div style={{
          width: '23%',
          textAlign: 'center',
          backgroundColor: '#bfbfbf',
          padding: '20px',
          borderRadius: '4px'
        }}>
          <div style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>对外投资</div>
          <div style={{ color: 'white', fontSize: '32px', fontWeight: 'bold', marginTop: '8px' }}>
            {statistics.investmentInfo}
          </div>
        </div>
      </div>

      {/* 标签页和搜索 */}
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <div style={{ display: 'flex', gap: '24px' }}>
            <span
              style={{
                cursor: 'pointer',
                fontWeight: activeTab === 'all' ? 'bold' : 'normal',
                color: activeTab === 'all' ? '#1890ff' : '#000',
                borderBottom: activeTab === 'all' ? '2px solid #1890ff' : 'none',
                paddingBottom: '4px'
              }}
              onClick={() => handleTabChange('all')}
            >
              全部
            </span>
            <span
              style={{
                cursor: 'pointer',
                fontWeight: activeTab === 'pending' ? 'bold' : 'normal',
                color: activeTab === 'pending' ? '#1890ff' : '#000',
                borderBottom: activeTab === 'pending' ? '2px solid #1890ff' : 'none',
                paddingBottom: '4px'
              }}
              onClick={() => handleTabChange('pending')}
            >
              待确认
            </span>
            <span
              style={{
                cursor: 'pointer',
                fontWeight: activeTab === 'confirmed' ? 'bold' : 'normal',
                color: activeTab === 'confirmed' ? '#1890ff' : '#000',
                borderBottom: activeTab === 'confirmed' ? '2px solid #1890ff' : 'none',
                paddingBottom: '4px'
              }}
              onClick={() => handleTabChange('confirmed')}
            >
              已确认
            </span>
          </div>
          <Input.Search
            placeholder="搜索股东主体信息"
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
            onChange={(e) => handleSearch(e.target.value)}
            prefix={<SearchOutlined />}
          />
        </div>

        {/* 数据表格 */}
        <Table
          columns={columns}
          dataSource={filteredRecords}
          rowKey="id"
          loading={loading}
          rowClassName={(record) => record.status === 'pending' ? 'pending-row' : ''}
          pagination={{
            total: filteredRecords.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1000 }}
          size="small"
        />
      </Card>

      {/* 详情确认弹窗 */}
      <Modal
        title="变更详情确认"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setDetailModalVisible(false)}>
            取消
          </Button>,
          <Button key="return" onClick={() => setDetailModalVisible(false)}>
            退回
          </Button>,
          <Button 
            key="confirm" 
            type="primary" 
            onClick={() => selectedRecord && handleConfirm(selectedRecord)}
          >
            确认
          </Button>,
        ]}
        width={800}
      >
        {selectedRecord && (
          <div>
            <p><strong>变更类型：</strong>{selectedRecord.changeTypeText}</p>
            <p><strong>变更内容：</strong>{selectedRecord.content}</p>
            <p><strong>变更时间：</strong>{selectedRecord.changeDate}</p>
            <p><strong>操作人：</strong>{selectedRecord.operator}</p>
            <p><strong>操作时间：</strong>{selectedRecord.operateDate}</p>
            {/* 这里可以根据变更类型显示更详细的信息 */}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CompanyChangeConfirmation;
