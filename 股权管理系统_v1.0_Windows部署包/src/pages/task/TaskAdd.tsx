import React, { useState, useEffect } from 'react';
import {
  Tabs,
  Form,
  Select,
  DatePicker,
  Input,
  Button,
  message,
  Card,
  Space
} from 'antd';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import dayjs from 'dayjs';
import './TaskAdd.css';

const { Option } = Select;
const { TextArea } = Input;

interface Company {
  id: number;
  name: string;
}

const TaskAdd: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('annual');
  const [loading, setLoading] = useState(false);
  const [companies, setCompanies] = useState<Company[]>([]);

  // 表单实例
  const [annualForm] = Form.useForm();
  const [addressForm] = Form.useForm();
  const [customForm] = Form.useForm();

  // 获取年份选项（当前年份及前后几年）
  const getYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 2; i <= currentYear + 2; i++) {
      years.push(i.toString());
    }
    return years;
  };

  // 获取公司列表
  const fetchCompanies = async () => {
    try {
      const response = await axios.get('http://localhost:8080/api/companies-list');
      if (response.data.success) {
        setCompanies(response.data.data);
      }
    } catch (error) {
      console.error('获取公司列表失败:', error);
      message.error('获取公司列表失败');
    }
  };

  useEffect(() => {
    fetchCompanies();
  }, []);

  // 处理年审年报任务提交
  const handleAnnualSubmit = async (values: any) => {
    try {
      setLoading(true);
      const response = await axios.post('http://localhost:8080/api/task/create-annual-tasks', {
        taskType: '年审年报',
        year: values.year,
        remarks: values.remarks
      });

      if (response.data.success) {
        message.success(response.data.message);
        navigate('/task');
      } else {
        message.error(response.data.message || '创建任务失败');
      }
    } catch (error) {
      console.error('创建年审年报任务失败:', error);
      message.error('创建年审年报任务失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理地址维护任务提交
  const handleAddressSubmit = async (values: any) => {
    try {
      setLoading(true);
      const response = await axios.post('http://localhost:8080/api/task/create-address-tasks', {
        taskType: '地址维护',
        year: values.year,
        companyIds: values.companies,
        startDate: values.startDate.format('YYYY-MM-DD'),
        deadline: values.deadline.format('YYYY-MM-DD'),
        repeatCycle: values.repeatCycle || 1,
        remarks: values.remarks
      });

      if (response.data.success) {
        message.success(response.data.message);
        navigate('/task');
      } else {
        message.error(response.data.message || '创建任务失败');
      }
    } catch (error) {
      console.error('创建地址维护任务失败:', error);
      message.error('创建地址维护任务失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理自定义任务提交
  const handleCustomSubmit = async (values: any) => {
    try {
      setLoading(true);
      const response = await axios.post('http://localhost:8080/api/task/create-custom-task', {
        taskName: values.taskName,
        taskDescription: values.taskDescription,
        year: values.year,
        companyId: values.company,
        startDate: values.startDate ? values.startDate.format('YYYY-MM-DD') : null,
        deadline: values.deadline.format('YYYY-MM-DD'),
        remarks: values.remarks
      });

      if (response.data.success) {
        message.success(response.data.message);
        navigate('/task');
      } else {
        message.error(response.data.message || '创建任务失败');
      }
    } catch (error) {
      console.error('创建自定义任务失败:', error);
      message.error('创建自定义任务失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    navigate('/task');
  };

  return (
    <div className="task-add-container">
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={[
          {
            key: 'annual',
            label: '新增年审年报任务',
            children: null
          },
          {
            key: 'address',
            label: '新增地址维护任务',
            children: null
          },
          {
            key: 'custom',
            label: '新增自定义任务',
            children: null
          }
        ]}
      />

      {/* 年审年报任务标签页 */}
      {activeTab === 'annual' && (
        <Card>
          <Form
            form={annualForm}
            layout="vertical"
            onFinish={handleAnnualSubmit}
            style={{ maxWidth: '600px' }}
          >
            <Form.Item
              label="任务类型"
              name="taskType"
              initialValue="年审年报"
            >
              <Select disabled>
                <Option value="年审年报">年审年报</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="年度"
              name="year"
              rules={[{ required: true, message: '请选择年度' }]}
            >
              <Select placeholder="请选择（单选）">
                {getYearOptions().map(year => (
                  <Option key={year} value={year}>{year}</Option>
                ))}
              </Select>
            </Form.Item>

            <div style={{
              background: '#f5f5f5',
              padding: '16px',
              borderRadius: '6px',
              marginBottom: '24px',
              fontSize: '14px',
              lineHeight: '1.6'
            }}>
              <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>
                系统将按公司信息中的年审要求批量自动生成对应的待办任务。
              </div>
              <div>1.不管年审：不需要创建待办</div>
              <div>2.管年审（固定周期）：开始日期：当年1月1日，结束日期：当年6月30日</div>
              <div>3.管年审（滚动周期）：开始日期：按公司的设立日期对应今年的日期 - 3个月，结束日期：按公司的设立日期对应今年的日期+2个月</div>
            </div>

            <Form.Item
              label="备注"
              name="remarks"
            >
              <TextArea
                placeholder="请输入"
                rows={4}
                maxLength={500}
                showCount
              />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  style={{ backgroundColor: '#000', borderColor: '#000' }}
                >
                  保存
                </Button>
                <Button onClick={handleCancel}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>
      )}

      {/* 地址维护任务标签页 */}
      {activeTab === 'address' && (
        <Card>
          <Form
            form={addressForm}
            layout="vertical"
            onFinish={handleAddressSubmit}
            style={{ maxWidth: '600px' }}
          >
            <Form.Item
              label="任务类型"
              name="taskType"
              initialValue="地址维护"
            >
              <Select disabled>
                <Option value="地址维护">地址维护</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="年度"
              name="year"
              rules={[{ required: true, message: '请选择年度' }]}
            >
              <Select placeholder="请选择（单选）">
                {getYearOptions().map(year => (
                  <Option key={year} value={year}>{year}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="主体"
              name="companies"
              rules={[{ required: true, message: '请选择主体公司' }]}
            >
              <Select
                mode="multiple"
                placeholder="请选择（多选）"
                showSearch
                filterOption={(input, option) =>
                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {companies.map(company => (
                  <Option key={company.id} value={company.id}>
                    {company.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="开始日期"
              name="startDate"
              rules={[{ required: true, message: '请选择开始日期' }]}
            >
              <DatePicker
                placeholder="请输入"
                style={{ width: '100%' }}
                format="YYYY-MM-DD"
              />
            </Form.Item>

            <Form.Item
              label="结束日期"
              name="deadline"
              rules={[{ required: true, message: '请选择结束日期' }]}
            >
              <DatePicker
                placeholder="请输入"
                style={{ width: '100%' }}
                format="YYYY-MM-DD"
              />
            </Form.Item>

            <Form.Item
              label="重复周期（年）"
              name="repeatCycle"
              initialValue={1}
              rules={[{ required: true, message: '请输入重复周期' }]}
            >
              <Input
                placeholder="1"
                type="number"
                min={1}
                max={10}
              />
            </Form.Item>

            <Form.Item
              label="备注"
              name="remarks"
            >
              <TextArea
                placeholder="请输入"
                rows={4}
                maxLength={500}
                showCount
              />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  style={{ backgroundColor: '#000', borderColor: '#000' }}
                >
                  保存
                </Button>
                <Button onClick={handleCancel}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>
      )}

      {/* 自定义任务标签页 */}
      {activeTab === 'custom' && (
        <Card>
          <Form
            form={customForm}
            layout="vertical"
            onFinish={handleCustomSubmit}
            style={{ maxWidth: '600px' }}
          >
            <Form.Item
              label="任务名称"
              name="taskName"
              rules={[{ required: true, message: '请输入任务名称' }]}
            >
              <Input placeholder="请输入任务名称" />
            </Form.Item>

            <Form.Item
              label="任务描述"
              name="taskDescription"
            >
              <Input placeholder="请输入任务描述" />
            </Form.Item>

            <Form.Item
              label="任务年度"
              name="year"
              rules={[{ required: true, message: '请选择任务年度' }]}
            >
              <Select placeholder="请选择年度">
                {getYearOptions().map(year => (
                  <Option key={year} value={year}>{year}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="任务主体"
              name="company"
              rules={[{ required: true, message: '请选择任务主体' }]}
            >
              <Select
                placeholder="请选择公司"
                showSearch
                filterOption={(input, option) =>
                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {companies.map(company => (
                  <Option key={company.id} value={company.id}>
                    {company.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="开始日期"
              name="startDate"
            >
              <DatePicker
                placeholder="请选择开始日期"
                style={{ width: '100%' }}
                format="YYYY-MM-DD"
              />
            </Form.Item>

            <Form.Item
              label="结束日期"
              name="deadline"
              rules={[{ required: true, message: '请选择结束日期' }]}
            >
              <DatePicker
                placeholder="请选择结束日期"
                style={{ width: '100%' }}
                format="YYYY-MM-DD"
              />
            </Form.Item>

            <Form.Item
              label="备注"
              name="remarks"
            >
              <TextArea
                placeholder="请输入备注"
                rows={4}
                maxLength={500}
                showCount
              />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  style={{ backgroundColor: '#000', borderColor: '#000' }}
                >
                  保存
                </Button>
                <Button onClick={handleCancel}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Card>
      )}
    </div>
  );
};

export default TaskAdd;
