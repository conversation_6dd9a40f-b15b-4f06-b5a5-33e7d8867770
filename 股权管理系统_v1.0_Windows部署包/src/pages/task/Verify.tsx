import React, { useState, useEffect } from 'react';
import {
  Radio,
  Form,
  Select,
  DatePicker,
  Input,
  Button,
  message,
  Typography
} from 'antd';
import { useNavigate, useSearchParams } from 'react-router-dom';
import axios from 'axios';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;
const { Text } = Typography;

interface TaskItem {
  id: number;
  task_status: string;
  task_type: string;
  year: string;
  company_id: number;
  company_name: string;
  business_segment: string;
  deadline: string;
  remarks: string;
}

const Verify: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const taskId = searchParams.get('id');

  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [taskData, setTaskData] = useState<TaskItem | null>(null);
  const [isVerified, setIsVerified] = useState<boolean>(false);
  const [shouldCreateNext, setShouldCreateNext] = useState<boolean>(true);
  const [taskTypes, setTaskTypes] = useState<string[]>([]);
  const [availableYears, setAvailableYears] = useState<string[]>([]);

  // 获取任务详情
  const fetchTaskDetail = async () => {
    if (!taskId) return;

    try {
      const response = await axios.get(`http://localhost:8080/api/tasks/${taskId}`);
      if (response.data.success) {
        setTaskData(response.data.data);
      } else {
        message.error('获取任务详情失败');
      }
    } catch (error) {
      console.error('获取任务详情失败:', error);
      message.error('获取任务详情失败');
    }
  };

  // 获取任务类型列表
  const fetchTaskTypes = async () => {
    try {
      const response = await axios.get('http://localhost:8080/api/task/types');
      if (response.data.success) {
        setTaskTypes(response.data.data);
      }
    } catch (error) {
      console.error('获取任务类型失败:', error);
    }
  };

  // 生成年份选项（当前年份的下一年到未来几年）
  const generateYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const nextYear = currentYear + 1;
    const years = [];
    for (let i = 0; i < 5; i++) {
      years.push((nextYear + i).toString());
    }
    setAvailableYears(years);
    // 设置默认值为下一年度
    form.setFieldsValue({ year: nextYear.toString() });
  };

  useEffect(() => {
    if (taskId) {
      fetchTaskDetail();
      fetchTaskTypes();
      generateYearOptions();
    }
  }, [taskId]);

  // 当任务数据加载完成后，设置默认的任务类型和主体公司
  useEffect(() => {
    if (taskData) {
      form.setFieldsValue({
        taskType: taskData.task_type,
        companyName: taskData.company_name
      });
    }
  }, [taskData, form]);

  // 处理保存
  const handleSave = async () => {
    if (!isVerified) {
      message.error('请先选择已核实');
      return;
    }

    if (shouldCreateNext) {
      try {
        await form.validateFields();
      } catch (error) {
        message.error('请填写完整的任务信息');
        return;
      }
    }

    setLoading(true);
    try {
      const formValues = shouldCreateNext ? form.getFieldsValue() : null;

      const requestData = {
        taskId: parseInt(taskId!),
        isVerified: true,
        shouldCreateNext,
        nextTaskData: shouldCreateNext ? {
          taskType: formValues.taskType,
          year: formValues.year,
          startDate: formValues.startDate ? formValues.startDate.format('YYYY-MM-DD') : null,
          deadline: formValues.deadline ? formValues.deadline.format('YYYY-MM-DD') : null,
          companyId: taskData?.company_id,
          companyName: taskData?.company_name,
          businessSegment: taskData?.business_segment,
          repeatCycle: formValues.repeatCycle,
          remarks: formValues.remarks || ''
        } : null
      };

      const response = await axios.post('http://localhost:8080/api/task/verify', requestData);

      if (response.data.success) {
        message.success(response.data.message);
        navigate('/task');
      } else {
        message.error(response.data.message);
      }
    } catch (error) {
      console.error('核实任务失败:', error);
      message.error('核实任务失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    navigate('/task');
  };

  if (!taskId) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <h2>无效的访问</h2>
        <p>请从任务列表页面点击核实按钮进入此页面</p>
        <Button type="primary" onClick={() => navigate('/task')}>
          返回任务列表
        </Button>
      </div>
    );
  }

  if (!taskData) {
    return (
      <div style={{ padding: '24px', textAlign: 'center' }}>
        <div>加载中...</div>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      {/* 页面标题 */}
      <div style={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginBottom: '24px',
        borderBottom: '1px solid #f0f0f0',
        paddingBottom: '16px'
      }}>
        <h1 style={{ margin: 0, fontSize: '24px', fontWeight: 'bold' }}>核实</h1>
        <Button
          onClick={handleCancel}
          style={{ fontSize: '18px', padding: '4px 8px' }}
        >
          ✕
        </Button>
      </div>

      {/* 当前处理的任务信息 */}
      <div style={{ marginBottom: '32px', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
        <Text strong style={{ fontSize: '16px' }}>
          当前处理：{taskData.company_name} - {taskData.task_type}（{taskData.year}年度）
        </Text>
      </div>

      {/* 核实任务完成情况 */}
      <div style={{ marginBottom: '32px' }}>
        <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '20px' }}>
          核实任务完成情况
        </h2>

        <div style={{ marginBottom: '20px' }}>
          <Text style={{ marginRight: '20px', fontSize: '16px' }}>任务状态</Text>
          <Radio.Group
            value={isVerified}
            onChange={(e) => setIsVerified(e.target.value)}
          >
            <Radio value={true}>已核实</Radio>
          </Radio.Group>
        </div>

        <div>
          <Text style={{ marginRight: '20px', fontSize: '16px' }}>下一周期任务</Text>
          <Radio.Group
            value={shouldCreateNext}
            onChange={(e) => setShouldCreateNext(e.target.value)}
          >
            <Radio value={false}>不创建</Radio>
            <Radio value={true}>创建</Radio>
          </Radio.Group>
        </div>
      </div>

      {/* 任务信息 */}
      <div style={{ marginBottom: '32px' }}>
        <h2 style={{ fontSize: '18px', fontWeight: 'bold', marginBottom: '20px' }}>
          任务信息
        </h2>

        <Form
          form={form}
          layout="vertical"
          style={{ maxWidth: '600px' }}
        >
          <Form.Item
            label="任务类型"
            name="taskType"
            rules={shouldCreateNext ? [{ required: true, message: '请选择任务类型' }] : []}
          >
            <Select
              placeholder="请选择任务类型"
              disabled={!shouldCreateNext}
              size="large"
            >
              {taskTypes.map(type => (
                <Option key={type} value={type}>{type}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="主体公司"
            name="companyName"
            rules={shouldCreateNext ? [{ required: true, message: '请输入主体公司' }] : []}
          >
            <Input
              placeholder="请输入主体公司"
              disabled={!shouldCreateNext}
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="年度"
            name="year"
            rules={shouldCreateNext ? [{ required: true, message: '请选择年度' }] : []}
          >
            <Select
              placeholder="请选择年度"
              disabled={!shouldCreateNext}
              size="large"
            >
              {availableYears.map(year => (
                <Option key={year} value={year}>{year}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="开始日期"
            name="startDate"
            rules={shouldCreateNext ? [{ required: true, message: '请选择开始日期' }] : []}
          >
            <DatePicker
              style={{ width: '100%' }}
              placeholder="请选择开始日期"
              format="YYYY-MM-DD"
              disabled={!shouldCreateNext}
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="结束日期"
            name="deadline"
            rules={shouldCreateNext ? [{ required: true, message: '请选择结束日期' }] : []}
          >
            <DatePicker
              style={{ width: '100%' }}
              placeholder="请选择结束日期"
              format="YYYY-MM-DD"
              disabled={!shouldCreateNext}
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="重复周期（年）"
            name="repeatCycle"
            rules={shouldCreateNext ? [{ required: true, message: '请输入重复周期' }] : []}
          >
            <Input
              placeholder="请输入重复周期"
              disabled={!shouldCreateNext}
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="备注"
            name="remarks"
          >
            <TextArea
              rows={4}
              placeholder="请输入备注"
              maxLength={500}
              disabled={!shouldCreateNext}
            />
          </Form.Item>
        </Form>
      </div>

      {/* 操作按钮 */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        gap: '16px',
        paddingTop: '24px',
        borderTop: '1px solid #f0f0f0'
      }}>
        <Button
          size="large"
          onClick={handleCancel}
          style={{ minWidth: '100px' }}
        >
          取消
        </Button>
        <Button
          type="primary"
          size="large"
          loading={loading}
          onClick={handleSave}
          style={{
            minWidth: '100px',
            backgroundColor: '#000',
            borderColor: '#000'
          }}
        >
          保存
        </Button>
      </div>
    </div>
  );
};

export default Verify;
