import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Select, Button, Table, message, Spin } from 'antd';
import axios from 'axios';

const { Option } = Select;

interface InvestigationData {
  year: string;
  totalCompanies: number;
  noAnnualCompanies: number;
  fixedAnnualCompanies: number;
  rollingAnnualCompanies: number;
  annualReport: {
    theory: number;
    actual: number;
    missing: number;
  };
  addressMaintenance: {
    theory: number;
    actual: number;
    missing: number;
  };
  missingTasks: Array<{
    companyName: string;
    missingAnnualReport: boolean;
    missingAddressMaintenance: boolean;
  }>;
}

const Investigation: React.FC = () => {
  const [selectedYear, setSelectedYear] = useState<string>(new Date().getFullYear().toString());
  const [loading, setLoading] = useState(false);
  const [completingTasks, setCompletingTasks] = useState(false);
  const [investigationData, setInvestigationData] = useState<InvestigationData | null>(null);
  const [availableYears, setAvailableYears] = useState<string[]>([]);

  // 获取年份列表
  const fetchAvailableYears = async () => {
    try {
      const response = await axios.get('http://localhost:8080/api/task/years');
      if (response.data.success) {
        const years = response.data.data;
        setAvailableYears(years);
        // 如果当前选择的年份不在列表中，设置为第一个年份或当前年份
        if (years.length > 0 && !years.includes(selectedYear)) {
          const currentYear = new Date().getFullYear().toString();
          setSelectedYear(years.includes(currentYear) ? currentYear : years[0]);
        }
      }
    } catch (error) {
      console.error('获取年份列表失败:', error);
      // 如果获取失败，使用默认年份
      const currentYear = new Date().getFullYear();
      const defaultYears = [];
      for (let i = 0; i < 5; i++) {
        defaultYears.push((currentYear - i).toString());
      }
      setAvailableYears(defaultYears);
    }
  };

  // 组件加载时获取年份列表
  useEffect(() => {
    fetchAvailableYears();
  }, []);

  // 开始排查
  const handleInvestigation = async () => {
    try {
      setLoading(true);
      console.log('开始任务排查，年度:', selectedYear);

      const response = await axios.post('http://localhost:8080/api/task/investigation', {
        year: selectedYear
      });

      if (response.data.success) {
        setInvestigationData(response.data.data);
        message.success('任务排查完成');
      } else {
        message.error('任务排查失败');
      }
    } catch (error) {
      console.error('任务排查失败:', error);
      message.error('任务排查失败，请检查网络连接');
    } finally {
      setLoading(false);
    }
  };

  // 补全缺失任务
  const handleCompleteMissingTasks = async () => {
    try {
      setCompletingTasks(true);
      console.log('开始补全缺失任务，年度:', selectedYear);

      const response = await axios.post('http://localhost:8080/api/task/complete-missing', {
        year: selectedYear
      });

      if (response.data.success) {
        message.success(response.data.message);
        // 重新排查以更新数据
        await handleInvestigation();
      } else {
        message.error('补全缺失任务失败');
      }
    } catch (error) {
      console.error('补全缺失任务失败:', error);
      message.error('补全缺失任务失败，请检查网络连接');
    } finally {
      setCompletingTasks(false);
    }
  };

  // 缺失任务表格列定义
  const missingTaskColumns = [
    {
      title: '主体',
      dataIndex: 'companyName',
      key: 'companyName',
      width: 300,
    },
    {
      title: '年审年报',
      dataIndex: 'missingAnnualReport',
      key: 'missingAnnualReport',
      width: 150,
      align: 'center' as const,
      render: (_: any, record: any) => record.missingAnnualReport ? '×' : '',
    },
    {
      title: '地址维护',
      dataIndex: 'missingAddressMaintenance',
      key: 'missingAddressMaintenance',
      width: 150,
      align: 'center' as const,
      render: (_: any, record: any) => record.missingAddressMaintenance ? '×' : '',
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      {/* 年度选择和排查按钮 */}
      <Card style={{ marginBottom: '24px' }}>
        <Row align="middle" gutter={16}>
          <Col>
            <span style={{ fontSize: '16px', fontWeight: 'bold' }}>年度</span>
          </Col>
          <Col>
            <Select
              style={{ width: 200 }}
              value={selectedYear}
              onChange={setSelectedYear}
              placeholder="请选择（单选）"
            >
              {availableYears.map(year => (
                <Option key={year} value={year}>{year}</Option>
              ))}
            </Select>
          </Col>
          <Col>
            <Button
              type="primary"
              onClick={handleInvestigation}
              loading={loading}
              style={{
                backgroundColor: '#000',
                borderColor: '#000',
                color: '#fff'
              }}
            >
              开始排查
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 排查结果 */}
      {investigationData && (
        <>
          {/* 统计数据 */}
          <Card style={{ marginBottom: '24px' }}>
            {/* 第一行：存续公司统计 */}
            <div style={{ fontSize: '16px', marginBottom: '16px', lineHeight: '1.8' }}>
              <span style={{ fontWeight: 'bold' }}>存续公司：</span>
              <span style={{ marginLeft: '8px' }}>{investigationData.totalCompanies}</span>
              <span style={{ marginLeft: '40px', fontWeight: 'bold' }}>不管年审：</span>
              <span style={{ marginLeft: '8px' }}>{investigationData.noAnnualCompanies}</span>
              <span style={{ marginLeft: '40px', fontWeight: 'bold' }}>管年审（固定周期）：</span>
              <span style={{ marginLeft: '8px' }}>{investigationData.fixedAnnualCompanies}</span>
              <span style={{ marginLeft: '40px', fontWeight: 'bold' }}>管年审（滚动周期）：</span>
              <span style={{ marginLeft: '8px' }}>{investigationData.rollingAnnualCompanies}</span>
            </div>

            {/* 第二行：年审年报统计 */}
            <div style={{ fontSize: '16px', marginBottom: '16px', lineHeight: '1.8' }}>
              <span style={{ fontWeight: 'bold' }}>年审年报：</span>
              <span style={{ marginLeft: '40px', fontWeight: 'bold' }}>理论数：</span>
              <span style={{ marginLeft: '8px' }}>{investigationData.annualReport.theory}</span>
              <span style={{ marginLeft: '40px', fontWeight: 'bold' }}>实际数：</span>
              <span style={{ marginLeft: '8px' }}>{investigationData.annualReport.actual}</span>
              <span style={{ marginLeft: '40px', fontWeight: 'bold' }}>缺失数：</span>
              <span style={{ marginLeft: '8px' }}>{investigationData.annualReport.missing}</span>
            </div>

            {/* 第三行：地址维护统计 */}
            <div style={{ fontSize: '16px', lineHeight: '1.8' }}>
              <span style={{ fontWeight: 'bold' }}>地址维护：</span>
              <span style={{ marginLeft: '40px', fontWeight: 'bold' }}>理论数：</span>
              <span style={{ marginLeft: '8px' }}>{investigationData.addressMaintenance.theory}</span>
              <span style={{ marginLeft: '40px', fontWeight: 'bold' }}>实际数：</span>
              <span style={{ marginLeft: '8px' }}>{investigationData.addressMaintenance.actual}</span>
              <span style={{ marginLeft: '40px', fontWeight: 'bold' }}>缺失数：</span>
              <span style={{ marginLeft: '8px' }}>{investigationData.addressMaintenance.missing}</span>
            </div>
          </Card>

          {/* 缺失任务列表 */}
          <Card>
            <Row align="middle" justify="space-between" style={{ marginBottom: '16px' }}>
              <Col>
                <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                  缺失任务的公司名单（共 {investigationData.missingTasks.length} 条）
                </span>
                <span style={{ marginLeft: '24px', fontSize: '14px', color: '#666' }}>
                  年审年报缺失：{investigationData.missingTasks.filter(task => task.missingAnnualReport).length} 条，
                  地址维护缺失：{investigationData.missingTasks.filter(task => task.missingAddressMaintenance).length} 条
                </span>
                <Button
                  style={{
                    marginLeft: '16px',
                    backgroundColor: '#000',
                    borderColor: '#000',
                    color: '#fff'
                  }}
                  onClick={handleCompleteMissingTasks}
                  loading={completingTasks}
                  disabled={investigationData.missingTasks.length === 0}
                >
                  补全缺失任务
                </Button>
              </Col>
            </Row>

            <Table
              columns={missingTaskColumns}
              dataSource={investigationData.missingTasks.map((item, index) => ({
                ...item,
                key: index
              }))}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
                pageSizeOptions: ['10', '20', '50', '100']
              }}
              size="small"
              bordered
              style={{ marginTop: '16px' }}
              scroll={{ y: 400 }}
            />
          </Card>
        </>
      )}

      {/* 加载状态 */}
      {loading && (
        <Card>
          <div style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            height: '200px'
          }}>
            <Spin size="large" />
            <span style={{ marginLeft: '16px', fontSize: '16px' }}>正在排查任务...</span>
          </div>
        </Card>
      )}
    </div>
  );
};

export default Investigation;
