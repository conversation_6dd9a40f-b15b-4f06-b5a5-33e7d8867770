.task-add-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 标签页样式 - 与数据字典页面保持一致 */
.task-add-container .ant-tabs {
  background: transparent;
  border-radius: 0;
  padding: 0;
  margin-bottom: 0;
  box-shadow: none;
}

.task-add-container .ant-tabs-nav {
  margin-bottom: 0;
  position: relative;
}

.task-add-container .ant-tabs-nav-wrap {
  background: white;
  border-radius: 0;
  padding: 8px 16px 0 16px;
  position: relative;
  border-bottom: 3px solid #1a73e8;
  box-shadow: none;
}

.task-add-container .ant-tabs-nav-list {
  background: transparent;
  border-radius: 0;
  padding: 0;
  position: relative;
}

.task-add-container .ant-tabs-tab {
  background: white !important;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  margin: 0 6px 8px 0;
  padding: 12px 30px;
  font-size: 14px;
  font-weight: 500;
  color: #1a73e8 !important;
  transition: all 0.3s ease;
  min-width: 120px;
  text-align: center;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 40px;
  line-height: 1;
}

.task-add-container .ant-tabs-tab .ant-tabs-tab-btn {
  color: #1a73e8 !important;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.task-add-container .ant-tabs-tab:hover {
  background: #f0f4ff !important;
  color: #1a73e8 !important;
  border-color: #1a73e8;
}

.task-add-container .ant-tabs-tab:hover .ant-tabs-tab-btn {
  color: #1a73e8 !important;
}

.task-add-container .ant-tabs-tab-active {
  background: #1a73e8 !important;
  color: white !important;
  font-weight: 600;
  border-color: #1a73e8;
  box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
}

.task-add-container .ant-tabs-tab-active .ant-tabs-tab-btn {
  color: white !important;
  font-weight: 600;
}

.task-add-container .ant-tabs-content-holder {
  background: white;
  border-radius: 0;
  box-shadow: none;
  padding: 24px;
  border: none;
  margin-top: 0;
}

.task-add-container .ant-tabs-ink-bar {
  display: none;
}

/* 卡片样式 */
.task-add-container .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: none;
}

/* 表单样式 */
.task-add-container .ant-form-item {
  margin-bottom: 24px;
}

.task-add-container .ant-form-item:last-child {
  margin-bottom: 0;
}

.task-add-container .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

/* 输入框样式 */
.task-add-container .ant-input,
.task-add-container .ant-select-selector {
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.task-add-container .ant-input:focus,
.task-add-container .ant-input-focused,
.task-add-container .ant-select-focused .ant-select-selector {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 按钮样式 */
.task-add-container .ant-btn {
  border-radius: 6px;
  font-weight: 500;
}

.task-add-container .ant-btn-primary {
  background-color: #000;
  border-color: #000;
}

.task-add-container .ant-btn-primary:hover {
  background-color: #333;
  border-color: #333;
}

/* 日期选择器样式 */
.task-add-container .ant-picker {
  border-radius: 6px;
  width: 100%;
}

.task-add-container .ant-picker:hover {
  border-color: #40a9ff;
}

.task-add-container .ant-picker-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 文本域样式 */
.task-add-container .ant-input {
  border-radius: 6px;
}

/* 选择器下拉样式 */
.ant-select-dropdown {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 说明文字样式 */
.task-add-container .info-text {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 24px;
  font-size: 14px;
  line-height: 1.6;
  color: #666;
}

.task-add-container .info-text .info-title {
  margin-bottom: 8px;
  font-weight: bold;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .task-add-container {
    padding: 16px;
  }
  
  .task-add-container .ant-tabs-nav-wrap {
    padding: 8px 12px 0 12px;
  }
  
  .task-add-container .ant-tabs-tab {
    padding: 8px 16px;
    font-size: 13px;
    min-width: 100px;
  }
  
  .task-add-container .ant-tabs-content-holder {
    padding: 16px;
  }
}

@media (max-width: 576px) {
  .task-add-container {
    padding: 12px;
  }
  
  .task-add-container .ant-tabs-nav-wrap {
    padding: 8px;
  }
  
  .task-add-container .ant-tabs-tab {
    padding: 6px 12px;
    font-size: 12px;
    min-width: 80px;
    margin: 0 4px 8px 0;
  }
  
  .task-add-container .ant-tabs-content-holder {
    padding: 12px;
  }
  
  .task-add-container .ant-form-item {
    margin-bottom: 16px;
  }
}

/* 加载状态 */
.task-add-container .ant-spin-container {
  min-height: 200px;
}

/* 多选标签样式 */
.task-add-container .ant-select-multiple .ant-select-selection-item {
  background: #f0f4ff;
  border: 1px solid #1a73e8;
  color: #1a73e8;
  border-radius: 4px;
}

.task-add-container .ant-select-multiple .ant-select-selection-item-remove {
  color: #1a73e8;
}

.task-add-container .ant-select-multiple .ant-select-selection-item-remove:hover {
  color: #ff4d4f;
}
