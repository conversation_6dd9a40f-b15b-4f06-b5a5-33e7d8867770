.business-segment-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 查询条件卡片 */
.search-card {
  margin-bottom: 16px;
}

.search-row {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.search-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.search-label {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
  text-align: left;
}

.search-input-group {
  display: flex;
  align-items: center;
}

/* 操作按钮卡片 */
.action-card {
  margin-bottom: 16px;
}

/* 表格样式 */
.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .business-segment-container {
    padding: 16px;
  }

  .search-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .search-item {
    width: 100%;
  }

  .search-item .ant-input,
  .search-item .ant-select {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .business-segment-container {
    padding: 12px;
  }

  .search-item .ant-input,
  .search-item .ant-select {
    width: 100%;
  }
}

/* 按钮样式优化 */
.ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 表格操作按钮 */
.ant-btn-link {
  padding: 0;
  height: auto;
}

.ant-btn-link:hover {
  background-color: transparent;
}

/* 公司数量链接样式 */
.ant-btn-link[style*="color: rgb(24, 144, 255)"] {
  font-weight: 500;
}

.ant-btn-link[style*="color: rgb(24, 144, 255)"]:hover {
  color: #40a9ff !important;
  text-decoration: underline;
}

/* 删除按钮样式 */
.ant-btn-link.ant-btn-dangerous {
  color: #ff4d4f;
}

.ant-btn-link.ant-btn-dangerous:hover {
  color: #ff7875;
}

/* 表格行样式 */
.ant-table-tbody > tr {
  transition: all 0.2s ease;
}

/* 分页样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: right;
}

/* 空数据样式 */
.ant-empty {
  margin: 40px 0;
}

/* 加载状态样式 */
.ant-spin-container {
  min-height: 200px;
}

/* 表格列宽度调整 */
.ant-table-column-title {
  font-weight: 600;
}

/* 操作列按钮间距 */
.ant-space-item {
  margin-right: 8px;
}

.ant-space-item:last-child {
  margin-right: 0;
}

/* 弹窗内表格样式 */
.ant-modal .ant-table {
  margin-top: 16px;
}

.ant-modal .ant-table-thead > tr > th {
  background-color: #fafafa;
}

/* 表单样式 */
.ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.ant-form-item-required::before {
  color: #ff4d4f;
}

/* 输入框样式 */
.ant-input:focus,
.ant-input-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 确认弹窗样式 */
.ant-popconfirm .ant-popconfirm-message {
  margin-bottom: 12px;
}

.ant-popconfirm .ant-popconfirm-buttons {
  text-align: right;
}

/* 卡片样式优化 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.ant-card-body {
  padding: 20px;
}

/* 搜索区域样式优化 */
.search-card .ant-card-body {
  padding: 16px 20px;
}

.action-card .ant-card-body {
  padding: 12px 20px;
}

/* 表格容器样式 */
.ant-table-wrapper {
  background: white;
  border-radius: 8px;
}

/* 表格头部样式 */
.ant-table-thead > tr > th {
  border-bottom: 2px solid #f0f0f0;
  padding: 16px;
}

/* 表格内容样式 */
.ant-table-tbody > tr > td {
  padding: 12px 16px;
  border-bottom: 1px solid #f5f5f5;
}

/* 最后一行去掉下边框 */
.ant-table-tbody > tr:last-child > td {
  border-bottom: none;
}
