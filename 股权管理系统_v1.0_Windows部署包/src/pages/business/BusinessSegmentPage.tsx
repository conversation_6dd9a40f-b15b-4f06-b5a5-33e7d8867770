import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  Modal,
  Form,
  message,
  Popconfirm
} from 'antd';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import { apiService } from '../../services/api-simple';
import './BusinessSegmentPage.css';

interface BusinessSegmentItem {
  id: string;
  name: string;
  companyCount: number;
  createTime: string;
}

interface CompanyItem {
  id: string;
  companyNameCn: string;
  companyNameEn: string;
  businessSegment: string;
}

const BusinessSegmentPage: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [segmentData, setSegmentData] = useState<BusinessSegmentItem[]>([]);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  const [isCompanyListModalVisible, setIsCompanyListModalVisible] = useState(false);
  const [selectedSegment, setSelectedSegment] = useState<BusinessSegmentItem | null>(null);
  const [companyList, setCompanyList] = useState<CompanyItem[]>([]);
  const [companyListLoading, setCompanyListLoading] = useState(false);
  
  // 查询表单状态
  const [searchForm, setSearchForm] = useState({
    name: ''
  });

  // 错误状态管理
  const [error, setError] = useState<string | null>(null);




  // 获取业务板块数据
  const fetchSegmentData = async () => {
    setLoading(true);
    setError(null);
    try {
      console.log('🔄 开始获取业务板块数据...');
      console.log('🌐 API基础URL:', import.meta.env.VITE_API_URL || 'http://localhost:8080');

      const response = await apiService.getBusinessSegments();
      console.log('📊 API响应:', response);

      if (response.data.success) {
        console.log('✅ 数据获取成功:', response.data.data);

        // 直接使用后端已经计算好的公司数量
        const segmentsWithCount = response.data.data.map((segment: any) => ({
          id: segment.id.toString(),
          name: segment.name,
          companyCount: segment.companyCount || 0,
          createTime: segment.created_at || segment.createTime || new Date().toISOString()
        }));

        setSegmentData(segmentsWithCount);
        console.log(`获取到 ${segmentsWithCount.length} 条业务板块数据，包含公司数量统计`);
      } else {
        throw new Error(response.data.message || 'API返回失败');
      }
    } catch (error) {
      console.error('❌ 获取业务板块数据失败:', error);
      const errorMessage = `获取业务板块数据失败: ${error.message}`;
      setError(errorMessage);
      message.error(errorMessage);
      setSegmentData([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取公司列表数据
  const fetchCompanyList = async (segmentName: string) => {
    try {
      console.log('🔄 获取业务板块公司列表:', segmentName);
      const response = await apiService.getCompaniesBySegment(segmentName);

      if (response.data.success) {
        console.log('✅ 公司列表获取成功:', response.data.data);
        return response.data.data || [];
      } else {
        throw new Error(response.data.message || '获取公司列表API返回失败');
      }
    } catch (error) {
      console.error('❌ 获取公司列表失败:', error);
      message.error(`获取公司列表失败: ${error.message}`);
      return [];
    }
  };

  useEffect(() => {
    fetchSegmentData();
  }, []);

  // 处理查询表单变化
  const handleSearchFormChange = (field: string, value: string) => {
    setSearchForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 处理查询
  const handleQuery = async () => {
    setLoading(true);
    setError(null);
    try {
      const response = await apiService.getBusinessSegments({ name: searchForm.name });
      if (response.data.success) {
        // 直接使用后端已经计算好的公司数量
        const segmentsWithCount = response.data.data.map((segment: any) => ({
          id: segment.id.toString(),
          name: segment.name,
          companyCount: segment.companyCount || 0,
          createTime: segment.created_at || segment.createTime || new Date().toISOString()
        }));
        setSegmentData(segmentsWithCount);
        console.log(`查询到 ${segmentsWithCount.length} 条业务板块数据`);
      } else {
        throw new Error(response.data.message || '查询API返回失败');
      }
    } catch (error) {
      console.error('查询失败:', error);
      const errorMessage = `查询失败: ${error.message}`;
      setError(errorMessage);
      message.error(errorMessage);
      setSegmentData([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理重置
  const handleReset = () => {
    setSearchForm({
      name: ''
    });
    // 重新从数据库获取所有数据
    fetchSegmentData();
  };

  // 处理新增
  const handleAdd = () => {
    form.resetFields();
    setIsAddModalVisible(true);
  };

  // 处理新增确认
  const handleAddConfirm = async () => {
    try {
      const values = await form.validateFields();
      
      // TODO: 检查重复
      const isDuplicate = segmentData.some(item => item.name === values.name);
      if (isDuplicate) {
        message.error('业务板块名称已存在');
        return;
      }

      // TODO: 替换为实际API调用
      // await apiService.createBusinessSegment(values);
      
      // 模拟添加
      const newSegment: BusinessSegmentItem = {
        id: Date.now().toString(),
        name: values.name,
        companyCount: 0,
        createTime: new Date().toLocaleString()
      };
      
      setSegmentData([newSegment, ...segmentData]);
      setIsAddModalVisible(false);
      message.success('新增成功');
    } catch (error) {
      console.error('新增失败:', error);
      message.error('新增失败');
    }
  };

  // 处理删除
  const handleDelete = async (record: BusinessSegmentItem) => {
    try {
      // TODO: 替换为实际API调用
      // await apiService.deleteBusinessSegment(record.id);
      
      // 模拟删除
      setSegmentData(segmentData.filter(item => item.id !== record.id));
      message.success('删除成功');
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 处理公司数量点击
  const handleCompanyCountClick = async (record: BusinessSegmentItem) => {
    setSelectedSegment(record);
    setIsCompanyListModalVisible(true);
    setCompanyListLoading(true);

    try {
      const companies = await fetchCompanyList(record.name);
      setCompanyList(companies);
    } catch (error) {
      console.error('获取公司列表失败:', error);
      message.error('获取公司列表失败');
      setCompanyList([]);
    } finally {
      setCompanyListLoading(false);
    }
  };

  // 处理股权图跳转
  const handleEquityChart = (record: BusinessSegmentItem) => {
    navigate('/business/equity-chart', { state: { segment: record.name } });
  };

  // 处理公司详情跳转
  const handleCompanyDetail = (company: CompanyItem) => {
    // 关闭弹窗
    setIsCompanyListModalVisible(false);
    // 跳转到公司详情页面
    navigate('/company/detail', { state: { companyId: company.id, companyName: company.companyNameCn } });
  };

  // 按创建时间排序（最新的在前）
  const sortedData = [...segmentData].sort((a, b) => 
    new Date(b.createTime).getTime() - new Date(a.createTime).getTime()
  );

  // 表格列定义
  const columns: ColumnsType<BusinessSegmentItem> = [
    {
      title: '业务板块',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center',
    },
    {
      title: '公司数量',
      dataIndex: 'companyCount',
      key: 'companyCount',
      width: 150,
      align: 'center',
      render: (count, record) => (
        <Button
          type="link"
          onClick={() => handleCompanyCountClick(record)}
          style={{ padding: 0, color: '#1890ff' }}
        >
          {count}
        </Button>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      align: 'center',
      render: (_, record) => (
        <Space size="small">
          <Button type="link" onClick={() => handleEquityChart(record)}>
            股权图
          </Button>
          <Popconfirm
            title="确定要删除这个业务板块吗？"
            onConfirm={() => handleDelete(record)}
            okText="确定"
            cancelText="取消"
          >
            <Button type="link" danger>
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 公司列表表格列定义
  const companyColumns: ColumnsType<CompanyItem> = [
    {
      title: '公司中文名',
      dataIndex: 'companyNameCn',
      key: 'companyNameCn',
      align: 'center',
      render: (text, record) => (
        <Button
          type="link"
          onClick={() => handleCompanyDetail(record)}
          style={{
            padding: 0,
            textAlign: 'left',
            height: 'auto',
            color: '#1890ff',
            fontWeight: 'normal'
          }}
        >
          {text}
        </Button>
      ),
    },
    {
      title: '公司英文名',
      dataIndex: 'companyNameEn',
      key: 'companyNameEn',
      align: 'center',
      render: (text) => (
        <span style={{ color: '#666' }}>{text || '-'}</span>
      ),
    },
  ];

  // 重试功能
  const handleRetry = () => {
    setError(null);
    fetchSegmentData();
  };

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <div className="business-segment-container">
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <h3>数据加载失败</h3>
            <p style={{ color: '#ff4d4f', marginBottom: '16px' }}>{error}</p>
            <Space>
              <Button type="primary" onClick={handleRetry}>
                重试
              </Button>
              <Button onClick={() => navigate(-1)}>
                返回
              </Button>
            </Space>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="business-segment-container">
      {/* 查询条件 */}
      <Card className="search-card">
        <div className="search-row">
          <div className="search-item">
            <span className="search-label">业务板块</span>
            <div className="search-input-group">
              <Input
                placeholder="请输入"
                value={searchForm.name}
                onChange={(e) => handleSearchFormChange('name', e.target.value)}
                style={{ width: 200 }}
              />
              <Space style={{ marginLeft: 16 }}>
                <Button type="primary" onClick={handleQuery}>
                  查询
                </Button>
                <Button onClick={handleReset}>
                  重置
                </Button>
              </Space>
            </div>
          </div>
        </div>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card">
        <Space>
          <Button type="primary" onClick={handleAdd}>
            新增
          </Button>
        </Space>
      </Card>

      {/* 数据表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={sortedData}
          loading={loading}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showTotal: (total) => `共 ${total} 条记录`,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          locale={{
            emptyText: (
              <div style={{ padding: '20px 0' }}>
                <p>暂无业务板块数据</p>
                <Button type="link" onClick={handleRetry}>
                  刷新数据
                </Button>
              </div>
            )
          }}
        />
      </Card>

      {/* 新增业务板块弹窗 */}
      <Modal
        title="新增业务板块"
        open={isAddModalVisible}
        onOk={handleAddConfirm}
        onCancel={() => setIsAddModalVisible(false)}
        width={500}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="业务板块名称"
            rules={[{ required: true, message: '请输入业务板块名称' }]}
          >
            <Input placeholder="请输入业务板块名称" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 公司列表弹窗 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
              {selectedSegment?.name} - 公司列表
            </span>
            {!companyListLoading && companyList.length > 0 && (
              <span style={{ marginLeft: '8px', color: '#666', fontSize: '14px' }}>
                (共 {companyList.length} 家公司)
              </span>
            )}
          </div>
        }
        open={isCompanyListModalVisible}
        onCancel={() => setIsCompanyListModalVisible(false)}
        footer={[
          <Button key="back" onClick={() => setIsCompanyListModalVisible(false)}>
            返回
          </Button>
        ]}
        width={800}
        destroyOnClose={true}
      >
        <div style={{ marginBottom: '16px', color: '#666' }}>
          点击公司名称可查看详细信息
        </div>
        <Table
          columns={companyColumns}
          dataSource={companyList}
          loading={companyListLoading}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showTotal: (total) => `共 ${total} 家公司`,
            showSizeChanger: false,
            showQuickJumper: false,
          }}
          locale={{
            emptyText: companyListLoading ? '加载中...' : '该业务板块下暂无公司'
          }}
          size="middle"
        />
      </Modal>
    </div>
  );
};

export default BusinessSegmentPage;
