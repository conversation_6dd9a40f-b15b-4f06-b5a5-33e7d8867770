import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Space, Tag, Modal, message, Row, Col, Select, Pagination, Checkbox, DatePicker } from 'antd';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import dayjs from 'dayjs';
import './CompanyInfo.css';

// 配置axios默认URL
axios.defaults.baseURL = 'http://localhost:3307';

const { confirm } = Modal;

interface CompanyItem {
  id: string;
  chineseName: string;
  englishName: string;
  registeredCapital: string;
  establishmentDate: string;
  region: string;
  agentOrganization: string;
  annualReport: string;
  changeHistory: number;
  businessScope: string;
  legalRepresentative: string;
  director: string;
  supervisor: string;
  generalManager: string;
  financialOfficer: string;
  chairman: string; // 董事长
  viceChairman: string; // 副董事长
  viceGeneralManager: string; // 副总经理
  technicalDirector: string; // 技术总监
  lastUpdateTime: string;
}

const CompanyInfo: React.FC = () => {
  const navigate = useNavigate();
  const [searchForm, setSearchForm] = useState({
    companyName: '',
    businessScope: '',
    establishmentDate: '',
    agentOrganization: '',
    region: '',
    regionType: ''
  });
  const [loading, setLoading] = useState(false);
  const [selectedCompanies, setSelectedCompanies] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 下拉菜单选项数据
  const [businessScopeOptions, setBusinessScopeOptions] = useState<string[]>([]);
  const [agentOrganizationOptions, setAgentOrganizationOptions] = useState<string[]>([]);
  const [domesticRegions, setDomesticRegions] = useState<string[]>([]);
  const [overseasRegions, setOverseasRegions] = useState<string[]>([]);
  
  const [companyData, setCompanyData] = useState<CompanyItem[]>([]);
  const [error, setError] = useState<string | null>(null);

  // 从API获取下拉菜单选项数据
  useEffect(() => {
    const fetchOptions = async () => {
      try {
        // 获取业务板块选项
        const businessResponse = await axios.get('http://localhost:8080/api/business-segments');
        if (businessResponse.data.success) {
          const businessOptions = businessResponse.data.data.map((item: any) => item.name);
          setBusinessScopeOptions(businessOptions);
        } else {
          throw new Error('业务板块API返回失败');
        }

        // 获取代理机构选项
        const agentResponse = await axios.get('http://localhost:8080/api/agencies');
        if (agentResponse.data.success) {
          const agentOptions = agentResponse.data.data.map((item: any) => item.agencyName || item.agency_name || item.name);
          setAgentOrganizationOptions(agentOptions);
          console.log('✅ 代理机构选项加载成功:', agentOptions);
        } else {
          throw new Error('代理机构API返回失败');
        }

        // 获取地区选项
        const regionsResponse = await axios.get('http://localhost:8080/api/regions');
        if (regionsResponse.data.success) {
          const allRegions = regionsResponse.data.data;
          const domestic = allRegions.filter((item: any) => item.type === '国内').map((item: any) => item.region);
          const overseas = allRegions.filter((item: any) => item.type !== '国内').map((item: any) => item.region);
          setDomesticRegions(domestic);
          setOverseasRegions(overseas);
        } else {
          throw new Error('地区API返回失败');
        }

      } catch (error) {
        console.error('获取选项数据失败:', error);
        setError(`获取选项数据失败: ${error.message}`);
        message.error(`获取选项数据失败: ${error.message}`);
      }
    };

    fetchOptions();
  }, []);

  // 从API获取公司数据
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await axios.get('http://localhost:8080/api/companies');
        if (response.data.success) {
          // 获取业务板块数据用于映射
          const businessResponse = await axios.get('http://localhost:8080/api/business-segments');
          const businessSegments = businessResponse.data.success ? businessResponse.data.data : [];

          // 获取任职档案数据用于映射高管信息
          const employmentResponse = await axios.get('http://localhost:8080/api/employment-records');
          const employmentRecords = employmentResponse.data.success ? employmentResponse.data.data : [];

          // 转换API数据格式为前端需要的格式
          const transformedData = response.data.data.map((company: any) => {
            // 查找对应的业务板块名称
            const businessSegment = businessSegments.find((segment: any) => segment.id === company.business_segment_id);
            const businessScopeName = businessSegment ? businessSegment.name : '未分类';

            // 查找当前在职的高管信息
            const currentExecutives = employmentRecords.filter((record: any) =>
              record.companyId === company.id &&
              record.isActive === 1 &&
              record.endDate === null
            );

            // 提取各个职位的人员 - 严格按照数据库中的职位名称匹配
            const legalRepresentative = currentExecutives.find((exec: any) =>
              exec.position === '法定代表人'
            )?.personName || '';

            const director = currentExecutives.find((exec: any) =>
              exec.position === '董事' || exec.position === '执行董事'
            )?.personName || '';

            const supervisor = currentExecutives.find((exec: any) =>
              exec.position === '监事' || exec.position === '监事会主席'
            )?.personName || '';

            const generalManager = currentExecutives.find((exec: any) =>
              exec.position === '总经理' || exec.position === '经理'
            )?.personName || '';

            const financialOfficer = currentExecutives.find((exec: any) =>
              exec.position === '财务总监' || exec.position === '财务负责人'
            )?.personName || '';

            // 添加其他常见职位的显示
            const chairman = currentExecutives.find((exec: any) =>
              exec.position === '董事长'
            )?.personName || '';

            const viceChairman = currentExecutives.find((exec: any) =>
              exec.position === '副董事长'
            )?.personName || '';

            const viceGeneralManager = currentExecutives.find((exec: any) =>
              exec.position === '副总经理'
            )?.personName || '';

            const technicalDirector = currentExecutives.find((exec: any) =>
              exec.position === '技术总监'
            )?.personName || '';

            return {
              id: company.id.toString(),
              chineseName: company.company_name_cn,
              englishName: company.company_name_en,
              registeredCapital: company.registered_capital,
              establishmentDate: company.establish_date ? new Date(company.establish_date).toISOString().split('T')[0] : '',
              region: '', // 需要通过region_id查询地区名称
              agentOrganization: '', // 需要通过agency_id查询代理机构名称
              annualReport: '', // 需要通过annual_update_status_id查询年审状态
              changeHistory: 0, // 暂时设为0，后续可以通过变更记录API获取
              businessScope: businessScopeName, // 从数据库获取真实的业务板块名称
              legalRepresentative, // 法定代表人
              director, // 董事
              supervisor, // 监事
              generalManager, // 总经理
              financialOfficer, // 财务负责人
              chairman, // 董事长
              viceChairman, // 副董事长
              viceGeneralManager, // 副总经理
              technicalDirector, // 技术总监
              lastUpdateTime: company.updated_at ? new Date(company.updated_at).toISOString().split('T')[0] : ''
            };
          });

          setCompanyData(transformedData);
          console.log('✅ 公司数据加载成功，共', transformedData.length, '家公司');
        } else {
          throw new Error(response.data.message || '公司数据API返回失败');
        }

        setLoading(false);
      } catch (error) {
        console.error('获取公司数据失败:', error);
        setError(`获取公司数据失败: ${error.message}`);
        message.error(`获取公司数据失败: ${error.message}`);
        setLoading(false);
      }
    };

    fetchCompanies();
  }, []);

  // 处理搜索表单变化
  const handleSearchFormChange = (field: string, value: string) => {
    setSearchForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 处理查询
  const handleQuery = () => {
    // 这里可以添加查询逻辑
    message.success('查询完成');
  };

  // 处理重置
  const handleReset = () => {
    setSearchForm({
      companyName: '',
      businessScope: '',
      establishmentDate: '',
      agentOrganization: '',
      region: '',
      regionType: ''
    });
  };

  // 过滤数据
  const filteredData = companyData.filter(item => {
    return (
      (!searchForm.companyName || item.chineseName.includes(searchForm.companyName) || item.englishName.includes(searchForm.companyName)) &&
      (!searchForm.businessScope || item.businessScope.includes(searchForm.businessScope)) &&
      (!searchForm.region || item.region.includes(searchForm.region)) &&
      (!searchForm.agentOrganization || item.agentOrganization.includes(searchForm.agentOrganization))
    );
  });

  // 按变更时间排序（最近变更的在前面）
  const sortedData = [...filteredData].sort((a, b) => {
    return new Date(b.lastUpdateTime).getTime() - new Date(a.lastUpdateTime).getTime();
  });

  // 处理添加新公司
  const handleAddCompany = () => {
    navigate('/company/add');
  };

  // 处理查看公司详情
  const handleViewCompany = (id: string) => {
    navigate('/company/detail', { state: { companyId: parseInt(id) } });
  };

  // 处理查看人员详情
  const handleViewPerson = (personName: string) => {
    navigate('/employment-archive', { state: { personName } });
  };

  // 获取公司的实际职位信息
  const getCompanyExecutives = (company: CompanyItem) => {
    const executives = [];

    // 按照重要性顺序显示职位
    if (company.legalRepresentative) {
      executives.push({ position: '法定代表人', name: company.legalRepresentative });
    }
    if (company.chairman) {
      executives.push({ position: '董事长', name: company.chairman });
    }
    if (company.viceChairman) {
      executives.push({ position: '副董事长', name: company.viceChairman });
    }
    if (company.director) {
      executives.push({ position: '董事', name: company.director });
    }
    if (company.generalManager) {
      executives.push({ position: '总经理', name: company.generalManager });
    }
    if (company.viceGeneralManager) {
      executives.push({ position: '副总经理', name: company.viceGeneralManager });
    }
    if (company.supervisor) {
      executives.push({ position: '监事', name: company.supervisor });
    }
    if (company.financialOfficer) {
      executives.push({ position: '财务负责人', name: company.financialOfficer });
    }
    if (company.technicalDirector) {
      executives.push({ position: '技术总监', name: company.technicalDirector });
    }

    return executives;
  };

  // 处理股权图
  const handleEquityChart = (companyId: string) => {
    Modal.info({
      title: '股权图',
      content: `显示公司 ${companyId} 的股权结构图`,
      width: 800,
    });
  };

  // 处理添加财务信息
  const handleAddFinance = (companyId: string) => {
    navigate('/company/finance', { state: { companyId } });
  };

  // 变更类型弹窗状态
  const [changeModalVisible, setChangeModalVisible] = useState(false);
  const [selectedCompanyId, setSelectedCompanyId] = useState<string>('');

  // 处理变更类型选择
  const handleChangeType = (companyId: string) => {
    setSelectedCompanyId(companyId);
    setChangeModalVisible(true);
  };

  // 关闭变更弹窗
  const handleChangeModalClose = () => {
    setChangeModalVisible(false);
    setSelectedCompanyId('');
  };

  // 处理删除公司
  const handleDeleteCompany = () => {
    if (selectedCompanies.length === 0) {
      message.warning('请先选择要删除的公司');
      return;
    }
    
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除选中的 ${selectedCompanies.length} 家公司吗？此操作不可逆。`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        // 实际项目中应该调用API删除数据
        setCompanyData(companyData.filter(item => !selectedCompanies.includes(item.id)));
        setSelectedCompanies([]);
        message.success('删除成功');
      },
    });
  };

  // 处理添加/更新财务信息
  const handleFinanceManagement = () => {
    navigate('/company/finance');
  };

  // 处理选择公司
  const handleSelectCompany = (companyId: string, checked: boolean) => {
    if (checked) {
      setSelectedCompanies([...selectedCompanies, companyId]);
    } else {
      setSelectedCompanies(selectedCompanies.filter(id => id !== companyId));
    }
  };

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedCompanies(sortedData.map(item => item.id));
    } else {
      setSelectedCompanies([]);
    }
  };

  // 分页数据
  const startIndex = (currentPage - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedData = sortedData.slice(startIndex, endIndex);

  // 渲染公司卡片
  const renderCompanyCard = (company: CompanyItem) => (
    <Card
      key={company.id}
      className="company-card"
      hoverable
      onClick={() => handleViewCompany(company.id)}
    >
      <div className="company-card-header">
        <div className="company-selection">
          <Checkbox
            checked={selectedCompanies.includes(company.id)}
            onChange={(e) => {
              e.stopPropagation();
              handleSelectCompany(company.id, e.target.checked);
            }}
            onClick={(e) => e.stopPropagation()}
          />
        </div>
        <div className="company-title">
          <h3>{company.chineseName}/{company.englishName}</h3>
        </div>
        <div className="change-count">
          <span className="change-badge">{company.changeHistory}</span>
          <span className="change-text">变更历史</span>
        </div>
      </div>

      <div className="company-card-content">
        <Row gutter={[16, 8]}>
          <Col span={8}>
            <div className="info-item">
              <span className="label">注册资本：</span>
              <span className="value">{company.registeredCapital}万元人民币</span>
            </div>
          </Col>
          <Col span={8}>
            <div className="info-item">
              <span className="label">设立日期：</span>
              <span className="value">{company.establishmentDate}</span>
            </div>
          </Col>
          <Col span={8}>
            <div className="info-item">
              <span className="label">地区：</span>
              <span className="value">{company.region}</span>
            </div>
          </Col>
          <Col span={8}>
            <div className="info-item">
              <span className="label">代理机构：</span>
              <span className="value">{company.agentOrganization}</span>
            </div>
          </Col>
          <Col span={8}>
            <div className="info-item">
              <span className="label">年审要求：</span>
              <span className="value">{company.annualReport}</span>
            </div>
          </Col>
          <Col span={8}>
            <div className="info-item">
              <span className="label">业务板块：</span>
              <span className="value">{company.businessScope}</span>
            </div>
          </Col>
        </Row>

        <div className="personnel-info">
          <Row gutter={[16, 4]}>
            {getCompanyExecutives(company).map((executive, index) => (
              <Col span={12} key={index}>
                <span className="label">{executive.position}：</span>
                <a
                  className="person-link"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleViewPerson(executive.name);
                  }}
                >
                  {executive.name}
                </a>
              </Col>
            ))}
            {getCompanyExecutives(company).length === 0 && (
              <Col span={24}>
                <span className="label" style={{ color: '#999' }}>暂无高管信息</span>
              </Col>
            )}
          </Row>
        </div>

        <div className="business-tags">
          <Tag color="blue">{company.businessScope}</Tag>
        </div>
      </div>

      <div className="company-card-actions">
        <Button
          type="primary"
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            handleEquityChart(company.id);
          }}
        >
          股权图
        </Button>
        <Button
          type="primary"
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            handleAddFinance(company.id);
          }}
        >
          添加财务信息
        </Button>
        <Button
          type="primary"
          size="small"
          onClick={(e) => {
            e.stopPropagation();
            handleChangeType(company.id);
          }}
        >
          变更
        </Button>
      </div>
    </Card>
  );

  // 重试功能
  const handleRetry = () => {
    setError(null);
    window.location.reload(); // 重新加载页面以重新获取所有数据
  };

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <div className="company-info-container">
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <h3>数据加载失败</h3>
            <p style={{ color: '#ff4d4f', marginBottom: '16px' }}>{error}</p>
            <Button type="primary" onClick={handleRetry}>
              重试
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="company-info-container">
      {/* 查询条件 */}
      <Card className="search-card">
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <div className="search-item">
              <span className="search-label">公司名称</span>
              <Input
                placeholder="请输入"
                value={searchForm.companyName}
                onChange={(e) => handleSearchFormChange('companyName', e.target.value)}
              />
            </div>
          </Col>
          <Col span={8}>
            <div className="search-item">
              <span className="search-label">业务板块</span>
              <Select
                placeholder="请选择"
                style={{ width: '100%' }}
                value={searchForm.businessScope}
                onChange={(value) => handleSearchFormChange('businessScope', value)}
                allowClear
              >
                <Select.Option value="">全部</Select.Option>
                {businessScopeOptions.map(option => (
                  <Select.Option key={option} value={option}>{option}</Select.Option>
                ))}
              </Select>
            </div>
          </Col>
          <Col span={8}>
            <div className="search-item">
              <span className="search-label">设立日期</span>
              <DatePicker
                placeholder="请选择日期"
                style={{ width: '100%' }}
                value={searchForm.establishmentDate ? dayjs(searchForm.establishmentDate) : null}
                onChange={(date) => handleSearchFormChange('establishmentDate', date ? date.format('YYYY-MM-DD') : '')}
                format="YYYY-MM-DD"
              />
            </div>
          </Col>
          <Col span={8}>
            <div className="search-item">
              <span className="search-label">代理机构</span>
              <Select
                placeholder="请选择"
                style={{ width: '100%' }}
                value={searchForm.agentOrganization}
                onChange={(value) => handleSearchFormChange('agentOrganization', value)}
                allowClear
              >
                <Select.Option value="">全部</Select.Option>
                {agentOrganizationOptions.map(option => (
                  <Select.Option key={option} value={option}>{option}</Select.Option>
                ))}
              </Select>
            </div>
          </Col>
          <Col span={8}>
            <div className="search-item">
              <span className="search-label">地区类型</span>
              <Select
                placeholder="请选择"
                style={{ width: '100%' }}
                value={searchForm.regionType}
                onChange={(value) => {
                  handleSearchFormChange('regionType', value);
                  // 当地区类型改变时，清空地区选择
                  handleSearchFormChange('region', '');
                }}
                allowClear
              >
                <Select.Option value="">全部</Select.Option>
                <Select.Option value="domestic">国内</Select.Option>
                <Select.Option value="overseas">海外</Select.Option>
              </Select>
            </div>
          </Col>
          <Col span={8}>
            <div className="search-item">
              <span className="search-label">地区</span>
              <Select
                placeholder="请选择"
                style={{ width: '100%' }}
                value={searchForm.region}
                onChange={(value) => handleSearchFormChange('region', value)}
                allowClear
                disabled={!searchForm.regionType}
              >
                <Select.Option value="">全部</Select.Option>
                {searchForm.regionType === 'domestic' && domesticRegions.map(region => (
                  <Select.Option key={region} value={region}>{region}</Select.Option>
                ))}
                {searchForm.regionType === 'overseas' && overseasRegions.map(region => (
                  <Select.Option key={region} value={region}>{region}</Select.Option>
                ))}
              </Select>
            </div>
          </Col>
        </Row>

        <Row style={{ marginTop: 16 }}>
          <Col>
            <Space>
              <Button type="primary" onClick={handleQuery}>
                查询
              </Button>
              <Button onClick={handleReset}>
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card">
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button type="primary" onClick={handleAddCompany}>
                新增
              </Button>
              <Button
                danger
                onClick={handleDeleteCompany}
                disabled={selectedCompanies.length === 0}
              >
                删除
              </Button>
              <Button type="primary" onClick={handleFinanceManagement}>
                添加/更新财务信息
              </Button>
            </Space>
          </Col>
          <Col>
            <Checkbox
              checked={selectedCompanies.length === sortedData.length && sortedData.length > 0}
              indeterminate={selectedCompanies.length > 0 && selectedCompanies.length < sortedData.length}
              onChange={(e) => handleSelectAll(e.target.checked)}
            >
              全选
            </Checkbox>
          </Col>
        </Row>
      </Card>

      {/* 公司列表 */}
      <div className="company-list">
        {loading ? (
          <div className="loading-container">
            <Card>加载中...</Card>
          </div>
        ) : paginatedData.length > 0 ? (
          paginatedData.map(company => renderCompanyCard(company))
        ) : (
          <Card>
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              暂无数据
            </div>
          </Card>
        )}
      </div>

      {/* 分页 */}
      <div className="pagination-container">
        <Pagination
          current={currentPage}
          pageSize={pageSize}
          total={sortedData.length}
          showTotal={(total, range) => `共 ${total} 条记录`}
          showSizeChanger
          showQuickJumper
          onChange={(page, size) => {
            setCurrentPage(page);
            setPageSize(size || 10);
          }}
          onShowSizeChange={(current, size) => {
            setCurrentPage(1);
            setPageSize(size);
          }}
        />
      </div>

      {/* 变更类型选择弹窗 */}
      <Modal
        title="选择变更类型"
        open={changeModalVisible}
        onCancel={handleChangeModalClose}
        footer={[
          <Button key="cancel" onClick={handleChangeModalClose}>
            取消
          </Button>
        ]}
        width={400}
      >
        <div>
          <p>请选择要进行的变更类型：</p>
          <Button
            block
            style={{ marginBottom: 8 }}
            onClick={() => {
              handleChangeModalClose();
              // 获取选中公司的详细信息
              const selectedCompany = companyData.find(company => company.id === selectedCompanyId);
              navigate('/company/change/basic-info', {
                state: {
                  companyId: parseInt(selectedCompanyId),
                  companyName: selectedCompany?.chineseName || ''
                }
              });
            }}
          >
            变更基础信息
          </Button>
          <Button
            block
            style={{ marginBottom: 8 }}
            onClick={() => {
              handleChangeModalClose();
              // 获取选中公司的详细信息
              const selectedCompany = companyData.find(company => company.id === selectedCompanyId);
              navigate('/company/change/executive-info', {
                state: {
                  companyId: parseInt(selectedCompanyId),
                  companyName: selectedCompany?.chineseName || ''
                }
              });
            }}
          >
            变更高管信息
          </Button>
          <Button
            block
            style={{ marginBottom: 8 }}
            onClick={() => {
              handleChangeModalClose();
              // 获取选中公司的详细信息
              const selectedCompany = companyData.find(company => company.id === selectedCompanyId);
              navigate('/company/change/shareholder-info', {
                state: {
                  companyId: parseInt(selectedCompanyId),
                  companyName: selectedCompany?.chineseName || ''
                }
              });
            }}
          >
            变更股东信息
          </Button>
          <Button
            block
            onClick={() => {
              handleChangeModalClose();
              // 获取选中公司的详细信息
              const selectedCompany = companyData.find(company => company.id === selectedCompanyId);
              navigate('/company/change/investment-info', {
                state: {
                  companyId: parseInt(selectedCompanyId),
                  companyName: selectedCompany?.chineseName || ''
                }
              });
            }}
          >
            变更对外投资信息
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default CompanyInfo;
