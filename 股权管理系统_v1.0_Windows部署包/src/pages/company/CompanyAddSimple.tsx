import React, { useState, useEffect } from 'react';
import { 
  Form, 
  Input, 
  Button, 
  Select, 
  DatePicker, 
  message, 
  Row, 
  Col, 
  Card, 
  Table, 
  Space, 
  Modal, 
  Badge,
  Grid,
  Typography,
  Alert
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  PlusOutlined,
  DeleteOutlined,
  ArrowLeftOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const { useBreakpoint } = Grid;
const { Title, Text } = Typography;
const { Option } = Select;

// TypeScript Interfaces
interface CompanyFormData {
  companyNameCn: string;
  companyNameEn: string;
  registeredCapital: string;
  establishDate: string;
  businessSegment: string;
  region: string;
  agency: string;
  annualUpdate: string;
  registeredAddress: string;
  operationStatus: string;
}

interface ServerStatus {
  status: 'checking' | 'online' | 'offline';
  message?: string;
}

const CompanyAddSimple: React.FC = () => {
  // Hooks
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const screens = useBreakpoint();
  
  // State Management
  const [loading, setLoading] = useState(false);
  const [serverStatus, setServerStatus] = useState<ServerStatus>({ status: 'checking' });
  const [isSuccessModalVisible, setIsSuccessModalVisible] = useState(false);
  
  // Responsive Design
  const isMobile = !screens.md;

  // Basic Data
  const businessSegments = [
    'A. 农、林、牧、渔业',
    'B. 采矿业',
    'C. 制造业',
    'D. 电力、热力、燃气及水的生产和供应业',
    'E. 建筑业',
    'F. 批发和零售业',
    'G. 交通运输、仓储和邮政业',
    'H. 住宿和餐饮业',
    'I. 信息传输、软件和信息技术服务业',
    'J. 金融业'
  ];
  
  const regions = ['华南', '华东', '华北', '华中', '西南', '西北', '东北'];
  const agencies = ['代理机构A', '代理机构B', '代理机构C'];
  const annualUpdates = ['不管年审', '管年审（固定周期）', '管年审（滚动周期）'];
  const operationStatuses = ['正常经营', '已注销', '异常'];

  // Server Status Check
  const checkServerStatus = async () => {
    try {
      setServerStatus({ status: 'checking' });
      console.log('🔍 Checking server status...');
      
      const response = await axios.get('http://localhost:8080/api/test-db', {
        timeout: 5000
      });
      
      if (response.data.success) {
        setServerStatus({ 
          status: 'online', 
          message: '服务器连接正常' 
        });
      } else {
        setServerStatus({ 
          status: 'offline', 
          message: '服务器连接异常' 
        });
      }
    } catch (error) {
      console.error('❌ Server status check failed:', error);
      setServerStatus({ 
        status: 'offline', 
        message: '服务器连接失败' 
      });
    }
  };

  // Component Initialization
  useEffect(() => {
    checkServerStatus();
  }, []);

  // Form Submission Handler
  const onFinish = async (values: CompanyFormData) => {
    try {
      setLoading(true);
      console.log('📝 Starting form submission...', values);
      
      // Check server status
      if (serverStatus.status === 'offline') {
        message.error('服务器连接失败，无法提交表单，请稍后再试');
        return;
      }
      
      // Submit company data
      console.log('💾 Creating company...');
      const response = await axios.post('http://localhost:8080/api/company/add', values, {
        timeout: 10000,
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (response.data.success) {
        console.log('✅ Company created successfully:', response.data);
        message.success('公司添加成功！');
        setIsSuccessModalVisible(true);
      } else {
        throw new Error(response.data.message || '添加公司失败');
      }
      
    } catch (error: any) {
      console.error('❌ Failed to create company:', error);
      
      if (error.response?.data?.message) {
        message.error(error.response.data.message);
      } else {
        message.error('添加公司失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  // Event Handlers
  const handleSuccessModalOk = () => {
    setIsSuccessModalVisible(false);
    handleReset();
  };

  const handleSuccessModalCancel = () => {
    setIsSuccessModalVisible(false);
    navigate('/company/list');
  };

  const handleReset = () => {
    form.resetFields();
    message.info('表单已重置');
  };

  const handleBack = () => {
    navigate(-1);
  };

  // Render Server Status Badge
  const renderServerStatus = () => {
    switch (serverStatus.status) {
      case 'online':
        return <Badge status="success" text="服务器在线" />;
      case 'offline':
        return <Badge status="error" text="服务器离线" />;
      case 'checking':
      default:
        return <Badge status="processing" text="检查中..." />;
    }
  };

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f5f5f5', padding: isMobile ? 16 : 24 }}>
      {/* Success Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
            <span>添加成功</span>
          </div>
        }
        open={isSuccessModalVisible}
        onOk={handleSuccessModalOk}
        onCancel={handleSuccessModalCancel}
        okText="继续添加"
        cancelText="查看列表"
        width={isMobile ? '90%' : 520}
      >
        <div style={{ padding: '16px 0' }}>
          <Alert
            message="公司信息已成功添加到数据库！"
            description="您可以继续添加新公司，或者查看公司列表。"
            type="success"
            showIcon
          />
        </div>
      </Modal>

      {/* Page Header */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              style={{ marginRight: 12 }}
            >
              {!isMobile && '返回'}
            </Button>
            <div>
              <Title level={isMobile ? 4 : 3} style={{ margin: 0 }}>
                新增公司
              </Title>
              <Text type="secondary">
                添加新的公司信息到系统中
              </Text>
            </div>
          </div>
          
          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            {renderServerStatus()}
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={checkServerStatus}
              loading={serverStatus.status === 'checking'}
            >
              {!isMobile && '刷新状态'}
            </Button>
          </div>
        </div>

        {/* Server Status Alert */}
        {serverStatus.status === 'offline' && (
          <Alert
            message="服务器连接异常"
            description="当前无法连接到服务器，部分功能可能无法正常使用。请检查网络连接或联系管理员。"
            type="warning"
            showIcon
            closable
            style={{ marginBottom: 16 }}
          />
        )}
      </div>

      {/* Main Form Card */}
      <Card 
        title="公司基本信息"
        style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}
        loading={loading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{}}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="companyNameCn"
                label="公司中文名"
                rules={[
                  { required: true, message: '请输入公司中文名' },
                  { max: 255, message: '公司中文名不能超过255个字符' }
                ]}
              >
                <Input 
                  placeholder="请输入公司中文名" 
                  size={isMobile ? 'large' : 'middle'}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="companyNameEn"
                label="公司英文名"
                rules={[
                  { required: true, message: '请输入公司英文名' },
                  { max: 255, message: '公司英文名不能超过255个字符' }
                ]}
              >
                <Input 
                  placeholder="请输入公司英文名" 
                  size={isMobile ? 'large' : 'middle'}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="registeredCapital"
                label="注册资本（万元）"
                rules={[
                  { required: true, message: '请输入注册资本' },
                  { pattern: /^\d+(\.\d{1,4})?$/, message: '请输入有效的数字，最多4位小数' }
                ]}
              >
                <Input 
                  placeholder="请输入注册资本" 
                  addonAfter="万元"
                  size={isMobile ? 'large' : 'middle'}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="establishDate"
                label="设立日期"
                rules={[{ required: true, message: '请选择设立日期' }]}
              >
                <DatePicker 
                  style={{ width: '100%' }} 
                  placeholder="请选择设立日期"
                  size={isMobile ? 'large' : 'middle'}
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="businessSegment"
                label="业务板块"
                rules={[{ required: true, message: '请选择业务板块' }]}
              >
                <Select 
                  placeholder="请选择业务板块"
                  size={isMobile ? 'large' : 'middle'}
                  showSearch
                >
                  {businessSegments.map(segment => (
                    <Option key={segment} value={segment}>{segment}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="region"
                label="地区"
                rules={[{ required: true, message: '请选择地区' }]}
              >
                <Select
                  placeholder="请选择地区"
                  size={isMobile ? 'large' : 'middle'}
                  showSearch
                >
                  {regions.map(region => (
                    <Option key={region} value={region}>{region}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="agency"
                label="代理机构"
                rules={[{ required: true, message: '请选择代理机构' }]}
              >
                <Select 
                  placeholder="请选择代理机构"
                  size={isMobile ? 'large' : 'middle'}
                  showSearch
                >
                  {agencies.map(agency => (
                    <Option key={agency} value={agency}>{agency}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="annualUpdate"
                label="年审更新"
                rules={[{ required: true, message: '请选择年审更新' }]}
              >
                <Select 
                  placeholder="请选择年审更新"
                  size={isMobile ? 'large' : 'middle'}
                >
                  {annualUpdates.map(update => (
                    <Option key={update} value={update}>{update}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="registeredAddress"
                label="注册地址"
                rules={[
                  { required: true, message: '请输入注册地址' },
                  { max: 500, message: '注册地址不能超过500个字符' }
                ]}
              >
                <Input.TextArea 
                  placeholder="请输入注册地址" 
                  rows={isMobile ? 3 : 2}
                  size={isMobile ? 'large' : 'middle'}
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="operationStatus"
                label="存续状况"
                rules={[{ required: true, message: '请选择存续状况' }]}
              >
                <Select 
                  placeholder="请选择存续状况"
                  size={isMobile ? 'large' : 'middle'}
                >
                  {operationStatuses.map(status => (
                    <Option key={status} value={status}>{status}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* Form Actions */}
          <div style={{
            paddingTop: 24,
            borderTop: '1px solid #f0f0f0',
            display: isMobile ? 'block' : 'flex',
            justifyContent: isMobile ? 'stretch' : 'center',
            gap: isMobile ? 0 : 16
          }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
              size={isMobile ? 'large' : 'middle'}
              style={isMobile ? { width: '100%', marginBottom: 12 } : {}}
            >
              {loading ? '提交中...' : '确定'}
            </Button>
            <Button
              onClick={handleReset}
              icon={<ReloadOutlined />}
              size={isMobile ? 'large' : 'middle'}
              style={isMobile ? { width: '100%', marginBottom: 12 } : {}}
            >
              重置
            </Button>
            <Button
              onClick={handleBack}
              size={isMobile ? 'large' : 'middle'}
              style={isMobile ? { width: '100%' } : {}}
            >
              取消
            </Button>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default CompanyAddSimple;
