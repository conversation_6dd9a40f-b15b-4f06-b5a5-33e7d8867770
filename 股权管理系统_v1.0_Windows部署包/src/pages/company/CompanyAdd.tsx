import React, { useState } from 'react';
import {
  Form,
  Input,
  Button,
  Select,
  DatePicker,
  message,
  Row,
  Col,
  Card,
  Typography,
  Modal,
  Alert
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  ArrowLeftOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;
const { Option } = Select;

// TypeScript Interfaces
interface CompanyFormData {
  companyNameCn: string;
  companyNameEn: string;
  registeredCapital: number;
  establishDate: string;
  businessSegment: string;
  region: string;
  agency: string;
  annualUpdate: string;
  registeredAddress: string;
  operationStatus: string;
  businessAddress?: string;
  legalRepresentative?: string;
  unifiedSocialCreditCode?: string;
  businessScope?: string;
  companyType?: string;
}

interface ExecutiveData {
  key: number;
  position: string;
  person: string;
  idNumber?: string;
  appointmentDate?: string;
}

interface ShareholderData {
  key: number;
  name: string;
  investmentAmount: string;
  percentage: string;
  startDate: string;
  shareholderType: 'individual' | 'company';
}

interface InvestmentData {
  key: number;
  companyName: string;
  investmentAmount: string;
  percentage: string;
  startDate: string;
}

interface ServerStatus {
  status: 'checking' | 'online' | 'offline';
  message?: string;
}

const CompanyAdd: React.FC = () => {
  // Hooks
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const screens = useBreakpoint();

  // State Management
  const [loading, setLoading] = useState(false);
  const [serverStatus, setServerStatus] = useState<ServerStatus>({ status: 'checking' });
  const [isSuccessModalVisible, setIsSuccessModalVisible] = useState(false);

  // Form Data State
  const [executives, setExecutives] = useState<ExecutiveData[]>([]);
  const [shareholders, setShareholders] = useState<ShareholderData[]>([]);
  const [investments, setInvestments] = useState<InvestmentData[]>([]);

  // Responsive Design
  const isMobile = !screens.md;
  const isTablet = screens.md && !screens.lg;

  // Server Status Check
  const checkServerStatus = async () => {
    try {
      setServerStatus({ status: 'checking' });
      console.log('🔍 Checking server status...');

      const response = await apiService.checkServerStatus();

      if (response.data.success) {
        setServerStatus({
          status: 'online',
          message: '服务器连接正常'
        });
        message.success('服务器连接正常');
      } else {
        setServerStatus({
          status: 'offline',
          message: '服务器连接异常'
        });
        message.warning('服务器连接异常，部分功能可能无法正常使用');
      }
    } catch (error) {
      console.error('❌ Server status check failed:', error);
      setServerStatus({
        status: 'offline',
        message: '服务器连接失败'
      });
      message.warning('服务器连接失败，请检查网络连接或联系管理员');
    }
  };

  // Basic Data State
  const [businessSegments, setBusinessSegments] = useState<string[]>([
    'A. 农、林、牧、渔业',
    'B. 采矿业',
    'C. 制造业',
    'D. 电力、热力、燃气及水的生产和供应业',
    'E. 建筑业',
    'F. 批发和零售业',
    'G. 交通运输、仓储和邮政业',
    'H. 住宿和餐饮业',
    'I. 信息传输、软件和信息技术服务业',
    'J. 金融业',
    'K. 房地产业',
    'L. 租赁和商务服务业',
    'M. 科学研究和技术服务业',
    'N. 水利、环境和公共设施管理业',
    'O. 居民服务、修理和其他服务业',
    'P. 教育',
    'Q. 卫生和社会工作',
    'R. 文化、体育和娱乐业',
    'S. 公共管理、社会保障和社会组织',
    'T. 国际组织'
  ]);
  const [regions, setRegions] = useState<string[]>([]);
  const [agencies, setAgencies] = useState<string[]>([]);

  // Constants
  const annualUpdates = ['不管年审', '管年审（固定周期）', '管年审（滚动周期）'];
  const operationStatuses = ['正常经营', '已注销', '异常'];

  // Remove duplicate state declarations (already declared above)

  // Component Initialization
  useEffect(() => {
    checkServerStatus();
    loadBasicData();

    // Set up periodic server status check
    const statusCheckInterval = setInterval(checkServerStatus, 60000);

    return () => clearInterval(statusCheckInterval);
  }, []);

  // Load Basic Data (regions, agencies, etc.)
  const loadBasicData = async () => {
    try {
      console.log('📊 Loading basic data...');

      // Load agencies with fallback
      try {
        const agenciesRes = await apiService.getAgencies();
        if (agenciesRes.data.success && agenciesRes.data.data) {
          setAgencies(agenciesRes.data.data.map((item: any) => item.name || item));
        }
      } catch (error) {
        console.warn('⚠️ Failed to load agencies, using defaults');
        setAgencies(['代理机构A', '代理机构B', '代理机构C']);
      }

      // Load regions with fallback
      try {
        const regionsRes = await apiService.getRegions();
        if (regionsRes.data.success && regionsRes.data.data) {
          setRegions(regionsRes.data.data);
        }
      } catch (error) {
        console.warn('⚠️ Failed to load regions, using defaults');
        setRegions(['华南', '华东', '华北', '华中', '西南', '西北', '东北']);
      }

    } catch (error) {
      console.error('❌ Failed to load basic data:', error);
      message.warning('部分选项数据获取失败，已使用默认数据');
    }
  };

  // Validation Helpers
  const isValidNumber = (value: string | number): boolean => {
    if (!value && value !== 0) return false;
    const num = Number(value);
    return !isNaN(num) && num >= 0;
  };

  const checkShareholdingPercentage = (items: any[]): boolean => {
    const totalPercentage = items.reduce((sum, item) => {
      const percentage = Number(item.percentage) || 0;
      return sum + percentage;
    }, 0);
    return totalPercentage <= 100;
  };

  const validateFormData = (values: CompanyFormData): string | null => {
    // Check required fields
    const requiredFields = [
      'companyNameCn', 'companyNameEn', 'registeredCapital', 'establishDate',
      'businessSegment', 'region', 'agency', 'annualUpdate', 'registeredAddress',
      'operationStatus'
    ];

    const missingFields = requiredFields.filter(field => !values[field as keyof CompanyFormData]);
    if (missingFields.length > 0) {
      return '请填写所有必填字段';
    }

    // Validate registered capital
    if (!isValidNumber(values.registeredCapital)) {
      return '注册资本必须为有效数字';
    }

    // Validate shareholders
    const invalidShareholders = shareholders.filter(
      shareholder =>
        (shareholder.investmentAmount && !isValidNumber(shareholder.investmentAmount)) ||
        (shareholder.percentage && !isValidNumber(shareholder.percentage))
    );

    if (invalidShareholders.length > 0) {
      return '股东信息中的认缴出资额和持股比例必须为有效数字';
    }

    if (!checkShareholdingPercentage(shareholders)) {
      return '股东信息中的持股比例总和不能超过100%';
    }

    // Validate investments
    const invalidInvestments = investments.filter(
      investment =>
        (investment.investmentAmount && !isValidNumber(investment.investmentAmount)) ||
        (investment.percentage && !isValidNumber(investment.percentage))
    );

    if (invalidInvestments.length > 0) {
      return '对外投资信息中的投资金额和持股比例必须为有效数字';
    }

    if (!checkShareholdingPercentage(investments)) {
      return '对外投资信息中的持股比例总和不能超过100%';
    }

    return null;
  };

  // Form Submission Handler
  const onFinish = async (values: CompanyFormData) => {
    try {
      setLoading(true);
      console.log('📝 Starting form submission...', values);

      // Check server status
      if (serverStatus.status === 'offline') {
        message.error('服务器连接失败，无法提交表单，请稍后再试');
        return;
      }

      // Validate form data
      const validationError = validateFormData(values);
      if (validationError) {
        message.error(validationError);
        return;
      }

      // Check for duplicates
      console.log('🔍 Checking for duplicate companies...');
      const duplicateCheck = await apiService.checkCompanyDuplicate({
        companyNameCn: values.companyNameCn,
        companyNameEn: values.companyNameEn
      });

      if (duplicateCheck.data.data?.duplicate) {
        message.error('公司已存在，中英文名称与现有记录重复');
        return;
      }

      // Submit company data
      console.log('💾 Creating company...');
      const companyData = {
        ...values,
        executives,
        shareholders,
        investments
      };

      const response = await apiService.createCompany(companyData);

      if (response.data.success) {
        console.log('✅ Company created successfully:', response.data);
        message.success('公司添加成功！');
        setIsSuccessModalVisible(true);
      } else {
        throw new Error(response.data.message || '添加公司失败');
      }
    } catch (error: any) {
      console.error('❌ Failed to create company:', error);

      // Error is already handled by the API service interceptor
      // Just log it here for debugging
      if (error.response?.data?.message) {
        message.error(error.response.data.message);
      } else {
        message.error('添加公司失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  // Event Handlers
  const handleSuccessModalOk = () => {
    setIsSuccessModalVisible(false);
    handleReset();
  };

  const handleSuccessModalCancel = () => {
    setIsSuccessModalVisible(false);
    // Navigate to company list
    navigate('/company/list');
  };

  const handleReset = () => {
    form.resetFields();
    setExecutives([]);
    setShareholders([]);
    setInvestments([]);
    message.info('表单已重置');
  };

  const handleBack = () => {
    navigate(-1);
  };

  // 添加高管信息
  const addExecutive = () => {
    setExecutives([...executives, { key: Date.now(), position: '', person: '' }]);
  };

  // 删除高管信息
  const removeExecutive = (key: number) => {
    setExecutives(executives.filter(item => item.key !== key));
  };

  // 更新高管信息
  const updateExecutive = (key: number, field: string, value: string) => {
    setExecutives(executives.map(item => 
      item.key === key ? { ...item, [field]: value } : item
    ));
  };

  // 添加股东信息
  const addShareholder = () => {
    setShareholders([...shareholders, { 
      key: Date.now(), 
      name: '', 
      investmentAmount: '', 
      percentage: '', 
      startDate: '' 
    }]);
  };

  // 删除股东信息
  const removeShareholder = (key: number) => {
    setShareholders(shareholders.filter(item => item.key !== key));
  };

  // 更新股东信息
  const updateShareholder = (key: number, field: string, value: any) => {
    setShareholders(shareholders.map(item => 
      item.key === key ? { ...item, [field]: value } : item
    ));
  };

  // 添加对外投资信息
  const addInvestment = () => {
    setInvestments([...investments, { 
      key: Date.now(), 
      companyName: '', 
      investmentAmount: '', 
      percentage: '', 
      startDate: '' 
    }]);
  };

  // 删除对外投资信息
  const removeInvestment = (key: number) => {
    setInvestments(investments.filter(item => item.key !== key));
  };

  // 更新对外投资信息
  const updateInvestment = (key: number, field: string, value: any) => {
    setInvestments(investments.map(item => 
      item.key === key ? { ...item, [field]: value } : item
    ));
  };

  // 高管信息表格列
  const executiveColumns = [
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
      render: (text: string, record: any) => (
        <Input 
          placeholder="请输入职位" 
          value={text} 
          onChange={(e) => updateExecutive(record.key, 'position', e.target.value)}
        />
      )
    },
    {
      title: '任职人员',
      dataIndex: 'person',
      key: 'person',
      render: (text: string, record: any) => (
        <Select 
          style={{ width: '100%' }} 
          value={text || undefined} 
          placeholder="请选择任职人员"
          onChange={(value) => updateExecutive(record.key, 'person', value)}
        >
          <Option value="张三">张三</Option>
          <Option value="李四">李四</Option>
          <Option value="王五">王五</Option>
          <Option value="赵六">赵六</Option>
        </Select>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Button 
          type="link" 
          danger 
          icon={<DeleteOutlined />} 
          onClick={() => removeExecutive(record.key)}
        >
          删除
        </Button>
      )
    }
  ];

  // 股东信息表格列
  const shareholderColumns = [
    {
      title: '登记股东',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <Input 
          placeholder="请输入" 
          value={text} 
          onChange={(e) => updateShareholder(record.key, 'name', e.target.value)}
        />
      )
    },
    {
      title: '认缴出资额（万元人民币）',
      dataIndex: 'investmentAmount',
      key: 'investmentAmount',
      render: (text: string, record: any) => (
        <Input 
          placeholder="请输入" 
          value={text} 
          onChange={(e) => updateShareholder(record.key, 'investmentAmount', e.target.value)}
        />
      )
    },
    {
      title: '持股比例',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (text: string, record: any) => (
        <Input 
          placeholder="请输入" 
          value={text} 
          addonAfter="%"
          onChange={(e) => updateShareholder(record.key, 'percentage', e.target.value)}
        />
      )
    },
    {
      title: '是否代持',
      dataIndex: 'isProxy',
      key: 'isProxy',
      render: (text: string, record: any) => (
        <Select 
          style={{ width: '100%' }} 
          value={text || undefined} 
          placeholder="请选择"
          onChange={(value) => updateShareholder(record.key, 'isProxy', value)}
        >
          <Option value="是">是</Option>
          <Option value="否">否</Option>
        </Select>
      )
    },
    {
      title: '实际股东',
      dataIndex: 'actualShareholder',
      key: 'actualShareholder',
      render: (text: string, record: any) => (
        <Input 
          placeholder="请输入" 
          value={text} 
          onChange={(e) => updateShareholder(record.key, 'actualShareholder', e.target.value)}
        />
      )
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      key: 'startDate',
      render: (text: string, record: any) => (
        <DatePicker 
          style={{ width: '100%' }} 
          placeholder="请选择" 
          value={text ? new Date(text) : undefined} 
          onChange={(date) => updateShareholder(record.key, 'startDate', date)}
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Button 
          type="link" 
          danger 
          icon={<DeleteOutlined />} 
          onClick={() => removeShareholder(record.key)}
        >
          删除
        </Button>
      )
    }
  ];

  // 对外投资信息表格列
  const investmentColumns = [
    {
      title: '投资对象',
      dataIndex: 'companyName',
      key: 'companyName',
      render: (text: string, record: any) => (
        <Input 
          placeholder="请输入" 
          value={text} 
          onChange={(e) => updateInvestment(record.key, 'companyName', e.target.value)}
        />
      )
    },
    {
      title: '投资金额（万元人民币）',
      dataIndex: 'investmentAmount',
      key: 'investmentAmount',
      render: (text: string, record: any) => (
        <Input 
          placeholder="请输入" 
          value={text} 
          onChange={(e) => updateInvestment(record.key, 'investmentAmount', e.target.value)}
        />
      )
    },
    {
      title: '持股比例',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (text: string, record: any) => (
        <Input 
          placeholder="请输入" 
          value={text} 
          addonAfter="%"
          onChange={(e) => updateInvestment(record.key, 'percentage', e.target.value)}
        />
      )
    },
    {
      title: '是否登记投资权益',
      dataIndex: 'isRegistered',
      key: 'isRegistered',
      render: (text: string, record: any) => (
        <Select 
          style={{ width: '100%' }} 
          value={text || undefined} 
          placeholder="请选择"
          onChange={(value) => updateInvestment(record.key, 'isRegistered', value)}
        >
          <Option value="是">是</Option>
          <Option value="否">否</Option>
        </Select>
      )
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      key: 'startDate',
      render: (text: string, record: any) => (
        <DatePicker 
          style={{ width: '100%' }} 
          placeholder="请选择" 
          value={text ? new Date(text) : undefined} 
          onChange={(date) => updateInvestment(record.key, 'startDate', date)}
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_: any, record: any) => (
        <Button 
          type="link" 
          danger 
          icon={<DeleteOutlined />} 
          onClick={() => removeInvestment(record.key)}
        >
          删除
        </Button>
      )
    }
  ];

  // 根据服务器状态返回状态标签
  const renderServerStatusBadge = () => {
    switch (serverStatus.status) {
      case 'online':
        return <Badge status="success" text="服务器在线" />;
      case 'offline':
        return <Badge status="error" text="服务器离线" />;
      case 'checking':
      default:
        return <Badge status="processing" text="检查中..." />;
    }
  };

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f5f5f5', padding: isMobile ? 16 : 24 }}>
      {/* Success Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
            <span>添加成功</span>
          </div>
        }
        open={isSuccessModalVisible}
        onOk={handleSuccessModalOk}
        onCancel={handleSuccessModalCancel}
        okText="继续添加"
        cancelText="查看列表"
        width={isMobile ? '90%' : 520}
      >
        <div style={{ padding: '16px 0' }}>
          <Alert
            message="公司信息已成功添加到数据库！"
            description="您可以继续添加新公司，或者查看公司列表。"
            type="success"
            showIcon
          />
        </div>
      </Modal>

      {/* Page Header */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={<ArrowLeftOutlined />}
              onClick={handleBack}
              style={{ marginRight: 12 }}
            >
              {!isMobile && '返回'}
            </Button>
            <div>
              <Title level={isMobile ? 4 : 3} style={{ margin: 0 }}>
                新增公司
              </Title>
              <Text type="secondary">
                添加新的公司信息到系统中
              </Text>
            </div>
          </div>

          <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
            {renderServerStatusBadge()}
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={checkServerStatus}
              loading={serverStatus.status === 'checking'}
            >
              {!isMobile && '刷新状态'}
            </Button>
          </div>
        </div>

        {/* Server Status Alert */}
        {serverStatus.status === 'offline' && (
          <Alert
            message="服务器连接异常"
            description="当前无法连接到服务器，部分功能可能无法正常使用。请检查网络连接或联系管理员。"
            type="warning"
            showIcon
            closable
            style={{ marginBottom: 16 }}
          />
        )}
      </div>

      {/* Main Form Card */}
      <Card
        title="新增公司"
        style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.1)' }}
        loading={loading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{}}
        >
          {/* Basic Information Section */}
          <div style={{ marginBottom: 32 }}>
            <Title level={5} style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
              <span style={{ width: 4, height: 24, backgroundColor: '#1890ff', marginRight: 12 }}></span>
              基础信息
            </Title>

            <Row gutter={[16, 16]}>
              <Col xs={24} sm={24} md={12} lg={12}>
                <Form.Item
                  name="companyNameCn"
                  label="公司中文名"
                  rules={[
                    { required: true, message: '请输入公司中文名' },
                    { max: 255, message: '公司中文名不能超过255个字符' }
                  ]}
                >
                  <Input
                    placeholder="请输入公司中文名"
                    size={isMobile ? 'large' : 'middle'}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={24} md={12} lg={12}>
                <Form.Item
                  name="companyNameEn"
                  label="公司英文名"
                  rules={[
                    { required: true, message: '请输入公司英文名' },
                    { max: 255, message: '公司英文名不能超过255个字符' }
                  ]}
                >
                  <Input
                    placeholder="请输入公司英文名"
                    size={isMobile ? 'large' : 'middle'}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 16]}>
              <Col xs={24} sm={24} md={12} lg={12}>
                <Form.Item
                  name="registeredCapital"
                  label="注册资本（万元）"
                  rules={[
                    { required: true, message: '请输入注册资本' },
                    { pattern: /^\d+(\.\d{1,4})?$/, message: '请输入有效的数字，最多4位小数' }
                  ]}
                >
                  <Input
                    placeholder="请输入注册资本"
                    addonAfter="万元"
                    size={isMobile ? 'large' : 'middle'}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={24} md={12} lg={12}>
                <Form.Item
                  name="establishDate"
                  label="设立日期"
                  rules={[{ required: true, message: '请选择设立日期' }]}
                >
                  <DatePicker
                    style={{ width: '100%' }}
                    placeholder="请选择设立日期"
                    size={isMobile ? 'large' : 'middle'}
                    format="YYYY-MM-DD"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 16]}>
              <Col xs={24} sm={24} md={12} lg={12}>
                <Form.Item
                  name="businessSegment"
                  label="业务板块"
                  rules={[{ required: true, message: '请选择业务板块' }]}
                >
                  <Select
                    placeholder="请选择业务板块"
                    size={isMobile ? 'large' : 'middle'}
                    showSearch
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {businessSegments.map(segment => (
                      <Option key={segment} value={segment}>{segment}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={24} md={12} lg={12}>
                <Form.Item
                  name="region"
                  label="地区"
                  rules={[{ required: true, message: '请选择或输入地区' }]}
                >
                  <Select
                    placeholder="请选择或输入地区"
                    size={isMobile ? 'large' : 'middle'}
                    showSearch
                    allowClear
                    mode="combobox"
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {regions.map(region => (
                      <Option key={region} value={region}>{region}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 16]}>
              <Col xs={24} sm={24} md={12} lg={12}>
                <Form.Item
                  name="agency"
                  label="代理机构"
                  rules={[{ required: true, message: '请选择代理机构' }]}
                >
                  <Select
                    placeholder="请选择代理机构"
                    size={isMobile ? 'large' : 'middle'}
                    showSearch
                    allowClear
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {agencies.map(agency => (
                      <Option key={agency} value={agency}>{agency}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col xs={24} sm={24} md={12} lg={12}>
                <Form.Item
                  name="annualUpdate"
                  label="年审更新"
                  rules={[{ required: true, message: '请选择年审更新' }]}
                >
                  <Select
                    placeholder="请选择年审更新"
                    size={isMobile ? 'large' : 'middle'}
                  >
                    {annualUpdates.map(update => (
                      <Option key={update} value={update}>{update}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={[16, 16]}>
              <Col xs={24} sm={24} md={12} lg={12}>
                <Form.Item
                  name="registeredAddress"
                  label="注册地址"
                  rules={[
                    { required: true, message: '请输入注册地址' },
                    { max: 500, message: '注册地址不能超过500个字符' }
                  ]}
                >
                  <Input.TextArea
                    placeholder="请输入注册地址"
                    rows={isMobile ? 3 : 2}
                    size={isMobile ? 'large' : 'middle'}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} sm={24} md={12} lg={12}>
                <Form.Item
                  name="operationStatus"
                  label="存续状况"
                  rules={[{ required: true, message: '请选择存续状况' }]}
                >
                  <Select
                    placeholder="请选择存续状况"
                    size={isMobile ? 'large' : 'middle'}
                  >
                    {operationStatuses.map(status => (
                      <Option key={status} value={status}>{status}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </div>

          {/* Executive Information Section */}
          <div style={{ marginBottom: 32 }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
              <Title level={5} style={{ margin: 0 }}>
                高管信息
              </Title>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={addExecutive}
                size={isMobile ? 'large' : 'middle'}
              >
                {isMobile ? '添加' : '添加高管'}
              </Button>
            </div>

            <div style={isMobile ? { overflowX: 'auto' } : {}}>
              <Table
                columns={executiveColumns}
                dataSource={executives}
                pagination={false}
                locale={{ emptyText: '暂无高管信息' }}
                size={isMobile ? 'small' : 'middle'}
                scroll={isMobile ? { x: 600 } : undefined}
              />
            </div>
          </div>

          {/* Shareholder Information Section */}
          <div style={{ marginBottom: 32 }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
              <div>
                <Title level={5} style={{ margin: 0 }}>
                  股东信息
                </Title>
                <Text type="secondary" style={{ fontSize: 12 }}>
                  持股比例合计可以不是100%，但不能超过100%
                </Text>
              </div>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={addShareholder}
                size={isMobile ? 'large' : 'middle'}
              >
                {isMobile ? '添加' : '添加股东'}
              </Button>
            </div>

            <div style={isMobile ? { overflowX: 'auto' } : {}}>
              <Table
                columns={shareholderColumns}
                dataSource={shareholders}
                pagination={false}
                locale={{ emptyText: '暂无股东信息' }}
                size={isMobile ? 'small' : 'middle'}
                scroll={isMobile ? { x: 800 } : undefined}
              />
            </div>
          </div>

          {/* Investment Information Section */}
          <div style={{ marginBottom: 32 }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
              <Title level={5} style={{ margin: 0 }}>
                对外投资信息
              </Title>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={addInvestment}
                size={isMobile ? 'large' : 'middle'}
              >
                {isMobile ? '添加' : '添加投资'}
              </Button>
            </div>

            <div style={isMobile ? { overflowX: 'auto' } : {}}>
              <Table
                columns={investmentColumns}
                dataSource={investments}
                pagination={false}
                locale={{ emptyText: '暂无投资信息' }}
                size={isMobile ? 'small' : 'middle'}
                scroll={isMobile ? { x: 700 } : undefined}
              />
            </div>
          </div>

          {/* Form Actions */}
          <div style={{
            paddingTop: 24,
            borderTop: '1px solid #f0f0f0',
            display: isMobile ? 'block' : 'flex',
            justifyContent: isMobile ? 'stretch' : 'center',
            gap: isMobile ? 0 : 16
          }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
              size={isMobile ? 'large' : 'middle'}
              style={isMobile ? { width: '100%', marginBottom: 12 } : {}}
            >
              {loading ? '提交中...' : '确定'}
            </Button>
            <Button
              onClick={handleReset}
              icon={<ReloadOutlined />}
              size={isMobile ? 'large' : 'middle'}
              style={isMobile ? { width: '100%', marginBottom: 12 } : {}}
            >
              重置
            </Button>
            <Button
              onClick={handleBack}
              size={isMobile ? 'large' : 'middle'}
              style={isMobile ? { width: '100%' } : {}}
            >
              取消
            </Button>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default CompanyAdd;
