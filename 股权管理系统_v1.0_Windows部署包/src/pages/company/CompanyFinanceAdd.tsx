import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, Select, Row, Col, message, Table, Space, DatePicker } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import dayjs from 'dayjs';

const { Option } = Select;

interface CompanyOption {
  id: number;
  company_name_cn: string;
  company_name_en: string;
}

interface ShareholderContribution {
  key: string;
  shareholderName: string;
  contributionAmount: number;
  contributionMethod: string;
  contributionTime: string;
}

const CompanyFinanceAdd: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const location = useLocation();
  const [loading, setLoading] = useState(false);
  const [companies, setCompanies] = useState<CompanyOption[]>([]);
  const [contributionMethods, setContributionMethods] = useState<string[]>([]);
  const [shareholderContributions, setShareholderContributions] = useState<ShareholderContribution[]>([
    {
      key: '1',
      shareholderName: '',
      contributionAmount: 0,
      contributionMethod: '',
      contributionTime: ''
    }
  ]);

  // 从路由状态获取编辑模式信息
  const editMode = location.state?.editMode;
  const companyId = location.state?.companyId;
  const year = location.state?.year;

  useEffect(() => {
    fetchCompanies();
    fetchContributionMethods();
    if (editMode && companyId && year) {
      // 编辑模式，设置初始值
      form.setFieldsValue({
        companyId: companyId,
        year: year.toString()
      });
      fetchFinancialData(companyId, year);
    }
  }, [editMode, companyId, year]);

  const fetchCompanies = async () => {
    try {
      const response = await fetch('/api/companies');
      if (response.ok) {
        const result = await response.json();
        if (result.success && Array.isArray(result.data)) {
          setCompanies(result.data);
        } else {
          console.error('获取公司列表失败: 数据格式错误', result);
          setCompanies([]);
        }
      }
    } catch (error) {
      console.error('获取公司列表失败:', error);
      setCompanies([]);
    }
  };

  const fetchContributionMethods = async () => {
    try {
      // 实际项目中从数据库获取
      // const response = await fetch('/api/contribution-methods');
      // if (response.ok) {
      //   const data = await response.json();
      //   setContributionMethods(data);
      // }

      // 模拟数据
      setContributionMethods([
        '货币',
        '实物',
        '知识产权',
        '土地使用权',
        '股权',
        '债权',
        '其他财产权利'
      ]);
    } catch (error) {
      console.error('获取出资方式失败:', error);
    }
  };

  const fetchFinancialData = async (companyId: number, year: number) => {
    try {
      const response = await fetch(`/api/companies/${companyId}/financial/${year}`);
      if (response.ok) {
        const data = await response.json();
        form.setFieldsValue(data);
        if (data.shareholderContributions) {
          setShareholderContributions(data.shareholderContributions);
        }
      }
    } catch (error) {
      console.error('获取财务数据失败:', error);
    }
  };

  const onFinish = async (values: any) => {
    try {
      setLoading(true);
      
      const formData = {
        ...values,
        shareholderContributions: shareholderContributions.filter(item => 
          item.shareholderName.trim() !== ''
        )
      };

      const url = editMode 
        ? `/api/companies/${companyId}/financial/${year}`
        : '/api/companies/financial';
      
      const method = editMode ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        message.success(editMode ? '财务信息更新成功' : '财务信息添加成功');
        if (editMode && companyId) {
          navigate('/company/detail', { state: { companyId } });
        } else {
          navigate('/company/info');
        }
      } else {
        message.error(editMode ? '更新失败' : '添加失败');
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败');
    } finally {
      setLoading(false);
    }
  };

  const onCancel = () => {
    if (editMode && companyId) {
      navigate('/company/detail', { state: { companyId } });
    } else {
      navigate('/company/info');
    }
  };

  const updateShareholderContribution = (key: string, field: string, value: any) => {
    setShareholderContributions(prev => 
      prev.map(item => 
        item.key === key ? { ...item, [field]: value } : item
      )
    );
  };

  const addShareholderContribution = () => {
    const newKey = (shareholderContributions.length + 1).toString();
    setShareholderContributions(prev => [
      ...prev,
      {
        key: newKey,
        shareholderName: '',
        contributionAmount: 0,
        contributionMethod: '',
        contributionTime: ''
      }
    ]);
  };

  const removeShareholderContribution = (key: string) => {
    if (shareholderContributions.length > 1) {
      setShareholderContributions(prev => prev.filter(item => item.key !== key));
    }
  };

  const shareholderColumns = [
    {
      title: '股东名称',
      dataIndex: 'shareholderName',
      key: 'shareholderName',
      render: (text: string, record: ShareholderContribution) => (
        <Input
          value={text}
          placeholder="请输入股东名称"
          onChange={(e) => updateShareholderContribution(record.key, 'shareholderName', e.target.value)}
        />
      ),
    },
    {
      title: '实缴出资额（万元人民币）',
      dataIndex: 'contributionAmount',
      key: 'contributionAmount',
      render: (value: number, record: ShareholderContribution) => (
        <Input
          type="number"
          value={value}
          placeholder="请输入实缴出资额"
          onChange={(e) => updateShareholderContribution(record.key, 'contributionAmount', parseFloat(e.target.value) || 0)}
        />
      ),
    },
    {
      title: '实缴出资方式',
      dataIndex: 'contributionMethod',
      key: 'contributionMethod',
      render: (text: string, record: ShareholderContribution) => (
        <Select
          value={text}
          placeholder="请选择出资方式"
          style={{ width: '100%' }}
          onChange={(value) => updateShareholderContribution(record.key, 'contributionMethod', value)}
        >
          {contributionMethods.map(method => (
            <Option key={method} value={method}>{method}</Option>
          ))}
        </Select>
      ),
    },
    {
      title: '实缴出资时间',
      dataIndex: 'contributionTime',
      key: 'contributionTime',
      render: (text: string, record: ShareholderContribution) => (
        <DatePicker
          value={text ? dayjs(text) : null}
          placeholder="请选择日期"
          style={{ width: '100%' }}
          format="YYYY-MM-DD"
          onChange={(date) => updateShareholderContribution(record.key, 'contributionTime', date ? date.format('YYYY-MM-DD') : '')}
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: any, record: ShareholderContribution) => (
        <Button
          type="link"
          danger
          onClick={() => removeShareholderContribution(record.key)}
          disabled={shareholderContributions.length === 1}
        >
          删除
        </Button>
      ),
    },
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 10 }, (_, i) => currentYear - i);

  return (
    <div style={{ padding: '24px' }}>
      <Card title={editMode ? "编辑财务信息" : "添加财务信息"}>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            year: currentYear.toString()
          }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="companyId"
                label="公司名称"
                rules={[{ required: true, message: '请选择公司' }]}
              >
                <Select 
                  placeholder="请选择公司"
                  disabled={editMode}
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {companies.map(company => (
                    <Option key={company.id} value={company.id}>
                      {company.company_name_cn}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={8}>
              <Form.Item
                name="year"
                label="年度"
                rules={[{ required: true, message: '请选择年度' }]}
              >
                <Select 
                  placeholder="请选择年度"
                  disabled={editMode}
                >
                  {years.map(year => (
                    <Option key={year} value={year.toString()}>{year}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Card title="资产状况信息（万元人民币）" style={{ marginBottom: 24 }}>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="totalAssets"
                  label="资产总额"
                  rules={[{ required: true, message: '请输入资产总额' }]}
                >
                  <Input type="number" placeholder="请输入资产总额" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="totalLiabilities"
                  label="负债总额"
                  rules={[{ required: true, message: '请输入负债总额' }]}
                >
                  <Input type="number" placeholder="请输入负债总额" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="totalEquity"
                  label="所有者权益合计"
                  rules={[{ required: true, message: '请输入所有者权益合计' }]}
                >
                  <Input type="number" placeholder="请输入所有者权益合计" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="businessIncome"
                  label="营业总收入"
                  rules={[{ required: true, message: '请输入营业总收入' }]}
                >
                  <Input type="number" placeholder="请输入营业总收入" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="mainBusinessIncome"
                  label="主营业务收入"
                  rules={[{ required: true, message: '请输入主营业务收入' }]}
                >
                  <Input type="number" placeholder="请输入主营业务收入" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="profitBeforeTax"
                  label="利润总额"
                  rules={[{ required: true, message: '请输入利润总额' }]}
                >
                  <Input type="number" placeholder="请输入利润总额" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="netProfit"
                  label="净利润"
                  rules={[{ required: true, message: '请输入净利润' }]}
                >
                  <Input type="number" placeholder="请输入净利润" />
                </Form.Item>
              </Col>
              <Col xs={24} sm={12} md={8}>
                <Form.Item
                  name="taxPayable"
                  label="纳税总额"
                  rules={[{ required: true, message: '请输入纳税总额' }]}
                >
                  <Input type="number" placeholder="请输入纳税总额" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={[16, 16]}>
              <Col xs={24}>
                <Form.Item
                  name="remarks"
                  label="备注"
                >
                  <Input.TextArea 
                    rows={3} 
                    placeholder="请输入备注信息" 
                    maxLength={500}
                    showCount
                  />
                </Form.Item>
              </Col>
            </Row>
          </Card>

          <Card
            title="股东及出资信息"
            extra={
              <Button type="primary" onClick={addShareholderContribution}>
                新增股东
              </Button>
            }
            style={{ marginBottom: 24 }}
          >
            <Table
              columns={shareholderColumns}
              dataSource={shareholderContributions}
              pagination={false}
              rowKey="key"
              size="small"
            />
          </Card>

          <Row justify="center">
            <Space size="large">
              <Button onClick={onCancel}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editMode ? '更新' : '保存'}
              </Button>
            </Space>
          </Row>
        </Form>
      </Card>
    </div>
  );
};

export default CompanyFinanceAdd;
