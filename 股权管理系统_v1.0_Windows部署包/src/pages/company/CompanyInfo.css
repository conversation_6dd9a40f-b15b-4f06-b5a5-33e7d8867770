.company-info-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 查询条件卡片 */
.search-card {
  margin-bottom: 16px;
}

.search-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.search-label {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

/* 操作按钮卡片 */
.action-card {
  margin-bottom: 16px;
}

/* 公司列表 */
.company-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

/* 公司卡片样式 */
.company-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.company-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.company-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.company-selection {
  flex-shrink: 0;
}

.company-title {
  flex: 1;
  margin: 0 16px;
}

.company-title h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.change-count {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-shrink: 0;
}

.change-badge {
  background-color: #ff4d4f;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
}

.change-text {
  font-size: 12px;
  color: #8c8c8c;
}

/* 公司卡片内容 */
.company-card-content {
  margin-bottom: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.info-item .label {
  font-size: 12px;
  color: #8c8c8c;
  min-width: 80px;
}

.info-item .value {
  font-size: 12px;
  color: #262626;
  flex: 1;
}

/* 人员信息 */
.personnel-info {
  margin: 12px 0;
  padding: 12px;
  background-color: #fafafa;
  border-radius: 6px;
}

.personnel-info .label {
  font-size: 12px;
  color: #8c8c8c;
  margin-right: 8px;
}

.person-link {
  font-size: 12px;
  color: #1890ff;
  text-decoration: none;
}

.person-link:hover {
  text-decoration: underline;
}

/* 业务标签 */
.business-tags {
  margin: 12px 0;
}

.business-tags .ant-tag {
  margin-bottom: 4px;
}

/* 公司卡片操作按钮 */
.company-card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

/* 分页容器 */
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 24px 0;
}

/* 加载状态 */
.loading-container {
  text-align: center;
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .company-info-container {
    padding: 16px;
  }
  
  .search-item {
    margin-bottom: 16px;
  }
  
  .company-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .company-title {
    margin: 0;
  }
  
  .change-count {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .company-info-container {
    padding: 12px;
  }
  
  .company-card-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .company-card-actions .ant-btn {
    width: 100%;
  }
  
  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .info-item .label {
    min-width: auto;
  }
  
  .personnel-info .ant-col {
    margin-bottom: 8px;
  }
}

/* 卡片内容区域的网格布局优化 */
.company-card .ant-card-body {
  padding: 20px;
}

/* 确保按钮在小屏幕上的可用性 */
@media (max-width: 576px) {
  .action-card .ant-row {
    flex-direction: column;
    gap: 16px;
  }
  
  .action-card .ant-space {
    width: 100%;
    justify-content: center;
  }
  
  .company-card-header {
    padding-bottom: 8px;
  }
  
  .company-title h3 {
    font-size: 14px;
  }
  
  .change-badge {
    width: 20px;
    height: 20px;
    font-size: 10px;
  }
}
