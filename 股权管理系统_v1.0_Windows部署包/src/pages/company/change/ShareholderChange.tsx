import React, { useState } from 'react';
import { Card, Form, Input, Button, Select, DatePicker, Row, Col, Space, Table, InputNumber, Tabs } from 'antd';
import { SaveOutlined, RollbackOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import type { TabsProps } from 'antd';

const { Option } = Select;
const { TabPane } = Tabs;

interface ShareholderRecord {
  id: string;
  name: string;
  idType: string;
  idNumber: string;
  stockAmount: number;
  percentage: string;
  investmentDate: string;
  status: string;
}

interface InvestmentRecord {
  id: string;
  companyName: string;
  investmentAmount: number;
  percentage: string;
  investmentDate: string;
  status: string;
}

const ShareholderChange: React.FC = () => {
  const [form] = Form.useForm();
  const [investmentForm] = Form.useForm();
  const navigate = useNavigate();
  const [shareholders, setShareholders] = useState<ShareholderRecord[]>([
    {
      id: '1',
      name: '张三',
      idType: '身份证',
      idNumber: '110101199001011234',
      stockAmount: 500,
      percentage: '50%',
      investmentDate: '2020-01-15',
      status: '有效',
    },
    {
      id: '2',
      name: '李四',
      idType: '身份证',
      idNumber: '110101199002022345',
      stockAmount: 300,
      percentage: '30%',
      investmentDate: '2020-01-15',
      status: '有效',
    },
    {
      id: '3',
      name: '王五',
      idType: '身份证',
      idNumber: '110101199003033456',
      stockAmount: 200,
      percentage: '20%',
      investmentDate: '2020-01-15',
      status: '有效',
    },
  ]);

  const [investments, setInvestments] = useState<InvestmentRecord[]>([
    {
      id: '1',
      companyName: '北京科技有限公司',
      investmentAmount: 1000,
      percentage: '60%',
      investmentDate: '2021-03-10',
      status: '有效',
    },
    {
      id: '2',
      companyName: '上海贸易有限公司',
      investmentAmount: 500,
      percentage: '40%',
      investmentDate: '2022-05-20',
      status: '有效',
    },
  ]);

  const onFinish = (values: any) => {
    console.log('Form values:', values);
    // 这里添加提交表单的逻辑
  };

  const onReset = () => {
    form.resetFields();
  };

  const shareholderColumns: ColumnsType<ShareholderRecord> = [
    {
      title: '股东名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '证件类型',
      dataIndex: 'idType',
      key: 'idType',
      align: 'center',
    },
    {
      title: '证件号码',
      dataIndex: 'idNumber',
      key: 'idNumber',
      align: 'center',
    },
    {
      title: '出资额(万元)',
      dataIndex: 'stockAmount',
      key: 'stockAmount',
      align: 'center',
    },
    {
      title: '持股比例',
      dataIndex: 'percentage',
      key: 'percentage',
      align: 'center',
    },
    {
      title: '出资日期',
      dataIndex: 'investmentDate',
      key: 'investmentDate',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (text) => {
        let color = text === '有效' ? 'green' : 'red';
        return <span style={{ color }}>{text}</span>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleEditShareholder(record)}>编辑</Button>
          <Button type="link" danger onClick={() => handleDeleteShareholder(record.id)}>删除</Button>
        </Space>
      ),
    },
  ];

  const investmentColumns: ColumnsType<InvestmentRecord> = [
    {
      title: '被投资公司名称',
      dataIndex: 'companyName',
      key: 'companyName',
      align: 'center',
    },
    {
      title: '投资金额(万元)',
      dataIndex: 'investmentAmount',
      key: 'investmentAmount',
      align: 'center',
    },
    {
      title: '持股比例',
      dataIndex: 'percentage',
      key: 'percentage',
      align: 'center',
    },
    {
      title: '投资日期',
      dataIndex: 'investmentDate',
      key: 'investmentDate',
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (text) => {
        let color = text === '有效' ? 'green' : 'red';
        return <span style={{ color }}>{text}</span>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleEditInvestment(record)}>编辑</Button>
          <Button type="link" danger onClick={() => handleDeleteInvestment(record.id)}>删除</Button>
        </Space>
      ),
    },
  ];

  const handleEditShareholder = (record: ShareholderRecord) => {
    form.setFieldsValue({
      name: record.name,
      idType: record.idType,
      idNumber: record.idNumber,
      stockAmount: record.stockAmount,
      percentage: record.percentage.replace('%', ''),
      investmentDate: record.investmentDate,
      status: record.status,
    });
  };

  const handleDeleteShareholder = (id: string) => {
    setShareholders(shareholders.filter(item => item.id !== id));
  };

  const handleAddShareholder = () => {
    const values = form.getFieldsValue();
    const newShareholder: ShareholderRecord = {
      id: Date.now().toString(),
      name: values.name,
      idType: values.idType,
      idNumber: values.idNumber,
      stockAmount: values.stockAmount,
      percentage: values.percentage + '%',
      investmentDate: values.investmentDate,
      status: values.status,
    };
    setShareholders([...shareholders, newShareholder]);
    form.resetFields();
  };

  const handleEditInvestment = (record: InvestmentRecord) => {
    investmentForm.setFieldsValue({
      companyName: record.companyName,
      investmentAmount: record.investmentAmount,
      percentage: record.percentage.replace('%', ''),
      investmentDate: record.investmentDate,
      status: record.status,
    });
  };

  const handleDeleteInvestment = (id: string) => {
    setInvestments(investments.filter(item => item.id !== id));
  };

  const handleAddInvestment = () => {
    const values = investmentForm.getFieldsValue();
    const newInvestment: InvestmentRecord = {
      id: Date.now().toString(),
      companyName: values.companyName,
      investmentAmount: values.investmentAmount,
      percentage: values.percentage + '%',
      investmentDate: values.investmentDate,
      status: values.status,
    };
    setInvestments([...investments, newInvestment]);
    investmentForm.resetFields();
  };

  const items: TabsProps['items'] = [
    {
      key: 'shareholder',
      label: '股东信息变更',
      children: (
        <>
          <Card title="添加/编辑股东信息" style={{ marginBottom: 20 }}>
            <Form
              form={form}
              layout="vertical"
              onFinish={onFinish}
            >
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item
                    name="name"
                    label="股东名称"
                    rules={[{ required: true, message: '请输入股东名称' }]}
                  >
                    <Input />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="idType"
                    label="证件类型"
                    rules={[{ required: true, message: '请选择证件类型' }]}
                  >
                    <Select>
                      <Option value="身份证">身份证</Option>
                      <Option value="护照">护照</Option>
                      <Option value="营业执照">营业执照</Option>
                      <Option value="其他">其他</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="idNumber"
                    label="证件号码"
                    rules={[{ required: true, message: '请输入证件号码' }]}
                  >
                    <Input />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item
                    name="stockAmount"
                    label="出资额(万元)"
                    rules={[{ required: true, message: '请输入出资额' }]}
                  >
                    <InputNumber style={{ width: '100%' }} min={0} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="percentage"
                    label="持股比例(%)"
                    rules={[{ required: true, message: '请输入持股比例' }]}
                  >
                    <InputNumber style={{ width: '100%' }} min={0} max={100} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="investmentDate"
                    label="出资日期"
                    rules={[{ required: true, message: '请选择出资日期' }]}
                  >
                    <DatePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item
                    name="status"
                    label="状态"
                    rules={[{ required: true, message: '请选择状态' }]}
                  >
                    <Select>
                      <Option value="有效">有效</Option>
                      <Option value="无效">无效</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item>
                <Space>
                  <Button type="primary" onClick={handleAddShareholder} icon={<PlusOutlined />}>
                    添加
                  </Button>
                  <Button htmlType="button" onClick={onReset} icon={<RollbackOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>

          <Card title="股东信息列表">
            <Table columns={shareholderColumns} dataSource={shareholders} rowKey="id" />
          </Card>
        </>
      ),
    },
    {
      key: 'investment',
      label: '对外投资信息变更',
      children: (
        <>
          <Card title="添加/编辑对外投资信息" style={{ marginBottom: 20 }}>
            <Form
              form={investmentForm}
              layout="vertical"
              onFinish={onFinish}
            >
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item
                    name="companyName"
                    label="被投资公司名称"
                    rules={[{ required: true, message: '请输入被投资公司名称' }]}
                  >
                    <Input />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="investmentAmount"
                    label="投资金额(万元)"
                    rules={[{ required: true, message: '请输入投资金额' }]}
                  >
                    <InputNumber style={{ width: '100%' }} min={0} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="percentage"
                    label="持股比例(%)"
                    rules={[{ required: true, message: '请输入持股比例' }]}
                  >
                    <InputNumber style={{ width: '100%' }} min={0} max={100} />
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col span={8}>
                  <Form.Item
                    name="investmentDate"
                    label="投资日期"
                    rules={[{ required: true, message: '请选择投资日期' }]}
                  >
                    <DatePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="status"
                    label="状态"
                    rules={[{ required: true, message: '请选择状态' }]}
                  >
                    <Select>
                      <Option value="有效">有效</Option>
                      <Option value="无效">无效</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Form.Item>
                <Space>
                  <Button type="primary" onClick={handleAddInvestment} icon={<PlusOutlined />}>
                    添加
                  </Button>
                  <Button htmlType="button" onClick={() => investmentForm.resetFields()} icon={<RollbackOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>

          <Card title="对外投资信息列表">
            <Table columns={investmentColumns} dataSource={investments} rowKey="id" />
          </Card>
        </>
      ),
    },
  ];

  return (
    <div>
      <Tabs defaultActiveKey="shareholder" items={items} />
      <div style={{ marginTop: 20, textAlign: 'center' }}>
        <Button onClick={() => navigate('/company/info')}>返回公司信息</Button>
      </div>
    </div>
  );
};

export default ShareholderChange;