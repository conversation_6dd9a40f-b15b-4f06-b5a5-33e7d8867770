import React from 'react';
import { Card, Form, Input, Button, Select, DatePicker, Row, Col, Space, Table } from 'antd';
import { SaveOutlined, RollbackOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';

const { Option } = Select;

interface ChangeRecord {
  id: string;
  field: string;
  oldValue: string;
  newValue: string;
  changeDate: string;
  changeReason: string;
  status: string;
}

const BasicChange: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const onFinish = (values: any) => {
    console.log('Form values:', values);
    // 这里添加提交表单的逻辑
    // 提交成功后跳转到公司信息页面
    navigate('/company/info');
  };

  const onReset = () => {
    form.resetFields();
  };

  const columns: ColumnsType<ChangeRecord> = [
    {
      title: '变更字段',
      dataIndex: 'field',
      key: 'field',
    },
    {
      title: '原值',
      dataIndex: 'oldValue',
      key: 'oldValue',
    },
    {
      title: '新值',
      dataIndex: 'newValue',
      key: 'newValue',
    },
    {
      title: '变更日期',
      dataIndex: 'changeDate',
      key: 'changeDate',
    },
    {
      title: '变更原因',
      dataIndex: 'changeReason',
      key: 'changeReason',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => {
        let color = text === '已审核' ? 'green' : text === '待审核' ? 'orange' : 'red';
        return <span style={{ color }}>{text}</span>;
      },
    },
  ];

  const data: ChangeRecord[] = [
    {
      id: '1',
      field: '公司名称',
      oldValue: '深圳科技有限公司',
      newValue: '深圳科技股份有限公司',
      changeDate: '2023-05-15',
      changeReason: '公司类型变更',
      status: '已审核',
    },
    {
      id: '2',
      field: '注册资本',
      oldValue: '1000万元',
      newValue: '2000万元',
      changeDate: '2023-06-20',
      changeReason: '增资扩股',
      status: '待审核',
    },
  ];

  return (
    <div>
      <Card title="公司基础信息变更" style={{ marginBottom: 20 }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{
            companyName: '深圳科技有限公司',
            creditCode: '91440300MA5EYKUW1X',
            changeType: 'name',
          }}
        >
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                name="companyName"
                label="公司名称"
                rules={[{ required: true, message: '请输入公司名称' }]}
              >
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="creditCode"
                label="统一社会信用代码"
                rules={[{ required: true, message: '请输入统一社会信用代码' }]}
              >
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="changeType"
                label="变更类型"
                rules={[{ required: true, message: '请选择变更类型' }]}
              >
                <Select>
                  <Option value="name">名称变更</Option>
                  <Option value="address">地址变更</Option>
                  <Option value="capital">注册资本变更</Option>
                  <Option value="business">经营范围变更</Option>
                  <Option value="type">公司类型变更</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                name="oldValue"
                label="原值"
                rules={[{ required: true, message: '请输入原值' }]}
              >
                <Input disabled />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="newValue"
                label="新值"
                rules={[{ required: true, message: '请输入新值' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="changeDate"
                label="变更日期"
                rules={[{ required: true, message: '请选择变更日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                name="changeReason"
                label="变更原因"
                rules={[{ required: true, message: '请输入变更原因' }]}
              >
                <Input.TextArea rows={4} />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SaveOutlined />}>
                提交
              </Button>
              <Button htmlType="button" onClick={onReset} icon={<RollbackOutlined />}>
                重置
              </Button>
              <Button htmlType="button" onClick={() => navigate('/company/info')}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Card title="变更记录">
        <Table columns={columns} dataSource={data} rowKey="id" />
      </Card>
    </div>
  );
};

export default BasicChange;