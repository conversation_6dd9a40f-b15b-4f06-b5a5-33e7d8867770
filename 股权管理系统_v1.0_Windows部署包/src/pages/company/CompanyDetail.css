.company-detail {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页内导航菜单样式 */
.page-navigation {
  position: fixed;
  top: 80px;
  left: 240px; /* 调整位置，避免被左侧主菜单遮挡 */
  z-index: 1000; /* 提高z-index确保显示在最上层 */
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 8px 0;
  min-width: 140px;
}

.navigation-menu {
  display: flex;
  flex-direction: column;
}

.navigation-item {
  padding: 8px 16px;
  cursor: pointer;
  font-size: 14px;
  color: #595959;
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
}

.navigation-item:hover {
  background-color: #f5f5f5;
  color: #1890ff;
}

.navigation-item.active {
  background-color: #e6f7ff;
  color: #1890ff;
  border-left-color: #1890ff;
  font-weight: 500;
}

/* 固定返回按钮样式 */
.fixed-back-button {
  position: fixed;
  top: 80px;
  right: 24px;
  z-index: 1000;
}

.back-button {
  background-color: #1890ff;
  border-color: #1890ff;
  color: white;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
}

.back-button:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
  color: white;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* 内容区域样式 */
.company-detail-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-left: 200px; /* 为左侧导航菜单留出空间 */
  margin-top: 40px; /* 为固定的返回按钮和导航菜单留出空间 */
}

.info-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.info-card .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.info-card .ant-card-head-title {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.info-card .ant-card-body {
  padding: 24px;
}

/* 基础信息样式 */
.basic-info-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-row {
  display: flex;
  gap: 32px;
}

.info-item {
  display: flex;
  flex: 1;
  align-items: center;
  gap: 16px;
}

.info-item.full-width {
  flex: 2;
}

.info-item .label {
  min-width: 120px;
  font-weight: 500;
  color: #595959;
  text-align: right;
}

.info-item .value {
  flex: 1;
  color: #262626;
  padding: 8px 12px;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  min-height: 32px;
  display: flex;
  align-items: center;
}

/* 财务信息样式 */
.financial-info {
  padding: 16px 0;
}

.financial-info h4 {
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.financial-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.financial-row {
  display: flex;
  gap: 32px;
}

.financial-item {
  display: flex;
  flex: 1;
  align-items: center;
  gap: 16px;
}

.financial-item .label {
  min-width: 100px;
  font-weight: 500;
  color: #595959;
  text-align: right;
}

.financial-item .value {
  flex: 1;
  color: #262626;
  padding: 6px 12px;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  min-height: 28px;
  display: flex;
  align-items: center;
}

.notes {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-top: 16px;
}

.notes .label {
  min-width: 100px;
  font-weight: 500;
  color: #595959;
  text-align: right;
  margin-top: 6px;
}

.notes .value {
  flex: 1;
  color: #262626;
  padding: 6px 12px;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  min-height: 60px;
  display: flex;
  align-items: flex-start;
  padding-top: 8px;
}

/* 表格样式 */
.info-card .ant-table {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.info-card .ant-table-thead > tr > th {
  background-color: #fafafa;
  border-bottom: 1px solid #f0f0f0;
  font-weight: 600;
  color: #262626;
  text-align: center;
}

.info-card .ant-table-tbody > tr > td {
  border-bottom: 1px solid #f0f0f0;
  text-align: center;
}

.info-card .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* Tabs样式 */
.info-card .ant-tabs-tab {
  font-weight: 500;
}

.info-card .ant-tabs-tab-active {
  font-weight: 600;
}

.info-card .ant-tabs-content-holder {
  padding-top: 16px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .fixed-back-button {
    top: 20px;
    right: 20px;
  }

  .page-navigation {
    position: fixed;
    top: 60px;
    left: 20px;
    right: 20px;
    width: auto;
    margin-bottom: 0;
  }

  .navigation-menu {
    flex-direction: row;
    flex-wrap: wrap;
    gap: 8px;
    padding: 8px;
  }

  .navigation-item {
    border-left: none;
    border-bottom: 2px solid transparent;
    border-radius: 4px;
    padding: 6px 12px;
    font-size: 12px;
  }

  .navigation-item.active {
    border-left: none;
    border-bottom-color: #1890ff;
  }

  .company-detail-content {
    margin-left: 0;
    margin-top: 120px; /* 为固定的返回按钮和导航菜单留出空间 */
  }

  .info-row {
    flex-direction: column;
    gap: 16px;
  }

  .financial-row {
    flex-direction: column;
    gap: 12px;
  }

  .info-item .label,
  .financial-item .label {
    min-width: 80px;
    text-align: left;
  }
}

@media (max-width: 768px) {
  .company-detail {
    padding: 16px;
  }

  .fixed-back-button {
    top: 10px;
    right: 10px;
  }

  .back-button {
    padding: 4px 12px;
    font-size: 12px;
    height: auto;
  }

  .page-navigation {
    top: 50px;
    left: 10px;
    right: 10px;
  }

  .navigation-item {
    font-size: 11px;
    padding: 4px 8px;
  }

  .company-detail-content {
    margin-top: 120px; /* 为移动端返回按钮和导航菜单留出更多空间 */
  }

  .info-card .ant-card-body {
    padding: 16px;
  }

  .info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .financial-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .notes {
    flex-direction: column;
    gap: 8px;
  }

  .info-item .label,
  .financial-item .label,
  .notes .label {
    min-width: auto;
    text-align: left;
  }
}
