import React, { useState } from 'react';
import {
  Form,
  Input,
  Button,
  Select,
  DatePicker,
  message,
  Row,
  Col,
  Card,
  Typography,
  Modal,
  Alert,
  Table,
  Divider
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  ArrowLeftOutlined,
  CheckCircleOutlined,
  PlusOutlined,
  DeleteOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { apiService } from '../../services/api-simple';
import axios from 'axios';

const { Title, Text } = Typography;
const { Option } = Select;

// TypeScript Interfaces
interface CompanyFormData {
  companyNameCn: string;
  companyNameEn: string;
  registeredCapital: string;
  establishDate: string;
  businessSegment: string;
  region: string;
  agency: string;
  annualUpdate: string;
  registeredAddress: string;
  operationStatus: string;
}

interface ExecutiveData {
  key: number;
  position: string;
  person: string;
}

interface ShareholderData {
  key: number;
  name: string;
  investmentAmount: string;
  percentage: string;
  isProxy: string;
  actualShareholder?: string;
  startDate: string;
}

interface InvestmentData {
  key: number;
  companyName: string;
  investmentAmount: string;
  percentage: string;
  startDate: string;
}

const CompanyAddWorking: React.FC = () => {
  // Hooks
  const [form] = Form.useForm();
  const navigate = useNavigate();
  
  // State Management
  const [loading, setLoading] = useState(false);
  const [isSuccessModalVisible, setIsSuccessModalVisible] = useState(false);

  // Dynamic Data State
  const [executives, setExecutives] = useState<ExecutiveData[]>([]);
  const [shareholders, setShareholders] = useState<ShareholderData[]>([]);
  const [investments, setInvestments] = useState<InvestmentData[]>([]);

  // Personnel Data from Database
  const [personnelList, setPersonnelList] = useState<string[]>([]);

  // Basic Data from Database
  const [businessSegments, setBusinessSegments] = useState<string[]>([]);
  const [regions, setRegions] = useState<string[]>([]);
  const [agencies, setAgencies] = useState<string[]>([]);
  const [positions, setPositions] = useState<string[]>([]);
  const [optionsLoading, setOptionsLoading] = useState(true);
  const [optionsError, setOptionsError] = useState<string | null>(null);

  const annualUpdates = ['不管年审', '管年审（固定周期）', '管年审（滚动周期）'];
  const operationStatuses = ['正常经营', '已注销', '异常'];

  // 加载所有选项数据
  const loadOptionsData = async () => {
    try {
      setOptionsLoading(true);
      setOptionsError(null);

      // 并行加载所有选项数据
      const [businessResponse, regionsResponse, agenciesResponse, positionsResponse] = await Promise.all([
        apiService.getBusinessSegments(),
        axios.get('http://localhost:8080/api/regions'),
        axios.get('http://localhost:8080/api/agencies'),
        axios.get('http://localhost:8080/api/positions')
      ]);

      // 处理业务板块数据
      if (businessResponse.data.success) {
        const segments = businessResponse.data.data.map((item: any) => item.name);
        setBusinessSegments(segments);
        console.log('✅ 业务板块数据加载成功:', segments);
      } else {
        throw new Error('业务板块数据API返回失败');
      }

      // 处理地区数据
      if (regionsResponse.data.success) {
        const regionNames = regionsResponse.data.data.map((item: any) => item.region);
        setRegions(regionNames);
        console.log('✅ 地区数据加载成功:', regionNames);
      } else {
        throw new Error('地区数据API返回失败');
      }

      // 处理代理机构数据
      if (agenciesResponse.data.success) {
        const agencyNames = agenciesResponse.data.data.map((item: any) => item.agencyName || item.agency_name || item.name);
        setAgencies(agencyNames);
        console.log('✅ 代理机构数据加载成功:', agencyNames);
      } else {
        throw new Error('代理机构数据API返回失败');
      }

      // 处理职位数据
      if (positionsResponse.data.success) {
        const positionNames = positionsResponse.data.data.map((item: any) => item.name);
        setPositions(positionNames);
        console.log('✅ 职位数据加载成功:', positionNames);
      } else {
        throw new Error('职位数据API返回失败');
      }

    } catch (error) {
      console.error('❌ 加载选项数据失败:', error);
      const errorMessage = `加载选项数据失败: ${error.message}`;
      setOptionsError(errorMessage);
      message.error(errorMessage);
    } finally {
      setOptionsLoading(false);
    }
  };

  // 加载人员数据
  const loadPersonnelData = async () => {
    try {
      console.log('🔄 加载人员数据...');

      // 使用新的API端点获取所有人员数据（不限制是否有任职记录）
      const response = await axios.get('http://localhost:8080/api/persons/all');

      if (response.data.success) {
        const personnelNames = response.data.data.map((item: any) => item.name);
        setPersonnelList(personnelNames.filter(name => name)); // 过滤掉空值
        console.log('✅ 人员数据加载成功:', personnelNames);
      } else {
        throw new Error('人员数据API返回失败');
      }
    } catch (error) {
      console.error('❌ 加载人员数据失败:', error);
      message.warning('人员数据加载失败，任职人员下拉列表将为空');
      setPersonnelList([]);
    }
  };

  // 组件初始化
  React.useEffect(() => {
    loadOptionsData();
    loadPersonnelData();
  }, []);

  // Form Submission Handler
  const onFinish = async (values: CompanyFormData) => {
    try {
      setLoading(true);
      console.log('📝 开始提交表单数据...');
      console.log('📝 基本信息:', values);
      console.log('📝 高管信息:', executives);
      console.log('📝 股东信息:', shareholders);
      console.log('📝 投资信息:', investments);

      // 验证必填字段
      if (!values.companyNameCn || !values.companyNameEn) {
        message.error('请填写公司中英文名称');
        return;
      }

      // 准备提交的数据
      const submitData = {
        // 基本信息
        companyNameCn: values.companyNameCn,
        companyNameEn: values.companyNameEn,
        registeredCapital: values.registeredCapital,
        establishDate: values.establishDate,
        businessSegment: values.businessSegment,
        region: values.region,
        agency: values.agency,
        annualUpdate: values.annualUpdate,
        registeredAddress: values.registeredAddress,
        operationStatus: values.operationStatus,

        // 动态数据
        executives: executives,
        shareholders: shareholders,
        investments: investments
      };

      console.log('📤 提交到后端的数据:', submitData);

      // 调用后端API保存数据
      const response = await axios.post('http://localhost:8080/api/company/add', submitData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      console.log('✅ 后端响应:', response.data);

      if (response.data.success) {
        message.success('公司信息已成功保存到数据库！');
        setIsSuccessModalVisible(true);
      } else {
        throw new Error(response.data.message || '保存失败');
      }

    } catch (error: any) {
      console.error('❌ 保存公司信息失败:', error);

      if (error.response) {
        // 服务器返回错误响应
        const errorMsg = error.response.data?.message || '服务器返回错误';
        message.error(`保存失败: ${errorMsg}`);
        console.error('服务器错误详情:', error.response.data);
      } else if (error.request) {
        // 网络错误
        message.error('网络连接失败，请检查网络连接');
        console.error('网络错误:', error.request);
      } else {
        // 其他错误
        message.error('保存失败，请稍后重试');
        console.error('其他错误:', error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  // Event Handlers
  const handleSuccessModalOk = () => {
    setIsSuccessModalVisible(false);
    handleReset();
  };

  const handleSuccessModalCancel = () => {
    setIsSuccessModalVisible(false);
    navigate('/');
  };

  const handleReset = () => {
    form.resetFields();
    setExecutives([]);
    setShareholders([]);
    setInvestments([]);
    message.info('表单已重置');
  };

  const handleBack = () => {
    console.log('🔙 Navigating back to company info page...');
    navigate('/company');
  };

  // 高管信息处理函数
  const addExecutive = () => {
    setExecutives([...executives, { key: Date.now(), position: '', person: '' }]);
  };

  const removeExecutive = (key: number) => {
    setExecutives(executives.filter(item => item.key !== key));
  };

  const updateExecutive = (key: number, field: string, value: string) => {
    setExecutives(executives.map(item =>
      item.key === key ? { ...item, [field]: value } : item
    ));
  };

  // 股东信息处理函数
  const addShareholder = () => {
    setShareholders([...shareholders, {
      key: Date.now(),
      name: '',
      investmentAmount: '',
      percentage: '',
      isProxy: '',
      startDate: ''
    }]);
  };

  const removeShareholder = (key: number) => {
    setShareholders(shareholders.filter(item => item.key !== key));
  };

  const updateShareholder = (key: number, field: string, value: any) => {
    setShareholders(shareholders.map(item =>
      item.key === key ? { ...item, [field]: value } : item
    ));
  };

  // 对外投资信息处理函数
  const addInvestment = () => {
    setInvestments([...investments, {
      key: Date.now(),
      companyName: '',
      investmentAmount: '',
      percentage: '',
      startDate: ''
    }]);
  };

  const removeInvestment = (key: number) => {
    setInvestments(investments.filter(item => item.key !== key));
  };

  const updateInvestment = (key: number, field: string, value: any) => {
    setInvestments(investments.map(item =>
      item.key === key ? { ...item, [field]: value } : item
    ));
  };

  // 高管信息表格列
  const executiveColumns = [
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
      render: (text: string, record: any) => (
        <Select
          placeholder="请选择职位"
          value={text}
          onChange={(value) => updateExecutive(record.key, 'position', value)}
          style={{ width: '100%' }}
          allowClear
        >
          {positions.map(position => (
            <Select.Option key={position} value={position}>{position}</Select.Option>
          ))}
        </Select>
      )
    },
    {
      title: '任职人员',
      dataIndex: 'person',
      key: 'person',
      render: (text: string, record: any) => (
        <Select
          style={{ width: '100%' }}
          value={text || undefined}
          placeholder="请选择任职人员"
          showSearch
          filterOption={(input, option) =>
            (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
          }
          onChange={(value) => updateExecutive(record.key, 'person', value)}
        >
          {personnelList.map(person => (
            <Option key={person} value={person}>{person}</Option>
          ))}
        </Select>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: any, record: any) => (
        <Button
          type="link"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeExecutive(record.key)}
        />
      )
    }
  ];

  // 股东信息表格列
  const shareholderColumns = [
    {
      title: '登记股东',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: any) => (
        <Input
          placeholder="请输入股东名称"
          value={text}
          onChange={(e) => updateShareholder(record.key, 'name', e.target.value)}
        />
      )
    },
    {
      title: '认缴出资额（万元人民币）',
      dataIndex: 'investmentAmount',
      key: 'investmentAmount',
      render: (text: string, record: any) => (
        <Input
          placeholder="请输入"
          value={text}
          onChange={(e) => updateShareholder(record.key, 'investmentAmount', e.target.value)}
        />
      )
    },
    {
      title: '持股比例',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (text: string, record: any) => (
        <Input
          placeholder="请输入"
          value={text}
          addonAfter="%"
          onChange={(e) => updateShareholder(record.key, 'percentage', e.target.value)}
        />
      )
    },
    {
      title: '代持',
      dataIndex: 'isProxy',
      key: 'isProxy',
      render: (text: string, record: any) => (
        <Select
          style={{ width: '100%' }}
          value={text || undefined}
          placeholder="请选择"
          onChange={(value) => updateShareholder(record.key, 'isProxy', value)}
        >
          <Option value="是">是</Option>
          <Option value="否">否</Option>
        </Select>
      )
    },
    {
      title: '实际股东',
      dataIndex: 'actualShareholder',
      key: 'actualShareholder',
      render: (text: string, record: any) => (
        <Input
          placeholder="请输入"
          value={text}
          onChange={(e) => updateShareholder(record.key, 'actualShareholder', e.target.value)}
        />
      )
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      key: 'startDate',
      render: (text: string, record: any) => (
        <DatePicker
          style={{ width: '100%' }}
          placeholder="年月日"
          format="YYYY-MM-DD"
          onChange={(date, dateString) => updateShareholder(record.key, 'startDate', dateString)}
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: any, record: any) => (
        <Button
          type="link"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeShareholder(record.key)}
        />
      )
    }
  ];

  // 对外投资信息表格列
  const investmentColumns = [
    {
      title: '主体名称',
      dataIndex: 'companyName',
      key: 'companyName',
      render: (text: string, record: any) => (
        <Input
          placeholder="请输入公司名称"
          value={text}
          onChange={(e) => updateInvestment(record.key, 'companyName', e.target.value)}
        />
      )
    },
    {
      title: '投资金额（万元人民币）',
      dataIndex: 'investmentAmount',
      key: 'investmentAmount',
      render: (text: string, record: any) => (
        <Input
          placeholder="请输入"
          value={text}
          onChange={(e) => updateInvestment(record.key, 'investmentAmount', e.target.value)}
        />
      )
    },
    {
      title: '持股比例',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (text: string, record: any) => (
        <Input
          placeholder="请输入"
          value={text}
          addonAfter="%"
          onChange={(e) => updateInvestment(record.key, 'percentage', e.target.value)}
        />
      )
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      key: 'startDate',
      render: (text: string, record: any) => (
        <DatePicker
          style={{ width: '100%' }}
          placeholder="年月日"
          format="YYYY-MM-DD"
          onChange={(date, dateString) => updateInvestment(record.key, 'startDate', dateString)}
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_: any, record: any) => (
        <Button
          type="link"
          danger
          icon={<DeleteOutlined />}
          onClick={() => removeInvestment(record.key)}
        />
      )
    }
  ];

  // 重试功能
  const handleRetryOptions = () => {
    setOptionsError(null);
    loadOptionsData();
  };

  // 如果选项数据加载失败，显示错误状态
  if (optionsError) {
    return (
      <div style={{ minHeight: '100vh', backgroundColor: '#f5f5f5', padding: 24 }}>
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <h3>选项数据加载失败</h3>
            <p style={{ color: '#ff4d4f', marginBottom: '16px' }}>{optionsError}</p>
            <p style={{ marginBottom: '16px' }}>表单已禁用，请重试加载选项数据</p>
            <Space>
              <Button type="primary" onClick={handleRetryOptions}>
                重试
              </Button>
            </Space>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div style={{ minHeight: '100vh', backgroundColor: '#f5f5f5', padding: 24 }}>
      {/* Success Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
            <span>添加成功</span>
          </div>
        }
        open={isSuccessModalVisible}
        onOk={handleSuccessModalOk}
        onCancel={handleSuccessModalCancel}
        okText="继续添加"
        cancelText="返回首页"
        width={520}
      >
        <div style={{ padding: '16px 0' }}>
          <Alert
            message="公司信息已成功添加！"
            description="您可以继续添加新公司，或者返回首页。"
            type="success"
            showIcon
          />
        </div>
      </Modal>

      {/* Page Header */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            style={{ marginRight: 12 }}
          >
            返回
          </Button>
          <div>
            <Title level={3} style={{ margin: 0 }}>
              新增公司
            </Title>
            <Text type="secondary">
              添加新的公司信息到系统中
            </Text>
          </div>
        </div>
      </div>

      {/* Main Form Card */}
      <Card 
        title="公司基本信息"
        style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.1)', maxWidth: 1200, margin: '0 auto' }}
        loading={loading}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          initialValues={{}}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="companyNameCn"
                label="公司中文名"
                rules={[
                  { required: true, message: '请输入公司中文名' },
                  { max: 255, message: '公司中文名不能超过255个字符' }
                ]}
              >
                <Input placeholder="请输入公司中文名" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="companyNameEn"
                label="公司英文名"
                rules={[
                  { required: true, message: '请输入公司英文名' },
                  { max: 255, message: '公司英文名不能超过255个字符' }
                ]}
              >
                <Input placeholder="请输入公司英文名" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="registeredCapital"
                label="注册资本（万元）"
                rules={[
                  { required: true, message: '请输入注册资本' },
                  { pattern: /^\d+(\.\d{1,4})?$/, message: '请输入有效的数字，最多4位小数' }
                ]}
              >
                <Input placeholder="请输入注册资本" addonAfter="万元" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="establishDate"
                label="设立日期"
                rules={[{ required: true, message: '请选择设立日期' }]}
              >
                <DatePicker 
                  style={{ width: '100%' }} 
                  placeholder="请选择设立日期"
                  format="YYYY-MM-DD"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="businessSegment"
                label="业务板块"
                rules={[{ required: true, message: '请选择业务板块' }]}
              >
                <Select placeholder="请选择业务板块" showSearch>
                  {businessSegments.map(segment => (
                    <Option key={segment} value={segment}>{segment}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="region"
                label="地区"
                rules={[{ required: true, message: '请输入地区' }]}
              >
                <Input placeholder="请输入地区" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="agency"
                label="代理机构"
                rules={[{ required: true, message: '请选择代理机构' }]}
              >
                <Select placeholder="请选择代理机构" showSearch>
                  {agencies.map(agency => (
                    <Option key={agency} value={agency}>{agency}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="annualUpdate"
                label="年审更新"
                rules={[{ required: true, message: '请选择年审更新' }]}
              >
                <Select placeholder="请选择年审更新">
                  {annualUpdates.map(update => (
                    <Option key={update} value={update}>{update}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={[16, 16]}>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="registeredAddress"
                label="注册地址"
                rules={[
                  { required: true, message: '请输入注册地址' },
                  { max: 500, message: '注册地址不能超过500个字符' }
                ]}
              >
                <Input.TextArea placeholder="请输入注册地址" rows={2} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={24} md={12} lg={12}>
              <Form.Item
                name="operationStatus"
                label="存续状况"
                rules={[{ required: true, message: '请选择存续状况' }]}
              >
                <Select placeholder="请选择存续状况">
                  {operationStatuses.map(status => (
                    <Option key={status} value={status}>{status}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* 高管信息部分 */}
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: 32, marginBottom: 16 }}>
            <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 600 }}>高管信息</h3>
            <Button type="primary" icon={<PlusOutlined />} onClick={addExecutive}>
              添加高管
            </Button>
          </div>

          <Table
            columns={executiveColumns}
            dataSource={executives}
            pagination={false}
            locale={{ emptyText: '暂无数据' }}
            size="small"
            style={{ marginBottom: 32 }}
          />

          {/* 股东信息部分 */}
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
            <div>
              <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 600 }}>股东信息</h3>
              <div style={{ marginTop: 4, color: '#666', fontSize: '12px' }}>
                持股比例合计可以不是100%，但不能超过100%
              </div>
            </div>
            <Button type="primary" icon={<PlusOutlined />} onClick={addShareholder}>
              添加股东
            </Button>
          </div>

          <Table
            columns={shareholderColumns}
            dataSource={shareholders}
            pagination={false}
            locale={{ emptyText: '暂无数据' }}
            size="small"
            scroll={{ x: 800 }}
            style={{ marginBottom: 32 }}
          />

          {/* 对外投资信息部分 */}
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 16 }}>
            <h3 style={{ margin: 0, fontSize: '16px', fontWeight: 600 }}>对外投资信息</h3>
            <Button type="primary" icon={<PlusOutlined />} onClick={addInvestment}>
              添加对外投资
            </Button>
          </div>

          <Table
            columns={investmentColumns}
            dataSource={investments}
            pagination={false}
            locale={{ emptyText: '暂无数据' }}
            size="small"
            scroll={{ x: 600 }}
            style={{ marginBottom: 32 }}
          />

          {/* Form Actions */}
          <div style={{
            paddingTop: 24,
            borderTop: '1px solid #f0f0f0',
            display: 'flex',
            justifyContent: 'center',
            gap: 16
          }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              icon={<SaveOutlined />}
              size="large"
            >
              {loading ? '提交中...' : '确定'}
            </Button>
            <Button
              onClick={handleReset}
              icon={<ReloadOutlined />}
              size="large"
            >
              重置
            </Button>
            <Button
              onClick={handleBack}
              size="large"
            >
              取消
            </Button>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default CompanyAddWorking;
