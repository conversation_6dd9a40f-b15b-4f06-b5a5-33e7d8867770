import React, { useState, useEffect } from 'react';
import { Card, Select, Button, message, Spin, Alert } from 'antd';
import { useSearchParams } from 'react-router-dom';
import axios from 'axios';

const { Option } = Select;

interface Company {
  id: number;
  name: string;
}

interface ShareholderNode {
  id: number;
  shareholderId: number;
  shareholderName: string;
  shareholderType: 'person' | 'company';
  companyId: number;
  companyName: string;
  percentage: number;
  isProxy: boolean;
  actualShareholderId?: number;
  actualShareholderName?: string;
  level?: number; // 层级信息
}

interface InvestmentNode {
  id: number;
  investorId: number;
  investorName: string;
  investeeId?: number;
  investeeName: string;
  percentage: number;
  level?: number; // 层级信息
}

interface EquityChartData {
  centerCompany: Company;
  upstreamShareholders: ShareholderNode[];
  downstreamInvestments: InvestmentNode[];
  relatedCompanies: Company[];
}

interface NodePosition {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  name: string;
  type: 'company' | 'person';
  isCenter?: boolean;
  collapsed?: boolean;
  children?: string[];
  level?: number;
  isProxy?: boolean;
}

const EquityChart: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [companies, setCompanies] = useState<Company[]>([]);
  const [selectedCompanyId, setSelectedCompanyId] = useState<number | null>(null);
  const [selectedCompanyIds, setSelectedCompanyIds] = useState<number[]>([]);
  const [version, setVersion] = useState<'legal' | 'investor'>('investor');
  const [equityData, setEquityData] = useState<EquityChartData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hiddenCompanies, setHiddenCompanies] = useState<Set<number>>(new Set());
  const [collapsedNodes, setCollapsedNodes] = useState<Set<string>>(new Set());

  // 从URL参数获取公司ID
  useEffect(() => {
    const companyId = searchParams.get('companyId');
    if (companyId) {
      setSelectedCompanyId(parseInt(companyId));
    }
  }, [searchParams]);

  // 加载公司列表
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        const response = await axios.get('http://localhost:8080/api/companies');
        if (response.data.success) {
          const companiesList = response.data.data.map((company: any) => ({
            id: company.id,
            name: company.chineseName || company.company_name_cn
          }));
          setCompanies(companiesList);
        }
      } catch (error) {
        console.error('获取公司列表失败:', error);
        message.error('获取公司列表失败');
      }
    };

    fetchCompanies();
  }, []);

  // 自动选择第一个公司（仅在投资者版本且没有选择公司时）
  useEffect(() => {
    if (!selectedCompanyId && version === 'investor' && companies.length > 0) {
      setSelectedCompanyId(companies[0].id);
    }
  }, [companies, selectedCompanyId, version]);

  // 加载股权图数据
  useEffect(() => {
    if (version === 'investor' && selectedCompanyId) {
      fetchEquityData(selectedCompanyId);
    } else if (version === 'legal' && selectedCompanyIds.length > 0) {
      fetchMultipleEquityData(selectedCompanyIds);
    }
  }, [selectedCompanyId, selectedCompanyIds, version]);

  const fetchEquityData = async (companyId: number) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get(`http://localhost:8080/api/equity-chart/${companyId}`);
      if (response.data.success) {
        setEquityData(response.data.data);
      } else {
        throw new Error(response.data.message || '获取股权图数据失败');
      }
    } catch (error: any) {
      console.error('获取股权图数据失败:', error);
      setError(error.message || '获取股权图数据失败');
      message.error(error.message || '获取股权图数据失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchMultipleEquityData = async (companyIds: number[]) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.post(`http://localhost:8080/api/equity-chart/multiple`, {
        companyIds
      });
      if (response.data.success) {
        setEquityData(response.data.data);
      } else {
        throw new Error(response.data.message || '获取股权图数据失败');
      }
    } catch (error: any) {
      console.error('获取股权图数据失败:', error);
      setError(error.message || '获取股权图数据失败');
      message.error(error.message || '获取股权图数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 计算节点位置 - 改进的布局算法
  const calculateNodePositions = (): NodePosition[] => {
    if (!equityData) return [];

    const positions: NodePosition[] = [];
    const nodeWidth = 140;
    const nodeHeight = 80;
    const horizontalSpacing = 180;
    const verticalSpacing = 140;

    // 动态计算画布中心，确保所有节点都能显示
    const maxUpstreamNodes = Math.max(equityData.upstreamShareholders.length, 1);
    const maxDownstreamNodes = Math.max(equityData.downstreamInvestments.length, 1);
    const maxNodesInRow = Math.max(maxUpstreamNodes, maxDownstreamNodes);

    const minCanvasWidth = (maxNodesInRow * horizontalSpacing) + 200;
    const centerX = Math.max(600, minCanvasWidth / 2);
    const centerY = 300;

    if (version === 'investor') {
      // 投资者版：单个中心公司
      positions.push({
        id: `company-${equityData.centerCompany.id}`,
        x: centerX,
        y: centerY,
        width: nodeWidth,
        height: nodeHeight,
        name: equityData.centerCompany.name,
        type: 'company',
        isCenter: true
      });

      // 上游股东（向上排列，支持多层级）
      const upstreamLevels = organizeUpstreamByLevels(equityData.upstreamShareholders);
      upstreamLevels.forEach((level, levelIndex) => {
        const y = centerY - verticalSpacing * (levelIndex + 1);
        level.forEach((shareholder, index) => {
          // 确保节点不会超出画布边界
          const totalWidth = (level.length - 1) * horizontalSpacing;
          const startX = centerX - totalWidth / 2;
          const x = Math.max(nodeWidth / 2, startX + index * horizontalSpacing);

          // 为代持关系和不同层级添加特殊标识
          const displayName = shareholder.isProxy && shareholder.actualShareholderName
            ? shareholder.actualShareholderName
            : shareholder.shareholderName;

          positions.push({
            id: `${shareholder.shareholderType}-${shareholder.shareholderId}-${shareholder.level || 0}`,
            x: x - nodeWidth / 2,
            y,
            width: nodeWidth,
            height: nodeHeight,
            name: displayName,
            type: shareholder.shareholderType,
            level: shareholder.level || 0,
            isProxy: shareholder.isProxy
          });
        });
      });

      // 下游投资（向下排列，支持多层级）
      const downstreamLevels = organizeDownstreamByLevels(equityData.downstreamInvestments);
      downstreamLevels.forEach((level, levelIndex) => {
        const y = centerY + verticalSpacing * (levelIndex + 1);
        level.forEach((investment, index) => {
          // 确保节点不会超出画布边界
          const totalWidth = (level.length - 1) * horizontalSpacing;
          const startX = centerX - totalWidth / 2;
          const x = Math.max(nodeWidth / 2, startX + index * horizontalSpacing);

          positions.push({
            id: `investment-${investment.investeeId || investment.id}-${investment.level || 0}`,
            x: x - nodeWidth / 2,
            y,
            width: nodeWidth,
            height: nodeHeight,
            name: investment.investeeName,
            type: 'company',
            level: investment.level || 0
          });
        });
      });
    } else {
      // 法务版：多个中心公司
      const centerCompanies = selectedCompanyIds.map(id => 
        companies.find(c => c.id === id)
      ).filter(Boolean) as Company[];

      centerCompanies.forEach((company, index) => {
        const totalWidth = (centerCompanies.length - 1) * horizontalSpacing;
        const startX = centerX - totalWidth / 2;
        const x = Math.max(nodeWidth / 2, startX + index * horizontalSpacing);

        positions.push({
          id: `company-${company.id}`,
          x: x - nodeWidth / 2,
          y: centerY,
          width: nodeWidth,
          height: nodeHeight,
          name: company.name,
          type: 'company',
          isCenter: true
        });
      });
    }

    return positions;
  };

  // 合并同一个人的重复记录
  const mergeDuplicatePersons = (shareholders: ShareholderNode[]): ShareholderNode[] => {
    const personMap = new Map<number, ShareholderNode>();
    const companyList: ShareholderNode[] = [];

    shareholders.forEach(shareholder => {
      if (shareholder.shareholderType === 'person') {
        const personId = shareholder.actualShareholderId || shareholder.shareholderId;
        const personName = shareholder.actualShareholderName || shareholder.shareholderName;

        if (personMap.has(personId)) {
          // 合并持股比例
          const existing = personMap.get(personId)!;
          const existingPercentage = parseFloat(existing.percentage.toString());
          const currentPercentage = parseFloat(shareholder.percentage.toString());
          existing.percentage = (existingPercentage + currentPercentage).toFixed(2);

          // 更新名称为实际股东名称（如果有代持关系）
          if (shareholder.actualShareholderName) {
            existing.shareholderName = shareholder.actualShareholderName;
          }
        } else {
          // 创建新的合并记录
          const mergedShareholder = {
            ...shareholder,
            shareholderId: personId,
            shareholderName: personName,
            isProxy: false // 合并后不再显示代持标识
          };
          personMap.set(personId, mergedShareholder);
        }
      } else {
        // 企业股东直接添加
        companyList.push(shareholder);
      }
    });

    return [...Array.from(personMap.values()), ...companyList];
  };

  // 组织上游股东按层级
  const organizeUpstreamByLevels = (shareholders: ShareholderNode[]): ShareholderNode[][] => {
    // 按层级分组
    const levelMap = new Map<number, ShareholderNode[]>();

    shareholders.forEach(shareholder => {
      const level = shareholder.level || 0;
      if (!levelMap.has(level)) {
        levelMap.set(level, []);
      }
      levelMap.get(level)!.push(shareholder);
    });

    // 转换为数组，按层级排序
    const levels: ShareholderNode[][] = [];
    const sortedLevels = Array.from(levelMap.keys()).sort((a, b) => a - b);

    sortedLevels.forEach(level => {
      const shareholdersAtLevel = levelMap.get(level) || [];
      // 对每一层的股东进行去重处理
      const mergedShareholders = mergeDuplicatePersons(shareholdersAtLevel);
      levels.push(mergedShareholders);
    });

    return levels;
  };

  // 组织下游投资按层级
  const organizeDownstreamByLevels = (investments: InvestmentNode[]): InvestmentNode[][] => {
    // 按层级分组
    const levelMap = new Map<number, InvestmentNode[]>();

    investments.forEach(investment => {
      const level = investment.level || 0;
      if (!levelMap.has(level)) {
        levelMap.set(level, []);
      }
      levelMap.get(level)!.push(investment);
    });

    // 转换为数组，按层级排序
    const levels: InvestmentNode[][] = [];
    const sortedLevels = Array.from(levelMap.keys()).sort((a, b) => a - b);

    sortedLevels.forEach(level => {
      const investmentsAtLevel = levelMap.get(level) || [];
      levels.push(investmentsAtLevel);
    });

    return levels;
  };

  // 渲染连接线
  const renderConnections = (positions: NodePosition[]) => {
    if (!equityData || version !== 'investor') return null;

    const connections: JSX.Element[] = [];
    const centerPos = positions.find(p => p.isCenter);
    if (!centerPos) return null;

    // 上游连接线（股东 -> 中心公司）
    equityData.upstreamShareholders.forEach((shareholder) => {
      const shareholderPos = positions.find(p =>
        p.id === `${shareholder.shareholderType}-${shareholder.shareholderId}`
      );
      if (shareholderPos) {
        const startX = shareholderPos.x + shareholderPos.width / 2;
        const startY = shareholderPos.y + shareholderPos.height;
        const endX = centerPos.x + centerPos.width / 2;
        const endY = centerPos.y;

        connections.push(
          <g key={`upstream-${shareholder.id}`}>
            <line
              x1={startX}
              y1={startY}
              x2={endX}
              y2={endY}
              stroke="#1890ff"
              strokeWidth="2"
              markerEnd="url(#arrowhead)"
            />
            <rect
              x={(startX + endX) / 2 - 20}
              y={(startY + endY) / 2 - 10}
              width="40"
              height="20"
              fill="white"
              stroke="#1890ff"
              strokeWidth="1"
              rx="3"
            />
            <text
              x={(startX + endX) / 2}
              y={(startY + endY) / 2 + 3}
              textAnchor="middle"
              fontSize="11"
              fill="#1890ff"
              fontWeight="bold"
            >
              {shareholder.percentage}%{shareholder.isProxy ? ' (代持)' : ''}
            </text>
          </g>
        );
      }
    });

    // 下游连接线（中心公司 -> 投资对象）
    equityData.downstreamInvestments.forEach((investment) => {
      const investmentPos = positions.find(p =>
        p.id === `investment-${investment.investeeId || investment.id}`
      );
      if (investmentPos) {
        const startX = centerPos.x + centerPos.width / 2;
        const startY = centerPos.y + centerPos.height;
        const endX = investmentPos.x + investmentPos.width / 2;
        const endY = investmentPos.y;

        connections.push(
          <g key={`downstream-${investment.id}`}>
            <line
              x1={startX}
              y1={startY}
              x2={endX}
              y2={endY}
              stroke="#52c41a"
              strokeWidth="2"
              markerEnd="url(#arrowhead-green)"
            />
            <rect
              x={(startX + endX) / 2 - 20}
              y={(startY + endY) / 2 - 10}
              width="40"
              height="20"
              fill="white"
              stroke="#52c41a"
              strokeWidth="1"
              rx="3"
            />
            <text
              x={(startX + endX) / 2}
              y={(startY + endY) / 2 + 3}
              textAnchor="middle"
              fontSize="11"
              fill="#52c41a"
              fontWeight="bold"
            >
              {investment.percentage}%
            </text>
          </g>
        );
      }
    });

    return connections;
  };

  // 渲染节点
  const renderNodes = (positions: NodePosition[]) => {
    return positions.map((pos) => (
      <g key={pos.id}>
        {/* 节点阴影 */}
        <rect
          x={pos.x + 2}
          y={pos.y + 2}
          width={pos.width}
          height={pos.height}
          fill="rgba(0,0,0,0.1)"
          rx="6"
        />

        {/* 节点主体 */}
        <rect
          x={pos.x}
          y={pos.y}
          width={pos.width}
          height={pos.height}
          fill={pos.isCenter ? '#1890ff' : pos.type === 'person' ? '#f6ffed' : '#fff'}
          stroke={pos.isCenter ? '#1890ff' : pos.type === 'person' ? '#52c41a' : '#d9d9d9'}
          strokeWidth={pos.isCenter ? "3" : "2"}
          rx="6"
          style={{ cursor: 'pointer' }}
        />

        {/* 节点类型图标 */}
        <circle
          cx={pos.x + 15}
          cy={pos.y + 15}
          r="8"
          fill={pos.type === 'person' ? '#52c41a' : '#1890ff'}
        />
        <text
          x={pos.x + 15}
          y={pos.y + 19}
          textAnchor="middle"
          fontSize="10"
          fill="white"
          fontWeight="bold"
        >
          {pos.type === 'person' ? '人' : '企'}
        </text>

        {/* 节点名称 */}
        <text
          x={pos.x + pos.width / 2}
          y={pos.y + pos.height / 2 - 5}
          textAnchor="middle"
          dominantBaseline="middle"
          fontSize="12"
          fill={pos.isCenter ? '#fff' : '#333'}
          fontWeight={pos.isCenter ? 'bold' : 'normal'}
        >
          {pos.name.length > 10 ? `${pos.name.substring(0, 10)}...` : pos.name}
        </text>

        {/* 代持关系标识 */}
        {pos.id.includes('person') && equityData?.upstreamShareholders.some(s =>
          s.shareholderType === 'person' &&
          s.shareholderId.toString() === pos.id.split('-')[1] &&
          s.isProxy
        ) && (
          <g>
            <rect
              x={pos.x + pos.width - 25}
              y={pos.y + 5}
              width="20"
              height="15"
              fill="#ff4d4f"
              rx="2"
            />
            <text
              x={pos.x + pos.width - 15}
              y={pos.y + 14}
              textAnchor="middle"
              fontSize="9"
              fill="white"
              fontWeight="bold"
            >
              代
            </text>
          </g>
        )}

        {/* 中心标识 */}
        {pos.isCenter && (
          <text
            x={pos.x + pos.width / 2}
            y={pos.y + pos.height / 2 + 12}
            textAnchor="middle"
            fontSize="10"
            fill="#fff"
            fontWeight="bold"
          >
            [中心]
          </text>
        )}
      </g>
    ));
  };

  // 处理公司筛选变化
  const handleCompanyFilterChange = (companyIds: number[]) => {
    const hiddenSet = new Set<number>();
    equityData?.relatedCompanies.forEach(company => {
      if (!companyIds.includes(company.id)) {
        hiddenSet.add(company.id);
      }
    });
    setHiddenCompanies(hiddenSet);
  };

  // 清理重复数据
  const handleCleanupDuplicates = async () => {
    try {
      setLoading(true);
      const response = await axios.post('http://localhost:8080/api/equity-chart/cleanup-duplicates');
      if (response.data.success) {
        message.success(`数据清理完成！清理了 ${response.data.data.totalCleaned} 条重复记录`);
        // 重新加载数据
        if (version === 'investor' && selectedCompanyId) {
          await fetchEquityData(selectedCompanyId);
        } else if (version === 'legal' && selectedCompanyIds.length > 0) {
          await fetchMultipleEquityData(selectedCompanyIds);
        }
      } else {
        throw new Error(response.data.message || '数据清理失败');
      }
    } catch (error: any) {
      console.error('数据清理失败:', error);
      message.error(error.message || '数据清理失败');
    } finally {
      setLoading(false);
    }
  };

  const positions = calculateNodePositions();

  // 调试信息
  console.log('🔍 股权图调试信息:', {
    equityData,
    positions,
    version,
    selectedCompanyId,
    loading,
    error
  });

  // 动态计算SVG画布尺寸
  const calculateCanvasSize = () => {
    if (!equityData || positions.length === 0) {
      return { width: 1200, height: 600 };
    }

    const maxX = Math.max(...positions.map(p => p.x + p.width));
    const maxY = Math.max(...positions.map(p => p.y + p.height));
    const minX = Math.min(...positions.map(p => p.x));
    const minY = Math.min(...positions.map(p => p.y));

    // 确保有足够的边距，特别是右侧
    const width = Math.max(1200, maxX - minX + 300);
    const height = Math.max(600, maxY - minY + 200);

    return { width, height };
  };

  const canvasSize = calculateCanvasSize();

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px' }}>
          <h2>股权图-主体公司</h2>
          
          {/* 版本选择 */}
          <div style={{ marginBottom: '16px' }}>
            <label style={{ marginRight: '8px' }}>选择版本：</label>
            <Select
              style={{ width: 200 }}
              value={version}
              onChange={(value) => {
                setVersion(value);
                setSelectedCompanyId(null);
                setSelectedCompanyIds([]);
                setEquityData(null);
              }}
            >
              <Option value="investor">投资者版</Option>
              <Option value="legal">法务版</Option>
            </Select>
          </div>

          {/* 公司选择下拉框 */}
          <div style={{ marginBottom: '16px' }}>
            <label style={{ marginRight: '8px' }}>选择主体公司：</label>
            {version === 'investor' ? (
              <Select
                style={{ width: 300 }}
                placeholder="请选择公司"
                value={selectedCompanyId}
                onChange={setSelectedCompanyId}
                showSearch
                filterOption={(input, option) =>
                  option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
                }
              >
                {companies.map(company => (
                  <Option key={company.id} value={company.id}>
                    {company.name}
                  </Option>
                ))}
              </Select>
            ) : (
              <Select
                mode="multiple"
                style={{ width: 400 }}
                placeholder="请选择多个公司"
                value={selectedCompanyIds}
                onChange={setSelectedCompanyIds}
                showSearch
                filterOption={(input, option) =>
                  option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
                }
              >
                {companies.map(company => (
                  <Option key={company.id} value={company.id}>
                    {company.name}
                  </Option>
                ))}
              </Select>
            )}
          </div>

          {/* 数据清理按钮 */}
          <div style={{ marginBottom: '16px' }}>
            <Button
              type="default"
              onClick={handleCleanupDuplicates}
              loading={loading}
              style={{ marginRight: '8px' }}
            >
              🧹 清理重复数据
            </Button>
            <span style={{ fontSize: '12px', color: '#666' }}>
              清理数据库中重复的股权关系记录
            </span>
          </div>
        </div>

        {/* 加载状态 */}
        {loading && (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>正在加载股权图数据...</div>
          </div>
        )}

        {/* 错误状态 */}
        {error && (
          <Alert
            message="加载失败"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        )}

        {/* 无数据提示 */}
        {!loading && !error && !equityData && (
          <div style={{
            textAlign: 'center',
            padding: '50px',
            backgroundColor: '#fafafa',
            border: '1px solid #d9d9d9',
            borderRadius: '4px'
          }}>
            <div style={{ fontSize: '16px', color: '#666', marginBottom: '8px' }}>
              📊 请选择公司查看股权关系图
            </div>
            <div style={{ fontSize: '14px', color: '#999' }}>
              {version === 'investor' ? '选择一个公司查看其股权结构' : '选择多个公司查看综合股权关系'}
            </div>
          </div>
        )}

        {/* 股权图显示 */}
        {!loading && !error && equityData && (
          <div style={{
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            backgroundColor: '#fafafa',
            minHeight: `${canvasSize.height}px`,
            position: 'relative',
            overflow: 'auto'
          }}>
            <svg
              width={canvasSize.width}
              height={canvasSize.height}
              style={{ display: 'block' }}
            >
              {/* 定义箭头标记 */}
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon
                    points="0 0, 10 3.5, 0 7"
                    fill="#1890ff"
                  />
                </marker>
                <marker
                  id="arrowhead-green"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon
                    points="0 0, 10 3.5, 0 7"
                    fill="#52c41a"
                  />
                </marker>
              </defs>

              {/* 渲染连接线 */}
              {renderConnections(positions)}

              {/* 渲染节点 */}
              {renderNodes(positions)}
            </svg>

            {/* 滚动提示 */}
            {canvasSize.width > 1200 && (
              <div style={{
                position: 'absolute',
                bottom: '10px',
                left: '10px',
                backgroundColor: '#fff3cd',
                padding: '8px 12px',
                border: '1px solid #ffeaa7',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#856404'
              }}>
                💡 图表较宽，可以左右滚动查看完整内容
              </div>
            )}

            {/* 图例说明 - 移到左下角 */}
            <div style={{
              position: 'absolute',
              bottom: '10px',
              left: '10px',
              backgroundColor: '#fff',
              padding: '12px',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              fontSize: '11px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              maxWidth: '180px',
              zIndex: 10
            }}>
              <div style={{ marginBottom: '12px', fontWeight: 'bold', fontSize: '14px' }}>图例说明</div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '20px',
                  height: '3px',
                  backgroundColor: '#1890ff',
                  marginRight: '8px'
                }}></div>
                <span>股东持股关系</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '20px',
                  height: '3px',
                  backgroundColor: '#52c41a',
                  marginRight: '8px'
                }}></div>
                <span>对外投资关系</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '16px',
                  backgroundColor: '#1890ff',
                  borderRadius: '3px',
                  marginRight: '8px',
                  border: '2px solid #1890ff'
                }}></div>
                <span>中心公司</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '16px',
                  backgroundColor: '#f6ffed',
                  borderRadius: '3px',
                  marginRight: '8px',
                  border: '2px solid #52c41a'
                }}></div>
                <span>个人股东</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '16px',
                  backgroundColor: '#fff',
                  borderRadius: '3px',
                  marginRight: '8px',
                  border: '2px solid #d9d9d9'
                }}></div>
                <span>企业主体</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '12px',
                  backgroundColor: '#ff4d4f',
                  borderRadius: '2px',
                  marginRight: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '8px'
                }}>代</div>
                <span>代持关系</span>
              </div>

              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '16px',
                  backgroundColor: '#52c41a',
                  borderRadius: '50%',
                  marginRight: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '10px'
                }}>↑</div>
                <span>展开/收起按钮</span>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default EquityChart;
