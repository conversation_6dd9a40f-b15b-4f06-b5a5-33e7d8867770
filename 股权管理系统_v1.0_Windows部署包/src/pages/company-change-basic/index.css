.company-change-basic {
  padding: 20px;
  background: #fff;
  min-height: 100vh;
}

/* 顶部按钮样式 */
.top-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

.submit-btn,
.confirm-btn {
  background: #000;
  border: none;
  color: #fff;
  padding: 8px 24px;
  border-radius: 4px;
  font-size: 14px;
  min-width: 80px;
}

.submit-btn:hover,
.confirm-btn:hover {
  background: #333;
}

.cancel-btn {
  background: #fff;
  border: 1px solid #d9d9d9;
  color: #000;
  padding: 8px 24px;
  border-radius: 4px;
  font-size: 14px;
  min-width: 80px;
}

.cancel-btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

/* 表单区域样式 */
.form-section {
  margin-bottom: 30px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.form-label {
  width: 100px;
  text-align: right;
  margin-right: 20px;
  font-size: 14px;
  color: #333;
}

/* 公司信息编辑区域样式 */
.company-info-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.company-info-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}

.form-item {
  margin-bottom: 16px;
}

.form-item label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.required-label::before {
  content: '* ';
  color: #ff4d4f;
  margin-right: 4px;
}

/* 参考规范样式 */
.reference-section {
  margin-bottom: 30px;
}

.reference-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

.reference-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.reference-item {
  display: flex;
  align-items: center;
  gap: 20px;
}

.reference-item span {
  width: 60px;
  font-size: 14px;
  color: #333;
}

.reference-link {
  color: #1890ff;
  text-decoration: none;
  font-size: 14px;
}

.reference-link:hover {
  text-decoration: underline;
}

/* 具体变更样式 */
.change-details-section {
  margin-bottom: 30px;
}

.change-details-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

/* 上传文件样式 */
.upload-section {
  margin-bottom: 30px;
}

.upload-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

/* 表格样式调整 */
.ant-table {
  font-size: 14px;
}

.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  text-align: center;
  padding: 12px 8px;
}

.ant-table-tbody > tr > td {
  padding: 12px 8px;
  text-align: center;
}

/* 输入框样式 */
.ant-select {
  font-size: 14px;
}

.ant-select-selector {
  border-radius: 4px;
}

.ant-date-picker {
  font-size: 14px;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .company-change-basic {
    padding: 15px;
  }
  
  .form-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .form-label {
    width: auto;
    text-align: left;
    margin-right: 0;
  }
  
  .top-buttons {
    flex-wrap: wrap;
  }
  
  .reference-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}

/* 禁用状态样式 */
.ant-select-disabled .ant-select-selector,
.ant-date-picker-disabled,
.ant-input-disabled {
  background-color: #f5f5f5;
  color: #999;
}

/* 文件上传按钮样式 */
.ant-btn-link {
  padding: 0;
  height: auto;
  font-size: 14px;
}

/* 表格边框样式 */
.ant-table-bordered .ant-table-container {
  border: 1px solid #d9d9d9;
}

.ant-table-bordered .ant-table-thead > tr > th,
.ant-table-bordered .ant-table-tbody > tr > td {
  border-right: 1px solid #d9d9d9;
}

/* 加载状态样式 */
.ant-btn-loading {
  opacity: 0.7;
}
