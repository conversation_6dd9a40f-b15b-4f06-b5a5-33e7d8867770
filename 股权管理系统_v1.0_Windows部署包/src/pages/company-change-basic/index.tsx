import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { Button, Select, DatePicker, Table, Upload, message, Input, Row, Col } from 'antd';
import { UploadOutlined, DownloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import './index.css';

const { Option } = Select;

interface CompanyChangeBasicProps {}

interface ChangeRecord {
  field: string;
  fieldName: string;
  oldValue: string;
  newValue: string;
}

interface CompanyInfo {
  id: number;
  chineseName: string;
  englishName: string;
  registeredCapital: string;
  establishmentDate: string;
  businessScopeId: number;
  regionId: number;
  agencyId: number | null;
  registrationAddress: string;
  operationStatusId: number | null;
  businessScope: string;
}

interface BusinessSegment {
  id: number;
  name: string;
}

interface Region {
  id: number;
  type: string;
  region: string;
}

interface Agency {
  id: number;
  agencyName: string;
  contactPerson: string;
  contactMethod: string;
}

const CompanyChangeBasic: React.FC<CompanyChangeBasicProps> = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);

  // 表单状态
  const [changeType, setChangeType] = useState<string>('');
  const [changeContent, setChangeContent] = useState<string>('');
  const [changeDate, setChangeDate] = useState<dayjs.Dayjs>(dayjs());
  const [changeRecords, setChangeRecords] = useState<ChangeRecord[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  // 公司信息状态
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null);
  const [originalCompanyInfo, setOriginalCompanyInfo] = useState<CompanyInfo | null>(null);
  const [businessSegments, setBusinessSegments] = useState<BusinessSegment[]>([]);
  const [regions, setRegions] = useState<Region[]>([]);
  const [agencies, setAgencies] = useState<Agency[]>([]);
  const [operationStatuses] = useState([
    { id: 1, name: '正常' },
    { id: 2, name: '注销' },
    { id: 3, name: '吊销' },
    { id: 4, name: '停业' },
  ]);

  // 从路由参数获取公司信息，支持URL参数和state参数
  const companyId = location.state?.companyId || searchParams.get('companyId');
  const companyName = location.state?.companyName || '';
  const isConfirmMode = location.state?.isConfirmMode || false;

  // 变更类型选项
  const changeTypeOptions = [
    { value: 'basic', label: '基础信息变更' },
    { value: 'executive', label: '高管信息变更' },
    { value: 'shareholder', label: '股东信息变更' },
  ];

  // 变更内容选项（根据变更类型动态变化）
  const getChangeContentOptions = (type: string) => {
    switch (type) {
      case 'basic':
        return [
          { value: 'company_name', label: '公司名称变更' },
          { value: 'registered_capital', label: '注册资本变更' },
          { value: 'registration_address', label: '注册地址变更' },
          { value: 'business_scope', label: '经营范围变更' },
        ];
      case 'executive':
        return [
          { value: 'add_executive', label: '新增高管' },
          { value: 'remove_executive', label: '删除高管' },
          { value: 'change_position', label: '职位变更' },
        ];
      case 'shareholder':
        return [
          { value: 'add_shareholder', label: '新增股东' },
          { value: 'remove_shareholder', label: '删除股东' },
          { value: 'change_percentage', label: '持股比例变更' },
        ];
      default:
        return [];
    }
  };

  // 具体变更表格列配置
  const changeColumns = [
    {
      title: '内容',
      dataIndex: 'fieldName',
      key: 'fieldName',
      width: 200,
      align: 'center' as const,
    },
    {
      title: '修改前',
      dataIndex: 'oldValue',
      key: 'oldValue',
      align: 'center' as const,
    },
    {
      title: '修改后',
      dataIndex: 'newValue',
      key: 'newValue',
      align: 'center' as const,
      render: (text: string, record: ChangeRecord) => (
        <input
          type="text"
          value={text}
          onChange={(e) => handleChangeRecordUpdate(record.field, e.target.value)}
          placeholder="请输入"
          style={{
            border: '1px solid #d9d9d9',
            padding: '4px 8px',
            width: '100%',
            borderRadius: '4px'
          }}
        />
      ),
    },
  ];

  // 上传文件列配置
  const fileColumns = [
    {
      title: '文件类型',
      dataIndex: 'type',
      key: 'type',
      width: 150,
    },
    {
      title: '文件名',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (text: any, record: any) => (
        <div>
          <Button 
            type="link" 
            icon={<DownloadOutlined />}
            onClick={() => handleDownloadTemplate(record.type)}
          >
            下载模板
          </Button>
          <Upload
            showUploadList={false}
            beforeUpload={(file) => handleFileUpload(file, record.type)}
          >
            <Button type="link" icon={<UploadOutlined />}>
              重新上传
            </Button>
          </Upload>
        </div>
      ),
    },
  ];

  // 初始化数据加载
  useEffect(() => {
    loadInitialData();
  }, []);

  // 初始化变更记录
  useEffect(() => {
    if (changeContent) {
      initializeChangeRecords();
    }
  }, [changeContent]);

  // 加载初始数据
  const loadInitialData = async () => {
    try {
      // 加载公司信息
      if (companyId) {
        const companyResponse = await fetch(`http://localhost:8080/api/companies/${companyId}`);
        const companyResult = await companyResponse.json();
        if (companyResult.success) {
          setCompanyInfo(companyResult.data);
          setOriginalCompanyInfo(companyResult.data);
        }
      }

      // 加载业务板块
      const businessResponse = await fetch('http://localhost:8080/api/business-segments');
      const businessResult = await businessResponse.json();
      if (businessResult.success) {
        setBusinessSegments(businessResult.data);
      }

      // 加载地区
      const regionResponse = await fetch('http://localhost:8080/api/regions');
      const regionResult = await regionResponse.json();
      if (regionResult.success) {
        setRegions(regionResult.data);
      }

      // 加载代理机构
      const agencyResponse = await fetch('http://localhost:8080/api/agencies');
      const agencyResult = await agencyResponse.json();
      if (agencyResult.success) {
        setAgencies(agencyResult.data);
      }
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    }
  };

  // 初始化变更记录
  const initializeChangeRecords = () => {
    let records: ChangeRecord[] = [];
    
    switch (changeContent) {
      case 'company_name':
        records = [
          { field: 'company_name_cn', fieldName: '公司中文名', oldValue: companyName || '深圳A公司', newValue: '' },
          { field: 'company_name_en', fieldName: '公司英文名', oldValue: 'Shenzhen A Company', newValue: '' },
        ];
        break;
      case 'registered_capital':
        records = [
          { field: 'registered_capital', fieldName: '注册资本', oldValue: '1000万元', newValue: '' },
        ];
        break;
      case 'registration_address':
        records = [
          { field: 'registration_address', fieldName: '注册地址', oldValue: '深圳市南山区', newValue: '' },
        ];
        break;
      default:
        records = [];
    }
    
    setChangeRecords(records);
  };

  // 更新变更记录
  const handleChangeRecordUpdate = (field: string, value: string) => {
    setChangeRecords(prev =>
      prev.map(record =>
        record.field === field ? { ...record, newValue: value } : record
      )
    );
  };

  // 更新公司信息
  const handleCompanyInfoChange = (field: keyof CompanyInfo, value: any) => {
    if (companyInfo) {
      setCompanyInfo(prev => ({
        ...prev!,
        [field]: value
      }));
    }
  };

  // 文件上传处理
  const handleFileUpload = (file: any, type: string) => {
    const newFile = {
      type: type,
      name: file.name,
      file: file,
    };
    
    setUploadedFiles(prev => {
      const filtered = prev.filter(f => f.type !== type);
      return [...filtered, newFile];
    });
    
    message.success(`${file.name} 上传成功`);
    return false; // 阻止自动上传
  };

  // 下载模板
  const handleDownloadTemplate = (type: string) => {
    message.info(`下载 ${type} 模板`);
  };

  // 提交处理
  const handleSubmit = async () => {
    if (!companyInfo || !originalCompanyInfo) {
      message.error('公司信息加载中，请稍后再试');
      return;
    }

    // 检查是否有变更
    const changes = [];
    if (companyInfo.chineseName !== originalCompanyInfo.chineseName) {
      changes.push(`公司中文名: ${originalCompanyInfo.chineseName} → ${companyInfo.chineseName}`);
    }
    if (companyInfo.englishName !== originalCompanyInfo.englishName) {
      changes.push(`公司英文名: ${originalCompanyInfo.englishName} → ${companyInfo.englishName}`);
    }
    if (companyInfo.registeredCapital !== originalCompanyInfo.registeredCapital) {
      changes.push(`注册资本: ${originalCompanyInfo.registeredCapital} → ${companyInfo.registeredCapital}`);
    }
    if (companyInfo.establishmentDate !== originalCompanyInfo.establishmentDate) {
      changes.push(`成立日期: ${originalCompanyInfo.establishmentDate} → ${companyInfo.establishmentDate}`);
    }
    if (companyInfo.registrationAddress !== originalCompanyInfo.registrationAddress) {
      changes.push(`注册地址: ${originalCompanyInfo.registrationAddress} → ${companyInfo.registrationAddress}`);
    }

    if (changes.length === 0) {
      message.warning('没有检测到任何变更');
      return;
    }

    setLoading(true);
    try {
      // 创建变更记录
      const changeResponse = await fetch('http://localhost:8080/api/company-change-confirmation/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          company_id: parseInt(companyId),
          change_type: 'basic',
          change_content: changes.join('; '),
          old_value: JSON.stringify(originalCompanyInfo),
          new_value: JSON.stringify(companyInfo),
          change_date: changeDate.format('YYYY-MM-DD'),
          operator: '当前用户' // 这里应该从用户上下文获取
        }),
      });

      const changeResult = await changeResponse.json();

      if (changeResult.success) {
        message.success('基础信息变更提交成功，等待确认');
        navigate('/company');
      } else {
        message.error(changeResult.message || '提交失败');
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败');
    } finally {
      setLoading(false);
    }
  };

  // 确认处理
  const handleConfirm = async () => {
    setLoading(true);
    try {
      // 这里应该调用确认API
      message.success('变更确认成功');
      navigate('/company-change-confirmation');
    } catch (error) {
      console.error('确认失败:', error);
      message.error('确认失败');
    } finally {
      setLoading(false);
    }
  };

  // 取消处理
  const handleCancel = () => {
    navigate('/company');
  };

  // 获取变更描述
  const getChangeDescription = () => {
    return changeRecords.map(record => 
      `${record.fieldName}：【${record.oldValue}】→【${record.newValue}】`
    ).join('；');
  };

  // 获取旧值
  const getOldValues = () => {
    return changeRecords.map(record => `${record.fieldName} - ${record.oldValue}`).join(', ');
  };

  // 获取新值
  const getNewValues = () => {
    return changeRecords.map(record => `${record.fieldName} - ${record.newValue}`).join(', ');
  };

  // 文件数据
  const fileData = [
    { key: 1, type: 'C规范', name: 'C规范模板.docx' },
    { key: 2, type: 'D规范', name: 'D规范模板.docx' },
  ];

  // 如果没有公司ID，显示错误信息
  if (!companyId) {
    return (
      <div className="company-change-basic">
        <div className="page-header">
          <h2>股权管理</h2>
        </div>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <h3>访问错误</h3>
          <p>请通过正确的入口访问此页面：</p>
          <ul style={{ textAlign: 'left', display: 'inline-block' }}>
            <li>从公司信息页面点击公司卡片的"变更"按钮，然后选择"变更基础信息"</li>
            <li>从公司详情页面点击右上角的"变更基础信息"按钮</li>
          </ul>
          <div style={{ marginTop: '20px' }}>
            <Button type="primary" onClick={() => navigate('/company')}>
              返回公司信息页面
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="company-change-basic">
      {/* 顶部按钮 */}
      <div className="top-buttons">
        <Button 
          type="primary" 
          className="submit-btn"
          onClick={handleSubmit}
          loading={loading}
          disabled={isConfirmMode}
        >
          提交
        </Button>
        {isConfirmMode && (
          <Button 
            type="primary" 
            className="confirm-btn"
            onClick={handleConfirm}
            loading={loading}
          >
            确认
          </Button>
        )}
        <Button 
          className="cancel-btn"
          onClick={handleCancel}
        >
          取消
        </Button>
      </div>

      {/* 表单区域 */}
      <div className="form-section">


        <div className="form-row">
          <label className="form-label">变更日期</label>
          <DatePicker
            value={changeDate}
            onChange={(date) => date && setChangeDate(date)}
            style={{ width: 300 }}
            disabled={isConfirmMode}
          />
        </div>
      </div>

      {/* 公司信息编辑表单 */}
      {companyInfo && (
        <div className="company-info-section">
          <h3>公司信息</h3>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <div className="form-item">
                <label className="required-label">公司中文名</label>
                <Input
                value={companyInfo.chineseName}
                onChange={(e) => handleCompanyInfoChange('chineseName', e.target.value)}
                placeholder="请输入公司中文名"
                disabled={isConfirmMode}
                />
              </div>
            </Col>
            <Col span={12}>
              <div className="form-item">
                <label className="required-label">公司英文名</label>
                <Input
                value={companyInfo.englishName}
                onChange={(e) => handleCompanyInfoChange('englishName', e.target.value)}
                placeholder="请输入公司英文名"
                disabled={isConfirmMode}
                />
              </div>
            </Col>
            <Col span={12}>
              <div className="form-item">
                <label className="required-label">注册资本（万元）</label>
                <Input
                value={companyInfo.registeredCapital}
                onChange={(e) => handleCompanyInfoChange('registeredCapital', e.target.value)}
                placeholder="请输入注册资本"
                suffix="万元"
                disabled={isConfirmMode}
                />
              </div>
            </Col>
            <Col span={12}>
              <div className="form-item">
                <label className="required-label">设立日期</label>
                <DatePicker
                value={companyInfo.establishmentDate ? dayjs(companyInfo.establishmentDate) : null}
                onChange={(date) => handleCompanyInfoChange('establishmentDate', date ? date.format('YYYY-MM-DD') : '')}
                placeholder="请选择设立日期"
                style={{ width: '100%' }}
                disabled={isConfirmMode}
                />
              </div>
            </Col>
            <Col span={12}>
              <div className="form-item">
                <label>业务板块</label>
                <Select
                value={companyInfo.businessScopeId}
                onChange={(value) => handleCompanyInfoChange('businessScopeId', value)}
                placeholder="请选择业务板块"
                style={{ width: '100%' }}
                disabled={isConfirmMode}
                >
                {businessSegments.map(segment => (
                  <Option key={segment.id} value={segment.id}>{segment.name}</Option>
                ))}
                </Select>
              </div>
            </Col>
            <Col span={12}>
              <div className="form-item">
                <label>地区</label>
                <Input
                value={companyInfo.region}
                onChange={(e) => handleCompanyInfoChange('region', e.target.value)}
                placeholder="请输入地区"
                disabled={isConfirmMode}
                />
              </div>
            </Col>
            <Col span={12}>
              <div className="form-item">
                <label className="required-label">代理机构</label>
                <Select
                value={companyInfo.agentOrganization}
                onChange={(value) => handleCompanyInfoChange('agentOrganization', value)}
                placeholder="请选择代理机构"
                style={{ width: '100%' }}
                disabled={isConfirmMode}
                >
                <Option value="代理机构A">代理机构A</Option>
                <Option value="代理机构B">代理机构B</Option>
                <Option value="代理机构C">代理机构C</Option>
                </Select>
              </div>
            </Col>
            <Col span={12}>
              <div className="form-item">
                <label className="required-label">年审更新</label>
                <Select
                value={companyInfo.annualUpdate}
                onChange={(value) => handleCompanyInfoChange('annualUpdate', value)}
                placeholder="请选择年审更新"
                style={{ width: '100%' }}
                disabled={isConfirmMode}
                >
                <Option value="是">是</Option>
                <Option value="否">否</Option>
                </Select>
              </div>
            </Col>
            <Col span={24}>
              <div className="form-item">
                <label className="required-label">注册地址</label>
                <Input.TextArea
                value={companyInfo.registeredAddress}
                onChange={(e) => handleCompanyInfoChange('registeredAddress', e.target.value)}
                placeholder="请输入注册地址"
                rows={3}
                disabled={isConfirmMode}
                />
              </div>
            </Col>
            <Col span={12}>
              <div className="form-item">
                <label>存续状况</label>
                <Select
                value={companyInfo.operatingStatus}
                onChange={(value) => handleCompanyInfoChange('operatingStatus', value)}
                placeholder="请选择存续状况"
                style={{ width: '100%' }}
                disabled={isConfirmMode}
                >
                <Option value="存续">存续</Option>
                <Option value="注销">注销</Option>
                <Option value="吊销">吊销</Option>
                <Option value="迁出">迁出</Option>
                </Select>
              </div>
            </Col>
          </Row>
        </div>
      )}

      {/* 参考规范 */}
      <div className="reference-section">
        <h3>参考规范</h3>
        <div className="reference-links">
          <div className="reference-item">
            <span>A规范</span>
            <a href="#" className="reference-link">A规范.docx</a>
          </div>
          <div className="reference-item">
            <span>B规范</span>
            <a href="#" className="reference-link">B规范.docx</a>
          </div>
        </div>
      </div>

      {/* 具体变更 */}
      {changeRecords.length > 0 && (
        <div className="change-details-section">
          <h3>具体变更</h3>
          <Table
            columns={changeColumns}
            dataSource={changeRecords.map((record, index) => ({ ...record, key: index }))}
            pagination={false}
            bordered
            size="small"
          />
        </div>
      )}

      {/* 上传文件 */}
      <div className="upload-section">
        <h3>上传文件</h3>
        <Table
          columns={fileColumns}
          dataSource={fileData}
          pagination={false}
          bordered
          size="small"
        />
      </div>
    </div>
  );
};

export default CompanyChangeBasic;
