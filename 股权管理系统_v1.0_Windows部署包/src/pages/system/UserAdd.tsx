import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Space,
  Typography,
  message,
  Checkbox,
  Divider,
  Avatar,
  Upload
} from 'antd';
import {
  UserOutlined,
  ArrowLeftOutlined,
  SaveOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const { Title, Text } = Typography;
const { Option } = Select;

interface UserFormData {
  username: string;
  password: string;
  confirmPassword: string;
  realName: string;
  email: string;
  phone: string;
  department: string;
  position: string;
  role: string;
  permissions: string[];
  status: string;
}

const UserAdd: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 部门选项
  const departments = [
    '管理部', '财务部', '法务部', '投资部', '人力资源部', '信息技术部', '审计部'
  ];

  // 职位选项
  const positions = [
    '总经理', '副总经理', '部门经理', '主管', '专员', '助理', '实习生'
  ];

  // 角色选项
  const roles = [
    { value: 'admin', label: '系统管理员', description: '拥有所有权限' },
    { value: 'manager', label: '管理员', description: '拥有大部分管理权限' },
    { value: 'operator', label: '操作员', description: '拥有基本操作权限' },
    { value: 'viewer', label: '查看员', description: '只能查看数据' }
  ];

  // 权限选项
  const permissions = [
    { value: 'user_management', label: '用户管理', group: '系统管理' },
    { value: 'system_config', label: '系统配置', group: '系统管理' },
    { value: 'company_add', label: '新增公司', group: '公司管理' },
    { value: 'company_edit', label: '编辑公司', group: '公司管理' },
    { value: 'company_delete', label: '删除公司', group: '公司管理' },
    { value: 'company_view', label: '查看公司', group: '公司管理' },
    { value: 'finance_add', label: '新增财务信息', group: '财务管理' },
    { value: 'finance_edit', label: '编辑财务信息', group: '财务管理' },
    { value: 'finance_delete', label: '删除财务信息', group: '财务管理' },
    { value: 'finance_view', label: '查看财务信息', group: '财务管理' },
    { value: 'shareholder_add', label: '新增股东', group: '股东管理' },
    { value: 'shareholder_edit', label: '编辑股东', group: '股东管理' },
    { value: 'shareholder_delete', label: '删除股东', group: '股东管理' },
    { value: 'shareholder_view', label: '查看股东', group: '股东管理' },
    { value: 'task_add', label: '新增任务', group: '任务管理' },
    { value: 'task_edit', label: '编辑任务', group: '任务管理' },
    { value: 'task_delete', label: '删除任务', group: '任务管理' },
    { value: 'task_view', label: '查看任务', group: '任务管理' },
    { value: 'archive_add', label: '新增档案规范', group: '档案管理' },
    { value: 'archive_edit', label: '编辑档案规范', group: '档案管理' },
    { value: 'archive_delete', label: '删除档案规范', group: '档案管理' },
    { value: 'archive_view', label: '查看档案规范', group: '档案管理' }
  ];

  // 根据角色设置默认权限
  const handleRoleChange = (role: string) => {
    let defaultPermissions: string[] = [];

    switch (role) {
      case 'admin':
        defaultPermissions = permissions.map(p => p.value);
        break;
      case 'manager':
        defaultPermissions = permissions.filter(p =>
          !p.value.includes('delete') && !p.value.includes('user_management')
        ).map(p => p.value);
        break;
      case 'operator':
        defaultPermissions = permissions.filter(p =>
          p.value.includes('add') || p.value.includes('edit') || p.value.includes('view')
        ).map(p => p.value);
        break;
      case 'viewer':
        defaultPermissions = permissions.filter(p =>
          p.value.includes('view')
        ).map(p => p.value);
        break;
    }

    form.setFieldsValue({ permissions: defaultPermissions });
  };

  // 提交表单
  const handleSubmit = async (values: UserFormData) => {
    try {
      setLoading(true);

      // 验证密码
      if (values.password !== values.confirmPassword) {
        message.error('两次输入的密码不一致');
        return;
      }

      // 准备提交数据
      const submitData = {
        username: values.username,
        password: values.password,
        realName: values.realName,
        email: values.email,
        phone: values.phone,
        department: values.department,
        position: values.position,
        role: values.role,
        permissions: values.permissions,
        status: values.status || 'active',
        createdBy: 'Admin User' // TODO: 从当前登录用户获取
      };

      console.log('📤 提交新增用户数据:', submitData);

      // 调用后端API
      const response = await axios.post('http://localhost:8080/api/users', submitData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      if (response.data.success) {
        message.success('用户创建成功！');
        navigate('/system/user/management');
      } else {
        throw new Error(response.data.message || '创建用户失败');
      }
    } catch (error: any) {
      console.error('❌ 创建用户失败:', error);
      if (error.response?.status === 409) {
        message.error('用户名已存在，请使用其他用户名');
      } else {
        message.error(`创建用户失败: ${error.message || '请稍后重试'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  // 按权限组分组显示
  const groupedPermissions = permissions.reduce((groups, permission) => {
    const group = permission.group;
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(permission);
    return groups;
  }, {} as Record<string, typeof permissions>);

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/system/user/management')}
            style={{ marginRight: 12 }}
          >
            返回
          </Button>
          <div>
            <Title level={3} style={{ margin: 0 }}>
              新增用户
            </Title>
            <Text type="secondary">
              添加新的系统用户并分配相应权限
            </Text>
          </div>
        </div>
      </div>

      {/* 主表单 */}
      <Card style={{ maxWidth: 1000, margin: '0 auto' }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            status: 'active',
            role: 'viewer'
          }}
        >
          {/* 基本信息 */}
          <Title level={4}>基本信息</Title>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="用户名"
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' },
                  { max: 20, message: '用户名最多20个字符' },
                  { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
                ]}
              >
                <Input placeholder="请输入用户名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="真实姓名"
                name="realName"
                rules={[{ required: true, message: '请输入真实姓名' }]}
              >
                <Input placeholder="请输入真实姓名" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="密码"
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' }
                ]}
              >
                <Input.Password placeholder="请输入密码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="确认密码"
                name="confirmPassword"
                rules={[
                  { required: true, message: '请确认密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
              >
                <Input.Password placeholder="请再次输入密码" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="邮箱"
                name="email"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="手机号"
                name="phone"
                rules={[
                  { required: true, message: '请输入手机号' },
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                ]}
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          {/* 职位信息 */}
          <Title level={4}>职位信息</Title>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="部门"
                name="department"
                rules={[{ required: true, message: '请选择部门' }]}
              >
                <Select placeholder="请选择部门">
                  {departments.map(dept => (
                    <Option key={dept} value={dept}>{dept}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="职位"
                name="position"
                rules={[{ required: true, message: '请选择职位' }]}
              >
                <Select placeholder="请选择职位">
                  {positions.map(pos => (
                    <Option key={pos} value={pos}>{pos}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          {/* 权限设置 */}
          <Title level={4}>权限设置</Title>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="用户角色"
                name="role"
                rules={[{ required: true, message: '请选择用户角色' }]}
              >
                <Select placeholder="请选择用户角色" onChange={handleRoleChange}>
                  {roles.map(role => (
                    <Option key={role.value} value={role.value}>
                      <div>
                        <div>{role.label}</div>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {role.description}
                        </Text>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="账户状态"
                name="status"
                rules={[{ required: true, message: '请选择账户状态' }]}
              >
                <Select placeholder="请选择账户状态">
                  <Option value="active">启用</Option>
                  <Option value="inactive">禁用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* 详细权限 */}
          <Form.Item
            label="详细权限"
            name="permissions"
            rules={[{ required: true, message: '请至少选择一个权限' }]}
          >
            <div style={{ border: '1px solid #d9d9d9', borderRadius: 6, padding: 16 }}>
              {Object.entries(groupedPermissions).map(([group, perms]) => (
                <div key={group} style={{ marginBottom: 16 }}>
                  <Text strong style={{ display: 'block', marginBottom: 8 }}>
                    {group}
                  </Text>
                  <Checkbox.Group style={{ width: '100%' }}>
                    <Row>
                      {perms.map(perm => (
                        <Col span={8} key={perm.value} style={{ marginBottom: 8 }}>
                          <Checkbox value={perm.value}>
                            {perm.label}
                          </Checkbox>
                        </Col>
                      ))}
                    </Row>
                  </Checkbox.Group>
                </div>
              ))}
            </div>
          </Form.Item>

          {/* 操作按钮 */}
          <Row justify="center" style={{ marginTop: 32 }}>
            <Space size="large">
              <Button
                size="large"
                onClick={() => navigate('/system/user/management')}
              >
                取消
              </Button>
              <Button
                type="primary"
                size="large"
                htmlType="submit"
                loading={loading}
                icon={<SaveOutlined />}
              >
                创建用户
              </Button>
            </Space>
          </Row>
        </Form>
      </Card>
    </div>
  );
};

export default UserAdd;
