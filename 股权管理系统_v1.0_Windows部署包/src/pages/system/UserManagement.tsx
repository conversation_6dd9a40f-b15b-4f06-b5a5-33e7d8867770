import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Input,
  Select,
  Space,
  Card,
  Row,
  Col,
  message,
  Modal,
  Typography,
  Pagination,
  Tag,
  Avatar,
  Tooltip,
  Switch,
  Dropdown,
  Menu
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined,
  UserOutlined,
  MoreOutlined,
  KeyOutlined,
  LockOutlined,
  UnlockOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { apiService } from '../../services/api-simple';
import dayjs from 'dayjs';

const { Option } = Select;
const { Title } = Typography;
const { confirm } = Modal;

interface User {
  id: number;
  username: string;
  realName: string;
  email: string;
  phone: string;
  department: string;
  position: string;
  role: string;
  status: string;
  lastLoginTime: string;
  createdAt: string;
  createdBy: string;
}

const UserManagement: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<User[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 查询条件
  const [searchForm, setSearchForm] = useState({
    username: '',
    realName: '',
    department: '',
    role: '',
    status: ''
  });
  const [error, setError] = useState<string | null>(null);

  // 部门选项
  const departments = [
    '管理部', '财务部', '法务部', '投资部', '人力资源部', '信息技术部', '审计部'
  ];

  // 角色选项
  const roles = [
    { value: 'admin', label: '系统管理员', color: 'red' },
    { value: 'manager', label: '管理员', color: 'orange' },
    { value: 'operator', label: '操作员', color: 'blue' },
    { value: 'viewer', label: '查看员', color: 'green' }
  ];

  // 加载数据
  const loadData = async (page = 1, size = 10, searchParams = searchForm) => {
    try {
      setLoading(true);
      console.log('📊 加载用户数据...', { page, size, searchParams });

      const params: any = {
        page,
        pageSize: size
      };

      // 只传递非空的查询参数
      Object.keys(searchParams).forEach(key => {
        if (searchParams[key as keyof typeof searchParams]) {
          params[key] = searchParams[key as keyof typeof searchParams];
        }
      });

      console.log('📤 发送请求参数:', params);

      const response = await apiService.getUsers(params);

      console.log('📥 收到响应:', response.data);

      if (response.data.success) {
        const data = response.data.data || [];
        setDataSource(data);
        setTotal(response.data.total || 0);
        console.log('✅ 加载用户数据成功:', {
          count: data.length,
          total: response.data.total,
          data: data
        });
      } else {
        throw new Error(response.data.message || '加载数据失败');
      }
    } catch (error: any) {
      console.error('❌ 加载用户数据失败:', error);
      const errorMessage = `加载用户数据失败: ${error.message || '请稍后重试'}`;
      setError(errorMessage);
      message.error(errorMessage);
      setDataSource([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 重试功能
  const handleRetry = () => {
    setError(null);
    loadData();
  };

  // 初始加载
  useEffect(() => {
    console.log('🚀 用户管理页面初始化，开始加载数据...');
    loadData();
  }, []);

  // 查询
  const handleSearch = () => {
    setCurrentPage(1);
    loadData(1, pageSize, searchForm);
  };

  // 重置
  const handleReset = () => {
    const resetForm = {
      username: '',
      realName: '',
      department: '',
      role: '',
      status: ''
    };
    setSearchForm(resetForm);
    setCurrentPage(1);
    loadData(1, pageSize, resetForm);
  };

  // 新增用户
  const handleAdd = () => {
    navigate('/system/user/add');
  };

  // 编辑用户
  const handleEdit = (record: User) => {
    navigate(`/system/user/edit/${record.id}`);
  };

  // 删除用户
  const handleDelete = (record: User) => {
    confirm({
      title: '确认删除',
      content: `确定要删除用户"${record.realName}(${record.username})"吗？删除后无法恢复。`,
      okText: '确定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const response = await apiService.deleteUser(record.id);
          if (response.data.success) {
            message.success('删除成功');
            loadData(currentPage, pageSize, searchForm);
          } else {
            throw new Error(response.data.message || '删除失败');
          }
        } catch (error: any) {
          console.error('删除失败:', error);
          message.error('删除失败，请稍后重试');
        }
      }
    });
  };

  // 切换用户状态
  const handleStatusChange = async (record: User, checked: boolean) => {
    try {
      const newStatus = checked ? 'active' : 'inactive';
      const response = await apiService.updateUserStatus(record.id, newStatus);

      if (response.data.success) {
        message.success(`用户已${checked ? '启用' : '禁用'}`);
        loadData(currentPage, pageSize, searchForm);
      } else {
        throw new Error(response.data.message || '状态更新失败');
      }
    } catch (error: any) {
      console.error('状态更新失败:', error);
      message.error('状态更新失败，请稍后重试');
    }
  };

  // 重置密码
  const handleResetPassword = (record: User) => {
    confirm({
      title: '确认重置密码',
      content: `确定要重置用户"${record.realName}(${record.username})"的密码吗？新密码将发送到用户邮箱。`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await apiService.resetUserPassword(record.id);
          if (response.data.success) {
            message.success('密码重置成功，新密码已发送到用户邮箱');
          } else {
            throw new Error(response.data.message || '密码重置失败');
          }
        } catch (error: any) {
          console.error('密码重置失败:', error);
          message.error('密码重置失败，请稍后重试');
        }
      }
    });
  };

  // 分页变化
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) setPageSize(size);
    loadData(page, size || pageSize, searchForm);
  };

  // 获取角色标签颜色
  const getRoleColor = (role: string) => {
    const roleConfig = roles.find(r => r.value === role);
    return roleConfig?.color || 'default';
  };

  // 获取角色标签文本
  const getRoleLabel = (role: string) => {
    const roleConfig = roles.find(r => r.value === role);
    return roleConfig?.label || role;
  };

  // 操作菜单
  const getActionMenu = (record: User) => (
    <Menu>
      <Menu.Item key="edit" icon={<EditOutlined />} onClick={() => handleEdit(record)}>
        编辑用户
      </Menu.Item>
      <Menu.Item key="reset" icon={<KeyOutlined />} onClick={() => handleResetPassword(record)}>
        重置密码
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item
        key="delete"
        icon={<DeleteOutlined />}
        danger
        onClick={() => handleDelete(record)}
      >
        删除用户
      </Menu.Item>
    </Menu>
  );

  // 表格列定义
  const columns = [
    {
      title: '用户信息',
      key: 'userInfo',
      width: 200,
      align: 'center',
      render: (_: any, record: User) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar icon={<UserOutlined />} style={{ marginRight: 12 }} />
          <div>
            <div style={{ fontWeight: 500 }}>{record.realName}</div>
            <div style={{ fontSize: 12, color: '#666' }}>@{record.username}</div>
          </div>
        </div>
      )
    },
    {
      title: '联系方式',
      key: 'contact',
      width: 180,
      align: 'center',
      render: (_: any, record: User) => (
        <div>
          <div style={{ marginBottom: 4 }}>{record.email}</div>
          <div style={{ fontSize: 12, color: '#666' }}>{record.phone}</div>
        </div>
      )
    },
    {
      title: '部门/职位',
      key: 'position',
      width: 150,
      align: 'center',
      render: (_: any, record: User) => (
        <div>
          <div>{record.department}</div>
          <div style={{ fontSize: 12, color: '#666' }}>{record.position}</div>
        </div>
      )
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 120,
      align: 'center',
      render: (role: string) => (
        <Tag color={getRoleColor(role)}>
          {getRoleLabel(role)}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: 'center',
      render: (status: string, record: User) => (
        <Switch
          checked={status === 'active'}
          onChange={(checked) => handleStatusChange(record, checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      )
    },
    {
      title: '最后登录',
      dataIndex: 'lastLoginTime',
      key: 'lastLoginTime',
      width: 150,
      align: 'center',
      render: (text: string) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '从未登录'
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 150,
      align: 'center',
      render: (text: string) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      align: 'center',
      fixed: 'right' as const,
      render: (_: any, record: User) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          <Dropdown overlay={getActionMenu(record)} trigger={['click']}>
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <div>
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <h3>用户数据加载失败</h3>
            <p style={{ color: '#ff4d4f', marginBottom: '16px' }}>{error}</p>
            <Space>
              <Button type="primary" onClick={handleRetry}>
                重试
              </Button>
            </Space>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={3} style={{ margin: 0 }}>
            用户管理
          </Title>
        </div>

        {/* 查询条件 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={4}>
              <Input
                placeholder="用户名"
                value={searchForm.username}
                onChange={(e) => setSearchForm({ ...searchForm, username: e.target.value })}
              />
            </Col>
            <Col span={4}>
              <Input
                placeholder="真实姓名"
                value={searchForm.realName}
                onChange={(e) => setSearchForm({ ...searchForm, realName: e.target.value })}
              />
            </Col>
            <Col span={4}>
              <Select
                placeholder="部门"
                value={searchForm.department || undefined}
                onChange={(value) => setSearchForm({ ...searchForm, department: value || '' })}
                style={{ width: '100%' }}
                allowClear
              >
                {departments.map(dept => (
                  <Option key={dept} value={dept}>{dept}</Option>
                ))}
              </Select>
            </Col>
            <Col span={4}>
              <Select
                placeholder="角色"
                value={searchForm.role || undefined}
                onChange={(value) => setSearchForm({ ...searchForm, role: value || '' })}
                style={{ width: '100%' }}
                allowClear
              >
                {roles.map(role => (
                  <Option key={role.value} value={role.value}>{role.label}</Option>
                ))}
              </Select>
            </Col>
            <Col span={4}>
              <Select
                placeholder="状态"
                value={searchForm.status || undefined}
                onChange={(value) => setSearchForm({ ...searchForm, status: value || '' })}
                style={{ width: '100%' }}
                allowClear
              >
                <Option value="active">启用</Option>
                <Option value="inactive">禁用</Option>
              </Select>
            </Col>
          </Row>
        </Card>

        {/* 操作按钮 */}
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type="primary"
              icon={<SearchOutlined />}
              onClick={handleSearch}
            >
              查询
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={handleReset}
            >
              重置
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增用户
            </Button>
          </Space>
        </div>

        {/* 数据表格 */}
        <Table
          columns={columns}
          dataSource={dataSource}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
          locale={{
            emptyText: '暂无用户数据'
          }}
        />

        {/* 分页 */}
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => `共 ${total} 条，第 ${range[0]}-${range[1]} 条`}
            onChange={handlePageChange}
            onShowSizeChange={handlePageChange}
          />
        </div>
      </Card>
    </div>
  );
};

export default UserManagement;
