import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Table, Form, message, Space, Tabs, Checkbox, Modal } from 'antd';
import { apiService } from '../../services/api-simple';
import './DataDictionary.css';

interface PositionItem {
  id: string;
  name: string;
  sort: number;
  updater: string;
  updateTime: string;
  isEditing?: boolean;
  isNew?: boolean;
}

interface PaymentMethodItem {
  id: string;
  name: string;
  sort: number;
  updater: string;
  updateTime: string;
  isEditing?: boolean;
  isNew?: boolean;
}

const DataDictionary: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('positions');
  const [positionData, setPositionData] = useState<PositionItem[]>([]);
  const [paymentMethodData, setPaymentMethodData] = useState<PaymentMethodItem[]>([]);
  const [positionError, setPositionError] = useState<string | null>(null);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [searchForm, setSearchForm] = useState({
    name: ''
  });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [form] = Form.useForm();



  // 获取数据
  useEffect(() => {
    fetchData();
  }, [activeTab]);

  const fetchData = async () => {
    try {
      setLoading(true);
      if (activeTab === 'positions') {
        setPositionError(null);
        // 从数据库获取任职职位数据
        const response = await apiService.getPositions();
        if (response.data.success) {
          setPositionData(response.data.data || []);
          console.log('✅ 职位数据加载成功，共', response.data.data?.length || 0, '条记录');
        } else {
          throw new Error(response.data.message || '职位数据API返回失败');
        }
      } else {
        setPaymentError(null);
        // 从数据库获取实缴出资方式数据
        const response = await apiService.getPaymentMethods();
        if (response.data.success) {
          setPaymentMethodData(response.data.data || []);
          console.log('✅ 出资方式数据加载成功，共', response.data.data?.length || 0, '条记录');
        } else {
          throw new Error(response.data.message || '出资方式数据API返回失败');
        }
      }
    } catch (error) {
      console.error('获取数据失败:', error);
      const errorMessage = `获取数据失败: ${error.message}`;

      if (activeTab === 'positions') {
        setPositionError(errorMessage);
        setPositionData([]);
      } else {
        setPaymentError(errorMessage);
        setPaymentMethodData([]);
      }
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索表单变化
  const handleSearchFormChange = (field: string, value: string) => {
    setSearchForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 处理查询
  const handleQuery = async () => {
    try {
      setLoading(true);
      const params = searchForm.name ? { name: searchForm.name } : {};

      let response;
      if (activeTab === 'positions') {
        setPositionError(null);
        response = await apiService.searchPositions(params);
      } else {
        setPaymentError(null);
        response = await apiService.searchPaymentMethods(params);
      }

      if (response.data.success) {
        if (activeTab === 'positions') {
          setPositionData(response.data.data || []);
          console.log(`查询到 ${response.data.data?.length || 0} 条职位数据`);
        } else {
          setPaymentMethodData(response.data.data || []);
          console.log(`查询到 ${response.data.data?.length || 0} 条出资方式数据`);
        }
      } else {
        throw new Error(response.data.message || '查询API返回失败');
      }
    } catch (error) {
      console.error('查询失败:', error);
      const errorMessage = `查询失败: ${error.message}`;

      if (activeTab === 'positions') {
        setPositionError(errorMessage);
        setPositionData([]);
      } else {
        setPaymentError(errorMessage);
        setPaymentMethodData([]);
      }
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // 处理重置
  const handleReset = () => {
    setSearchForm({
      name: ''
    });
    // 重新从数据库获取所有数据
    fetchData();
  };

  // 处理新增
  const handleAdd = () => {
    const newRecord = {
      id: 'new_' + Date.now(),
      name: '',
      sort: 0,
      updater: '',
      updateTime: '',
      isEditing: true,
      isNew: true
    };
    
    if (activeTab === 'positions') {
      setPositionData([newRecord, ...positionData]);
    } else {
      setPaymentMethodData([newRecord, ...paymentMethodData]);
    }
  };

  // 处理编辑
  const handleEdit = (record: PositionItem | PaymentMethodItem) => {
    if (activeTab === 'positions') {
      const newData = positionData.map(item => 
        item.id === record.id 
          ? { ...item, isEditing: true }
          : { ...item, isEditing: false }
      );
      setPositionData(newData);
    } else {
      const newData = paymentMethodData.map(item => 
        item.id === record.id 
          ? { ...item, isEditing: true }
          : { ...item, isEditing: false }
      );
      setPaymentMethodData(newData);
    }
  };

  // 处理保存
  const handleSave = async (record: PositionItem | PaymentMethodItem) => {
    try {
      // 获取表单数据
      const formData = form.getFieldsValue();
      const updatedRecord = {
        ...record,
        name: formData[`name_${record.id}`] || record.name,
      };

      // 数据校验
      if (!updatedRecord.name) {
        message.error('请填写名称');
        return;
      }

      const endpoint = activeTab === 'positions' ? '/api/positions' : '/api/payment-methods';
      const currentData = activeTab === 'positions' ? positionData : paymentMethodData;

      if (record.isNew) {
        // 新增模式
        try {
          let response;
          if (activeTab === 'positions') {
            response = await apiService.createPosition({ name: updatedRecord.name });
          } else {
            response = await apiService.createPaymentMethod({ name: updatedRecord.name });
          }

          if (response.data.success) {
            message.success('创建成功');
            fetchData(); // 重新获取数据
          } else {
            message.error('创建失败');
            return;
          }
        } catch (error) {
          console.error('创建失败:', error);
          // API失败时使用本地新增
          const newRecord = {
            id: Date.now().toString(),
            ...updatedRecord,
            sort: currentData.length + 1,
            updater: 'Current User',
            updateTime: new Date().toLocaleString(),
            isEditing: false,
            isNew: false
          };
          if (activeTab === 'positions') {
            const newData = positionData.map(item => 
              item.id === record.id ? newRecord : item
            );
            setPositionData(newData);
          } else {
            const newData = paymentMethodData.map(item => 
              item.id === record.id ? newRecord : item
            );
            setPaymentMethodData(newData);
          }
          message.warning('数据库保存失败，仅本地添加');
        }
      } else {
        // 编辑模式
        try {
          let response;
          if (activeTab === 'positions') {
            response = await apiService.updatePosition(record.id, { name: updatedRecord.name });
          } else {
            response = await apiService.updatePaymentMethod(record.id, { name: updatedRecord.name });
          }

          if (response.data.success) {
            message.success('更新成功');
            fetchData(); // 重新获取数据
          } else {
            message.error('更新失败');
            return;
          }
        } catch (error) {
          console.error('更新失败:', error);
          // API失败时使用本地更新
          if (activeTab === 'positions') {
            const newData = positionData.map(item => 
              item.id === record.id 
                ? { ...updatedRecord, isEditing: false, updater: 'Current User', updateTime: new Date().toLocaleString() }
                : item
            );
            setPositionData(newData);
          } else {
            const newData = paymentMethodData.map(item => 
              item.id === record.id 
                ? { ...updatedRecord, isEditing: false, updater: 'Current User', updateTime: new Date().toLocaleString() }
                : item
            );
            setPaymentMethodData(newData);
          }
          message.warning('数据库更新失败，仅本地更新');
        }
      }
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  // 处理取消
  const handleCancel = (record: PositionItem | PaymentMethodItem) => {
    if (record.isNew) {
      // 如果是新增记录，直接删除
      if (activeTab === 'positions') {
        const newData = positionData.filter(item => item.id !== record.id);
        setPositionData(newData);
      } else {
        const newData = paymentMethodData.filter(item => item.id !== record.id);
        setPaymentMethodData(newData);
      }
    } else {
      // 如果是编辑记录，取消编辑状态
      if (activeTab === 'positions') {
        const newData = positionData.map(item => 
          item.id === record.id 
            ? { ...item, isEditing: false }
            : item
        );
        setPositionData(newData);
      } else {
        const newData = paymentMethodData.map(item => 
          item.id === record.id 
            ? { ...item, isEditing: false }
            : item
        );
        setPaymentMethodData(newData);
      }
    }
  };

  // 处理删除
  const handleDelete = (record: PositionItem | PaymentMethodItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除"${record.name}"吗？此操作不可逆。`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      async onOk() {
        try {
          let response;
          if (activeTab === 'positions') {
            response = await apiService.deletePosition(record.id);
          } else {
            response = await apiService.deletePaymentMethod(record.id);
          }

          if (response.data.success) {
            message.success('删除成功');
            fetchData(); // 重新获取数据
          } else {
            message.error('删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          // API失败时使用本地删除
          if (activeTab === 'positions') {
            const newData = positionData.filter(item => item.id !== record.id);
            setPositionData(newData);
          } else {
            const newData = paymentMethodData.filter(item => item.id !== record.id);
            setPaymentMethodData(newData);
          }
          message.warning('数据库删除失败，仅本地删除');
        }
      },
    });
  };

  // 重试功能
  const handleRetry = () => {
    if (activeTab === 'positions') {
      setPositionError(null);
    } else {
      setPaymentError(null);
    }
    fetchData();
  };

  // 获取当前错误状态
  const currentError = activeTab === 'positions' ? positionError : paymentError;

  // 如果有错误，显示错误状态
  if (currentError) {
    return (
      <div className="data-dictionary-container">
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <h3>数据加载失败</h3>
            <p style={{ color: '#ff4d4f', marginBottom: '16px' }}>{currentError}</p>
            <Space>
              <Button type="primary" onClick={handleRetry}>
                重试
              </Button>
            </Space>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="data-dictionary-container">
      <Form form={form}>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          items={[
            {
              key: 'positions',
              label: '任职职位',
              children: null
            },
            {
              key: 'paymentMethods',
              label: '实缴出资方式',
              children: null
            }
          ]}
        />
        
        {/* 查询条件 */}
        <Card className="search-card">
          <div className="search-row">
            <div className="search-item">
              <span className="search-label">名称</span>
              <div className="search-input-group">
                <Input
                  placeholder="请输入"
                  value={searchForm.name}
                  onChange={(e) => handleSearchFormChange('name', e.target.value)}
                  style={{ width: 200 }}
                />
                <Space style={{ marginLeft: 16 }}>
                  <Button type="primary" onClick={handleQuery}>
                    查询
                  </Button>
                  <Button onClick={handleReset}>
                    重置
                  </Button>
                </Space>
              </div>
            </div>
          </div>
        </Card>

        {/* 新增按钮 */}
        <Card className="add-card">
          <Button type="primary" onClick={handleAdd}>
            新增
          </Button>
        </Card>

        {/* 数据表格 */}
        <Card>
          <Table
            columns={getColumns()}
            dataSource={getSortedData()}
            loading={loading}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showTotal: (total) => `共 ${total} 条记录`,
              showSizeChanger: true,
              showQuickJumper: true,
            }}
            locale={{ emptyText: '暂无数据' }}
          />
        </Card>
      </Form>
    </div>
  );

  // 获取表格列配置
  function getColumns() {
    return [
      {
        title: activeTab === 'positions' ? '职位名称' : '实缴出资方式',
        dataIndex: 'name',
        key: 'name',
        width: 200,
        align: 'center',
        render: (_: any, record: PositionItem | PaymentMethodItem) => {
          if (record.isEditing) {
            return (
              <Form.Item
                name={`name_${record.id}`}
                initialValue={record.name}
                style={{ margin: 0 }}
                rules={[{ required: true, message: '请输入名称' }]}
              >
                <Input placeholder="请输入名称" />
              </Form.Item>
            );
          }
          return record.name;
        },
      },
      {
        title: '更新人',
        dataIndex: 'updater',
        key: 'updater',
        width: 120,
        align: 'center',
      },
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
        width: 150,
        align: 'center',
      },
      {
        title: '操作',
        key: 'action',
        width: 150,
        align: 'center',
        render: (_: any, record: PositionItem | PaymentMethodItem) => {
          if (record.isEditing) {
            return (
              <Space size="middle">
                <Button
                  type="link"
                  onClick={() => handleSave(record)}
                >
                  确定
                </Button>
                <Button
                  type="link"
                  onClick={() => handleCancel(record)}
                >
                  取消
                </Button>
              </Space>
            );
          }
          return (
            <Space size="middle">
              <Button
                type="link"
                onClick={() => handleEdit(record)}
              >
                编辑
              </Button>
              <Button
                type="link"
                danger
                onClick={() => handleDelete(record)}
              >
                删除
              </Button>
            </Space>
          );
        },
      },
    ];
  }

  // 获取排序后的数据
  function getSortedData() {
    const currentData = activeTab === 'positions' ? positionData : paymentMethodData;
    return [...currentData].sort((a, b) => {
      // 新增的记录排在最前面
      if (a.isNew && !b.isNew) return -1;
      if (!a.isNew && b.isNew) return 1;

      // 按更新时间排序，最新的在前面
      const timeA = new Date(a.updateTime).getTime();
      const timeB = new Date(b.updateTime).getTime();
      return timeB - timeA;
    });
  }
};

export default DataDictionary;
