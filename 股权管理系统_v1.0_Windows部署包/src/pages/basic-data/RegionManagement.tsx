import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Select, Table, Modal, Form, message, Space } from 'antd';
import { apiService } from '../../services/api-simple';
import './RegionManagement.css';

const { confirm } = Modal;

interface RegionItem {
  id: string;
  type: string;
  region: string;
  createTime: string;
}

const RegionManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [regionData, setRegionData] = useState<RegionItem[]>([]);
  const [searchForm, setSearchForm] = useState({
    region: '',
    type: ''
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<RegionItem | null>(null);
  const [form] = Form.useForm();

  // 国内地区列表
  const domesticRegions = [
    '北京', '上海', '天津', '重庆',
    // 广东省
    '广州', '深圳', '珠海', '汕头', '佛山', '韶关', '湛江', '肇庆', '江门', '茂名', '惠州', '梅州', '汕尾', '河源', '阳江', '清远', '东莞', '中山', '潮州', '揭阳', '云浮',
    // 江苏省
    '南京', '无锡', '徐州', '常州', '苏州', '南通', '连云港', '淮安', '盐城', '扬州', '镇江', '泰州', '宿迁',
    // 浙江省
    '杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水',
    // 安徽省
    '合肥', '芜湖', '蚌埠', '淮南', '马鞍山', '淮北', '铜陵', '安庆', '黄山', '滁州', '阜阳', '宿州', '六安', '亳州', '池州', '宣城',
    // 福建省
    '福州', '厦门', '莆田', '三明', '泉州', '漳州', '南平', '龙岩', '宁德',
    // 江西省
    '南昌', '景德镇', '萍乡', '九江', '新余', '鹰潭', '赣州', '吉安', '宜春', '抚州', '上饶',
    // 山东省
    '济南', '青岛', '淄博', '枣庄', '东营', '烟台', '潍坊', '济宁', '泰安', '威海', '日照', '临沂', '德州', '聊城', '滨州', '菏泽',
    // 河南省
    '郑州', '开封', '洛阳', '平顶山', '安阳', '鹤壁', '新乡', '焦作', '濮阳', '许昌', '漯河', '三门峡', '南阳', '商丘', '信阳', '周口', '驻马店',
    // 湖北省
    '武汉', '黄石', '十堰', '宜昌', '襄阳', '鄂州', '荆门', '孝感', '荆州', '黄冈', '咸宁', '随州',
    // 湖南省
    '长沙', '株洲', '湘潭', '衡阳', '邵阳', '岳阳', '常德', '张家界', '益阳', '郴州', '永州', '怀化', '娄底',
    // 海南省
    '海口', '三亚', '三沙', '儋州', '五指山', '琼海', '文昌', '万宁', '东方', '定安', '屯昌', '澄迈', '临高', '白沙', '昌江', '乐东', '陵水', '保亭', '琼中',
    // 四川省
    '成都', '自贡', '攀枝花', '泸州', '德阳', '绵阳', '广元', '遂宁', '内江', '乐山', '南充', '眉山', '宜宾', '广安', '达州', '雅安', '巴中', '资阳',
    // 贵州省
    '贵阳', '六盘水', '遵义', '安顺', '毕节', '铜仁',
    // 云南省
    '昆明', '曲靖', '玉溪', '保山', '昭通', '丽江', '普洱', '临沧',
    // 西藏自治区
    '拉萨', '日喀则', '昌都', '林芝', '山南', '那曲', '阿里',
    // 陕西省
    '西安', '铜川', '宝鸡', '咸阳', '渭南', '延安', '汉中', '榆林', '安康', '商洛',
    // 甘肃省
    '兰州', '嘉峪关', '金昌', '白银', '天水', '武威', '张掖', '平凉', '酒泉', '庆阳', '定西', '陇南',
    // 青海省
    '西宁', '海东',
    // 宁夏回族自治区
    '银川', '石嘴山', '吴忠', '固原', '中卫',
    // 新疆维吾尔自治区
    '乌鲁木齐', '克拉玛依', '吐鲁番', '哈密',
    // 内蒙古自治区
    '呼和浩特', '包头', '乌海', '赤峰', '通辽', '鄂尔多斯', '呼伦贝尔', '巴彦淖尔', '乌兰察布',
    // 广西壮族自治区
    '南宁', '柳州', '桂林', '梧州', '北海', '防城港', '钦州', '贵港', '玉林', '百色', '贺州', '河池', '来宾', '崇左',
    // 河北省
    '石家庄', '唐山', '秦皇岛', '邯郸', '邢台', '保定', '张家口', '承德', '沧州', '廊坊', '衡水',
    // 山西省
    '太原', '大同', '阳泉', '长治', '晋城', '朔州', '晋中', '运城', '忻州', '临汾', '吕梁',
    // 辽宁省
    '沈阳', '大连', '鞍山', '抚顺', '本溪', '丹东', '锦州', '营口', '阜新', '辽阳', '盘锦', '铁岭', '朝阳', '葫芦岛',
    // 吉林省
    '长春', '吉林', '四平', '辽源', '通化', '白山', '松原', '白城',
    // 黑龙江省
    '哈尔滨', '齐齐哈尔', '鸡西', '鹤岗', '双鸭山', '大庆', '伊春', '佳木斯', '七台河', '牡丹江', '黑河', '绥化',
    // 省份名称
    '广东', '江苏', '浙江', '安徽', '福建', '江西', '山东', '河南', '湖北', '湖南', '河北', '山西', '辽宁', '吉林', '黑龙江', '内蒙古', '陕西', '甘肃', '青海', '宁夏', '新疆', '四川', '贵州', '云南', '西藏', '海南', '广西'
  ];

  // 海外地区列表
  const overseasRegions = [
    '美国', '加拿大', '英国', '法国', '德国', '意大利', '西班牙', '荷兰', '比利时', '瑞士', '奥地利', '瑞典', '挪威', '丹麦', '芬兰',
    '日本', '韩国', '新加坡', '马来西亚', '泰国', '印度尼西亚', '菲律宾', '越南', '印度', '巴基斯坦', '孟加拉国', '斯里兰卡',
    '澳大利亚', '新西兰',
    '巴西', '阿根廷', '智利', '墨西哥', '秘鲁', '哥伦比亚', '委内瑞拉',
    '南非', '埃及', '尼日利亚', '肯尼亚', '摩洛哥',
    '俄罗斯', '乌克兰', '波兰', '捷克', '匈牙利', '罗马尼亚',
    '土耳其', '以色列', '沙特阿拉伯', '阿联酋', '卡塔尔', '科威特',
    '香港', '澳门', '台湾',
    '纽约', '洛杉矶', '芝加哥', '休斯顿', '费城', '凤凰城', '圣安东尼奥', '圣地亚哥', '达拉斯', '圣何塞',
    '伦敦', '曼彻斯特', '伯明翰', '利兹', '格拉斯哥', '谢菲尔德', '布拉德福德', '爱丁堡', '利物浦', '布里斯托',
    '巴黎', '马赛', '里昂', '图卢兹', '尼斯', '南特', '斯特拉斯堡', '蒙彼利埃', '波尔多', '里尔',
    '柏林', '汉堡', '慕尼黑', '科隆', '法兰克福', '斯图加特', '杜塞尔多夫', '多特蒙德', '埃森', '莱比锡',
    '东京', '横滨', '大阪', '名古屋', '札幌', '神户', '京都', '福冈', '川崎', '埼玉',
    '首尔', '釜山', '仁川', '大邱', '大田', '光州', '蔚山', '水原', '高阳', '城南',
    '悉尼', '墨尔本', '布里斯班', '珀斯', '阿德莱德', '黄金海岸', '纽卡斯尔', '堪培拉', '卧龙岗', '霍巴特'
  ];

  // 数据校验函数
  const validateRegionData = (type: string, region: string): boolean => {
    if (type === '国内') {
      // 如果类型是国内，地区必须是国内城市或省份
      return domesticRegions.some(domesticRegion =>
        domesticRegion.includes(region) || region.includes(domesticRegion)
      );
    } else {
      // 如果类型是其他（海外公司对外投资），地区必须是非中国的国家或城市
      return overseasRegions.some(overseasRegion =>
        overseasRegion.includes(region) || region.includes(overseasRegion)
      );
    }
  };

  // 模拟数据
  const mockData: RegionItem[] = [
    {
      id: '1',
      type: '国内',
      region: '广州',
      createTime: '2023-12-01 10:00:00'
    },
    {
      id: '2',
      type: '海外公司对外投资',
      region: '亚太',
      createTime: '2023-11-15 14:30:00'
    },
    {
      id: '3',
      type: '海外公司对外投资',
      region: '美国',
      createTime: '2023-10-20 09:15:00'
    },
    {
      id: '4',
      type: '国内',
      region: '深圳',
      createTime: '2023-09-10 16:45:00'
    },
    {
      id: '5',
      type: '国内',
      region: '北京',
      createTime: '2023-08-05 11:20:00'
    },
    {
      id: '6',
      type: '国内',
      region: '上海',
      createTime: '2023-07-12 13:10:00'
    }
  ];

  // 获取地区数据
  useEffect(() => {
    fetchRegionData();
  }, []);

  const fetchRegionData = async () => {
    try {
      setLoading(true);
      // 从数据库获取数据
      const response = await apiService.getRegions();
      if (response.data.success) {
        setRegionData(response.data.data);
      } else {
        // 如果API失败，使用模拟数据作为备用
        console.warn('API获取失败，使用模拟数据');
        setRegionData(mockData);
      }
      setLoading(false);
    } catch (error) {
      console.error('获取地区数据失败:', error);
      // API失败时使用模拟数据
      setRegionData(mockData);
      setLoading(false);
      message.warning('连接数据库失败，显示模拟数据');
    }
  };

  // 处理搜索表单变化
  const handleSearchFormChange = (field: string, value: string) => {
    setSearchForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 处理查询
  const handleQuery = async () => {
    try {
      setLoading(true);
      // 从数据库查询数据
      const params = new URLSearchParams();
      if (searchForm.region) params.append('region', searchForm.region);
      if (searchForm.type) params.append('type', searchForm.type);

      const response = await apiService.searchRegions(Object.fromEntries(params));
      if (response.data.success) {
        setRegionData(response.data.data);
        message.success('查询完成');
      } else {
        message.error('查询失败');
      }
      setLoading(false);
    } catch (error) {
      console.error('查询失败:', error);
      // API失败时使用本地过滤
      const filteredData = mockData.filter(item => {
        return (
          (!searchForm.region || item.region.includes(searchForm.region)) &&
          (!searchForm.type || item.type === searchForm.type)
        );
      });
      setRegionData(filteredData);
      setLoading(false);
      message.warning('数据库查询失败，使用本地过滤');
    }
  };

  // 处理重置
  const handleReset = () => {
    setSearchForm({
      region: '',
      type: ''
    });
    // 重新从数据库获取所有数据
    fetchRegionData();
  };

  // 处理新增
  const handleAdd = () => {
    setEditingRecord(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 处理编辑
  const handleEdit = (record: RegionItem) => {
    setEditingRecord(record);
    form.setFieldsValue({
      type: record.type,
      region: record.region
    });
    setIsModalVisible(true);
  };

  // 处理删除
  const handleDelete = (record: RegionItem) => {
    confirm({
      title: '确认删除',
      content: `确定要删除地区"${record.region}"吗？此操作不可逆。`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      async onOk() {
        try {
          // 调用API删除数据
          const response = await apiService.deleteRegion(record.id);
          if (response.data.success) {
            message.success('删除成功');
            fetchRegionData(); // 重新获取数据
          } else {
            message.error('删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          // API失败时使用本地删除
          const newData = regionData.filter(item => item.id !== record.id);
          setRegionData(newData);
          message.warning('数据库删除失败，仅本地删除');
        }
      },
    });
  };

  // 处理模态框确认
  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();

      // 数据校验
      const isValid = validateRegionData(values.type, values.region);
      if (!isValid) {
        if (values.type === '国内') {
          message.error('类型选择为"国内"时，地区必须是中国的城市或省份');
        } else {
          message.error('类型选择为"海外公司对外投资"时，地区必须是非中国的国家或城市');
        }
        return;
      }

      // 检查是否已存在相同的类型和地区组合
      const isDuplicate = regionData.some(item =>
        item.type === values.type &&
        item.region === values.region &&
        (!editingRecord || item.id !== editingRecord.id)
      );

      if (isDuplicate) {
        message.error('该类型和地区的组合已存在');
        return;
      }

      if (editingRecord) {
        // 编辑模式
        try {
          const response = await apiService.updateRegion(editingRecord.id, values);
          if (response.data.success) {
            message.success('更新成功');
            fetchRegionData(); // 重新获取数据
          } else {
            message.error('更新失败');
            return;
          }
        } catch (error) {
          console.error('更新失败:', error);
          // API失败时使用本地更新
          const newData = regionData.map(item =>
            item.id === editingRecord.id
              ? { ...item, ...values }
              : item
          );
          setRegionData(newData);
          message.warning('数据库更新失败，仅本地更新');
        }
      } else {
        // 新增模式
        try {
          const response = await apiService.createRegion(values);
          if (response.data.success) {
            message.success('创建成功');
            fetchRegionData(); // 重新获取数据
          } else {
            message.error('创建失败');
            return;
          }
        } catch (error) {
          console.error('创建失败:', error);
          // API失败时使用本地新增
          const newRecord: RegionItem = {
            id: Date.now().toString(),
            ...values,
            createTime: new Date().toLocaleString()
          };
          setRegionData([newRecord, ...regionData]);
          message.warning('数据库保存失败，仅本地添加');
        }
      }

      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('操作失败:', error);
    }
  };

  // 处理模态框取消
  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  // 过滤和排序数据
  const filteredData = regionData.filter(item => {
    return (
      (!searchForm.region || item.region.includes(searchForm.region)) &&
      (!searchForm.type || item.type === searchForm.type)
    );
  });

  // 按创建时间排序（最近创建的在前面）
  const sortedData = [...filteredData].sort((a, b) => {
    return new Date(b.createTime).getTime() - new Date(a.createTime).getTime();
  });

  // 表格列定义
  const columns = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 200,
      align: 'center',
    },
    {
      title: '地区',
      dataIndex: 'region',
      key: 'region',
      width: 200,
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      align: 'center',
      render: (_: any, record: RegionItem) => (
        <Space size="middle">
          <Button 
            type="link" 
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            danger 
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="region-management-container">
      {/* 查询条件 */}
      <Card className="search-card">
        <div className="search-row">
          <div className="search-item">
            <span className="search-label">类型</span>
            <Select
              placeholder="请选择"
              value={searchForm.type}
              onChange={(value) => handleSearchFormChange('type', value)}
              style={{ width: 200 }}
              allowClear
            >
              <Select.Option value="">全部</Select.Option>
              <Select.Option value="国内">国内</Select.Option>
              <Select.Option value="海外公司对外投资">海外公司对外投资</Select.Option>
            </Select>
          </div>
          <div className="search-item">
            <span className="search-label">地区</span>
            <div className="search-input-group">
              <Input
                placeholder="请输入"
                value={searchForm.region}
                onChange={(e) => handleSearchFormChange('region', e.target.value)}
                style={{ width: 200 }}
              />
              <Space style={{ marginLeft: 16 }}>
                <Button type="primary" onClick={handleQuery}>
                  查询
                </Button>
                <Button onClick={handleReset}>
                  重置
                </Button>
              </Space>
            </div>
          </div>
        </div>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card">
        <Space>
          <Button type="primary" onClick={handleAdd}>
            新增
          </Button>
        </Space>
      </Card>

      {/* 数据表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={sortedData}
          loading={loading}
          rowKey="id"
          pagination={{
            pageSize: 10,
            showTotal: (total) => `共 ${total} 条记录`,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
          locale={{ emptyText: '暂无数据' }}
        />
      </Card>

      {/* 新增/编辑模态框 */}
      <Modal
        title={editingRecord ? '编辑地区' : '新增地区'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          style={{ marginTop: 16 }}
        >
          <Form.Item
            label="类型"
            name="type"
            rules={[{ required: true, message: '请选择类型' }]}
          >
            <Select placeholder="请选择类型">
              <Select.Option value="国内">国内</Select.Option>
              <Select.Option value="海外公司对外投资">海外公司对外投资</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            label="地区"
            name="region"
            rules={[{ required: true, message: '请输入地区名称' }]}
          >
            <Input placeholder="请输入地区名称" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default RegionManagement;
