import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Table, Form, message, Space, Select, Modal } from 'antd';
import { apiService } from '../../services/api-simple';
import './AgencyManagement.css';

interface AgencyItem {
  id: string;
  agencyName: string;
  region: string;
  contactPerson: string;
  contactMethod: string;
  createTime: string;
  isEditing?: boolean;
  isNew?: boolean;
}

interface RegionItem {
  id: string;
  type: string;
  region: string;
}

const AgencyManagement: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [agencyData, setAgencyData] = useState<AgencyItem[]>([]);
  const [regionOptions, setRegionOptions] = useState<RegionItem[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [searchForm, setSearchForm] = useState({
    agencyName: '',
    region: ''
  });
  const [form] = Form.useForm();

  // 获取代理机构数据和地区选项
  useEffect(() => {
    fetchAgencyData();
    fetchRegionOptions();
  }, []);

  const fetchAgencyData = async () => {
    try {
      setLoading(true);
      setError(null);
      // 从数据库获取数据
      const response = await apiService.getAgencies();
      if (response.data.success) {
        setAgencyData(response.data.data || []);
        console.log('✅ 代理机构数据加载成功，共', response.data.data?.length || 0, '条记录');
      } else {
        throw new Error(response.data.message || '代理机构数据API返回失败');
      }
    } catch (error) {
      console.error('获取代理机构数据失败:', error);
      const errorMessage = `获取代理机构数据失败: ${error.message}`;
      setError(errorMessage);
      message.error(errorMessage);
      setAgencyData([]);
    } finally {
      setLoading(false);
    }
  };

  const fetchRegionOptions = async () => {
    try {
      // 从地区表获取选项
      const response = await apiService.getRegions();
      if (response.data.success) {
        setRegionOptions(response.data.data);
      }
    } catch (error) {
      console.error('获取地区选项失败:', error);
      message.warning('获取地区选项失败');
    }
  };

  // 处理搜索表单变化
  const handleSearchFormChange = (field: string, value: string) => {
    setSearchForm(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // 处理查询
  const handleQuery = async () => {
    try {
      setLoading(true);
      setError(null);
      // 从数据库查询数据
      const params = new URLSearchParams();
      if (searchForm.agencyName) params.append('agencyName', searchForm.agencyName);
      if (searchForm.region) params.append('region', searchForm.region);

      const response = await apiService.getAgencies(Object.fromEntries(params));
      if (response.data.success) {
        setAgencyData(response.data.data || []);
        console.log(`查询到 ${response.data.data?.length || 0} 条代理机构数据`);
      } else {
        throw new Error(response.data.message || '查询API返回失败');
      }
    } catch (error) {
      console.error('查询失败:', error);
      const errorMessage = `查询失败: ${error.message}`;
      setError(errorMessage);
      message.error(errorMessage);
      setAgencyData([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理重置
  const handleReset = () => {
    setSearchForm({
      agencyName: '',
      region: ''
    });
    // 重新从数据库获取所有数据
    fetchAgencyData();
  };

  // 处理新增
  const handleAdd = () => {
    const newRecord: AgencyItem = {
      id: 'new_' + Date.now(),
      agencyName: '',
      region: '',
      contactPerson: '',
      contactMethod: '',
      createTime: '',
      isEditing: true,
      isNew: true
    };
    setAgencyData([newRecord, ...agencyData]);
  };

  // 处理编辑
  const handleEdit = (record: AgencyItem) => {
    const newData = agencyData.map(item =>
      item.id === record.id
        ? { ...item, isEditing: true }
        : { ...item, isEditing: false }
    );
    setAgencyData(newData);
  };

  // 处理保存
  const handleSave = async (record: AgencyItem) => {
    try {
      // 获取表单数据
      const formData = form.getFieldsValue();
      const updatedRecord = {
        ...record,
        agencyName: formData[`agencyName_${record.id}`] || record.agencyName,
        region: formData[`region_${record.id}`] || record.region,
        contactPerson: formData[`contactPerson_${record.id}`] || record.contactPerson,
        contactMethod: formData[`contactMethod_${record.id}`] || record.contactMethod,
      };

      // 数据校验
      if (!updatedRecord.agencyName || !updatedRecord.region || !updatedRecord.contactPerson || !updatedRecord.contactMethod) {
        message.error('请填写完整信息');
        return;
      }

      if (record.isNew) {
        // 新增模式
        try {
          const response = await apiService.createAgency({
            agencyName: updatedRecord.agencyName,
            region: updatedRecord.region,
            contactPerson: updatedRecord.contactPerson,
            contactMethod: updatedRecord.contactMethod
          });
          if (response.data.success) {
            message.success('创建成功');
            fetchAgencyData(); // 重新获取数据
          } else {
            message.error('创建失败');
            return;
          }
        } catch (error) {
          console.error('创建失败:', error);
          // API失败时使用本地新增
          const newRecord: AgencyItem = {
            id: Date.now().toString(),
            ...updatedRecord,
            createTime: new Date().toLocaleString(),
            isEditing: false,
            isNew: false
          };
          const newData = agencyData.map(item =>
            item.id === record.id ? newRecord : item
          );
          setAgencyData(newData);
          message.warning('数据库保存失败，仅本地添加');
        }
      } else {
        // 编辑模式
        try {
          const response = await apiService.updateAgency(record.id, {
            agencyName: updatedRecord.agencyName,
            region: updatedRecord.region,
            contactPerson: updatedRecord.contactPerson,
            contactMethod: updatedRecord.contactMethod
          });
          if (response.data.success) {
            message.success('更新成功');
            fetchAgencyData(); // 重新获取数据
          } else {
            message.error('更新失败');
            return;
          }
        } catch (error) {
          console.error('更新失败:', error);
          // API失败时使用本地更新
          const newData = agencyData.map(item =>
            item.id === record.id
              ? { ...updatedRecord, isEditing: false }
              : item
          );
          setAgencyData(newData);
          message.warning('数据库更新失败，仅本地更新');
        }
      }
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  // 处理取消
  const handleCancel = (record: AgencyItem) => {
    if (record.isNew) {
      // 如果是新增记录，直接删除
      const newData = agencyData.filter(item => item.id !== record.id);
      setAgencyData(newData);
    } else {
      // 如果是编辑记录，取消编辑状态
      const newData = agencyData.map(item =>
        item.id === record.id
          ? { ...item, isEditing: false }
          : item
      );
      setAgencyData(newData);
    }
  };

  // 处理删除
  const handleDelete = (record: AgencyItem) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除代理机构"${record.agencyName}"吗？此操作不可逆。`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      async onOk() {
        try {
          // 调用API删除数据
          const response = await apiService.deleteAgency(record.id);
          if (response.data.success) {
            message.success('删除成功');
            fetchAgencyData(); // 重新获取数据
          } else {
            message.error('删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          // API失败时使用本地删除
          const newData = agencyData.filter(item => item.id !== record.id);
          setAgencyData(newData);
          message.warning('数据库删除失败，仅本地删除');
        }
      },
    });
  };



  // 过滤和排序数据
  const filteredData = agencyData.filter(item => {
    return (
      (!searchForm.agencyName || item.agencyName.includes(searchForm.agencyName)) &&
      (!searchForm.region || item.region.includes(searchForm.region))
    );
  });

  // 按创建时间排序（最近创建的在前面）
  const sortedData = [...filteredData].sort((a, b) => {
    if (a.isNew) return -1; // 新增记录排在最前面
    if (b.isNew) return 1;
    return new Date(b.createTime).getTime() - new Date(a.createTime).getTime();
  });

  // 表格列定义
  const columns = [
    {
      title: '代理机构名称',
      dataIndex: 'agencyName',
      key: 'agencyName',
      width: 150,
      align: 'center',
      render: (text: string, record: AgencyItem) => {
        if (record.isEditing) {
          return (
            <Form.Item
              name={`agencyName_${record.id}`}
              initialValue={text}
              style={{ margin: 0 }}
              rules={[{ required: true, message: '请输入代理机构名称' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
          );
        }
        return text;
      },
    },
    {
      title: '地区',
      dataIndex: 'region',
      key: 'region',
      width: 120,
      align: 'center',
      render: (text: string, record: AgencyItem) => {
        if (record.isEditing) {
          return (
            <Form.Item
              name={`region_${record.id}`}
              initialValue={text}
              style={{ margin: 0 }}
              rules={[{ required: true, message: '请选择地区' }]}
            >
              <Select placeholder="请选择" style={{ width: '100%' }}>
                {regionOptions.map(option => (
                  <Select.Option key={option.id} value={option.region}>
                    {option.region}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          );
        }
        return text;
      },
    },
    {
      title: '联系人姓名',
      dataIndex: 'contactPerson',
      key: 'contactPerson',
      width: 120,
      align: 'center',
      render: (text: string, record: AgencyItem) => {
        if (record.isEditing) {
          return (
            <Form.Item
              name={`contactPerson_${record.id}`}
              initialValue={text}
              style={{ margin: 0 }}
              rules={[{ required: true, message: '请输入联系人姓名' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
          );
        }
        return text;
      },
    },
    {
      title: '联系方式',
      dataIndex: 'contactMethod',
      key: 'contactMethod',
      width: 120,
      align: 'center',
      render: (text: string, record: AgencyItem) => {
        if (record.isEditing) {
          return (
            <Form.Item
              name={`contactMethod_${record.id}`}
              initialValue={text}
              style={{ margin: 0 }}
              rules={[{ required: true, message: '请输入联系方式' }]}
            >
              <Input placeholder="请输入" />
            </Form.Item>
          );
        }
        return text;
      },
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      align: 'center',
      render: (_: any, record: AgencyItem) => {
        if (record.isEditing) {
          return (
            <Space size="middle">
              <Button
                type="link"
                onClick={() => handleSave(record)}
              >
                确定
              </Button>
              <Button
                type="link"
                onClick={() => handleCancel(record)}
              >
                取消
              </Button>
            </Space>
          );
        }
        return (
          <Space size="middle">
            <Button
              type="link"
              onClick={() => handleEdit(record)}
            >
              编辑
            </Button>
            <Button
              type="link"
              danger
              onClick={() => handleDelete(record)}
            >
              删除
            </Button>
          </Space>
        );
      },
    },
  ];

  // 重试功能
  const handleRetry = () => {
    setError(null);
    fetchAgencyData();
  };

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <div className="agency-management-container">
        <Card>
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <h3>数据加载失败</h3>
            <p style={{ color: '#ff4d4f', marginBottom: '16px' }}>{error}</p>
            <Space>
              <Button type="primary" onClick={handleRetry}>
                重试
              </Button>
            </Space>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="agency-management-container">
      <Form form={form}>
        {/* 查询条件 */}
        <Card className="search-card">
          <div className="search-row">
            <div className="search-item">
              <span className="search-label">代理机构名称</span>
              <Input
                placeholder="请输入"
                value={searchForm.agencyName}
                onChange={(e) => handleSearchFormChange('agencyName', e.target.value)}
                style={{ width: 200 }}
              />
            </div>
            <div className="search-item">
              <span className="search-label">地区</span>
              <div className="search-input-group">
                <Select
                  placeholder="请选择"
                  value={searchForm.region}
                  onChange={(value) => handleSearchFormChange('region', value)}
                  style={{ width: 200 }}
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {regionOptions.map(option => (
                    <Select.Option key={option.id} value={option.region}>
                      {option.region}
                    </Select.Option>
                  ))}
                </Select>
                <Space style={{ marginLeft: 16 }}>
                  <Button type="primary" onClick={handleQuery}>
                    查询
                  </Button>
                  <Button onClick={handleReset}>
                    重置
                  </Button>
                </Space>
              </div>
            </div>
          </div>
        </Card>

        {/* 操作按钮 */}
        <Card className="action-card">
          <Space>
            <Button type="primary" onClick={handleAdd}>
              新增
            </Button>
          </Space>
        </Card>

        {/* 数据表格 */}
        <Card>
          <Table
            columns={columns}
            dataSource={sortedData}
            loading={loading}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showTotal: (total) => `共 ${total} 条记录`,
              showSizeChanger: true,
              showQuickJumper: true,
            }}
            locale={{ emptyText: '暂无数据' }}
          />
        </Card>
      </Form>
    </div>
  );
};

export default AgencyManagement;
