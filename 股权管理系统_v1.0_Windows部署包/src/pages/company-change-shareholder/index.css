.company-change-shareholder {
  padding: 20px;
  background: #fff;
  min-height: 100vh;
}

/* 顶部按钮样式 */
.top-buttons {
  display: flex;
  gap: 10px;
  margin-bottom: 30px;
}

.submit-btn,
.confirm-btn {
  background: #000;
  border: none;
  color: #fff;
  padding: 8px 24px;
  border-radius: 4px;
  font-size: 14px;
  min-width: 80px;
}

.submit-btn:hover,
.confirm-btn:hover {
  background: #333;
}

.cancel-btn {
  background: #fff;
  border: 1px solid #d9d9d9;
  color: #000;
  padding: 8px 24px;
  border-radius: 4px;
  font-size: 14px;
  min-width: 80px;
}

.cancel-btn:hover {
  border-color: #40a9ff;
  color: #40a9ff;
}

/* 表单区域样式 */
.form-section {
  margin-bottom: 30px;
}

.form-row {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.form-label {
  width: 100px;
  text-align: right;
  margin-right: 20px;
  font-size: 14px;
  color: #333;
}

/* 股东信息编辑区域样式 */
.shareholder-info-section {
  margin-bottom: 30px;
  padding: 20px;
  background: #fafafa;
  border-radius: 6px;
  border: 1px solid #d9d9d9;
}

.shareholder-info-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  color: #333;
}

/* 参考规范样式 */
.reference-section {
  margin-bottom: 30px;
}

.reference-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

.reference-links {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.reference-item {
  display: flex;
  align-items: center;
  gap: 20px;
}

.reference-item span {
  width: 60px;
  font-size: 14px;
  color: #333;
}

.reference-link {
  color: #1890ff;
  text-decoration: none;
  font-size: 14px;
}

.reference-link:hover {
  text-decoration: underline;
}

/* 具体变更样式 */
.change-details-section {
  margin-bottom: 30px;
}

.change-details-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

/* 上传文件样式 */
.upload-section {
  margin-bottom: 30px;
}

.upload-section h3 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 15px;
  color: #333;
}

/* 表格样式调整 */
.ant-table {
  font-size: 14px;
}

.ant-table-thead > tr > th {
  background: #fafafa;
  font-weight: 600;
  text-align: center;
  padding: 12px 8px;
}

.ant-table-tbody > tr > td {
  padding: 12px 8px;
  text-align: center;
}

/* 输入框样式 */
.ant-select {
  font-size: 14px;
}

.ant-select-selector {
  border-radius: 4px;
}

.ant-date-picker {
  font-size: 14px;
  border-radius: 4px;
}

.ant-input {
  font-size: 14px;
  border-radius: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .company-change-shareholder {
    padding: 15px;
  }
  
  .form-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .form-label {
    width: auto;
    text-align: left;
    margin-right: 0;
  }
  
  .top-buttons {
    flex-wrap: wrap;
  }
  
  .reference-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}

/* 禁用状态样式 */
.ant-select-disabled .ant-select-selector,
.ant-date-picker-disabled,
.ant-input-disabled {
  background-color: #f5f5f5;
  color: #999;
}

/* 文件上传按钮样式 */
.ant-btn-link {
  padding: 0;
  height: auto;
  font-size: 14px;
}

/* 表格边框样式 */
.ant-table-bordered .ant-table-container {
  border: 1px solid #d9d9d9;
}

.ant-table-bordered .ant-table-thead > tr > th,
.ant-table-bordered .ant-table-tbody > tr > td {
  border-right: 1px solid #d9d9d9;
}

/* 加载状态样式 */
.ant-btn-loading {
  opacity: 0.7;
}

/* 股东表格特殊样式 */
.shareholder-info-section .ant-table-tbody > tr > td {
  padding: 8px;
  vertical-align: middle;
}

.shareholder-info-section .ant-select,
.shareholder-info-section .ant-input,
.shareholder-info-section .ant-picker {
  border: none;
  box-shadow: none;
  background: transparent;
}

.shareholder-info-section .ant-select:hover,
.shareholder-info-section .ant-input:hover,
.shareholder-info-section .ant-picker:hover {
  border-color: #40a9ff;
  background: #fff;
}

.shareholder-info-section .ant-select-focused,
.shareholder-info-section .ant-input:focus,
.shareholder-info-section .ant-picker-focused {
  border-color: #40a9ff;
  background: #fff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 删除按钮样式 */
.shareholder-info-section .ant-btn-link {
  font-size: 16px;
  padding: 4px;
  height: auto;
  line-height: 1;
}

.shareholder-info-section .ant-btn-link:hover {
  background: rgba(255, 77, 79, 0.1);
  border-radius: 4px;
}

/* 持股比例输入框样式 */
.shareholder-info-section .ant-table-tbody .ant-input {
  text-align: right;
}

/* 表格滚动条样式 */
.ant-table-body::-webkit-scrollbar {
  height: 8px;
}

.ant-table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.ant-table-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
