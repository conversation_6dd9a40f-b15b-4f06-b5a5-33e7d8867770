import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { Button, Select, DatePicker, Table, Upload, message, Input, Row, Col } from 'antd';
import { UploadOutlined, DownloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import './index.css';

const { Option } = Select;

interface CompanyChangeShareholderProps {}

interface ChangeRecord {
  field: string;
  fieldName: string;
  oldValue: string;
  newValue: string;
}

interface CompanyInfo {
  id: number;
  chineseName: string;
  englishName: string;
}

interface Person {
  id: number;
  name: string;
  idType: string;
  idNumber: string;
  maskedIdNumber: string;
  phone: string | null;
  email: string | null;
}

interface ShareholderRecord {
  id?: number;
  shareholderName: string;
  registeredAmount: string;
  percentage: string;
  isProxy: number;
  actualShareholderName: string;
  startDate: string;
}

const CompanyChangeShareholder: React.FC<CompanyChangeShareholderProps> = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  
  // 表单状态
  const [changeDate, setChangeDate] = useState<dayjs.Dayjs>(dayjs());
  const [changeRecords, setChangeRecords] = useState<ChangeRecord[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  // 公司和股东信息状态
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null);
  const [persons, setPersons] = useState<Person[]>([]);
  const [shareholders, setShareholders] = useState<ShareholderRecord[]>([]);
  const [originalShareholders, setOriginalShareholders] = useState<ShareholderRecord[]>([]);

  // 从路由参数获取公司信息，支持URL参数和state参数
  const companyId = location.state?.companyId || searchParams.get('companyId');
  const companyName = location.state?.companyName || '';
  const isConfirmMode = location.state?.isConfirmMode || false;

  // 代持选项
  const proxyOptions = [
    { value: 0, label: '否' },
    { value: 1, label: '是' },
  ];

  // 股东信息表格列配置
  const shareholderColumns = [
    {
      title: '登记股东',
      dataIndex: 'shareholderName',
      key: 'shareholderName',
      width: '15%',
      render: (text: string, record: ShareholderRecord, index: number) => (
        <Input
          value={text}
          onChange={(e) => handleShareholderChange(index, 'shareholderName', e.target.value)}
          placeholder="请输入股东名称"
          disabled={isConfirmMode}
        />
      ),
    },
    {
      title: '认缴出资额（万元人民币）',
      dataIndex: 'registeredAmount',
      key: 'registeredAmount',
      width: '18%',
      render: (text: string, record: ShareholderRecord, index: number) => (
        <Input
          value={text}
          onChange={(e) => handleShareholderChange(index, 'registeredAmount', e.target.value)}
          placeholder="请输入"
          disabled={isConfirmMode}
        />
      ),
    },
    {
      title: '持股比例',
      dataIndex: 'percentage',
      key: 'percentage',
      width: '12%',
      render: (text: string, record: ShareholderRecord, index: number) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Input
            value={text}
            onChange={(e) => handleShareholderChange(index, 'percentage', e.target.value)}
            placeholder="请输入"
            disabled={isConfirmMode}
            style={{ marginRight: '4px' }}
          />
          <span>%</span>
        </div>
      ),
    },
    {
      title: '代持',
      dataIndex: 'isProxy',
      key: 'isProxy',
      width: '10%',
      render: (value: number, record: ShareholderRecord, index: number) => (
        <Select
          value={value}
          onChange={(val) => handleShareholderChange(index, 'isProxy', val)}
          placeholder="请选择"
          style={{ width: '100%' }}
          disabled={isConfirmMode}
        >
          {proxyOptions.map(option => (
            <Option key={option.value} value={option.value}>{option.label}</Option>
          ))}
        </Select>
      ),
    },
    {
      title: '实际股东',
      dataIndex: 'actualShareholderName',
      key: 'actualShareholderName',
      width: '15%',
      render: (text: string, record: ShareholderRecord, index: number) => (
        <Input
          value={text}
          onChange={(e) => handleShareholderChange(index, 'actualShareholderName', e.target.value)}
          placeholder="请输入"
          disabled={isConfirmMode || record.isProxy === 0}
        />
      ),
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      key: 'startDate',
      width: '15%',
      render: (text: string, record: ShareholderRecord, index: number) => (
        <DatePicker
          value={text ? dayjs(text) : null}
          onChange={(date) => handleShareholderChange(index, 'startDate', date ? date.format('YYYY-MM-DD') : '')}
          placeholder="年月日"
          style={{ width: '100%' }}
          disabled={isConfirmMode}
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: '10%',
      render: (_, record: ShareholderRecord, index: number) => (
        <Button
          type="link"
          danger
          onClick={() => handleRemoveShareholder(index)}
          disabled={isConfirmMode}
          style={{ color: '#ff4d4f' }}
        >
          🗑️
        </Button>
      ),
    },
  ];

  useEffect(() => {
    if (companyId) {
      loadInitialData();
    }
  }, [companyId]);

  // 加载初始数据
  const loadInitialData = async () => {
    try {
      // 加载公司信息
      if (companyId) {
        const companyResponse = await fetch(`http://localhost:8080/api/companies/${companyId}`);
        const companyResult = await companyResponse.json();
        if (companyResult.success) {
          setCompanyInfo(companyResult.data);
        }
      }

      // 加载人员列表
      const personsResponse = await fetch('http://localhost:8080/api/persons');
      const personsResult = await personsResponse.json();
      if (personsResult.success) {
        setPersons(personsResult.data);
      }

      // 加载公司现有股东信息
      if (companyId) {
        const shareholdingResponse = await fetch('http://localhost:8080/api/shareholdings');
        const shareholdingResult = await shareholdingResponse.json();
        if (shareholdingResult.success) {
          // 筛选当前公司的在职股东
          const currentShareholders = shareholdingResult.data.filter((record: any) => 
            record.companyId === parseInt(companyId) && 
            record.isActive === 1 && 
            record.endDate === null
          );

          // 转换为页面需要的格式
          const shareholderRecords = currentShareholders.map((record: any) => ({
            id: record.id,
            shareholderName: record.personName,
            registeredAmount: record.investmentAmount,
            percentage: record.percentage,
            isProxy: record.isProxy,
            actualShareholderName: record.actualShareholderName || '',
            startDate: record.startDate ? dayjs(record.startDate).format('YYYY-MM-DD') : ''
          }));

          // 如果有现有股东，显示现有股东；否则添加一行空记录
          if (shareholderRecords.length > 0) {
            setShareholders(shareholderRecords);
            setOriginalShareholders(shareholderRecords); // 保存原始数据
          } else {
            setShareholders([{
              shareholderName: '',
              registeredAmount: '',
              percentage: '',
              isProxy: 0,
              actualShareholderName: '',
              startDate: ''
            }]);
            setOriginalShareholders([]); // 保存原始数据（空数组）
          }
        } else {
          // 如果获取失败，添加一行空记录
          setShareholders([{ 
            shareholderName: '', 
            registeredAmount: '', 
            percentage: '', 
            isProxy: 0, 
            actualShareholderName: '', 
            startDate: '' 
          }]);
        }
      } else {
        // 如果没有公司ID，添加一行空记录
        setShareholders([{ 
          shareholderName: '', 
          registeredAmount: '', 
          percentage: '', 
          isProxy: 0, 
          actualShareholderName: '', 
          startDate: '' 
        }]);
      }

    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    }
  };

  // 处理股东信息变更
  const handleShareholderChange = (index: number, field: string, value: any) => {
    const newShareholders = [...shareholders];
    newShareholders[index] = {
      ...newShareholders[index],
      [field]: value
    };

    // 如果代持选择为"否"，清空实际股东
    if (field === 'isProxy' && value === 0) {
      newShareholders[index].actualShareholderName = '';
    }

    setShareholders(newShareholders);
  };

  // 添加股东记录
  const handleAddShareholder = () => {
    setShareholders([...shareholders, { 
      shareholderName: '', 
      registeredAmount: '', 
      percentage: '', 
      isProxy: 0, 
      actualShareholderName: '', 
      startDate: '' 
    }]);
  };

  // 删除股东记录
  const handleRemoveShareholder = (index: number) => {
    if (shareholders.length > 1) {
      const newShareholders = shareholders.filter((_, i) => i !== index);
      setShareholders(newShareholders);
    }
  };

  // 文件上传处理
  const handleFileUpload = (file: any, type: string) => {
    const newFile = {
      type: type,
      name: file.name,
      file: file,
    };
    
    setUploadedFiles(prev => {
      const filtered = prev.filter(f => f.type !== type);
      return [...filtered, newFile];
    });
    
    message.success(`${file.name} 上传成功`);
    return false; // 阻止自动上传
  };

  // 下载模板
  const handleDownloadTemplate = (type: string) => {
    message.info(`下载 ${type} 模板`);
  };

  // 提交处理
  const handleSubmit = async () => {
    if (!companyInfo) {
      message.error('公司信息加载中，请稍后再试');
      return;
    }

    // 验证股东信息
    const validShareholders = shareholders.filter(sh => 
      sh.shareholderName && sh.registeredAmount && sh.percentage
    );
    if (validShareholders.length === 0) {
      message.error('请至少添加一条有效的股东信息');
      return;
    }

    // 验证持股比例总和不超过100%
    const totalPercentage = validShareholders.reduce((sum, sh) => 
      sum + parseFloat(sh.percentage || '0'), 0
    );
    if (totalPercentage > 100) {
      message.error('持股比例总和不能超过100%');
      return;
    }

    // 生成变更内容描述
    const changes = [];

    // 比较原始数据和当前数据
    const originalMap = new Map(originalShareholders.map(sh => [sh.shareholderName, sh]));
    const currentMap = new Map(validShareholders.map(sh => [sh.shareholderName, sh]));

    // 检查新增和变更
    for (const [shareholderName, shareholder] of currentMap) {
      if (!originalMap.has(shareholderName)) {
        changes.push(`新增股东: ${shareholderName} - ${shareholder.percentage}%${shareholder.isProxy ? ` (代持: ${shareholder.actualShareholderName})` : ''}`);
      } else {
        const original = originalMap.get(shareholderName);
        const changeDetails = [];
        if (original.percentage !== shareholder.percentage) {
          changeDetails.push(`持股比例: ${original.percentage}% → ${shareholder.percentage}%`);
        }
        if (original.registeredAmount !== shareholder.registeredAmount) {
          changeDetails.push(`认缴出资额: ${original.registeredAmount} → ${shareholder.registeredAmount}`);
        }
        if (original.isProxy !== shareholder.isProxy) {
          changeDetails.push(`代持: ${original.isProxy ? '是' : '否'} → ${shareholder.isProxy ? '是' : '否'}`);
        }
        if (original.actualShareholderName !== shareholder.actualShareholderName) {
          changeDetails.push(`实际股东: ${original.actualShareholderName || '无'} → ${shareholder.actualShareholderName || '无'}`);
        }
        if (changeDetails.length > 0) {
          changes.push(`股东变更: ${shareholderName} - ${changeDetails.join(', ')}`);
        }
      }
    }

    // 检查删除
    for (const [shareholderName, shareholder] of originalMap) {
      if (!currentMap.has(shareholderName)) {
        changes.push(`删除股东: ${shareholderName} - ${shareholder.percentage}%`);
      }
    }

    if (changes.length === 0) {
      message.warning('没有检测到任何变更');
      return;
    }

    setLoading(true);
    try {
      // 创建变更记录
      const changeResponse = await fetch('http://localhost:8080/api/company-change-confirmation/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          company_id: parseInt(companyId),
          change_type: 'shareholder',
          change_content: changes.join('; '),
          old_value: JSON.stringify(originalShareholders),
          new_value: JSON.stringify(validShareholders),
          change_date: changeDate.format('YYYY-MM-DD'),
          operator: '当前用户' // 这里应该从用户上下文获取
        }),
      });

      const changeResult = await changeResponse.json();

      if (changeResult.success) {
        message.success('股东信息变更提交成功，等待确认');
        navigate('/company');
      } else {
        message.error(changeResult.message || '提交失败');
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败');
    } finally {
      setLoading(false);
    }
  };

  // 确认处理
  const handleConfirm = async () => {
    setLoading(true);
    try {
      // 这里应该调用确认API
      message.success('变更确认成功');
      navigate('/company-change-confirmation');
    } catch (error) {
      console.error('确认失败:', error);
      message.error('确认失败');
    } finally {
      setLoading(false);
    }
  };

  // 取消处理
  const handleCancel = () => {
    navigate('/company');
  };

  // 生成变更内容描述
  const generateChangeDescription = () => {
    return shareholders.map(sh => 
      `${sh.shareholderName} - ${sh.percentage}%${sh.isProxy ? ` (代持: ${sh.actualShareholderName})` : ''}`
    ).join(', ');
  };

  // 文件数据
  const fileData = [
    { key: 1, type: 'C规范', name: 'C规范模板.docx' },
    { key: 2, type: 'D规范', name: 'D规范模板.docx' },
  ];

  // 如果没有公司ID，显示错误信息
  if (!companyId) {
    return (
      <div className="company-change-shareholder">
        <div className="page-header">
          <h2>股权管理</h2>
        </div>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <h3>访问错误</h3>
          <p>请通过正确的入口访问此页面：</p>
          <ul style={{ textAlign: 'left', display: 'inline-block' }}>
            <li>从公司信息页面点击公司卡片的"变更"按钮，然后选择"变更股东信息"</li>
            <li>从公司详情页面点击右上角的"变更股东信息"按钮</li>
          </ul>
          <div style={{ marginTop: '20px' }}>
            <Button type="primary" onClick={() => navigate('/company')}>
              返回公司信息页面
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="company-change-shareholder">
      {/* 顶部按钮 */}
      <div className="top-buttons">
        <Button
          type="primary"
          className="submit-btn"
          onClick={handleSubmit}
          loading={loading}
          disabled={isConfirmMode}
        >
          提交
        </Button>
        {isConfirmMode && (
          <Button
            type="primary"
            className="confirm-btn"
            onClick={handleConfirm}
            loading={loading}
          >
            确认
          </Button>
        )}
        <Button
          className="cancel-btn"
          onClick={handleCancel}
        >
          取消
        </Button>
      </div>

      {/* 表单区域 */}
      <div className="form-section">
        <div className="form-row">
          <label className="form-label">变更日期</label>
          <DatePicker
            value={changeDate}
            onChange={(date) => setChangeDate(date || dayjs())}
            style={{ width: 300 }}
            disabled={isConfirmMode}
          />
        </div>
      </div>

      {/* 显示公司名称 */}
      {companyInfo && (
        <div style={{ marginBottom: '20px', padding: '10px', background: '#f0f0f0', borderRadius: '4px' }}>
          <strong>变更公司：{companyInfo.chineseName}</strong>
        </div>
      )}

      {/* 股东信息编辑区域 */}
      <div className="shareholder-info-section">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3>股东信息</h3>
          {!isConfirmMode && (
            <Button type="primary" onClick={handleAddShareholder}>
              添加股东
            </Button>
          )}
        </div>

        <Table
          columns={shareholderColumns}
          dataSource={shareholders}
          pagination={false}
          rowKey={(record, index) => `shareholder-${index}`}
          bordered
          size="middle"
          scroll={{ x: 1200 }}
        />
      </div>

      {/* 参考规范 */}
      <div className="reference-section">
        <h3>参考规范</h3>
        <div className="reference-links">
          <div className="reference-item">
            <span>A规范</span>
            <a href="#" className="reference-link" onClick={() => handleDownloadTemplate('A规范')}>
              A规范.docx
            </a>
          </div>
          <div className="reference-item">
            <span>B规范</span>
            <a href="#" className="reference-link" onClick={() => handleDownloadTemplate('B规范')}>
              B规范.docx
            </a>
          </div>
        </div>
      </div>

      {/* 具体变更 */}
      <div className="change-details-section">
        <h3>具体变更</h3>
        <div style={{ padding: '10px', background: '#f9f9f9', borderRadius: '4px', minHeight: '60px' }}>
          {generateChangeDescription() || '请添加股东信息'}
        </div>
      </div>

      {/* 上传文件 */}
      <div className="upload-section">
        <h3>上传文件</h3>
        <Table
          columns={[
            {
              title: '文件类型',
              dataIndex: 'type',
              key: 'type',
              width: '20%',
              align: 'center',
            },
            {
              title: '文件名',
              dataIndex: 'name',
              key: 'name',
              width: '60%',
              align: 'center',
            },
            {
              title: '操作',
              key: 'action',
              width: '20%',
              align: 'center',
              render: (_, record) => (
                <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
                  <Upload
                    beforeUpload={(file) => handleFileUpload(file, record.type)}
                    showUploadList={false}
                    disabled={isConfirmMode}
                  >
                    <Button type="link" icon={<UploadOutlined />} disabled={isConfirmMode}>
                      下载模板
                    </Button>
                  </Upload>
                  <Button
                    type="link"
                    icon={<DownloadOutlined />}
                    onClick={() => handleDownloadTemplate(record.type)}
                  >
                    重新上传
                  </Button>
                </div>
              ),
            },
          ]}
          dataSource={fileData}
          pagination={false}
          bordered
          size="small"
        />
      </div>
    </div>
  );
};

export default CompanyChangeShareholder;
