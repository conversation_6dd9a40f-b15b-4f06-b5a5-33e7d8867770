import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Input,
  Select,
  Space,
  Card,
  Row,
  Col,
  message,
  Modal,
  Typography,
  Pagination
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SearchOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import dayjs from 'dayjs';

const { Option } = Select;
const { Title } = Typography;
const { confirm } = Modal;

interface ArchiveUpdateRule {
  id: number;
  update_type: string;
  update_operation_name: string;
  creator_name: string;
  created_at: string;
  updated_at: string;
  updater_name: string;
  update_time: string;
}

const ArchiveUpdateRuleList: React.FC = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [dataSource, setDataSource] = useState<ArchiveUpdateRule[]>([]);
  const [total, setTotal] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 查询条件
  const [searchForm, setSearchForm] = useState({
    updateOperationName: '',
    updateType: '',
    updateContent: ''
  });

  // 变更类型选项
  const updateTypes = [
    '基础信息变更',
    '高管信息变更',
    '股东信息变更',
    '对外投资信息变更'
  ];

  // 加载数据
  const loadData = async (page = 1, size = 10, searchParams = searchForm) => {
    try {
      setLoading(true);
      console.log('📊 加载档案更新规范数据...', { page, size, searchParams });

      // 只传递非空的查询参数
      const params: any = {
        page,
        pageSize: size
      };

      if (searchParams.updateOperationName) {
        params.updateOperationName = searchParams.updateOperationName;
      }
      if (searchParams.updateType) {
        params.updateType = searchParams.updateType;
      }
      if (searchParams.updateContent) {
        params.updateContent = searchParams.updateContent;
      }

      console.log('📤 发送请求参数:', params);

      const response = await axios.get('http://localhost:8080/api/archive/update-rules', {
        params,
        timeout: 10000
      });

      console.log('📥 收到响应:', response.data);

      if (response.data.success) {
        const data = response.data.data || [];
        setDataSource(data);
        setTotal(response.data.total || 0);
        console.log('✅ 加载档案更新规范数据成功:', {
          count: data.length,
          total: response.data.total,
          data: data
        });
      } else {
        throw new Error(response.data.message || '加载数据失败');
      }
    } catch (error: any) {
      console.error('❌ 加载档案更新规范数据失败:', error);
      message.error(`加载数据失败: ${error.message || '请稍后重试'}`);
      setDataSource([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    console.log('🚀 档案更新规范列表页面初始化，开始加载数据...');
    loadData();
  }, []);

  // 查询
  const handleSearch = () => {
    setCurrentPage(1);
    loadData(1, pageSize, searchForm);
  };

  // 重置
  const handleReset = () => {
    const resetForm = {
      updateOperationName: '',
      updateType: '',
      updateContent: ''
    };
    setSearchForm(resetForm);
    setCurrentPage(1);
    loadData(1, pageSize, resetForm);
  };

  // 删除
  const handleDelete = (record: ArchiveUpdateRule) => {
    confirm({
      title: '确认删除',
      content: `确定要删除档案更新规范"${record.update_operation_name}"吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await axios.delete(`http://localhost:8080/api/archive/update-rule/${record.id}`);
          if (response.data.success) {
            message.success('删除成功');
            loadData(currentPage, pageSize, searchForm);
          } else {
            throw new Error(response.data.message || '删除失败');
          }
        } catch (error: any) {
          console.error('删除失败:', error);
          message.error('删除失败，请稍后重试');
        }
      }
    });
  };

  // 编辑
  const handleEdit = (record: ArchiveUpdateRule) => {
    navigate(`/archive/update-rule/edit/${record.id}`);
  };

  // 新增
  const handleAdd = () => {
    navigate('/archive/update-rule/add');
  };

  // 分页变化
  const handlePageChange = (page: number, size?: number) => {
    setCurrentPage(page);
    if (size) setPageSize(size);
    loadData(page, size || pageSize, searchForm);
  };

  // 表格列定义
  const columns = [
    {
      title: '变更类型',
      dataIndex: 'update_type',
      key: 'update_type',
      width: 150,
      align: 'center',
    },
    {
      title: '变更操作规范名称',
      dataIndex: 'update_operation_name',
      key: 'update_operation_name',
      width: 200,
      align: 'center',
    },
    {
      title: '创建人',
      dataIndex: 'creator_name',
      key: 'creator_name',
      width: 120,
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      align: 'center',
      render: (text: string) => text ? dayjs(text).format('YYYY/MM/DD HH:mm:ss') : '-'
    },
    {
      title: '更新人',
      dataIndex: 'updater_name',
      key: 'updater_name',
      width: 120,
      align: 'center',
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 150,
      align: 'center',
      render: (text: string) => text ? dayjs(text).format('YYYY/MM/DD HH:mm:ss') : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      align: 'center',
      render: (_: any, record: ArchiveUpdateRule) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Title level={3} style={{ margin: 0 }}>
            档案更新规范
          </Title>
        </div>

        {/* 查询条件 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row gutter={16}>
            <Col span={8}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                <span style={{ fontWeight: 500, color: '#262626', fontSize: 14 }}>变更操作规范名称</span>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Input
                    placeholder="请输入"
                    value={searchForm.updateOperationName}
                    onChange={(e) => setSearchForm({ ...searchForm, updateOperationName: e.target.value })}
                    style={{ width: 200 }}
                  />
                  <Space style={{ marginLeft: 16 }}>
                    <Button
                      type="primary"
                      icon={<SearchOutlined />}
                      onClick={handleSearch}
                    >
                      查询
                    </Button>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={handleReset}
                    >
                      重置
                    </Button>
                  </Space>
                </div>
              </div>
            </Col>
            <Col span={6}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                <span style={{ fontWeight: 500, color: '#262626', fontSize: 14 }}>变更类型</span>
                <Select
                  placeholder="请选择"
                  value={searchForm.updateType || undefined}
                  onChange={(value) => setSearchForm({ ...searchForm, updateType: value || '' })}
                  style={{ width: '100%' }}
                  allowClear
                >
                  {updateTypes.map(type => (
                    <Option key={type} value={type}>{type}</Option>
                  ))}
                </Select>
              </div>
            </Col>
            <Col span={6}>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                <span style={{ fontWeight: 500, color: '#262626', fontSize: 14 }}>变更内容</span>
                <Input
                  placeholder="请输入"
                  value={searchForm.updateContent}
                  onChange={(e) => setSearchForm({ ...searchForm, updateContent: e.target.value })}
                />
              </div>
            </Col>
          </Row>
        </Card>

        {/* 操作按钮 */}
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增
            </Button>
          </Space>
        </div>

        {/* 数据表格 */}
        <Table
          columns={columns}
          dataSource={dataSource}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1000 }}
          locale={{
            emptyText: '暂无档案更新规范数据'
          }}
        />

        {/* 分页 */}
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Pagination
            current={currentPage}
            pageSize={pageSize}
            total={total}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => `共 ${total} 条，第 ${range[0]}-${range[1]} 条`}
            onChange={handlePageChange}
            onShowSizeChange={handlePageChange}
          />
        </div>
      </Card>
    </div>
  );
};

export default ArchiveUpdateRuleList;
