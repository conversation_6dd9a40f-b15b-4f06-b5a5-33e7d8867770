import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Button,
  Card,
  Row,
  Col,
  Table,
  Checkbox,
  Space,
  message,
  Upload,
  Tag,
  Typography
} from 'antd';
import {
  ArrowLeftOutlined,
  PlusOutlined,
  DeleteOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const { Option } = Select;
const { Title, Text } = Typography;

interface FormData {
  updateType: string;
  updateOperationName: string;
  changeSteps: {
    companyNameCn: boolean;
    companyNameEn: boolean;
    registeredCapital: boolean;
    businessSegment: boolean;
    region: boolean;
  };
  applicableScope: {
    businessSegments: string[];
    regionTypes: string[];
    regions: string[];
  };
  referenceRules: string[];
  uploadFiles: any[];
}

interface ReferenceRule {
  id: number;
  content: string;
  fileName?: string;
  file?: any;
  isFromUpload?: boolean;
}

const ArchiveUpdateRuleAdd: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  
  // 状态管理
  const [changeSteps, setChangeSteps] = useState({
    companyNameCn: false,
    companyNameEn: false,
    registeredCapital: false,
    businessSegment: false,
    region: false
  });
  
  const [applicableScope, setApplicableScope] = useState({
    businessSegments: [] as string[],
    regionTypes: [] as string[],
    regions: [] as string[]
  });
  
  const [referenceRules, setReferenceRules] = useState<ReferenceRule[]>([
    { id: 1, content: '', isFromUpload: false }
  ]);

  // 数据库数据状态
  const [businessSegments, setBusinessSegments] = useState<any[]>([]);
  const [allRegions, setAllRegions] = useState<any[]>([]);
  const [filteredRegions, setFilteredRegions] = useState<any[]>([]);
  const [selectedRegionTypes, setSelectedRegionTypes] = useState<string[]>([]);

  // 基础数据
  const updateTypes = [
    '基础信息变更',
    '高管信息变更',
    '股东信息变更',
    '对外投资信息变更'
  ];

  const regionTypes = ['国内', '海外'];

  // 加载基础数据
  useEffect(() => {
    loadBusinessSegments();
    loadRegions();
  }, []);

  // 加载业务板块数据
  const loadBusinessSegments = async () => {
    try {
      const response = await axios.get('http://localhost:8080/api/business-segments');
      if (response.data.success) {
        setBusinessSegments(response.data.data);
      }
    } catch (error) {
      console.error('加载业务板块失败:', error);
      message.error('加载业务板块数据失败');
    }
  };

  // 加载地区数据
  const loadRegions = async () => {
    try {
      const response = await axios.get('http://localhost:8080/api/regions');
      if (response.data.success) {
        setAllRegions(response.data.data);
        setFilteredRegions(response.data.data);
      }
    } catch (error) {
      console.error('加载地区失败:', error);
      message.error('加载地区数据失败');
    }
  };

  // 地区类型变化时过滤地区
  useEffect(() => {
    if (selectedRegionTypes.length === 0) {
      setFilteredRegions(allRegions);
    } else {
      const filtered = allRegions.filter(region => {
        if (selectedRegionTypes.includes('国内')) {
          // 国内：显示type为"国内"的地区
          if (region.type === '国内') return true;
        }
        if (selectedRegionTypes.includes('海外')) {
          // 海外：显示type不为"国内"的地区（包括"海外公司对外投资"等）
          if (region.type !== '国内') return true;
        }
        return false;
      });
      setFilteredRegions(filtered);
    }
  }, [selectedRegionTypes, allRegions]);

  // 变更字段处理
  const handleChangeStepChange = (field: string, checked: boolean) => {
    setChangeSteps(prev => ({
      ...prev,
      [field]: checked
    }));
  };

  // 适用范围处理
  const handleScopeChange = (type: string, values: string[]) => {
    setApplicableScope(prev => ({
      ...prev,
      [type]: values
    }));

    // 如果是地区类型变化，更新选中的地区类型但保留已选地区
    if (type === 'regionTypes') {
      setSelectedRegionTypes(values);
      // 不清空已选地区，保留用户的选择
    }
  };

  // 添加/删除标签
  const addTag = (type: string, value: string) => {
    if (value && !applicableScope[type as keyof typeof applicableScope].includes(value)) {
      handleScopeChange(type, [...applicableScope[type as keyof typeof applicableScope], value]);
    }
  };

  const removeTag = (type: string, value: string) => {
    const newValues = applicableScope[type as keyof typeof applicableScope].filter(item => item !== value);

    // 如果是地区类型的移除，需要智能处理已选地区
    if (type === 'regionTypes') {
      setSelectedRegionTypes(newValues);

      // 当移除地区类型时，需要检查已选地区是否还有效
      if (newValues.length === 0) {
        // 如果没有选择任何地区类型，清空已选地区
        setApplicableScope(prev => ({
          ...prev,
          regionTypes: newValues,
          regions: []
        }));
      } else {
        // 如果还有其他地区类型，过滤掉不匹配的已选地区
        const validRegions = applicableScope.regions.filter(region => {
          const regionData = allRegions.find(r => r.region === region);
          if (!regionData) return false;

          return newValues.some(regionType => {
            if (regionType === '国内') {
              return regionData.type === '国内';
            } else if (regionType === '海外') {
              return regionData.type !== '国内';
            }
            return false;
          });
        });

        setApplicableScope(prev => ({
          ...prev,
          regionTypes: newValues,
          regions: validRegions
        }));
      }
    } else {
      // 其他类型直接更新
      handleScopeChange(type, newValues);
    }
  };

  // 参考规范处理
  const addReferenceRule = () => {
    const newId = Math.max(...referenceRules.map(r => r.id)) + 1;
    setReferenceRules([...referenceRules, { id: newId, content: '', isFromUpload: false }]);
  };

  const removeReferenceRule = (id: number) => {
    if (referenceRules.length > 1) {
      setReferenceRules(referenceRules.filter(r => r.id !== id));
    }
  };

  const updateReferenceRule = (id: number, content: string) => {
    setReferenceRules(referenceRules.map(r =>
      r.id === id ? { ...r, content } : r
    ));
  };

  // 文件上传处理
  const handleFileUpload = (file: any) => {
    const newId = Math.max(...referenceRules.map(r => r.id)) + 1;
    const newRule: ReferenceRule = {
      id: newId,
      content: file.name,
      fileName: file.name,
      file: file,
      isFromUpload: true
    };
    setReferenceRules([...referenceRules, newRule]);
    return false; // 阻止自动上传
  };

  // 表单提交
  const handleSubmit = async () => {
    try {
      setLoading(true);

      // 验证表单
      const values = await form.validateFields();

      // 验证必填字段
      if (!values.updateType) {
        message.error('请选择变更类型');
        return;
      }

      if (!values.updateOperationName) {
        message.error('请输入变更操作规范名称');
        return;
      }

      // 准备提交数据
      const submitData = {
        updateType: values.updateType,
        updateOperationName: values.updateOperationName,
        changeSteps: changeSteps,
        applicableScope: applicableScope,
        referenceRules: referenceRules.filter(rule => rule.content.trim()),
        creatorName: 'Admin User' // TODO: 从用户登录信息获取真实用户名
      };

      console.log('📤 提交档案更新规范数据:', submitData);

      // 先检查是否重复
      const checkResponse = await axios.post('http://localhost:8080/api/archive/update-rule/check-duplicate', {
        updateType: values.updateType,
        updateOperationName: values.updateOperationName
      }, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      if (checkResponse.data.duplicate) {
        message.error('该变更类型和操作规范名称已存在，请修改后重试');
        return;
      }

      // 调用后端API保存数据
      const response = await axios.post('http://localhost:8080/api/archive/update-rule/add', submitData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      console.log('✅ 后端响应:', response.data);

      if (response.data.success) {
        message.success('档案更新规范已成功保存！');
        navigate('/archive');
      } else {
        throw new Error(response.data.message || '保存失败');
      }

    } catch (error: any) {
      console.error('❌ 保存档案更新规范失败:', error);

      if (error.response) {
        const errorMsg = error.response.data?.message || '服务器返回错误';
        message.error(`保存失败: ${errorMsg}`);
      } else if (error.request) {
        message.error('网络连接失败，请检查网络连接');
      } else {
        message.error('保存失败，请稍后重试');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/archive')}
            style={{ marginRight: 12 }}
          >
            返回
          </Button>
          <div>
            <Title level={3} style={{ margin: 0 }}>
              新增档案更新规范
            </Title>
            <Text type="secondary">
              添加新的档案更新规范到系统中
            </Text>
          </div>
        </div>
      </div>

      {/* 主表单卡片 */}
      <Card 
        style={{ boxShadow: '0 2px 8px rgba(0,0,0,0.1)', maxWidth: 1200, margin: '0 auto' }}
        loading={loading}
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{}}
        >
          {/* 基本信息 */}
          <Row gutter={24} style={{ marginBottom: 24 }}>
            <Col span={12}>
              <Form.Item
                label="变更类型"
                name="updateType"
                rules={[{ required: true, message: '请选择变更类型' }]}
              >
                <Select placeholder="请选择（单选）">
                  {updateTypes.map(type => (
                    <Option key={type} value={type}>{type}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="变更操作规范名称"
                name="updateOperationName"
                rules={[{ required: true, message: '请输入变更操作规范名称' }]}
              >
                <Input placeholder="请输入" />
              </Form.Item>
            </Col>
          </Row>

          {/* 变更字段 */}
          <Card title="变更字段" style={{ marginBottom: 24 }}>
            <Table
              dataSource={[
                { key: 'companyNameCn', field: '公司中文名', changeable: changeSteps.companyNameCn },
                { key: 'companyNameEn', field: '公司英文名', changeable: changeSteps.companyNameEn },
                { key: 'registeredCapital', field: '注册资本', changeable: changeSteps.registeredCapital },
                { key: 'businessSegment', field: '业务板块', changeable: changeSteps.businessSegment },
                { key: 'region', field: '地区', changeable: changeSteps.region }
              ]}
              columns={[
                {
                  title: '字段',
                  dataIndex: 'field',
                  key: 'field',
                  width: '50%'
                },
                {
                  title: '可变更',
                  dataIndex: 'changeable',
                  key: 'changeable',
                  width: '50%',
                  render: (checked: boolean, record: any) => (
                    <Checkbox
                      checked={checked}
                      onChange={(e) => handleChangeStepChange(record.key, e.target.checked)}
                    />
                  )
                }
              ]}
              pagination={false}
              size="small"
            />
          </Card>

          {/* 适用范围 */}
          <Card title="适用范围" style={{ marginBottom: 24 }}>
            <Table
              dataSource={[
                {
                  key: 'businessSegments',
                  scope: '业务板块',
                  tags: applicableScope.businessSegments,
                  options: businessSegments.map(item => item.name)
                },
                {
                  key: 'regionTypes',
                  scope: '地区类型',
                  tags: applicableScope.regionTypes,
                  options: regionTypes
                },
                {
                  key: 'regions',
                  scope: '地区',
                  tags: applicableScope.regions,
                  options: filteredRegions.map(item => item.region)
                }
              ]}
              columns={[
                {
                  title: '',
                  dataIndex: 'checkbox',
                  key: 'checkbox',
                  width: 50,
                  render: () => <Checkbox />
                },
                {
                  title: '范围',
                  dataIndex: 'scope',
                  key: 'scope',
                  width: 200
                },
                {
                  title: '选择项',
                  dataIndex: 'tags',
                  key: 'tags',
                  render: (tags: string[], record: any) => (
                    <div>
                      <Space wrap style={{ marginBottom: 8 }}>
                        {tags.map(tag => (
                          <Tag
                            key={tag}
                            closable
                            onClose={() => removeTag(record.key, tag)}
                          >
                            {tag}
                          </Tag>
                        ))}
                        <Select
                          style={{ width: 120 }}
                          placeholder="添加"
                          value={undefined}
                          onChange={(value) => addTag(record.key, value)}
                        >
                          {record.options.filter((option: string) => !tags.includes(option)).map((option: string) => (
                            <Option key={option} value={option}>{option}</Option>
                          ))}
                        </Select>
                        <Button
                          type="primary"
                          icon={<PlusOutlined />}
                          size="small"
                        />
                      </Space>
                    </div>
                  )
                }
              ]}
              pagination={false}
              size="small"
            />
          </Card>

          {/* 参考规范与文件上传 */}
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                <span>参考规范</span>
                <Space>
                  <Upload
                    beforeUpload={handleFileUpload}
                    showUploadList={false}
                    multiple
                  >
                    <Button icon={<UploadOutlined />} size="small">
                      上传文件
                    </Button>
                  </Upload>
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    size="small"
                    onClick={addReferenceRule}
                  >
                    添加规范
                  </Button>
                </Space>
              </div>
            }
            style={{ marginBottom: 24 }}
          >
            <div style={{ display: 'flex', gap: 16 }}>
              <div style={{ flex: 1 }}>
                {referenceRules.map(rule => (
                  <div key={rule.id} style={{ marginBottom: 8, display: 'flex', gap: 8, alignItems: 'center' }}>
                    <Input
                      placeholder={rule.isFromUpload ? "编辑文件名称" : "XX操作指引"}
                      value={rule.content}
                      onChange={(e) => updateReferenceRule(rule.id, e.target.value)}
                      style={{ flex: 1 }}
                    />
                    {rule.isFromUpload && (
                      <Tag color="blue">文件</Tag>
                    )}
                  </div>
                ))}
              </div>
              <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                {referenceRules.map(rule => (
                  <Button
                    key={rule.id}
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => removeReferenceRule(rule.id)}
                    disabled={referenceRules.length === 1}
                  />
                ))}
              </div>
            </div>
          </Card>

          {/* 操作按钮 */}
          <Row justify="center" style={{ marginTop: 32 }}>
            <Space size="large">
              <Button
                size="large"
                onClick={() => navigate('/archive')}
              >
                取消
              </Button>
              <Button
                type="primary"
                size="large"
                loading={loading}
                onClick={handleSubmit}
              >
                保存
              </Button>
            </Space>
          </Row>
        </Form>
      </Card>
    </div>
  );
};

export default ArchiveUpdateRuleAdd;
