import React, { useState, useEffect } from 'react';
import { Table, Card, Button, Space, Tabs, Statistic, Row, Col, Divider, message, Modal, Form, Input, Select } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import axios from 'axios';

interface ShareholderRecord {
  id: string;
  name: string;
  idType: string;
  idNumber: string;
  stockAmount: number;
  percentage: string;
  entryDate: string;
  status: string;
}

const ShareholderRegister: React.FC = () => {
  const [data, setData] = useState<ShareholderRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isAddModalVisible, setIsAddModalVisible] = useState(false);
  
  const columns: ColumnsType<ShareholderRecord> = [
    {
      title: '股东名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '证件类型',
      dataIndex: 'idType',
      key: 'idType',
      width: 100,
    },
    {
      title: '证件号码',
      dataIndex: 'idNumber',
      key: 'idNumber',
      width: 180,
    },
    {
      title: '持股数量',
      dataIndex: 'stockAmount',
      key: 'stockAmount',
      width: 100,
    },
    {
      title: '持股比例',
      dataIndex: 'percentage',
      key: 'percentage',
      width: 100,
    },
    {
      title: '入股日期',
      dataIndex: 'entryDate',
      key: 'entryDate',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" size="small">编辑</Button>
          <Button type="link" size="small" danger>删除</Button>
        </Space>
      ),
    },
  ];

  // 获取企业股东数据（只显示企业股东，不显示个人股东）
  useEffect(() => {
    const fetchShareholderData = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await axios.get('http://localhost:8080/api/shareholders?type=enterprise');
        if (response.data.success) {
          // 过滤出企业股东（假设API返回的数据中有type字段或者可以通过其他字段判断）
          const enterpriseShareholders = response.data.data.filter((shareholder: any) => {
            // 如果有type字段，直接过滤
            if (shareholder.type) {
              return shareholder.type === 'enterprise' || shareholder.type === '企业';
            }
            // 如果没有type字段，通过证件类型判断（企业一般是营业执照、组织机构代码等）
            const enterpriseIdTypes = ['营业执照', '组织机构代码证', '统一社会信用代码', '企业法人营业执照'];
            return enterpriseIdTypes.includes(shareholder.idType);
          });

          setData(enterpriseShareholders);
          console.log('✅ 企业股东数据加载成功，共', enterpriseShareholders.length, '条记录');
        } else {
          throw new Error(response.data.message || '企业股东数据API返回失败');
        }

        setLoading(false);
      } catch (error) {
        console.error('获取企业股东数据失败:', error);
        setError(`获取企业股东数据失败: ${error.message}`);
        message.error(`获取企业股东数据失败: ${error.message}`);
        setLoading(false);
      }
    };

    fetchShareholderData();
  }, []);

  // 处理新增企业股东
  const handleAddShareholder = () => {
    console.log('🔄 打开新增企业股东弹窗');
    setIsAddModalVisible(true);
  };

  // 关闭新增弹窗
  const handleAddModalClose = () => {
    setIsAddModalVisible(false);
  };

  // 处理新增股东提交
  const handleAddSubmit = async (values: any) => {
    try {
      console.log('🔄 提交新增企业股东:', values);

      const response = await axios.post('http://localhost:8080/api/shareholders', {
        ...values,
        type: 'enterprise' // 标记为企业股东
      });

      if (response.data.success) {
        message.success('新增企业股东成功');
        setIsAddModalVisible(false);
        // 重新加载数据
        window.location.reload();
      } else {
        throw new Error(response.data.message || '新增失败');
      }
    } catch (error) {
      console.error('新增企业股东失败:', error);
      message.error(`新增企业股东失败: ${error.message}`);
    }
  };

  // 重试功能
  const handleRetry = () => {
    setError(null);
    window.location.reload();
  };

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <h3>数据加载失败</h3>
          <p style={{ color: '#ff4d4f', marginBottom: '16px' }}>{error}</p>
          <Button type="primary" onClick={handleRetry}>
            重试
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Statistic title="股东总数" value={data.length} />
        </Col>
        <Col span={6}>
          <Statistic title="总股本" value={data.reduce((sum, item) => sum + item.stockAmount, 0)} />
        </Col>
        <Col span={6}>
          <Statistic title="最近变更日期" value="2021-05-20" />
        </Col>
        <Col span={6}>
          <Button type="primary">导出股东名册</Button>
        </Col>
      </Row>
      <Divider />
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" style={{ marginRight: 8 }} onClick={handleAddShareholder}>新增企业股东</Button>
        <Button style={{ marginRight: 8 }}>批量导入</Button>
      </div>
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        pagination={{
          total: data.length,
          pageSize: 10,
          current: 1,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />

      {/* 新增企业股东弹窗 */}
      <Modal
        title="新增企业股东"
        open={isAddModalVisible}
        onCancel={handleAddModalClose}
        footer={null}
        width={600}
      >
        <Form
          layout="vertical"
          onFinish={handleAddSubmit}
        >
          <Form.Item
            label="股东名称"
            name="name"
            rules={[{ required: true, message: '请输入股东名称' }]}
          >
            <Input placeholder="请输入企业股东名称" />
          </Form.Item>

          <Form.Item
            label="证件类型"
            name="idType"
            rules={[{ required: true, message: '请选择证件类型' }]}
          >
            <Select placeholder="请选择证件类型">
              <Select.Option value="营业执照">营业执照</Select.Option>
              <Select.Option value="组织机构代码证">组织机构代码证</Select.Option>
              <Select.Option value="统一社会信用代码">统一社会信用代码</Select.Option>
              <Select.Option value="企业法人营业执照">企业法人营业执照</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="证件号码"
            name="idNumber"
            rules={[{ required: true, message: '请输入证件号码' }]}
          >
            <Input placeholder="请输入证件号码" />
          </Form.Item>

          <Form.Item
            label="联系人"
            name="contactPerson"
          >
            <Input placeholder="请输入联系人" />
          </Form.Item>

          <Form.Item
            label="联系电话"
            name="contactPhone"
          >
            <Input placeholder="请输入联系电话" />
          </Form.Item>

          <Form.Item
            label="地址"
            name="address"
          >
            <Input.TextArea placeholder="请输入地址" rows={3} />
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={handleAddModalClose}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default ShareholderRegister;
