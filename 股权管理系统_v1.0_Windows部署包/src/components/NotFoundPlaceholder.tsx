import React from 'react';
import { Result, Button } from 'antd';
import { useNavigate } from 'react-router-dom';

interface NotFoundPlaceholderProps {
  title?: string;
  subTitle?: string;
  showBackButton?: boolean;
}

const NotFoundPlaceholder: React.FC<NotFoundPlaceholderProps> = ({
  title = "页面开发中",
  subTitle = "该页面正在开发中，敬请期待",
  showBackButton = true
}) => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(-1);
  };

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '60vh',
      background: 'white',
      borderRadius: '8px',
      margin: '24px'
    }}>
      <Result
        status="404"
        title={title}
        subTitle={subTitle}
        extra={
          showBackButton && (
            <Button type="primary" onClick={handleBack}>
              返回上一页
            </Button>
          )
        }
      />
    </div>
  );
};

export default NotFoundPlaceholder;
