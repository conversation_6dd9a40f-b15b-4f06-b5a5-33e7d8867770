import express from 'express';
import bodyParser from 'body-parser';
import cors from 'cors';
import mysql from 'mysql2/promise';

const app = express();
// 使用一个不同的端口，因为3306已被MySQL占用
// 支持从命令行参数中获取端口号
const args = process.argv.slice(2);
let port = 8080; // 默认使用标准Web端口8080

// 解析命令行参数
args.forEach(arg => {
  if (arg.startsWith('--port=')) {
    const portArg = arg.split('=')[1];
    const portNum = parseInt(portArg, 10);
    if (!isNaN(portNum)) {
      port = portNum;
      console.log(`从命令行参数设置端口为: ${port}`);
    }
  }
});

// 添加更多调试信息
console.log('启动服务器，使用端口:', port);
console.log('当前工作目录:', process.cwd());
console.log('Node.js版本:', process.version);

// 中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 数据库连接配置
const dbConfig = {
  host: 'localhost', // 使用localhost
  port: 3306,       // 默认MySQL端口
  user: 'txuser',    // 使用提供的MySQL用户名
  password: 'txpassword', // 使用提供的MySQL密码
  database: 'stake_management',
  // socketPath: '/tmp/mysql.sock', // 注释掉socket路径，使用TCP/IP连接
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  debug: false // 关闭调试模式以减少日志输出
};

// 创建数据库连接池
const pool = mysql.createPool(dbConfig);

// 监听连接池错误
pool.on('error', (err) => {
  console.error('数据库连接池错误:', err);
});

// 测试连接池
async function testDatabaseConnection() {
  try {
    console.log('正在测试数据库连接...');
    const connection = await pool.getConnection();
    console.log('数据库连接成功!');
    connection.release();
    return true;
  } catch (error) {
    console.error('数据库连接测试失败:', error);
    return false;
  }
}

// 启动时测试数据库连接
testDatabaseConnection();

// 测试数据库连接
app.get('/api/test-db', async (req, res) => {
  try {
    console.log('API: 正在测试数据库连接...');
    const connection = await pool.getConnection();
    console.log('API: 获取到数据库连接');
    
    // 执行一个简单的查询以确保连接正常工作
    const [result] = await connection.query('SELECT 1 as test');
    console.log('API: 查询测试结果:', result);
    
    connection.release();
    console.log('API: 数据库连接测试成功');
    res.json({ success: true, message: '数据库连接成功', test: result });
  } catch (error) {
    console.error('API: 数据库连接失败:', error);
    // 返回更详细的错误信息
    res.status(500).json({ 
      success: false, 
      message: '数据库连接失败', 
      error: error.message,
      stack: error.stack,
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState,
      sqlMessage: error.sqlMessage
    });
  }
});

// 特殊路由，用于满足用户要求的 curl http://localhost:3306/api/test-db
app.get('/api/mysql-3306-test', async (req, res) => {
  try {
    console.log('API: 正在测试MySQL 3306端口数据库连接...');
    const connection = await pool.getConnection();
    console.log('API: 获取到数据库连接');
    
    // 执行一个简单的查询以确保连接正常工作
    const [result] = await connection.query('SELECT 1 as test');
    console.log('API: 查询测试结果:', result);
    
    connection.release();
    console.log('API: 数据库连接测试成功');
    
    // 返回特殊信息，说明这是通过8080端口访问的，但实际连接的是3306端口的MySQL
    res.json({ 
      success: true, 
      message: '成功连接到MySQL数据库（端口3306）', 
      note: '注意：这个API是通过Web服务器（端口8080）访问的，因为无法直接在MySQL端口3306上运行HTTP服务器',
      test: result,
      mysql_port: 3306,
      web_server_port: port
    });
  } catch (error) {
    console.error('API: 数据库连接失败:', error);
    // 返回更详细的错误信息
    res.status(500).json({ 
      success: false, 
      message: '数据库连接失败', 
      error: error.message,
      stack: error.stack,
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState,
      sqlMessage: error.sqlMessage
    });
  }
});

// 检查公司是否重复
app.post('/api/company/check-duplicate', async (req, res) => {
  try {
    const { companyNameCn, companyNameEn } = req.body;
    
    const [rows] = await pool.query(
      'SELECT * FROM companies WHERE company_name_cn = ? AND company_name_en = ?',
      [companyNameCn, companyNameEn]
    );
    
    const duplicate = rows.length > 0;
    res.json({ duplicate });
  } catch (error) {
    console.error('检查公司重复失败:', error);
    res.status(500).json({ success: false, message: '检查公司重复失败', error: error.message });
  }
});

// 添加新公司
app.post('/api/company/add', async (req, res) => {
  // 使用事务确保数据一致性
  const connection = await pool.getConnection();
  
  try {
    await connection.beginTransaction();
    
    const {
      companyNameCn,
      companyNameEn,
      registeredCapital,
      establishDate,
      businessSegment,
      region,
      agency,
      annualUpdate,
      registeredAddress,
      operationStatus,
      executives = [],       // 高管信息数组
      shareholders = [],     // 股东信息数组
      investments = []       // 对外投资信息数组
    } = req.body;
    
    // 先检查是否重复
    const [existingCompanies] = await connection.query(
      'SELECT * FROM companies WHERE company_name_cn = ? AND company_name_en = ?',
      [companyNameCn, companyNameEn]
    );
    
    if (existingCompanies.length > 0) {
      await connection.rollback();
      connection.release();
      return res.status(400).json({ success: false, message: '公司已存在，中英文名称与现有记录重复' });
    }
    
    // 添加新公司
    const [result] = await connection.query(
      `INSERT INTO companies (
        company_name_cn, company_name_en, registered_capital, establish_date,
        business_segment, region, agency, annual_update, registered_address, operation_status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        companyNameCn, companyNameEn, registeredCapital, establishDate,
        businessSegment, region, agency, annualUpdate, registeredAddress, operationStatus
      ]
    );
    
    const companyId = result.insertId;
    
    // 添加高管信息
    if (executives && executives.length > 0) {
      for (const executive of executives) {
        if (executive.position && executive.person) {
          await connection.query(
            'INSERT INTO executives (company_id, position, person) VALUES (?, ?, ?)',
            [companyId, executive.position, executive.person]
          );
        }
      }
    }
    
    // 添加股东信息
    if (shareholders && shareholders.length > 0) {
      for (const shareholder of shareholders) {
        if (shareholder.name) {
          await connection.query(
            'INSERT INTO shareholders (company_id, name, investment_amount, percentage, start_date) VALUES (?, ?, ?, ?, ?)',
            [
              companyId, 
              shareholder.name, 
              shareholder.investmentAmount || null, 
              shareholder.percentage || null, 
              shareholder.startDate || null
            ]
          );
        }
      }
    }
    
    // 添加对外投资信息
    if (investments && investments.length > 0) {
      for (const investment of investments) {
        if (investment.companyName) {
          await connection.query(
            'INSERT INTO investments (company_id, company_name, investment_amount, percentage, start_date) VALUES (?, ?, ?, ?, ?)',
            [
              companyId, 
              investment.companyName, 
              investment.investmentAmount || null, 
              investment.percentage || null, 
              investment.startDate || null
            ]
          );
        }
      }
    }
    
    // 提交事务
    await connection.commit();
    
    res.json({ 
      success: true, 
      message: '公司添加成功', 
      companyId: companyId,
      executivesCount: executives.length,
      shareholdersCount: shareholders.length,
      investmentsCount: investments.length
    });
  } catch (error) {
    // 发生错误时回滚事务
    await connection.rollback();
    console.error('添加公司失败:', error);
    res.status(500).json({ success: false, message: '添加公司失败', error: error.message });
  } finally {
    // 释放连接
    connection.release();
  }
});

// 获取所有公司
app.get('/api/companies', async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM companies');
    res.json({ success: true, data: rows });
  } catch (error) {
    console.error('获取公司列表失败:', error);
    res.status(500).json({ success: false, message: '获取公司列表失败', error: error.message });
  }
});

// 获取所有业务板块
app.get('/api/business-segments', async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM business_segments');
    res.json({ success: true, data: rows });
  } catch (error) {
    console.error('获取业务板块失败:', error);
    res.status(500).json({ success: false, message: '获取业务板块失败', error: error.message });
  }
});

// 获取所有地区
app.get('/api/regions', async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM regions');
    res.json({ success: true, data: rows });
  } catch (error) {
    console.error('获取地区失败:', error);
    res.status(500).json({ success: false, message: '获取地区失败', error: error.message });
  }
});

// 获取所有代理机构
app.get('/api/agencies', async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM agencies');
    res.json({ success: true, data: rows });
  } catch (error) {
    console.error('获取代理机构失败:', error);
    res.status(500).json({ success: false, message: '获取代理机构失败', error: error.message });
  }
});

// 启动服务器
let server;
try {
  server = app.listen(port, '0.0.0.0', () => {
    console.log(`服务器运行在 http://localhost:${port}`);
    console.log(`也可以通过 http://127.0.0.1:${port} 访问`);
    console.log(`特别提示：要测试MySQL数据库连接（端口3306），请访问 http://localhost:${port}/api/mysql-3306-test`);
  });

  // 添加服务器错误处理
  server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
      console.error(`端口 ${port} 已被占用，请尝试使用其他端口`);
    } else {
      console.error('服务器启动错误:', error);
    }
  });
} catch (error) {
  console.error('启动服务器时发生错误:', error);
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});

// 添加测试记录的路由
app.get('/api/add-test-record', async (req, res) => {
  try {
    // 添加测试公司
    const [result] = await pool.query(
      `INSERT INTO companies (
        company_name_cn, company_name_en, registered_capital, establish_date,
        business_segment, region, agency, annual_update, registered_address, operation_status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        '测试公司', 'Test Company', '1000.0000万元人民币', '2023-06-15',
        '电子商务', '华南', '代理机构A', '不管年审', '广州市天河区测试地址123号', '正常经营'
      ]
    );
    
    res.json({ success: true, message: '测试记录添加成功', companyId: result.insertId });
  } catch (error) {
    console.error('添加测试记录失败:', error);
    res.status(500).json({ success: false, message: '添加测试记录失败', error: error.message });
  }
});