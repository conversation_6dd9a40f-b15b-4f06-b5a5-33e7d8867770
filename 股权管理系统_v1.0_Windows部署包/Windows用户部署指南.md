# 股权管理系统 - Windows用户部署指南

## 📋 系统概述

这是一个专业的股权管理系统，用于管理公司信息、股东信息、任职档案、业务板块等。本指南将帮助您在Windows系统上完整安装和配置该系统。

## 🎯 安装前准备

### 系统要求
- **操作系统**: Windows 10/11 (64位)
- **内存**: 最少8GB，推荐16GB
- **硬盘空间**: 最少10GB可用空间
- **网络**: 稳定的互联网连接（用于下载软件）

### 必需软件清单
1. Node.js (JavaScript运行环境)
2. MySQL数据库服务器
3. 文本编辑器（推荐使用记事本或Notepad++）

## 🚀 第一步：安装Node.js

### 1.1 下载Node.js
1. 打开浏览器，访问：https://nodejs.org/zh-cn/
2. 点击"下载"按钮，选择"LTS版本"（推荐版本）
3. 下载完成后，双击安装文件

### 1.2 安装Node.js
1. 运行下载的安装程序
2. 点击"Next"继续
3. 接受许可协议，点击"Next"
4. 选择安装路径（建议使用默认路径），点击"Next"
5. 确保勾选"Add to PATH"选项
6. 点击"Install"开始安装
7. 安装完成后点击"Finish"

### 1.3 验证安装
1. 按下 `Win + R` 键，输入 `cmd`，按回车打开命令提示符
2. 输入以下命令验证安装：
   ```
   node --version
   npm --version
   ```
3. 如果显示版本号，说明安装成功

## 🗄️ 第二步：安装MySQL数据库

### 2.1 下载MySQL
1. 访问：https://dev.mysql.com/downloads/mysql/
2. 选择"Windows (x86, 64-bit), MSI Installer"
3. 点击"Download"下载

### 2.2 安装MySQL
1. 双击下载的MSI文件
2. 选择"Developer Default"安装类型
3. 点击"Next"继续
4. 在"Configuration"页面：
   - **Root密码**: 设置为 `Yiwill@2025`（请记住此密码）
   - **端口**: 保持默认3306
5. 完成安装

### 2.3 验证MySQL安装
1. 打开命令提示符
2. 输入：`mysql --version`
3. 如果显示版本信息，说明安装成功

## 📦 第三步：获取系统文件

### 3.1 创建项目文件夹
1. 在C盘创建文件夹：`C:\股权管理系统`
2. 将提供的系统文件解压到此文件夹

### 3.2 文件结构说明
解压后应该看到以下文件结构：
```
C:\股权管理系统\
├── src/                    # 前端源代码
├── server/                 # 后端服务器代码
├── public/                 # 静态资源文件
├── package.json           # 项目配置文件
├── database-schema-complete.sql  # 数据库结构文件
└── README.md              # 项目说明
```

## 🔧 第四步：安装项目依赖

### 4.1 打开命令提示符
1. 按下 `Win + R`，输入 `cmd`，按回车
2. 输入以下命令进入项目目录：
   ```
   cd C:\股权管理系统
   ```

### 4.2 安装依赖包
输入以下命令并等待安装完成：
```
npm install
```
**注意**: 此过程可能需要5-10分钟，请耐心等待。

## 🗃️ 第五步：配置数据库

### 5.1 创建数据库
1. 打开命令提示符
2. 输入以下命令连接MySQL：
   ```
   mysql -u root -p
   ```
3. 输入密码：`Yiwill@2025`
4. 创建数据库：
   ```sql
   CREATE DATABASE stake_management_v2;
   EXIT;
   ```

### 5.2 导入数据库结构
在命令提示符中输入：
```
mysql -u root -p stake_management_v2 < database-schema-complete.sql
```
输入密码：`Yiwill@2025`

### 5.3 创建数据库用户（可选）
为了安全，建议创建专用数据库用户：
1. 连接MySQL：`mysql -u root -p`
2. 执行以下命令：
   ```sql
   CREATE USER 'stake_user'@'localhost' IDENTIFIED BY 'stake_password';
   GRANT ALL PRIVILEGES ON stake_management_v2.* TO 'stake_user'@'localhost';
   FLUSH PRIVILEGES;
   EXIT;
   ```

## ⚙️ 第六步：配置系统连接

### 6.1 修改数据库连接配置
1. 使用记事本打开文件：`C:\股权管理系统\server\index.js`
2. 找到数据库配置部分（大约在第40行）：
   ```javascript
   const dbConfig = {
     host: 'localhost',
     port: 3306,
     user: 'root',           // 如果创建了专用用户，改为 'stake_user'
     password: 'Yiwill@2025', // 如果创建了专用用户，改为 'stake_password'
     database: 'stake_management_v2',
   };
   ```
3. 保存文件

## 🚀 第七步：启动系统

### 7.1 启动后端服务器
1. 打开命令提示符
2. 进入项目目录：`cd C:\股权管理系统`
3. 启动后端服务：
   ```
   npm run server
   ```
4. 看到"服务器运行在 http://localhost:8080"表示启动成功

### 7.2 启动前端服务器
1. **保持第一个命令提示符窗口运行**
2. 打开**新的**命令提示符窗口
3. 进入项目目录：`cd C:\股权管理系统`
4. 启动前端服务：
   ```
   npm run dev
   ```
5. 看到"Local: http://localhost:5173/"表示启动成功

## 🌐 第八步：访问系统

### 8.1 打开系统
1. 打开浏览器（推荐Chrome或Edge）
2. 在地址栏输入：`http://localhost:5173`
3. 按回车键访问系统

### 8.2 系统功能说明
系统主要包含以下功能模块：
- **公司信息管理**: 添加、编辑、查询公司基本信息
- **股东信息管理**: 管理公司股东及持股比例
- **任职档案管理**: 记录人员任职情况
- **业务板块管理**: 管理公司业务分类
- **变更确认**: 处理公司信息变更申请

## 🔍 测试系统功能

### 测试步骤
1. 在浏览器中访问 `http://localhost:5173`
2. 点击左侧菜单"公司信息"
3. 点击"新增公司"按钮
4. 填写测试数据并保存
5. 查看是否能正常显示添加的公司信息

## ❗ 常见问题解决

### 问题1：端口被占用
**现象**: 显示"端口8080已被占用"
**解决**: 
1. 关闭其他可能占用端口的程序
2. 或者修改端口号（在server/index.js中修改port变量）

### 问题2：数据库连接失败
**现象**: 显示"数据库连接失败"
**解决**:
1. 确认MySQL服务正在运行
2. 检查用户名和密码是否正确
3. 确认数据库名称是否正确

### 问题3：npm install失败
**现象**: 安装依赖时出现错误
**解决**:
1. 确保网络连接正常
2. 尝试使用管理员权限运行命令提示符
3. 清除npm缓存：`npm cache clean --force`

### 问题4：页面无法访问
**现象**: 浏览器显示"无法访问此网站"
**解决**:
1. 确认前后端服务都已启动
2. 检查防火墙设置
3. 尝试使用 `http://127.0.0.1:5173` 访问

## 📞 技术支持

如果遇到其他问题，请：
1. 检查命令提示符中的错误信息
2. 确保按照步骤正确执行
3. 联系技术支持人员并提供具体错误信息

## 🔒 安全提醒

1. 请定期备份数据库数据
2. 不要在生产环境中使用默认密码
3. 建议定期更新系统和依赖包
4. 仅在可信网络环境中使用系统

## 📋 系统维护

### 日常维护
1. **定期重启服务**: 建议每周重启一次前后端服务
2. **数据备份**: 每天备份数据库数据
3. **日志检查**: 定期查看系统运行日志

### 数据备份方法
1. 打开命令提示符
2. 输入备份命令：
   ```
   mysqldump -u root -p stake_management_v2 > backup_数据库备份_日期.sql
   ```
3. 输入密码：`Yiwill@2025`
4. 备份文件将保存在当前目录

### 数据恢复方法
如需恢复数据：
```
mysql -u root -p stake_management_v2 < backup_数据库备份_日期.sql
```

## 🔧 高级配置（可选）

### 修改服务端口
如果需要修改服务端口：
1. 编辑 `server/index.js` 文件
2. 找到 `let port = 8080;` 这一行
3. 将8080改为其他端口号（如8081）
4. 保存文件并重启服务

### 配置开机自启动
1. 创建批处理文件 `启动股权管理系统.bat`
2. 内容如下：
   ```batch
   @echo off
   cd /d C:\股权管理系统
   start "后端服务" cmd /k npm run server
   timeout /t 5
   start "前端服务" cmd /k npm run dev
   timeout /t 10
   start http://localhost:5173
   ```
3. 双击运行即可一键启动所有服务

## 📊 系统性能优化

### 提升运行速度
1. **关闭不必要的程序**: 释放系统内存
2. **使用SSD硬盘**: 提升文件读写速度
3. **增加内存**: 推荐16GB以上内存

### 数据库优化
1. 定期清理无用数据
2. 重建数据库索引：
   ```sql
   USE stake_management_v2;
   OPTIMIZE TABLE companies;
   OPTIMIZE TABLE shareholders;
   OPTIMIZE TABLE executives;
   ```

## 🛡️ 安全设置

### 数据库安全
1. **修改默认密码**: 不要使用示例中的密码
2. **限制访问权限**: 只允许本地连接
3. **定期更新**: 保持MySQL版本最新

### 系统安全
1. **防火墙设置**: 只开放必要端口
2. **访问控制**: 限制系统访问人员
3. **数据加密**: 重要数据建议加密存储

## 📱 移动设备访问

### 局域网访问设置
如需在同一局域网的其他设备访问：
1. 查看本机IP地址：
   ```
   ipconfig
   ```
2. 修改前端启动命令为：
   ```
   npm run dev -- --host 0.0.0.0
   ```
3. 在其他设备浏览器输入：`http://本机IP:5173`

## 🔄 系统更新

### 更新步骤
1. **备份数据**: 按照备份方法保存数据
2. **停止服务**: 关闭前后端服务
3. **替换文件**: 用新版本文件替换旧文件
4. **更新依赖**: 运行 `npm install`
5. **重启服务**: 按照启动步骤重新启动

## 📞 联系支持

### 技术支持信息
- **支持邮箱**: <EMAIL>
- **技术文档**: 查看项目README.md文件
- **问题反馈**: 详细描述问题现象和错误信息

### 常用命令速查
```bash
# 查看Node.js版本
node --version

# 查看npm版本
npm --version

# 查看MySQL版本
mysql --version

# 进入项目目录
cd C:\股权管理系统

# 安装依赖
npm install

# 启动后端服务
npm run server

# 启动前端服务
npm run dev

# 连接MySQL数据库
mysql -u root -p

# 备份数据库
mysqldump -u root -p stake_management_v2 > backup.sql

# 恢复数据库
mysql -u root -p stake_management_v2 < backup.sql
```

---

## 🎉 部署完成检查清单

请确认以下所有项目都已完成：

- [ ] Node.js已安装并可正常运行
- [ ] MySQL已安装并设置密码
- [ ] 项目文件已解压到指定目录
- [ ] npm依赖包已安装完成
- [ ] 数据库已创建并导入结构
- [ ] 数据库连接配置已正确设置
- [ ] 后端服务可正常启动
- [ ] 前端服务可正常启动
- [ ] 浏览器可正常访问系统首页
- [ ] 系统基本功能测试通过
- [ ] 数据备份方法已了解

**恭喜！您已成功部署股权管理系统！**

如有任何问题，请参考故障排除部分或联系技术支持。

---

**版本**: v1.0
**更新日期**: 2025年6月30日
**适用系统**: Windows 10/11
