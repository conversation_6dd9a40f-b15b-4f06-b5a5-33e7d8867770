const mysql = require('mysql2/promise');

async function checkUsers() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection({
      host: 'SKiP-MBP.local',
      port: 3306,
      user: 'txuser',
      password: 'txpassword',
      database: 'stake_management_v2',
      socketPath: '/tmp/mysql.sock'
    });
    console.log('✅ 数据库连接成功');

    console.log('\n📊 检查用户数据:');
    const [users] = await connection.query(`
      SELECT
        u.id,
        u.username,
        u.password_hash,
        u.person_id,
        p.name as person_name,
        ur.name as role_name,
        COALESCE(p.name, u.username) as realName
      FROM users u
      LEFT JOIN persons p ON u.person_id = p.id
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      -- WHERE u.username IN ('admin', 'manager', 'operator', 'viewer')
      ORDER BY u.id
    `);

    users.forEach(user => {
      console.log(`ID: ${user.id}, Username: ${user.username}, Password Hash: ${user.password_hash}, Person ID: ${user.person_id}, Person Name: ${user.person_name}, Real Name: ${user.realName}, Role: ${user.role_name}`);
    });

    console.log('\n📊 检查persons表数据:');
    const [persons] = await connection.query(`
      SELECT id, name, email, phone
      FROM persons
      ORDER BY id
    `);

    persons.forEach(person => {
      console.log(`Person ID: ${person.id}, Name: ${person.name}, Email: ${person.email}, Phone: ${person.phone}`);
    });

  } catch (error) {
    console.error('❌ 错误:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔚 数据库连接已关闭');
    }
  }
}

checkUsers();
