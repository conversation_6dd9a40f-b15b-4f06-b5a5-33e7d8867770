import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function checkDatabaseTables() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 检查所有表
    console.log('\n📋 检查数据库中的所有表:');
    const [tables] = await connection.execute('SHOW TABLES');
    console.log('数据库表列表:');
    tables.forEach((table, index) => {
      const tableName = Object.values(table)[0];
      console.log(`  ${index + 1}. ${tableName}`);
    });

    // 检查是否有任务相关的表
    console.log('\n🔍 查找任务相关的表:');
    const taskRelatedTables = tables.filter(table => {
      const tableName = Object.values(table)[0].toString().toLowerCase();
      return tableName.includes('task') || tableName.includes('任务');
    });

    if (taskRelatedTables.length > 0) {
      console.log('找到任务相关的表:');
      taskRelatedTables.forEach(table => {
        console.log(`  - ${Object.values(table)[0]}`);
      });
    } else {
      console.log('❌ 没有找到任务相关的表');
    }

    // 检查公司表是否存在
    console.log('\n🏢 检查公司表:');
    const companyTables = tables.filter(table => {
      const tableName = Object.values(table)[0].toString().toLowerCase();
      return tableName.includes('compan') || tableName.includes('公司');
    });

    if (companyTables.length > 0) {
      console.log('找到公司相关的表:');
      companyTables.forEach(table => {
        console.log(`  - ${Object.values(table)[0]}`);
      });

      // 检查companies表的数据
      try {
        const [companyCount] = await connection.execute('SELECT COUNT(*) as count FROM companies');
        console.log(`📊 companies表中有 ${companyCount[0].count} 条记录`);

        // 检查companies表结构
        const [companyColumns] = await connection.execute('DESCRIBE companies');
        console.log('\ncompanies表字段:');
        companyColumns.forEach(col => {
          console.log(`  ${col.Field}: ${col.Type}`);
        });
      } catch (error) {
        console.log('❌ 无法查询companies表:', error.message);
      }
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行检查
checkDatabaseTables().catch(console.error);
