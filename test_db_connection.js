import mysql from 'mysql2/promise';

async function testConnection() {
  try {
    console.log('尝试连接到数据库...');
    const connection = await mysql.createConnection({
      host: 'SKiP-MBP.local',
      port: 3306,
      user: 'txuser',
      password: 'txpassword',
      database: 'stake_management',
      socketPath: '/tmp/mysql.sock'
    });
    
    console.log('数据库连接成功!');
    const [rows] = await connection.query('SELECT 1 as test');
    console.log('查询结果:', rows);
    await connection.end();
  } catch (error) {
    console.error('数据库连接失败:', error);
  }
}

testConnection();
