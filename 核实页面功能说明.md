# 核实页面功能实现说明

## 功能概述

核实页面用于核实任务完成情况，并可选择性地创建下一年度的对应任务。页面采用独立页面设计，通过URL参数获取任务信息，提供直观的操作界面。

## 主要功能

### 1. 任务信息显示
- 显示当前处理的任务信息：公司名称、任务类型、年度
- 信息以灰色背景卡片形式展示，便于用户识别

### 2. 核实任务完成情况
- **任务状态**：用户必须选择"已核实"才能保存
- **下一周期任务**：用户可选择"不创建"或"创建"
  - 选择"不创建"：只更新当前任务状态
  - 选择"创建"：更新当前任务状态并创建下一年度任务

### 3. 任务信息表单
当用户选择"创建"时，表单字段变为可编辑状态：

#### 必填字段
- **任务类型**：下拉选择，从数据库动态获取
- **年度**：下拉选择，默认为下一年度，最大值为当前年度+1
- **开始日期**：日期选择器，格式YYYY-MM-DD
- **结束日期**：日期选择器，格式YYYY-MM-DD
- **重复周期（年）**：输入框，数字类型

#### 可选字段
- **备注**：文本域，最大500字符

### 4. 数据验证
- 未选择"已核实"时，点击保存会提示错误
- 选择"创建"但未填写完整信息时，会提示填写完整信息
- 所有必填字段都有相应的验证规则

## 技术实现

### 后端API

#### 1. 获取任务详情
```
GET /api/tasks/:id
```
返回指定任务的详细信息，包括公司信息、任务类型等。

#### 2. 获取任务类型列表
```
GET /api/task/types
```
从数据库中动态获取所有可用的任务类型。

#### 3. 核实任务
```
POST /api/task/verify
参数: {
  taskId: number,
  isVerified: boolean,
  shouldCreateNext: boolean,
  nextTaskData?: {
    taskType: string,
    year: string,
    startDate: string,
    deadline: string,
    repeatCycle: string,
    remarks: string
  }
}
```

**功能逻辑：**
1. 更新当前任务状态为"已完成"
2. 如果选择创建下一年度任务，则：
   - 获取当前任务的公司信息
   - 创建新任务记录
   - 设置任务状态为"未开始"
   - 将重复周期信息合并到备注中

### 前端实现

#### 1. 页面结构
- 独立页面设计，居中布局，最大宽度800px
- 页面标题区域：显示"核实"标题和关闭按钮
- 分为三个主要区域：
  - 当前任务信息展示（灰色背景卡片）
  - 核实选项（单选按钮组）
  - 任务信息表单（最大宽度600px）
- 底部操作按钮区域：取消和保存按钮

#### 2. 状态管理
- `isVerified`：是否已核实
- `shouldCreateNext`：是否创建下一年度任务
- `taskData`：当前任务详情
- `taskTypes`：可用任务类型列表
- `availableYears`：可选年份列表

#### 3. 交互逻辑
- 根据`shouldCreateNext`状态动态禁用/启用表单字段
- 表单验证基于`shouldCreateNext`状态动态调整
- 保存成功后自动跳转回任务列表页面

## 数据流程

### 核实流程
1. 用户从任务列表点击"核实"按钮
2. 系统跳转到核实页面，URL包含任务ID参数（如：/task/verify?id=123）
3. 页面根据任务ID获取任务详情和相关数据
4. 显示当前处理的任务信息（公司名称 - 任务类型（年度））
5. 用户选择核实状态和是否创建下一年度任务
6. 如果创建下一年度任务，用户填写任务信息
7. 点击保存，系统执行以下操作：
   - 更新当前任务状态为"已完成"
   - 如果选择创建，则创建新任务记录
8. 操作成功后返回任务列表

### 数据库操作
- **更新操作**：修改pending_tasks表中的task_status字段
- **插入操作**：在pending_tasks表中创建新记录
- **事务处理**：确保更新和插入操作的原子性

## 样式设计

### 布局特点
- 独立页面，最大宽度800px，居中显示
- 页面内边距24px
- 各区域间距32px，保持清晰的视觉层次
- 表单区域最大宽度600px

### 视觉元素
- 页面标题：24px字体，加粗，右侧有关闭按钮
- 当前任务信息：灰色背景卡片，16px内边距
- 区域标题：18px字体，加粗
- 单选按钮：标准Ant Design样式，16px字体
- 表单字段：垂直布局，标签在上，大尺寸组件
- 按钮：居中布局，取消（默认）+ 保存（黑色主题）

### 交互反馈
- 表单字段根据选择状态动态禁用
- 保存过程显示加载状态
- 操作结果通过message组件反馈
- 无效访问时显示友好提示和返回按钮

### 访问控制
- 页面需要通过URL参数传递任务ID
- 无任务ID时显示错误提示和返回按钮
- 任务不存在时显示加载失败提示

## 扩展功能建议

1. **批量核实**：支持选择多个任务进行批量核实
2. **模板功能**：保存常用的任务配置作为模板
3. **审批流程**：添加核实审批机制
4. **历史记录**：记录核实操作的历史日志
5. **提醒功能**：核实截止日期提醒
