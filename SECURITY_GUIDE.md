# Security Implementation Guide

## 🔒 Security Architecture Overview

This guide outlines the comprehensive security measures implemented in the stake/equity management system for local deployment.

## 🛡️ Authentication & Authorization

### 1. JWT-Based Authentication
```typescript
// JWT Configuration
const JWT_CONFIG = {
  accessTokenExpiry: '15m',
  refreshTokenExpiry: '7d',
  algorithm: 'HS256',
  issuer: 'stake-management-system',
  audience: 'stake-management-users'
};
```

### 2. Role-Based Access Control (RBAC)
```sql
-- Default Roles
INSERT INTO roles (name, display_name, permissions) VALUES
('super_admin', '超级管理员', '{"all": true}'),
('admin', '管理员', '{"companies": ["read", "write"], "users": ["read", "write"], "equity": ["read", "write"]}'),
('manager', '经理', '{"companies": ["read", "write"], "equity": ["read", "write"]}'),
('analyst', '分析师', '{"companies": ["read"], "equity": ["read"]}'),
('viewer', '查看者', '{"companies": ["read"], "equity": ["read"]}');
```

### 3. Password Security
- Minimum 12 characters
- Must include uppercase, lowercase, numbers, and special characters
- bcrypt with salt rounds: 12
- Password history: prevent reuse of last 5 passwords
- Account lockout after 5 failed attempts

## 🔐 Data Protection

### 1. Database Security
```sql
-- Enable SSL for MySQL connections
-- Add to my.cnf
[mysqld]
ssl-ca=/path/to/ca.pem
ssl-cert=/path/to/server-cert.pem
ssl-key=/path/to/server-key.pem
require_secure_transport=ON

-- Create dedicated database user
CREATE USER 'stake_app'@'localhost' IDENTIFIED BY 'SecurePassword123!@#';
GRANT SELECT, INSERT, UPDATE, DELETE ON stake_management_v2.* TO 'stake_app'@'localhost';
FLUSH PRIVILEGES;
```

### 2. Environment Variables
```bash
# .env.production
NODE_ENV=production
PORT=8080

# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=stake_management_v2
DB_USER=stake_app
DB_PASSWORD=SecurePassword123!@#
DB_SSL=true

# JWT Secrets (Generate with: openssl rand -base64 64)
JWT_ACCESS_SECRET=your-super-secure-access-secret-here
JWT_REFRESH_SECRET=your-super-secure-refresh-secret-here

# Encryption
ENCRYPTION_KEY=your-32-character-encryption-key
SALT_ROUNDS=12

# Session
SESSION_SECRET=your-session-secret-here
SESSION_TIMEOUT=900000

# Security
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100
CORS_ORIGIN=http://localhost:5173

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log
```

### 3. Data Encryption
```typescript
// Sensitive data encryption
import crypto from 'crypto';

const ENCRYPTION_KEY = process.env.ENCRYPTION_KEY;
const ALGORITHM = 'aes-256-gcm';

export function encrypt(text: string): string {
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipher(ALGORITHM, ENCRYPTION_KEY);
  cipher.setAAD(Buffer.from('stake-management'));
  
  let encrypted = cipher.update(text, 'utf8', 'hex');
  encrypted += cipher.final('hex');
  
  const authTag = cipher.getAuthTag();
  return iv.toString('hex') + ':' + authTag.toString('hex') + ':' + encrypted;
}

export function decrypt(encryptedData: string): string {
  const parts = encryptedData.split(':');
  const iv = Buffer.from(parts[0], 'hex');
  const authTag = Buffer.from(parts[1], 'hex');
  const encrypted = parts[2];
  
  const decipher = crypto.createDecipher(ALGORITHM, ENCRYPTION_KEY);
  decipher.setAAD(Buffer.from('stake-management'));
  decipher.setAuthTag(authTag);
  
  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');
  
  return decrypted;
}
```

## 🚫 Input Validation & Sanitization

### 1. API Input Validation
```typescript
import { z } from 'zod';

// Company validation schema
export const companySchema = z.object({
  companyNameCn: z.string().min(1).max(255).regex(/^[\u4e00-\u9fa5a-zA-Z0-9\s\(\)（）]+$/),
  companyNameEn: z.string().min(1).max(255).regex(/^[a-zA-Z0-9\s\(\)\.,-]+$/),
  registeredCapital: z.number().positive().max(*********.99),
  establishDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/),
  businessSegment: z.string().min(1).max(100),
  region: z.string().min(1).max(100),
  registeredAddress: z.string().min(1).max(500)
});

// User validation schema
export const userSchema = z.object({
  username: z.string().min(3).max(50).regex(/^[a-zA-Z0-9_]+$/),
  email: z.string().email().max(100),
  password: z.string().min(12).regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]+$/),
  fullName: z.string().min(1).max(100).regex(/^[\u4e00-\u9fa5a-zA-Z\s]+$/),
  phone: z.string().regex(/^1[3-9]\d{9}$/).optional()
});
```

### 2. SQL Injection Prevention
- Use parameterized queries exclusively
- Input validation with Zod schemas
- ORM/Query Builder (Prisma) for type safety

### 3. XSS Protection
```typescript
import DOMPurify from 'isomorphic-dompurify';

// Sanitize HTML content
export function sanitizeHtml(dirty: string): string {
  return DOMPurify.sanitize(dirty, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  });
}

// CSP Headers
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"]
    }
  }
}));
```

## 🔍 Audit & Monitoring

### 1. Audit Logging
```typescript
// Audit middleware
export async function auditLog(
  userId: number,
  action: string,
  tableName: string,
  recordId: number,
  oldValues: any,
  newValues: any,
  req: Request
) {
  await prisma.auditLog.create({
    data: {
      userId,
      action,
      tableName,
      recordId,
      oldValues: JSON.stringify(oldValues),
      newValues: JSON.stringify(newValues),
      ipAddress: req.ip,
      userAgent: req.get('User-Agent'),
      requestId: req.headers['x-request-id'] as string
    }
  });
}
```

### 2. Security Monitoring
```typescript
// Failed login attempts monitoring
export async function trackFailedLogin(username: string, ip: string) {
  const user = await prisma.user.findUnique({ where: { username } });
  
  if (user) {
    await prisma.user.update({
      where: { id: user.id },
      data: {
        failedLoginAttempts: { increment: 1 },
        isLocked: user.failedLoginAttempts >= 4 // Lock after 5 attempts
      }
    });
  }
  
  // Log security event
  await prisma.systemLog.create({
    data: {
      level: 'WARN',
      message: `Failed login attempt for user: ${username}`,
      context: JSON.stringify({ ip, username }),
      ipAddress: ip
    }
  });
}
```

## 🌐 Network Security

### 1. HTTPS Configuration
```typescript
// Production HTTPS setup
import https from 'https';
import fs from 'fs';

const options = {
  key: fs.readFileSync('/path/to/private-key.pem'),
  cert: fs.readFileSync('/path/to/certificate.pem')
};

https.createServer(options, app).listen(443, () => {
  console.log('HTTPS Server running on port 443');
});
```

### 2. Rate Limiting
```typescript
import rateLimit from 'express-rate-limit';

// General API rate limiting
const apiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP',
  standardHeaders: true,
  legacyHeaders: false
});

// Strict rate limiting for auth endpoints
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 5, // limit each IP to 5 requests per windowMs
  skipSuccessfulRequests: true
});

app.use('/api/', apiLimiter);
app.use('/api/auth/', authLimiter);
```

## 🏠 Local Deployment Security

### 1. Firewall Configuration
```bash
# UFW firewall rules
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 3306/tcp from 127.0.0.1  # MySQL local only
```

### 2. System Hardening
```bash
# Disable unnecessary services
sudo systemctl disable apache2
sudo systemctl disable nginx
sudo systemctl disable ftp

# Update system
sudo apt update && sudo apt upgrade -y

# Install fail2ban
sudo apt install fail2ban -y
sudo systemctl enable fail2ban
```

### 3. Database Security
```bash
# MySQL secure installation
sudo mysql_secure_installation

# Remove test databases and anonymous users
# Set strong root password
# Disable remote root login
```

## 📋 Security Checklist

### Pre-Deployment
- [ ] All dependencies updated to latest secure versions
- [ ] Environment variables properly configured
- [ ] SSL certificates installed and configured
- [ ] Database users created with minimal privileges
- [ ] Firewall rules configured
- [ ] Rate limiting implemented
- [ ] Input validation schemas in place
- [ ] Audit logging enabled

### Post-Deployment
- [ ] Security headers verified
- [ ] HTTPS redirect working
- [ ] Rate limiting tested
- [ ] Authentication flow tested
- [ ] Authorization rules verified
- [ ] Audit logs generating correctly
- [ ] Backup procedures tested
- [ ] Monitoring alerts configured

### Regular Maintenance
- [ ] Weekly security updates
- [ ] Monthly audit log review
- [ ] Quarterly penetration testing
- [ ] Annual security assessment
- [ ] Password policy enforcement
- [ ] User access review
