# 股权管理系统数据库设计规范文档

## 📋 文档信息
- **项目名称**: 股权管理系统 (Stake Share Management System)
- **文档版本**: v1.0
- **创建日期**: 2025-06-23
- **最后更新**: 2025-06-23
- **设计原则**: 人员中心化、公司中心化、关系规范化、数据一致性

## 🎯 设计目标与原则

### 核心设计目标
1. **消除数据冗余** - 去除重复表，统一数据结构
2. **建立清晰关系** - 明确表与表之间的关联关系
3. **支持复杂业务** - 满足股权代持、变更记录、权限管理等需求
4. **确保数据一致性** - 通过外键约束和规范化设计保证数据质量
5. **便于扩展维护** - 模块化设计，便于后续功能扩展

### 设计原则
- **人员中心化**: 以persons表为核心，关联所有人员相关信息
- **公司中心化**: 以companies表为核心，管理所有业务数据
- **关系规范化**: 通过关系表管理多对多关系
- **基础数据标准化**: 枚举值通过基础数据表管理
- **操作可追溯**: 记录所有关键操作的用户和时间

## 🗂️ 数据库表分类

### 1. 核心实体表
- `persons` - 人员主表
- `companies` - 公司主表
- `users` - 系统用户表

### 2. 关系表
- `shareholdings` - 股东关系表
- `employments` - 任职关系表
- `investments` - 对外投资关系表

### 3. 基础数据表
- `business_segments` - 业务板块表
- `regions` - 地区表
- `agencies` - 代理机构表
- `annual_update_status` - 年审更新状态表
- `operation_status` - 公司存续状况表
- `contribution_methods` - 实缴出资方式表
- `change_types` - 公司变更类型表

### 4. 权限管理表
- `user_roles` - 用户角色表
- `permissions` - 权限表
- `role_permissions` - 角色权限关联表

### 5. 业务功能表
- `company_finances` - 财务信息表
- `shareholder_contributions` - 股东出资信息表
- `archive_update_rules` - 档案更新规范表
- `archive_rule_segments` - 档案规范适用业务板块关联表
- `archive_rule_regions` - 档案规范适用地区关联表
- `archive_rule_files` - 档案规范参考文件表

### 6. 审计日志表
- `company_change_logs` - 公司信息变更记录表
- `system_operation_logs` - 系统操作日志表

## 📊 核心表结构设计

### persons - 人员主表
```sql
CREATE TABLE persons (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL COMMENT '姓名',
  id_type ENUM('身份证', '护照', '其他') DEFAULT '身份证' COMMENT '证件类型',
  id_number VARCHAR(100) UNIQUE NOT NULL COMMENT '证件号码',
  phone VARCHAR(20) COMMENT '联系电话',
  email VARCHAR(100) COMMENT '邮箱地址',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by INT COMMENT '创建用户ID',
  updated_by INT COMMENT '更新用户ID',
  
  INDEX idx_person_name (name),
  INDEX idx_person_id_number (id_number),
  FOREIGN KEY (created_by) REFERENCES users(id),
  FOREIGN KEY (updated_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人员主表';
```

### companies - 公司主表
```sql
CREATE TABLE companies (
  id INT AUTO_INCREMENT PRIMARY KEY,
  company_name_cn VARCHAR(255) NOT NULL COMMENT '公司中文名',
  company_name_en VARCHAR(255) NOT NULL COMMENT '公司英文名',
  company_code VARCHAR(50) UNIQUE COMMENT '公司代码',
  registered_capital DECIMAL(15,4) COMMENT '注册资本（万元）',
  establish_date DATE COMMENT '设立日期',
  business_segment_id INT COMMENT '业务板块ID',
  region_id INT COMMENT '地区ID',
  agency_id INT COMMENT '代理机构ID',
  annual_update_status_id INT COMMENT '年审更新状态ID',
  operation_status_id INT COMMENT '存续状况ID',
  registered_address TEXT COMMENT '注册地址',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by INT COMMENT '创建用户ID',
  updated_by INT COMMENT '更新用户ID',
  
  UNIQUE INDEX idx_company_name (company_name_cn, company_name_en),
  INDEX idx_company_segment (business_segment_id),
  INDEX idx_company_region (region_id),
  FOREIGN KEY (business_segment_id) REFERENCES business_segments(id),
  FOREIGN KEY (region_id) REFERENCES regions(id),
  FOREIGN KEY (agency_id) REFERENCES agencies(id),
  FOREIGN KEY (annual_update_status_id) REFERENCES annual_update_status(id),
  FOREIGN KEY (operation_status_id) REFERENCES operation_status(id),
  FOREIGN KEY (created_by) REFERENCES users(id),
  FOREIGN KEY (updated_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司主表';
```

### shareholdings - 股东关系表
```sql
CREATE TABLE shareholdings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  person_id INT NOT NULL COMMENT '股东人员ID（名义股东）',
  company_id INT NOT NULL COMMENT '公司ID',
  investment_amount DECIMAL(15,4) COMMENT '投资金额（万元）',
  percentage DECIMAL(5,2) COMMENT '持股比例（%）',
  is_proxy BOOLEAN DEFAULT FALSE COMMENT '是否为代持',
  actual_shareholder_id INT COMMENT '实际股东ID（代持情况下）',
  start_date DATE COMMENT '开始日期',
  end_date DATE COMMENT '结束日期',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by INT COMMENT '创建用户ID',
  updated_by INT COMMENT '更新用户ID',
  
  INDEX idx_shareholding_person (person_id),
  INDEX idx_shareholding_company (company_id),
  INDEX idx_shareholding_actual (actual_shareholder_id),
  FOREIGN KEY (person_id) REFERENCES persons(id),
  FOREIGN KEY (company_id) REFERENCES companies(id),
  FOREIGN KEY (actual_shareholder_id) REFERENCES persons(id),
  FOREIGN KEY (created_by) REFERENCES users(id),
  FOREIGN KEY (updated_by) REFERENCES users(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股东关系表';
```

## 🔗 表关系说明

### 核心关系
1. **persons ↔ companies**: 通过shareholdings和employments表建立多对多关系
2. **persons → shareholdings**: 一个人可以持有多个公司股份
3. **companies → shareholdings**: 一个公司可以有多个股东
4. **persons → employments**: 一个人可以在多个公司任职
5. **companies → employments**: 一个公司可以有多个高管

### 代持关系
- `shareholdings.person_id`: 名义股东
- `shareholdings.actual_shareholder_id`: 实际股东（代持情况下）
- `shareholdings.is_proxy`: 标识是否为代持关系

### 基础数据关联
- companies表通过外键关联各基础数据表
- 避免硬编码枚举值，便于维护和扩展

## 📱 页面与表的对应关系

| 页面功能 | 主要表 | 关联表 | 说明 |
|---------|--------|--------|------|
| 股东信息页面 | persons, shareholdings | companies | 显示股东及其投资记录 |
| 任职档案页面 | persons, employments | companies | 显示人员及其任职记录 |
| 公司信息页面 | companies | business_segments, regions, agencies | 公司基本信息管理 |
| 业务板块页面 | business_segments | companies | 业务板块及关联公司 |
| 代理机构页面 | agencies | regions | 代理机构管理 |
| 财务信息页面 | company_finances | companies, persons | 财务数据管理 |
| 档案管理页面 | archive_update_rules | change_types, business_segments | 档案规范管理 |
| 用户管理页面 | users | user_roles, persons | 系统用户管理 |

## 🚀 实施计划

### 阶段一：核心表迁移
1. 创建新的persons表
2. 迁移现有股东和人员数据到persons表
3. 创建shareholdings和employments关系表
4. 迁移关系数据

### 阶段二：基础数据标准化
1. 创建所有基础数据表
2. 迁移现有枚举数据
3. 更新companies表结构
4. 建立外键关联

### 阶段三：功能表完善
1. 完善财务信息表结构
2. 创建档案管理相关表
3. 实施用户权限系统
4. 添加审计日志功能

### 阶段四：API和前端适配
1. 更新所有API接口
2. 适配前端页面
3. 测试验证功能
4. 性能优化

## 📝 维护指南

### 新增功能时的考虑
1. **遵循现有命名规范**: 使用统一的字段命名和数据类型
2. **保持关系一致性**: 新表要正确设置外键约束
3. **添加审计字段**: 重要表都要包含created_by, updated_by等字段
4. **考虑权限控制**: 新功能要在permissions表中添加相应权限

### 数据迁移注意事项
1. **备份数据**: 任何结构变更前都要完整备份
2. **分步执行**: 大批量数据迁移要分批进行
3. **验证完整性**: 迁移后要验证数据完整性和关联关系
4. **回滚准备**: 准备回滚脚本以防迁移失败

### 性能优化建议
1. **合理使用索引**: 在查询频繁的字段上建立索引
2. **分页查询**: 大数据量查询要使用分页
3. **定期维护**: 定期分析表结构和查询性能
4. **监控日志**: 监控慢查询和系统性能

## 🔄 版本控制

### 数据库版本管理
- 使用migration脚本管理数据库结构变更
- 每次变更都要记录版本号和变更说明
- 保留历史版本的结构文档

### 文档更新流程
1. 结构变更时同步更新此文档
2. 记录变更原因和影响范围
3. 更新相关的API文档和用户手册
4. 通知相关开发人员

---

**重要提醒**: 此文档是系统的核心设计文档，任何数据库结构变更都应该基于此文档进行，并及时更新文档内容。
