import React, { useState, useEffect } from 'react';
import { Table, Card, Button, Space, Tabs, Statistic, Row, Col, Divider, message } from 'antd';
import type { ColumnsType } from 'antd/es/table';
import axios from 'axios';

interface ShareholderRecord {
  id: string;
  name: string;
  idType: string;
  idNumber: string;
  stockAmount: number;
  percentage: string;
  entryDate: string;
  status: string;
}

const ShareholderRegister: React.FC = () => {
  const [data, setData] = useState<ShareholderRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const columns: ColumnsType<ShareholderRecord> = [
    {
      title: '股东名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
    },
    {
      title: '证件类型',
      dataIndex: 'idType',
      key: 'idType',
      width: 100,
    },
    {
      title: '证件号码',
      dataIndex: 'idNumber',
      key: 'idNumber',
      width: 180,
    },
    {
      title: '持股数量',
      dataIndex: 'stockAmount',
      key: 'stockAmount',
      width: 100,
    },
    {
      title: '持股比例',
      dataIndex: 'percentage',
      key: 'percentage',
      width: 100,
    },
    {
      title: '入股日期',
      dataIndex: 'entryDate',
      key: 'entryDate',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 80,
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: () => (
        <Space size="small">
          <Button type="link" size="small">查看详情</Button>
          <Button type="link" size="small">编辑</Button>
          <Button type="link" size="small">股权变更</Button>
        </Space>
      ),
    },
  ];

  // 获取股东数据
  useEffect(() => {
    const fetchShareholderData = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await axios.get('http://localhost:8080/api/shareholders');
        if (response.data.success) {
          setData(response.data.data);
          console.log('✅ 股东数据加载成功，共', response.data.data.length, '条记录');
        } else {
          throw new Error(response.data.message || '股东数据API返回失败');
        }

        setLoading(false);
      } catch (error) {
        console.error('获取股东数据失败:', error);
        setError(`获取股东数据失败: ${error.message}`);
        message.error(`获取股东数据失败: ${error.message}`);
        setLoading(false);
      }
    };

    fetchShareholderData();
  }, []);

  // 重试功能
  const handleRetry = () => {
    setError(null);
    window.location.reload();
  };

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <h3>数据加载失败</h3>
          <p style={{ color: '#ff4d4f', marginBottom: '16px' }}>{error}</p>
          <Button type="primary" onClick={handleRetry}>
            重试
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Statistic title="股东总数" value={3} />
        </Col>
        <Col span={6}>
          <Statistic title="总股本" value={10000} />
        </Col>
        <Col span={6}>
          <Statistic title="最近变更日期" value="2021-05-20" />
        </Col>
        <Col span={6}>
          <Button type="primary">导出股东名册</Button>
        </Col>
      </Row>
      <Divider />
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" style={{ marginRight: 8 }}>新增股东</Button>
        <Button style={{ marginRight: 8 }}>批量导入</Button>
      </div>
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        pagination={{
          total: data.length,
          pageSize: 10,
          current: 1,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />
    </Card>
  );
};

export default ShareholderRegister;