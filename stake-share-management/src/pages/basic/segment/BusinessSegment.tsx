import React, { useState, useEffect } from 'react';
import { Table, Card, Button, Space, Modal, Form, Input, Switch, TreeSelect, message } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import axios from 'axios';

interface SegmentRecord {
  id: string;
  name: string;
  code: string;
  parentId: string | null;
  parentName: string | null;
  level: number;
  status: boolean;
  description: string;
}

const BusinessSegment: React.FC = () => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [editingRecord, setEditingRecord] = useState<SegmentRecord | null>(null);
  const [data, setData] = useState<SegmentRecord[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const showModal = (record?: SegmentRecord) => {
    setEditingRecord(record || null);
    if (record) {
      form.setFieldsValue(record);
    } else {
      form.resetFields();
    }
    setIsModalVisible(true);
  };

  const handleOk = () => {
    form.validateFields().then(values => {
      console.log('Form values:', values);
      setIsModalVisible(false);
      form.resetFields();
    });
  };

  const handleCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  const columns: ColumnsType<SegmentRecord> = [
    {
      title: '板块名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '板块编码',
      dataIndex: 'code',
      key: 'code',
      width: 150,
    },
    {
      title: '上级板块',
      dataIndex: 'parentName',
      key: 'parentName',
      width: 200,
      render: (text) => text || '-',
    },
    {
      title: '层级',
      dataIndex: 'level',
      key: 'level',
      width: 80,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <span style={{ color: status ? '#52c41a' : '#ff4d4f' }}>
          {status ? '启用' : '禁用'}
        </span>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 300,
      ellipsis: true,
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button type="link" icon={<EditOutlined />} onClick={() => showModal(record)}>编辑</Button>
          <Button type="link" icon={<DeleteOutlined />} danger>删除</Button>
        </Space>
      ),
    },
  ];

  // 获取业务板块数据
  useEffect(() => {
    const fetchBusinessSegments = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await axios.get('http://localhost:8080/api/business-segments');
        if (response.data.success) {
          // 转换API数据格式为前端需要的格式
          const transformedData = response.data.data.map((item: any) => ({
            id: item.id.toString(),
            name: item.name,
            code: item.code || `CODE_${item.id}`,
            parentId: null, // 暂时设为null，如果API支持层级关系可以修改
            parentName: null,
            level: 1,
            status: item.is_active === 1,
            description: item.description || ''
          }));

          setData(transformedData);
          console.log('✅ 业务板块数据加载成功，共', transformedData.length, '条记录');
        } else {
          throw new Error(response.data.message || '业务板块数据API返回失败');
        }

        setLoading(false);
      } catch (error) {
        console.error('获取业务板块数据失败:', error);
        setError(`获取业务板块数据失败: ${error.message}`);
        message.error(`获取业务板块数据失败: ${error.message}`);
        setLoading(false);
      }
    };

    fetchBusinessSegments();
  }, []);

  // 重试功能
  const handleRetry = () => {
    setError(null);
    window.location.reload();
  };

  // 构建TreeSelect的数据
  const treeData = data
    .filter(item => item.level === 1)
    .map(item => ({
      title: item.name,
      value: item.id,
      children: data
        .filter(child => child.parentId === item.id)
        .map(child => ({
          title: child.name,
          value: child.id,
        })),
    }));

  // 如果有错误，显示错误状态
  if (error) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <h3>数据加载失败</h3>
          <p style={{ color: '#ff4d4f', marginBottom: '16px' }}>{error}</p>
          <Button type="primary" onClick={handleRetry}>
            重试
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <>
      <div style={{ marginBottom: 16 }}>
        <Button type="primary" icon={<PlusOutlined />} onClick={() => showModal()}>
          新增业务板块
        </Button>
      </div>
      <Table
        columns={columns}
        dataSource={data}
        rowKey="id"
        loading={loading}
        pagination={{
          total: data.length,
          pageSize: 10,
          current: 1,
          showSizeChanger: true,
          showQuickJumper: true,
        }}
      />

      <Modal
        title={editingRecord ? '编辑业务板块' : '新增业务板块'}
        open={isModalVisible}
        onOk={handleOk}
        onCancel={handleCancel}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item name="id" hidden>
            <Input />
          </Form.Item>
          <Form.Item
            name="name"
            label="板块名称"
            rules={[{ required: true, message: '请输入板块名称' }]}
          >
            <Input placeholder="请输入板块名称" />
          </Form.Item>
          <Form.Item
            name="code"
            label="板块编码"
            rules={[{ required: true, message: '请输入板块编码' }]}
          >
            <Input placeholder="请输入板块编码" />
          </Form.Item>
          <Form.Item
            name="parentId"
            label="上级板块"
          >
            <TreeSelect
              treeData={treeData}
              placeholder="请选择上级板块"
              allowClear
              treeDefaultExpandAll
            />
          </Form.Item>
          <Form.Item
            name="status"
            label="状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={4} placeholder="请输入描述信息" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

export default BusinessSegment;