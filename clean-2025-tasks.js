import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function clean2025Tasks() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 删除2025年的所有任务
    const [result] = await connection.query(
      'DELETE FROM pending_tasks WHERE year = 2025'
    );
    
    console.log(`🗑️ 已删除2025年的 ${result.affectedRows} 个任务`);

    // 获取所有公司
    const [companies] = await connection.query(
      'SELECT id, company_name_cn FROM companies ORDER BY id'
    );
    
    console.log(`\n🏢 找到 ${companies.length} 个公司:`);
    companies.forEach(company => {
      console.log(`  ID: ${company.id}, 名称: ${company.company_name_cn}`);
    });

    // 为前5个公司创建年审年报任务（模拟管年审公司）
    const managedCompanies = companies.slice(0, 5);
    console.log(`\n📊 为 ${managedCompanies.length} 个公司创建年审年报任务...`);
    
    for (const company of managedCompanies) {
      await connection.query(
        `INSERT INTO pending_tasks (
          company_id, company_name, task_type, year, deadline, task_status, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          company.id,
          company.company_name_cn,
          '年审年报',
          2025,
          '2025-12-31',
          '未开始',
          1
        ]
      );
    }

    // 为所有公司创建地址维护任务
    console.log(`\n🏠 为 ${companies.length} 个公司创建地址维护任务...`);
    
    for (const company of companies) {
      await connection.query(
        `INSERT INTO pending_tasks (
          company_id, company_name, task_type, year, deadline, task_status, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?)`,
        [
          company.id,
          company.company_name_cn,
          '地址维护',
          2025,
          '2025-12-31',
          '未开始',
          1
        ]
      );
    }

    console.log('\n✅ 2025年任务数据重新创建完成！');

    // 验证创建结果
    const [newTasks] = await connection.query(
      'SELECT task_type, COUNT(*) as count FROM pending_tasks WHERE year = 2025 GROUP BY task_type'
    );
    
    console.log('\n📋 新的2025年任务统计:');
    newTasks.forEach(task => {
      console.log(`${task.task_type}: ${task.count}个`);
    });

  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
clean2025Tasks();
