import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function fixPendingVerifyTasks() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 查询当前待核实状态的任务
    const [pendingTasks] = await connection.query(`
      SELECT id, task_status, task_type, company_name 
      FROM pending_tasks 
      WHERE task_status = '待核实'
    `);
    
    console.log(`\n📊 找到 ${pendingTasks.length} 个待核实状态的任务:`);
    pendingTasks.forEach(task => {
      console.log(`  ID: ${task.id}, 类型: ${task.task_type}, 公司: ${task.company_name}`);
    });

    if (pendingTasks.length > 0) {
      // 将所有待核实状态改为已完成
      const [result] = await connection.query(`
        UPDATE pending_tasks 
        SET task_status = '已完成', updated_at = NOW() 
        WHERE task_status = '待核实'
      `);
      
      console.log(`\n✅ 成功更新 ${result.affectedRows} 个任务状态：待核实 → 已完成`);
    } else {
      console.log('\n📝 没有找到待核实状态的任务');
    }

    // 查询更新后的状态分布
    const [statusStats] = await connection.query(`
      SELECT task_status, COUNT(*) as count 
      FROM pending_tasks 
      GROUP BY task_status 
      ORDER BY task_status
    `);
    
    console.log('\n📈 当前任务状态分布:');
    statusStats.forEach(stat => {
      console.log(`  ${stat.task_status}: ${stat.count} 个`);
    });

  } catch (error) {
    console.error('❌ 操作失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行修复
fixPendingVerifyTasks();
