-- 数据库备份 2025/6/24 15:20:39
-- 数据库: stake_management

-- 表: agencies
DROP TABLE IF EXISTS `agencies`;
CREATE TABLE `agencies` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '代理机构名称',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `region` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '地区',
  `contact_person` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '联系人姓名',
  `contact_method` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '联系方式',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=12 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 数据: agencies
INSERT INTO `agencies` (`id`, `name`, `created_at`, `region`, `contact_person`, `contact_method`) VALUES
(1, '苏州公司', '2025-06-17 10:41:32', '广州', 'John Lee', '712154521'),
(2, '代理机构B', '2025-06-17 10:41:32', '三亚', 'Alan', '712154522'),
(3, '代理机构C', '2025-06-17 10:41:32', '美国', 'Super He', '712154523'),
(8, '深圳代理', '2025-06-22 09:19:36', '深圳', 'Lucy Link', '712154524'),
(9, '北京代理', '2025-06-22 09:19:56', '北京', 'Ling Ying', '712154525'),
(10, '上海代理', '2025-06-22 09:20:17', '上海', 'Xu Yan', '712154526'),
(11, '牛逼哄哄', '2025-06-22 09:26:14', '深圳', '阿金', '88473822');

-- 表: archive_reference_rules
DROP TABLE IF EXISTS `archive_reference_rules`;
CREATE TABLE `archive_reference_rules` (
  `id` int NOT NULL AUTO_INCREMENT,
  `update_rule_id` int NOT NULL,
  `content` text COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '参考规范内容',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_archive_reference_rules_update_rule_id` (`update_rule_id`),
  CONSTRAINT `archive_reference_rules_ibfk_1` FOREIGN KEY (`update_rule_id`) REFERENCES `archive_update_rules` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案参考规范表';

-- 数据: archive_reference_rules
INSERT INTO `archive_reference_rules` (`id`, `update_rule_id`, `content`, `created_at`) VALUES
(1, 1, '98T Directory.txt', '2025-06-21 11:11:58'),
(4, 3, 'AI Programming Tools for iOS Prototypes.md', '2025-06-22 10:43:45');

-- 表: archive_update_rules
DROP TABLE IF EXISTS `archive_update_rules`;
CREATE TABLE `archive_update_rules` (
  `id` int NOT NULL AUTO_INCREMENT,
  `update_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '变更类型',
  `update_operation_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '变更操作规范名称',
  `change_steps` json DEFAULT NULL COMMENT '变更字段（JSON格式）',
  `applicable_scope` json DEFAULT NULL COMMENT '适用范围（JSON格式）',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_archive_update_rules_type` (`update_type`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案更新规范表';

-- 数据: archive_update_rules
INSERT INTO `archive_update_rules` (`id`, `update_type`, `update_operation_name`, `change_steps`, `applicable_scope`, `created_at`, `updated_at`) VALUES
(1, '基础信息变更', 'aaa', [object Object], [object Object], '2025-06-21 11:11:58', '2025-06-21 11:11:58'),
(3, '股东信息变更', 'CCCC', [object Object], [object Object], '2025-06-21 11:31:15', '2025-06-22 10:43:45');

-- 表: archive_upload_files
DROP TABLE IF EXISTS `archive_upload_files`;
CREATE TABLE `archive_upload_files` (
  `id` int NOT NULL AUTO_INCREMENT,
  `update_rule_id` int NOT NULL,
  `file_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '文件名称',
  `file_path` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '文件路径',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_archive_upload_files_update_rule_id` (`update_rule_id`),
  CONSTRAINT `archive_upload_files_ibfk_1` FOREIGN KEY (`update_rule_id`) REFERENCES `archive_update_rules` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案上传文件表';

-- 表: business_segments
DROP TABLE IF EXISTS `business_segments`;
CREATE TABLE `business_segments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务板块名称',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 数据: business_segments
INSERT INTO `business_segments` (`id`, `name`, `created_at`) VALUES
(1, '电子商务', '2025-06-17 10:41:32'),
(11, 'A. 农、林、牧、渔业', '2025-06-22 16:10:42'),
(12, 'B. 采矿业', '2025-06-22 16:10:42'),
(13, 'C. 制造业', '2025-06-22 16:10:42'),
(14, 'D. 电力、热力、燃气及水的生产和供应业', '2025-06-22 16:10:42'),
(15, 'E. 建筑业', '2025-06-22 16:10:42'),
(16, 'F. 批发和零售业', '2025-06-22 16:10:42'),
(17, 'G. 交通运输、仓储和邮政业', '2025-06-22 16:10:42'),
(18, 'H. 住宿和餐饮业', '2025-06-22 16:10:42'),
(19, 'I. 信息传输、软件和信息技术服务业', '2025-06-22 16:10:42'),
(20, 'J. 金融业', '2025-06-22 16:10:42'),
(21, 'K. 房地产业', '2025-06-22 16:10:42'),
(22, 'L. 租赁和商务服务业', '2025-06-22 16:10:42'),
(23, 'M. 科学研究和技术服务业', '2025-06-22 16:10:42'),
(24, 'N. 水利、环境和公共设施管理业', '2025-06-22 16:10:42'),
(25, 'O. 居民服务、修理和其他服务业', '2025-06-22 16:10:42'),
(26, 'P. 教育', '2025-06-22 16:10:42'),
(27, 'Q. 卫生和社会工作', '2025-06-22 16:10:42'),
(28, 'R. 文化、体育和娱乐业', '2025-06-22 16:10:42'),
(29, 'S. 公共管理、社会保障和社会组织', '2025-06-22 16:10:42'),
(30, 'T. 国际组织', '2025-06-22 16:10:42');

-- 表: companies
DROP TABLE IF EXISTS `companies`;
CREATE TABLE `companies` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_name_cn` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公司中文名',
  `company_name_en` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公司英文名',
  `registered_capital` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '注册资本（万元）',
  `establish_date` date DEFAULT NULL COMMENT '设立日期',
  `business_segment` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '业务板块',
  `region` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '地区',
  `agency` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '代理机构',
  `annual_update` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '年审更新',
  `registered_address` text COLLATE utf8mb4_unicode_ci COMMENT '注册地址',
  `operation_status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '存续状况',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_company_name` (`company_name_cn`,`company_name_en`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 数据: companies
INSERT INTO `companies` (`id`, `company_name_cn`, `company_name_en`, `registered_capital`, `establish_date`, `business_segment`, `region`, `agency`, `annual_update`, `registered_address`, `operation_status`, `created_at`, `updated_at`) VALUES
(1, '测试公司', 'Test Company', '1000.0000万元人民币', '2023-06-14 16:00:00', '电子商务', '华南', '代理机构A', '不管年审', '广州市天河区测试地址123号', '正常经营', '2025-06-17 10:41:40', '2025-06-17 10:41:40'),
(3, '牛逼', 'ABC', '122', '2025-05-30 16:00:00', 'C. 制造业', '上海', '代理机构A', '管年审（固定周期）', '上海', '正常经营', '2025-06-21 05:18:10', '2025-06-21 05:18:10'),
(4, '以为', 'yiwei', '300', '2025-05-30 16:00:00', 'C. 制造业', '上海', '代理机构A', '不管年审', '1029
yuanping', '正常经营', '2025-06-21 08:02:16', '2025-06-21 08:02:16'),
(5, '深圳科技有限公司', 'Shenzhen Tech Co., Ltd.', '1000万元人民币', '2023-01-14 16:00:00', 'A. 农、林、牧、渔业', '深圳', '代理机构A', '不管年审', '深圳市南山区科技园', '正常经营', '2025-06-23 03:54:37', '2025-06-23 03:54:37'),
(6, '广州贸易有限公司', 'Guangzhou Trade Co., Ltd.', '2000万元人民币', '2023-02-19 16:00:00', 'A. 农、林、牧、渔业', '广州', '代理机构B', '管年审（固定周期）', '广州市天河区商务中心', '正常经营', '2025-06-23 03:54:37', '2025-06-23 03:54:37'),
(7, '上海投资有限公司', 'Shanghai Investment Co., Ltd.', '5000万元人民币', '2023-03-09 16:00:00', 'B. 采矿业', '上海', '代理机构C', '管年审（滚动周期）', '上海市浦东新区金融街', '正常经营', '2025-06-23 03:54:37', '2025-06-23 03:54:37'),
(8, '北京制造有限公司', 'Beijing Manufacturing Co., Ltd.', '3000万元人民币', '2023-04-04 16:00:00', 'C. 制造业', '北京', '代理机构A', '不管年审', '北京市朝阳区工业园', '正常经营', '2025-06-23 03:54:37', '2025-06-23 03:54:37'),
(9, '杭州电力有限公司', 'Hangzhou Power Co., Ltd.', '8000万元人民币', '2023-05-11 16:00:00', 'D. 电力、热力、燃气及水的生产和供应业', '杭州', '代理机构B', '管年审（固定周期）', '杭州市西湖区电力大厦', '正常经营', '2025-06-23 03:54:37', '2025-06-23 03:54:37'),
(10, '成都建筑有限公司', 'Chengdu Construction Co., Ltd.', '4000万元人民币', '2023-06-17 16:00:00', 'E. 建筑业', '成都', '代理机构C', '管年审（滚动周期）', '成都市高新区建筑园', '正常经营', '2025-06-23 03:54:37', '2025-06-23 03:54:37');

-- 表: company_finances
DROP TABLE IF EXISTS `company_finances`;
CREATE TABLE `company_finances` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL,
  `year` varchar(4) COLLATE utf8mb4_unicode_ci NOT NULL,
  `total_assets` decimal(15,4) DEFAULT '0.0000' COMMENT '资产总额（万元人民币）',
  `total_liabilities` decimal(15,4) DEFAULT '0.0000' COMMENT '负债总额（万元人民币）',
  `total_equity` decimal(15,4) DEFAULT '0.0000' COMMENT '所有者权益合计（万元人民币）',
  `business_income` decimal(15,4) DEFAULT '0.0000' COMMENT '营业总收入（万元人民币）',
  `main_business_income` decimal(15,4) DEFAULT '0.0000' COMMENT '主营业务收入（万元人民币）',
  `profit_before_tax` decimal(15,4) DEFAULT '0.0000' COMMENT '利润总额（万元人民币）',
  `net_profit` decimal(15,4) DEFAULT '0.0000' COMMENT '净利润（万元人民币）',
  `tax_payable` decimal(15,4) DEFAULT '0.0000' COMMENT '纳税总额（万元人民币）',
  `remarks` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_company_year` (`company_id`,`year`),
  KEY `idx_company_finances_company_id` (`company_id`),
  KEY `idx_company_finances_year` (`year`),
  CONSTRAINT `company_finances_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司财务信息表';

-- 数据: company_finances
INSERT INTO `company_finances` (`id`, `company_id`, `year`, `total_assets`, `total_liabilities`, `total_equity`, `business_income`, `main_business_income`, `profit_before_tax`, `net_profit`, `tax_payable`, `remarks`, `created_at`, `updated_at`) VALUES
(1, 3, '2024', '500.0000', '200.0000', '300.0000', '700.0000', '500.0000', '50.0000', '20.0000', '5.0000', '', '2025-06-21 09:28:18', '2025-06-21 09:28:18'),
(2, 1, '2023', '500.0000', '200.0000', '300.0000', '700.0000', '500.0000', '50.0000', '20.0000', '5.0000', '', '2025-06-21 09:29:42', '2025-06-21 09:29:42');

-- 表: employment_records
DROP TABLE IF EXISTS `employment_records`;
CREATE TABLE `employment_records` (
  `id` int NOT NULL AUTO_INCREMENT,
  `person_id` int NOT NULL COMMENT '人员ID',
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公司名称',
  `position` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '职位',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `person_id` (`person_id`),
  CONSTRAINT `employment_records_ibfk_1` FOREIGN KEY (`person_id`) REFERENCES `persons` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任职记录表';

-- 数据: employment_records
INSERT INTO `employment_records` (`id`, `person_id`, `company_name`, `position`, `start_date`, `end_date`, `created_at`, `updated_at`) VALUES
(1, 1, '上海投资有限公司(Shenzhen A Company)', '董事', '2023-11-09 16:00:00', NULL, '2025-06-23 05:37:46', '2025-06-23 05:37:46'),
(2, 2, '上海投资有限公司(Shenzhen A Company)', '董事', '2023-11-09 16:00:00', NULL, '2025-06-23 05:37:46', '2025-06-23 05:37:46'),
(3, 3, '上海投资有限公司(Shenzhen A Company)', '董事', '2023-11-09 16:00:00', NULL, '2025-06-23 05:37:46', '2025-06-23 05:37:46'),
(4, 4, '上海投资有限公司(Shenzhen A Company)', '董事', '2023-11-09 16:00:00', NULL, '2025-06-23 05:37:46', '2025-06-23 05:37:46'),
(5, 5, '上海投资有限公司(Shenzhen A Company)', '董事', '2023-11-09 16:00:00', NULL, '2025-06-23 05:37:46', '2025-06-23 05:37:46');

-- 表: executives
DROP TABLE IF EXISTS `executives`;
CREATE TABLE `executives` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL COMMENT '关联的公司ID',
  `position` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '职位',
  `person` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '任职人员',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`),
  CONSTRAINT `executives_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 表: investment_records
DROP TABLE IF EXISTS `investment_records`;
CREATE TABLE `investment_records` (
  `id` int NOT NULL AUTO_INCREMENT,
  `shareholder_id` int NOT NULL COMMENT '股东ID',
  `company_id` int DEFAULT NULL COMMENT '公司ID',
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公司名称',
  `investment_amount` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '投资金额',
  `percentage` varchar(20) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '持股比例',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `shareholder_id` (`shareholder_id`),
  KEY `company_id` (`company_id`),
  CONSTRAINT `investment_records_ibfk_1` FOREIGN KEY (`shareholder_id`) REFERENCES `shareholder_entities` (`id`) ON DELETE CASCADE,
  CONSTRAINT `investment_records_ibfk_2` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='投资记录表';

-- 数据: investment_records
INSERT INTO `investment_records` (`id`, `shareholder_id`, `company_id`, `company_name`, `investment_amount`, `percentage`, `start_date`, `end_date`, `created_at`, `updated_at`) VALUES
(1, 3, 7, '上海投资有限公司(Shenzhen A Company)', '12%', '12%', '2023-11-09 16:00:00', NULL, '2025-06-23 05:20:48', '2025-06-23 05:20:48'),
(2, 4, 7, '上海投资有限公司(Shenzhen A Company)', '12%', '12%', '2023-11-09 16:00:00', NULL, '2025-06-23 05:20:48', '2025-06-23 05:20:48'),
(3, 2, 7, '上海投资有限公司(Shenzhen A Company)', '12%', '12%', '2023-11-09 16:00:00', NULL, '2025-06-23 05:20:48', '2025-06-23 05:20:48'),
(4, 1, 7, '上海投资有限公司(Shenzhen A Company)', '12%', '12%', '2023-11-09 16:00:00', NULL, '2025-06-23 05:20:48', '2025-06-23 05:20:48');

-- 表: investments
DROP TABLE IF EXISTS `investments`;
CREATE TABLE `investments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL COMMENT '关联的公司ID',
  `company_name` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '投资对象',
  `investment_amount` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '投资金额（万元）',
  `percentage` decimal(5,2) DEFAULT NULL COMMENT '持股比例（%）',
  `start_date` date DEFAULT NULL COMMENT '投资日期',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`),
  CONSTRAINT `investments_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 表: payment_methods
DROP TABLE IF EXISTS `payment_methods`;
CREATE TABLE `payment_methods` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '实缴出资方式名称',
  `sort` int DEFAULT '0' COMMENT '排序',
  `updater` varchar(100) DEFAULT '' COMMENT '更新人',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='实缴出资方式表';

-- 数据: payment_methods
INSERT INTO `payment_methods` (`id`, `name`, `sort`, `updater`, `update_time`, `create_time`) VALUES
(1, '货币', 1, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(2, '实物', 2, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(3, '知识产权', 3, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(4, '土地使用权', 4, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(5, '股权', 5, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(6, '债权', 6, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(7, '其他财产权利', 7, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03');

-- 表: persons
DROP TABLE IF EXISTS `persons`;
CREATE TABLE `persons` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '姓名',
  `id_type` enum('身份证','护照','其他') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '身份证' COMMENT '证件类型',
  `id_number` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '证件号码',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_id_number` (`id_number`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人员表';

-- 数据: persons
INSERT INTO `persons` (`id`, `name`, `id_type`, `id_number`, `created_at`, `updated_at`) VALUES
(1, '苏州海投资中心', '身份证', '320100199001011234', '2025-06-23 05:37:46', '2025-06-23 05:37:46'),
(2, '苏州海投资中心', '身份证', '320100199002022345', '2025-06-23 05:37:46', '2025-06-23 05:37:46'),
(3, '张天成', '身份证', '320100199003033456', '2025-06-23 05:37:46', '2025-06-23 05:37:46'),
(4, '张大威', '身份证', '320100199004044567', '2025-06-23 05:37:46', '2025-06-23 05:37:46'),
(5, '张天威', '身份证', '320100199005055678', '2025-06-23 05:37:46', '2025-06-23 05:37:46');

-- 表: positions
DROP TABLE IF EXISTS `positions`;
CREATE TABLE `positions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '职位名称',
  `sort` int DEFAULT '0' COMMENT '排序',
  `updater` varchar(100) DEFAULT '' COMMENT '更新人',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='任职职位表';

-- 数据: positions
INSERT INTO `positions` (`id`, `name`, `sort`, `updater`, `update_time`, `create_time`) VALUES
(1, '财务负责人', 1, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(2, '董事', 2, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(3, '监事', 3, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(4, '总经理', 4, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(5, '副总经理', 5, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(6, '董事长', 6, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(7, '副董事长', 7, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(8, '执行董事', 8, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(9, '经理', 9, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03'),
(10, '法定代表人', 10, 'System', '2025-06-22 10:02:03', '2025-06-22 10:02:03');

-- 表: region
DROP TABLE IF EXISTS `region`;
CREATE TABLE `region` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` varchar(50) NOT NULL COMMENT '类型：国内/海外公司对外投资',
  `region` varchar(100) NOT NULL COMMENT '地区名称',
  `create_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_type_region` (`type`,`region`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='地区管理表';

-- 数据: region
INSERT INTO `region` (`id`, `type`, `region`, `create_time`, `update_time`) VALUES
(1, '国内', '广州', '2025-06-22 08:45:44', '2025-06-22 08:45:44'),
(2, '海外公司对外投资', '亚太', '2025-06-22 08:45:44', '2025-06-22 08:45:44'),
(3, '海外公司对外投资', '美国', '2025-06-22 08:45:44', '2025-06-22 08:45:44'),
(4, '国内', '深圳', '2025-06-22 08:45:44', '2025-06-22 08:45:44'),
(6, '国内', '上海', '2025-06-22 08:45:44', '2025-06-22 08:45:44'),
(7, '国内', '三亚', '2025-06-22 08:46:20', '2025-06-22 08:46:20'),
(8, '国内', '北京', '2025-06-22 08:48:14', '2025-06-22 08:48:14');

-- 表: regions
DROP TABLE IF EXISTS `regions`;
CREATE TABLE `regions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地区名称',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=11 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 数据: regions
INSERT INTO `regions` (`id`, `name`, `created_at`) VALUES
(1, '华南', '2025-06-17 10:41:32'),
(2, '华东', '2025-06-17 10:41:32'),
(3, '华北', '2025-06-17 10:41:32'),
(4, '西南', '2025-06-17 10:41:32'),
(5, '东北', '2025-06-17 10:41:32');

-- 表: shareholder_contributions
DROP TABLE IF EXISTS `shareholder_contributions`;
CREATE TABLE `shareholder_contributions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `finance_id` int NOT NULL,
  `shareholder_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '股东名称',
  `contribution_amount` decimal(15,4) DEFAULT '0.0000' COMMENT '实缴出资额（万元人民币）',
  `contribution_method` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '实缴出资方式',
  `contribution_time` date DEFAULT NULL COMMENT '实缴出资时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_shareholder_contributions_finance_id` (`finance_id`),
  KEY `idx_shareholder_contributions_shareholder_name` (`shareholder_name`),
  CONSTRAINT `shareholder_contributions_ibfk_1` FOREIGN KEY (`finance_id`) REFERENCES `company_finances` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股东及出资信息表';

-- 数据: shareholder_contributions
INSERT INTO `shareholder_contributions` (`id`, `finance_id`, `shareholder_name`, `contribution_amount`, `contribution_method`, `contribution_time`, `created_at`, `updated_at`) VALUES
(1, 1, '股东B', '88.0000', '货币', '2025-05-31 16:00:00', '2025-06-21 09:28:18', '2025-06-21 09:28:18');

-- 表: shareholder_entities
DROP TABLE IF EXISTS `shareholder_entities`;
CREATE TABLE `shareholder_entities` (
  `id` int NOT NULL AUTO_INCREMENT,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '股东名称',
  `type` enum('individual','company','external') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'external' COMMENT '股东类型：个人、内部主体、外部投资主体',
  `id_number` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '身份证号/统一社会信用代码',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_name_type` (`name`,`type`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股东实体表';

-- 数据: shareholder_entities
INSERT INTO `shareholder_entities` (`id`, `name`, `type`, `id_number`, `created_at`, `updated_at`) VALUES
(1, '苏州海投资中心', 'external', NULL, '2025-06-23 05:20:48', '2025-06-23 05:20:48'),
(2, '张天成', 'individual', NULL, '2025-06-23 05:20:48', '2025-06-23 05:20:48'),
(3, '张大威', 'individual', NULL, '2025-06-23 05:20:48', '2025-06-23 05:20:48'),
(4, '张天威', 'individual', NULL, '2025-06-23 05:20:48', '2025-06-23 05:20:48');

-- 表: shareholders
DROP TABLE IF EXISTS `shareholders`;
CREATE TABLE `shareholders` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL COMMENT '关联的公司ID',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '股东名称',
  `investment_amount` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '认缴出资额（万元）',
  `percentage` decimal(5,2) DEFAULT NULL COMMENT '持股比例（%）',
  `start_date` date DEFAULT NULL COMMENT '出资日期',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `company_id` (`company_id`),
  CONSTRAINT `shareholders_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

