import mysql from 'mysql2/promise';

const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function createTestDataPart2() {
  console.log('🔄 开始创建第二部分测试数据...');
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 获取基础数据ID
    const [companies] = await connection.query('SELECT id FROM companies ORDER BY id');
    const [persons] = await connection.query('SELECT id FROM persons ORDER BY id');
    const [contributionMethods] = await connection.query('SELECT id FROM contribution_methods ORDER BY id');
    const [changeTypes] = await connection.query('SELECT id FROM change_types ORDER BY id');
    const [businessSegments] = await connection.query('SELECT id FROM business_segments ORDER BY id');
    const [regions] = await connection.query('SELECT id, type, name FROM regions ORDER BY id');
    
    // 1. 创建对外投资关系测试数据
    console.log('\n💼 创建对外投资关系测试数据...');
    
    const investments = [];
    for (let i = 0; i < 12; i++) {
      const investorCompanyId = companies[i % companies.length].id;
      const investeeCompanyId = i < companies.length - 1 ? companies[i + 1].id : null;
      const investeeCompanyName = `被投资公司${i + 1}`;
      const investmentAmount = (Math.random() * 5000 + 500).toFixed(4);
      const percentage = (Math.random() * 50 + 10).toFixed(2);
      const startDate = new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
      const endDate = Math.random() > 0.8 ? new Date(startDate.getTime() + Math.random() * 365 * 24 * 60 * 60 * 1000 * 2) : null;
      
      investments.push([investorCompanyId, investeeCompanyId, investeeCompanyName, investmentAmount, percentage, startDate, endDate]);
    }
    
    for (const [investorId, investeeId, investeeName, amount, percentage, startDate, endDate] of investments) {
      await connection.query(
        `INSERT INTO investments (
          investor_company_id, investee_company_id, investee_company_name, 
          investment_amount, percentage, start_date, end_date, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 1)`,
        [investorId, investeeId, investeeName, amount, percentage, startDate, endDate]
      );
    }
    console.log(`✅ 创建了 ${investments.length} 条对外投资记录`);
    
    // 2. 创建财务信息测试数据
    console.log('\n💰 创建财务信息测试数据...');
    
    const finances = [];
    const years = ['2021', '2022', '2023', '2024'];
    
    for (let i = 0; i < companies.length; i++) {
      for (let j = 0; j < years.length; j++) {
        if (Math.random() > 0.3) { // 70%概率创建财务记录
          const companyId = companies[i].id;
          const year = years[j];
          const totalAssets = (Math.random() * 10000 + 1000).toFixed(4);
          const totalLiabilities = (parseFloat(totalAssets) * (Math.random() * 0.6 + 0.1)).toFixed(4);
          const totalEquity = (parseFloat(totalAssets) - parseFloat(totalLiabilities)).toFixed(4);
          const businessIncome = (Math.random() * 5000 + 500).toFixed(4);
          const mainBusinessIncome = (parseFloat(businessIncome) * (Math.random() * 0.3 + 0.7)).toFixed(4);
          const profitBeforeTax = (parseFloat(businessIncome) * (Math.random() * 0.2 + 0.05)).toFixed(4);
          const netProfit = (parseFloat(profitBeforeTax) * (Math.random() * 0.3 + 0.7)).toFixed(4);
          const taxPayable = (parseFloat(profitBeforeTax) - parseFloat(netProfit)).toFixed(4);
          const remarks = `${year}年度财务数据`;
          
          finances.push([companyId, year, totalAssets, totalLiabilities, totalEquity, businessIncome, mainBusinessIncome, profitBeforeTax, netProfit, taxPayable, remarks]);
        }
      }
    }
    
    for (const [companyId, year, assets, liabilities, equity, income, mainIncome, profitBefore, netProfit, tax, remarks] of finances) {
      await connection.query(
        `INSERT INTO company_finances (
          company_id, year, total_assets, total_liabilities, total_equity,
          business_income, main_business_income, profit_before_tax, net_profit, tax_payable,
          remarks, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
        ON DUPLICATE KEY UPDATE total_assets = VALUES(total_assets)`,
        [companyId, year, assets, liabilities, equity, income, mainIncome, profitBefore, netProfit, tax, remarks]
      );
    }
    console.log(`✅ 创建了 ${finances.length} 条财务信息记录`);
    
    // 3. 创建股东出资信息测试数据
    console.log('\n💵 创建股东出资信息测试数据...');
    
    const [financeIds] = await connection.query('SELECT id FROM company_finances ORDER BY id');
    const contributions = [];
    
    for (let i = 0; i < Math.min(15, financeIds.length); i++) {
      const financeId = financeIds[i].id;
      const personId = persons[i % persons.length].id;
      const contributionAmount = (Math.random() * 1000 + 50).toFixed(4);
      const contributionMethodId = contributionMethods[i % contributionMethods.length].id;
      const contributionTime = new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
      
      contributions.push([financeId, personId, contributionAmount, contributionMethodId, contributionTime]);
    }
    
    for (const [financeId, personId, amount, methodId, time] of contributions) {
      await connection.query(
        `INSERT INTO shareholder_contributions (
          finance_id, person_id, contribution_amount, contribution_method_id, contribution_time
        ) VALUES (?, ?, ?, ?, ?)`,
        [financeId, personId, amount, methodId, time]
      );
    }
    console.log(`✅ 创建了 ${contributions.length} 条股东出资记录`);
    
    // 4. 创建档案更新规范测试数据
    console.log('\n📋 创建档案更新规范测试数据...');
    
    const archiveRules = [
      [1, '公司名称变更规范', '["company_name_cn", "company_name_en"]', '公司名称变更的标准操作流程'],
      [1, '注册资本变更规范', '["registered_capital"]', '注册资本变更的标准操作流程'],
      [1, '注册地址变更规范', '["registered_address"]', '注册地址变更的标准操作流程'],
      [2, '董事长变更规范', '["position", "person_id"]', '董事长变更的标准操作流程'],
      [2, '总经理变更规范', '["position", "person_id"]', '总经理变更的标准操作流程'],
      [2, '财务总监变更规范', '["position", "person_id"]', '财务总监变更的标准操作流程'],
      [3, '股东增加规范', '["person_id", "investment_amount", "percentage"]', '新增股东的标准操作流程'],
      [3, '股东减少规范', '["person_id", "end_date"]', '股东退出的标准操作流程'],
      [3, '股权转让规范', '["person_id", "percentage", "investment_amount"]', '股权转让的标准操作流程'],
      [4, '对外投资新增规范', '["investee_company_name", "investment_amount", "percentage"]', '新增对外投资的标准操作流程'],
      [4, '对外投资变更规范', '["investment_amount", "percentage"]', '对外投资变更的标准操作流程'],
      [4, '对外投资退出规范', '["end_date"]', '对外投资退出的标准操作流程']
    ];
    
    for (const [changeTypeId, operationName, allowedFields, description] of archiveRules) {
      await connection.query(
        `INSERT INTO archive_update_rules (
          change_type_id, operation_name, allowed_fields, description, created_by
        ) VALUES (?, ?, ?, ?, 1)`,
        [changeTypeId, operationName, allowedFields, description]
      );
    }
    console.log(`✅ 创建了 ${archiveRules.length} 条档案更新规范`);
    
    // 5. 创建档案规范适用业务板块关联
    console.log('\n🔗 创建档案规范适用业务板块关联...');
    
    const [ruleIds] = await connection.query('SELECT id FROM archive_update_rules ORDER BY id');
    const ruleSegments = [];
    
    for (let i = 0; i < ruleIds.length; i++) {
      const ruleId = ruleIds[i].id;
      // 每个规范适用2-4个业务板块
      const segmentCount = Math.floor(Math.random() * 3) + 2;
      for (let j = 0; j < segmentCount; j++) {
        const segmentId = businessSegments[(i + j) % businessSegments.length].id;
        ruleSegments.push([ruleId, segmentId]);
      }
    }
    
    for (const [ruleId, segmentId] of ruleSegments) {
      await connection.query(
        `INSERT INTO archive_rule_segments (rule_id, segment_id) VALUES (?, ?)
         ON DUPLICATE KEY UPDATE rule_id = VALUES(rule_id)`,
        [ruleId, segmentId]
      );
    }
    console.log(`✅ 创建了 ${ruleSegments.length} 条档案规范业务板块关联`);
    
    // 6. 创建档案规范适用地区关联
    console.log('\n🌍 创建档案规范适用地区关联...');
    
    const ruleRegions = [];
    for (let i = 0; i < ruleIds.length; i++) {
      const ruleId = ruleIds[i].id;
      // 每个规范适用1-3个地区
      const regionCount = Math.floor(Math.random() * 3) + 1;
      for (let j = 0; j < regionCount; j++) {
        const region = regions[(i + j) % regions.length];
        ruleRegions.push([ruleId, region.type, region.id]);
      }
    }
    
    for (const [ruleId, regionType, regionId] of ruleRegions) {
      await connection.query(
        `INSERT INTO archive_rule_regions (rule_id, region_type, region_id) VALUES (?, ?, ?)
         ON DUPLICATE KEY UPDATE rule_id = VALUES(rule_id)`,
        [ruleId, regionType, regionId]
      );
    }
    console.log(`✅ 创建了 ${ruleRegions.length} 条档案规范地区关联`);
    
    console.log('\n🎉 第二部分测试数据创建完成！');
    
  } catch (error) {
    console.error('❌ 创建第二部分测试数据失败:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

createTestDataPart2()
  .then(() => console.log('🎉 第二部分测试数据创建成功完成'))
  .catch(err => console.error('💥 创建失败:', err));
