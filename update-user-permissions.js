import mysql from 'mysql2/promise';
import fs from 'fs';

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function updateUserPermissions() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 读取SQL文件
    const sqlContent = fs.readFileSync('update-user-roles-permissions.sql', 'utf8');
    
    // 分割SQL语句（按分号分割，忽略注释）
    const statements = sqlContent
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt && !stmt.startsWith('--') && !stmt.startsWith('/*'));

    console.log(`📝 准备执行 ${statements.length} 条SQL语句`);

    // 逐条执行SQL语句
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      if (statement) {
        try {
          console.log(`⚡ 执行第 ${i + 1} 条语句...`);
          const [result] = await connection.execute(statement);
          console.log(`✅ 第 ${i + 1} 条语句执行成功`);
        } catch (error) {
          console.error(`❌ 第 ${i + 1} 条语句执行失败:`, error.message);
          console.log('语句内容:', statement.substring(0, 100) + '...');
        }
      }
    }

    // 验证更新结果
    console.log('\n🔍 验证更新结果...');
    
    // 检查角色
    const [roles] = await connection.execute('SELECT * FROM user_roles ORDER BY id');
    console.log('📋 用户角色:');
    roles.forEach(role => {
      console.log(`  ${role.id}: ${role.name} - ${role.description}`);
    });

    // 检查权限数量
    const [permCount] = await connection.execute('SELECT COUNT(*) as count FROM permissions');
    console.log(`📊 权限总数: ${permCount[0].count}`);

    // 检查角色权限关联数量
    const [rolePermCount] = await connection.execute('SELECT COUNT(*) as count FROM role_permissions');
    console.log(`🔗 角色权限关联数: ${rolePermCount[0].count}`);

    // 检查用户角色分布
    const [userRoles] = await connection.execute(`
      SELECT ur.name as role_name, COUNT(u.id) as user_count 
      FROM user_roles ur 
      LEFT JOIN users u ON ur.id = u.role_id 
      GROUP BY ur.id, ur.name 
      ORDER BY ur.id
    `);
    console.log('👥 用户角色分布:');
    userRoles.forEach(ur => {
      console.log(`  ${ur.role_name}: ${ur.user_count} 个用户`);
    });

    console.log('\n🎉 用户权限系统更新完成！');

  } catch (error) {
    console.error('❌ 更新失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行更新
updateUserPermissions().catch(console.error);
