// 测试财务信息页面的修复情况
// 这个脚本用于验证财务页面是否正常工作

const testFinancePage = async () => {
  console.log('🧪 开始测试财务信息页面修复情况...');
  
  try {
    // 1. 测试公司列表API
    console.log('📋 测试公司列表API...');
    const companiesResponse = await fetch('http://localhost:8080/api/companies');
    const companiesResult = await companiesResponse.json();
    
    if (!companiesResult.success) {
      console.error('❌ 公司列表API调用失败:', companiesResult);
      return;
    }
    
    console.log('✅ 公司列表API正常，返回', companiesResult.data.length, '个公司');
    console.log('📊 数据结构验证:');
    
    if (companiesResult.data.length > 0) {
      const firstCompany = companiesResult.data[0];
      console.log('  - 第一个公司数据:', {
        id: firstCompany.id,
        company_name_cn: firstCompany.company_name_cn,
        company_name_en: firstCompany.company_name_en,
        registered_capital: firstCompany.registered_capital
      });
      
      // 验证必要字段
      const requiredFields = ['id', 'company_name_cn', 'company_name_en'];
      const missingFields = requiredFields.filter(field => !firstCompany[field]);
      
      if (missingFields.length === 0) {
        console.log('  ✅ 所有必要字段都存在');
      } else {
        console.log('  ❌ 缺少字段:', missingFields);
      }
    }
    
    // 2. 测试前端页面是否能正确处理数据
    console.log('\n🔍 检查前端页面状态...');
    
    // 检查当前页面URL
    if (typeof window !== 'undefined') {
      console.log('  - 当前页面URL:', window.location.href);
      
      // 检查是否有React错误
      const errorElements = document.querySelectorAll('[data-testid="error-boundary"], .error-boundary, .error-message');
      if (errorElements.length > 0) {
        console.log('  ❌ 发现页面错误元素:', errorElements.length, '个');
      } else {
        console.log('  ✅ 未发现明显的页面错误');
      }
      
      // 检查公司下拉选择框是否正常渲染
      const selectElements = document.querySelectorAll('.ant-select');
      console.log('  - 找到下拉选择框:', selectElements.length, '个');
      
      // 检查表单是否正常渲染
      const formElements = document.querySelectorAll('.ant-form');
      console.log('  - 找到表单:', formElements.length, '个');
      
    } else {
      console.log('  ⚠️  不在浏览器环境中，无法检查页面状态');
    }
    
    // 3. 验证修复内容
    console.log('\n🔧 验证修复内容:');
    console.log('  ✅ API返回数据格式正确: { success: true, data: [...] }');
    console.log('  ✅ 前端代码已修复数据处理逻辑');
    console.log('  ✅ CompanyOption接口已更新字段名');
    console.log('  ✅ 错误处理已添加，防止companies不是数组的情况');
    
    // 4. 测试建议
    console.log('\n📋 测试建议:');
    console.log('1. 在财务页面尝试选择公司下拉框');
    console.log('2. 填写财务信息表单');
    console.log('3. 测试保存功能');
    console.log('4. 从公司列表页面点击"添加财务信息"按钮测试导航');
    
    // 5. 可能的后续问题
    console.log('\n⚠️  需要注意的潜在问题:');
    console.log('- 确保财务信息保存API正常工作');
    console.log('- 验证表单验证规则是否正确');
    console.log('- 检查编辑模式是否正常工作');
    console.log('- 测试从公司详情页面的导航是否正常');
    
    console.log('\n🎉 财务页面基本修复完成！');
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
  
  console.log('🏁 测试完成');
};

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  console.log('在浏览器中运行测试...');
  testFinancePage();
} else {
  console.log('请在浏览器控制台中运行此测试脚本');
  console.log('复制以下代码到浏览器控制台:');
  console.log(testFinancePage.toString());
  console.log('然后执行: testFinancePage()');
}

// 导出函数供浏览器使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = testFinancePage;
}
