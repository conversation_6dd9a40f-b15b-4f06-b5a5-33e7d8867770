-- 更新地区类型，添加"离岸"选项
-- 执行日期：2025-01-07

USE stake_management_v2;

-- 1. 修改regions表的type字段，添加"离岸"选项
ALTER TABLE regions 
MODIFY COLUMN type ENUM('国内', '离岸', '海外') NOT NULL COMMENT '地区类型';

-- 2. 修改archive_rule_regions表的region_type字段，添加"离岸"选项
ALTER TABLE archive_rule_regions 
MODIFY COLUMN region_type ENUM('国内', '离岸', '海外') NOT NULL COMMENT '地区类型';

-- 3. 如果存在region表（旧版本），也需要更新
-- 检查是否存在region表
SET @table_exists = (SELECT COUNT(*) FROM information_schema.tables 
                    WHERE table_schema = 'stake_management_v2' 
                    AND table_name = 'region');

-- 如果存在region表，更新其type字段
SET @sql = IF(@table_exists > 0, 
    'ALTER TABLE region MODIFY COLUMN type VARCHAR(50) NOT NULL COMMENT ''类型：国内/离岸/海外''',
    'SELECT "region表不存在，跳过更新" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 添加一些离岸地区的示例数据
INSERT IGNORE INTO regions (type, name, is_active) VALUES
('离岸', '开曼群岛', TRUE),
('离岸', '英属维尔京群岛', TRUE),
('离岸', '百慕大', TRUE),
('离岸', '塞舌尔', TRUE),
('离岸', '萨摩亚', TRUE),
('离岸', '马绍尔群岛', TRUE);

-- 5. 更新视图（如果需要重新创建）
DROP VIEW IF EXISTS v_companies_full;
CREATE VIEW v_companies_full AS
SELECT
  c.*,
  bs.name as business_segment_name,
  r.name as region_name,
  r.type as region_type,
  a.name as agency_name,
  aus.name as annual_update_status_name,
  os.name as operation_status_name
FROM companies c
LEFT JOIN business_segments bs ON c.business_segment_id = bs.id
LEFT JOIN regions r ON c.region_id = r.id
LEFT JOIN agencies a ON c.agency_id = a.id
LEFT JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
LEFT JOIN operation_status os ON c.operation_status_id = os.id;

-- 6. 验证修改结果
SELECT 'regions表type字段枚举值:' as info;
SHOW COLUMNS FROM regions LIKE 'type';

SELECT 'archive_rule_regions表region_type字段枚举值:' as info;
SHOW COLUMNS FROM archive_rule_regions LIKE 'region_type';

SELECT '离岸地区数据:' as info;
SELECT * FROM regions WHERE type = '离岸';

COMMIT;
