# 数据库迁移实施计划

## 📋 迁移概述
- **目标**: 将现有数据库结构优化为规范化的新结构
- **原则**: 零数据丢失、最小停机时间、可回滚
- **预计时间**: 2-3小时（包含测试验证）

## 🔍 现状分析

### 当前存在的表
1. `companies` - 公司信息表 ✅ 保留并优化
2. `shareholders` - 股东信息表 🔄 需要重构
3. `executives` - 高管信息表 🔄 需要重构
4. `investments` - 对外投资表 🔄 需要重构
5. `business_segments` - 业务板块表 ✅ 保留
6. `regions` - 地区表 🔄 需要统一
7. `agencies` - 代理机构表 🔄 需要统一
8. `shareholder_entities` - 股东实体表 🔄 需要合并
9. `investment_records` - 投资记录表 🔄 需要重构
10. `persons` - 人员表 🔄 需要扩展
11. `employment_records` - 任职记录表 🔄 需要重构
12. `company_finances` - 财务信息表 ✅ 保留并扩展
13. `archive_update_rules` - 档案规范表 ✅ 保留并扩展

### 问题识别
- **重复表**: shareholders vs shareholder_entities, regions vs region
- **命名不一致**: created_at vs create_time
- **关系缺失**: 缺乏外键约束
- **数据分散**: 人员信息分散在多个表中

## 🎯 迁移策略

### 阶段一：数据备份和环境准备
**时间**: 30分钟
**目标**: 确保数据安全和环境就绪

#### 1.1 完整数据备份
```bash
# 备份当前数据库
mysqldump -u root -p stake_management > backup_$(date +%Y%m%d_%H%M%S).sql

# 验证备份文件
mysql -u root -p -e "CREATE DATABASE backup_test; USE backup_test; SOURCE backup_$(date +%Y%m%d_%H%M%S).sql;"
```

#### 1.2 创建新数据库
```sql
-- 创建新数据库
CREATE DATABASE stake_management_v2 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 执行新结构SQL
SOURCE database-schema-complete.sql;
```

### 阶段二：基础数据迁移
**时间**: 30分钟
**目标**: 迁移基础数据表

#### 2.1 业务板块数据迁移
```sql
-- 从旧库迁移业务板块数据
INSERT INTO stake_management_v2.business_segments (name, created_at)
SELECT name, created_at 
FROM stake_management.business_segments
WHERE name NOT IN (SELECT name FROM stake_management_v2.business_segments);
```

#### 2.2 地区数据统一迁移
```sql
-- 合并regions和region表的数据
INSERT INTO stake_management_v2.regions (type, name, created_at)
SELECT 
  CASE 
    WHEN type = '国内' THEN '国内'
    WHEN type = '海外公司对外投资' THEN '海外'
    ELSE '国内'
  END as type,
  region as name,
  create_time as created_at
FROM stake_management.region
UNION
SELECT '国内' as type, name, created_at
FROM stake_management.regions
WHERE name NOT LIKE '%海外%';
```

#### 2.3 代理机构数据迁移
```sql
-- 迁移代理机构数据
INSERT INTO stake_management_v2.agencies (name, contact_person, contact_method, created_at)
SELECT 
  COALESCE(agency_name, name) as name,
  contact_person,
  contact_method,
  COALESCE(create_time, created_at) as created_at
FROM stake_management.agencies;
```

### 阶段三：核心实体数据迁移
**时间**: 45分钟
**目标**: 迁移人员和公司数据

#### 3.1 人员数据统一迁移
```sql
-- 创建临时用户（用于外键约束）
INSERT INTO stake_management_v2.user_roles (name, description) VALUES ('临时角色', '数据迁移临时角色');
INSERT INTO stake_management_v2.users (username, password_hash, role_id) VALUES ('migration_user', 'temp_hash', 1);

-- 合并所有人员数据到persons表
INSERT INTO stake_management_v2.persons (name, id_type, id_number, created_at, created_by)
SELECT DISTINCT
  name,
  COALESCE(id_type, '身份证') as id_type,
  id_number,
  created_at,
  1 as created_by
FROM (
  -- 从persons表
  SELECT name, id_type, id_number, created_at FROM stake_management.persons
  UNION
  -- 从shareholder_entities表
  SELECT name, '身份证' as id_type, CONCAT('SE_', id) as id_number, created_at FROM stake_management.shareholder_entities
  UNION
  -- 从shareholders表中提取人员
  SELECT DISTINCT name, '身份证' as id_type, CONCAT('SH_', ROW_NUMBER() OVER (ORDER BY name)) as id_number, created_at 
  FROM stake_management.shareholders WHERE name IS NOT NULL
  UNION
  -- 从executives表中提取人员
  SELECT DISTINCT person as name, '身份证' as id_type, CONCAT('EX_', ROW_NUMBER() OVER (ORDER BY person)) as id_number, created_at 
  FROM stake_management.executives WHERE person IS NOT NULL
) combined_persons
WHERE name IS NOT NULL AND name != '';
```

#### 3.2 公司数据迁移
```sql
-- 迁移公司数据，关联基础数据表
INSERT INTO stake_management_v2.companies (
  company_name_cn, company_name_en, registered_capital, establish_date,
  business_segment_id, region_id, agency_id, registered_address,
  created_at, created_by
)
SELECT 
  c.company_name_cn,
  c.company_name_en,
  c.registered_capital,
  c.establish_date,
  bs.id as business_segment_id,
  r.id as region_id,
  a.id as agency_id,
  c.registered_address,
  c.created_at,
  1 as created_by
FROM stake_management.companies c
LEFT JOIN stake_management_v2.business_segments bs ON c.business_segment = bs.name
LEFT JOIN stake_management_v2.regions r ON c.region = r.name
LEFT JOIN stake_management_v2.agencies a ON c.agency = a.name;
```

### 阶段四：关系数据迁移
**时间**: 45分钟
**目标**: 迁移股东关系和任职关系

#### 4.1 股东关系迁移
```sql
-- 迁移股东关系数据
INSERT INTO stake_management_v2.shareholdings (
  person_id, company_id, investment_amount, percentage, start_date, created_at, created_by
)
SELECT 
  p.id as person_id,
  c.id as company_id,
  s.investment_amount,
  s.percentage,
  s.start_date,
  s.created_at,
  1 as created_by
FROM stake_management.shareholders s
JOIN stake_management_v2.companies c ON s.company_id = c.id
JOIN stake_management_v2.persons p ON s.name = p.name
WHERE s.name IS NOT NULL;

-- 迁移投资记录数据
INSERT INTO stake_management_v2.shareholdings (
  person_id, company_id, investment_amount, percentage, start_date, created_at, created_by
)
SELECT 
  p.id as person_id,
  c.id as company_id,
  ir.investment_amount,
  ir.percentage,
  ir.start_date,
  ir.created_at,
  1 as created_by
FROM stake_management.investment_records ir
JOIN stake_management.shareholder_entities se ON ir.shareholder_id = se.id
JOIN stake_management_v2.persons p ON se.name = p.name
LEFT JOIN stake_management_v2.companies c ON ir.company_name = c.company_name_cn
WHERE p.id IS NOT NULL;
```

#### 4.2 任职关系迁移
```sql
-- 迁移任职关系数据
INSERT INTO stake_management_v2.employments (
  person_id, company_id, position, start_date, created_at, created_by
)
SELECT 
  p.id as person_id,
  c.id as company_id,
  e.position,
  er.start_date,
  e.created_at,
  1 as created_by
FROM stake_management.executives e
JOIN stake_management_v2.companies c ON e.company_id = c.id
JOIN stake_management_v2.persons p ON e.person = p.name
LEFT JOIN stake_management.employment_records er ON er.person_id = p.id AND er.company_name LIKE CONCAT('%', c.company_name_cn, '%')
WHERE e.person IS NOT NULL;
```

### 阶段五：财务和档案数据迁移
**时间**: 30分钟
**目标**: 迁移财务和档案数据

#### 5.1 财务数据迁移
```sql
-- 迁移财务信息
INSERT INTO stake_management_v2.company_finances (
  company_id, year, total_assets, total_liabilities, total_equity,
  business_income, main_business_income, profit_before_tax, net_profit, tax_payable,
  remarks, created_at, created_by
)
SELECT 
  c.id as company_id,
  cf.year,
  cf.total_assets,
  cf.total_liabilities,
  cf.total_equity,
  cf.business_income,
  cf.main_business_income,
  cf.profit_before_tax,
  cf.net_profit,
  cf.tax_payable,
  cf.remarks,
  cf.created_at,
  1 as created_by
FROM stake_management.company_finances cf
JOIN stake_management_v2.companies c ON cf.company_id = c.id;
```

#### 5.2 档案规范迁移
```sql
-- 迁移档案更新规范
INSERT INTO stake_management_v2.archive_update_rules (
  change_type_id, operation_name, allowed_fields, description, created_at, created_by
)
SELECT 
  ct.id as change_type_id,
  aur.update_operation_name as operation_name,
  aur.change_steps as allowed_fields,
  '从旧系统迁移' as description,
  aur.created_at,
  1 as created_by
FROM stake_management.archive_update_rules aur
JOIN stake_management_v2.change_types ct ON aur.update_type = ct.name;
```

## ✅ 验证和测试

### 数据完整性验证
```sql
-- 验证数据迁移完整性
SELECT 
  '原始公司数量' as item, COUNT(*) as count FROM stake_management.companies
UNION ALL
SELECT 
  '迁移公司数量' as item, COUNT(*) as count FROM stake_management_v2.companies
UNION ALL
SELECT 
  '原始股东记录' as item, COUNT(*) as count FROM stake_management.shareholders
UNION ALL
SELECT 
  '迁移股东关系' as item, COUNT(*) as count FROM stake_management_v2.shareholdings;
```

### 功能测试清单
- [ ] 股东信息页面数据显示正确
- [ ] 任职档案页面数据显示正确
- [ ] 公司信息页面关联数据正确
- [ ] 财务信息页面数据完整
- [ ] 新增、编辑、删除功能正常
- [ ] 外键约束正常工作

## 🔄 回滚计划

### 如果迁移失败
1. 停止应用服务
2. 删除新数据库：`DROP DATABASE stake_management_v2;`
3. 从备份恢复：`mysql -u root -p stake_management < backup_YYYYMMDD_HHMMSS.sql`
4. 重启应用服务
5. 分析失败原因，修正后重新执行

## 📋 迁移后清理

### 清理临时数据
```sql
-- 删除临时用户
DELETE FROM stake_management_v2.users WHERE username = 'migration_user';
DELETE FROM stake_management_v2.user_roles WHERE name = '临时角色';
```

### 更新应用配置
1. 更新数据库连接配置指向新数据库
2. 更新API接口适配新表结构
3. 测试所有功能页面
4. 部署到生产环境

## 📝 迁移记录

### 执行日志
- 开始时间: ___________
- 完成时间: ___________
- 执行人员: ___________
- 遇到问题: ___________
- 解决方案: ___________
- 验证结果: ___________

### 后续优化建议
1. 定期备份新数据库
2. 监控查询性能，必要时添加索引
3. 根据使用情况调整表结构
4. 完善用户权限配置
5. 建立定期维护计划
