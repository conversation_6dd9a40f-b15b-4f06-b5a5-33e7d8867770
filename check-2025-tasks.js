import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function check2025Tasks() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 查看2025年的所有任务
    const [tasks2025] = await connection.query(`
      SELECT 
        pt.id,
        pt.task_type,
        pt.year,
        pt.company_name,
        c.id as company_id,
        aus.name as annual_status
      FROM pending_tasks pt
      LEFT JOIN companies c ON pt.company_id = c.id
      LEFT JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      WHERE pt.year = '2025'
      ORDER BY pt.task_type, pt.company_name
    `);
    
    console.log(`\n📊 2025年任务总数: ${tasks2025.length}`);
    
    // 按任务类型分组
    const annualReportTasks = tasks2025.filter(task => task.task_type === '年审年报');
    const addressMaintenanceTasks = tasks2025.filter(task => task.task_type === '地址维护');
    
    console.log(`\n📋 年审年报任务 (${annualReportTasks.length}个):`);
    annualReportTasks.forEach(task => {
      console.log(`  ID: ${task.id}, 公司: ${task.company_name}, 年审状态: ${task.annual_status || '未设置'}`);
    });
    
    console.log(`\n📋 地址维护任务 (${addressMaintenanceTasks.length}个):`);
    addressMaintenanceTasks.forEach(task => {
      console.log(`  ID: ${task.id}, 公司: ${task.company_name}, 年审状态: ${task.annual_status || '未设置'}`);
    });
    
    // 检查管年审的公司
    const [managedCompanies] = await connection.query(`
      SELECT 
        c.id,
        c.company_name_cn,
        aus.name as annual_status
      FROM companies c
      JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      WHERE aus.name IN ('固定周期', '滚动周期')
      ORDER BY c.company_name_cn
    `);
    
    console.log(`\n🏢 管年审的公司 (${managedCompanies.length}个):`);
    managedCompanies.forEach(company => {
      console.log(`  ID: ${company.id}, 名称: ${company.company_name_cn}, 状态: ${company.annual_status}`);
    });
    
    // 检查是否有不管年审公司的任务
    const [unmanagedTasks] = await connection.query(`
      SELECT 
        pt.id,
        pt.task_type,
        pt.company_name,
        aus.name as annual_status
      FROM pending_tasks pt
      JOIN companies c ON pt.company_id = c.id
      JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      WHERE pt.year = '2025' AND aus.name = '不管年审'
      ORDER BY pt.task_type, pt.company_name
    `);
    
    if (unmanagedTasks.length > 0) {
      console.log(`\n⚠️ 发现不管年审公司的任务 (${unmanagedTasks.length}个):`);
      unmanagedTasks.forEach(task => {
        console.log(`  ID: ${task.id}, 类型: ${task.task_type}, 公司: ${task.company_name}`);
      });
    } else {
      console.log(`\n✅ 没有发现不管年审公司的任务`);
    }

  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行查询
check2025Tasks();
