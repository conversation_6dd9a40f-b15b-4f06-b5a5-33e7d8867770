import { createBrowserRouter } from 'react-router-dom';
import MainLayout from './layouts/MainLayout';
import ProtectedRoute from './components/ProtectedRoute';

// Index Pages
import TaskIndex from './pages/task/TaskIndex';
import CompanyIndex from './pages/company/CompanyIndex';

import BusinessIndex from './pages/business/BusinessIndex';
import BusinessSegmentPage from './pages/business/BusinessSegmentPage';
import ShareholderIndex from './pages/shareholder/ShareholderIndex';

import ArchiveIndex from './pages/archive/ArchiveIndex';
import SystemIndex from './pages/system/SystemIndex';

// Task Pages
import TaskAdd from './pages/task/TaskAdd';
import Verify from './pages/task/Verify';
import VerifyTasks from './pages/task/VerifyTasks';
import Investigation from './pages/task/Investigation';

// Company Pages
import CompanyAdd from './pages/company/CompanyAdd';
import CompanyAddWorking from './pages/company/CompanyAddWorking';
import CompanyAddBasic from './pages/company/CompanyAddBasic';
import CompanyAddSimple from './pages/company/CompanyAddSimple';
import CompanyAddTest from './pages/company/CompanyAddTest';
import CompanyInfo from './pages/company/CompanyInfo';
import CompanyDetail from './pages/company/CompanyDetail';
import EquityChart from './pages/company/EquityChart';
import CompanyFinanceAdd from './pages/company/CompanyFinanceAdd';
import RegionManagement from './pages/basic-data/RegionManagement';
import AgencyManagement from './pages/basic-data/AgencyManagement';
import DataDictionary from './pages/basic-data/DataDictionary';
import BasicDataIndexPage from './pages/basic-data/index';
import EmploymentArchive from './pages/employment/EmploymentArchive';

import BasicChange from './pages/company/change/BasicChange';
import ExecutiveChange from './pages/company/change/ExecutiveChange';
import ShareholderChange from './pages/company/change/ShareholderChange';
import ChangeIndex from './pages/company/change/ChangeIndex';
import ChangeBasicInfo from './pages/company/change/ChangeBasicInfo';
import ChangeExecutiveInfo from './pages/company/change/ChangeExecutiveInfo';
import ChangeShareholderInfo from './pages/company/change/ChangeShareholderInfo';
import ChangeInvestmentInfo from './pages/company/change/ChangeInvestmentInfo';

// Company Change Confirmation Page
import CompanyChangeConfirmation from './pages/company-change-confirmation';

// Company Change Basic Page
import CompanyChangeBasic from './pages/company-change-basic';

// Company Change Executive Page
import CompanyChangeExecutive from './pages/company-change-executive';

// Company Change Shareholder Page
import CompanyChangeShareholder from './pages/company-change-shareholder';

// Shareholder Pages (删除了ShareholderAdd和ShareholderRegister)

// Archive Pages
import ArchiveUpdateRuleAdd from './pages/archive/ArchiveUpdateRuleAdd';
import ArchiveUpdateRuleList from './pages/archive/ArchiveUpdateRuleList';
import ArchiveUpdateRuleEdit from './pages/archive/ArchiveUpdateRuleEdit';

// Auth Pages
import Login from './pages/auth/Login';

// System Pages
import UserAdd from './pages/system/UserAdd';
import UserEdit from './pages/system/UserEdit';
import UserManagement from './pages/system/UserManagement';
import DatabaseConfig from './pages/system/DatabaseConfig';

const router = createBrowserRouter([
  // 登录页面（不需要认证）
  {
    path: '/login',
    element: (
      <ProtectedRoute requireAuth={false}>
        <Login />
      </ProtectedRoute>
    ),
  },
  // Direct test route (bypasses layout)
  {
    path: '/test',
    element: <CompanyAddBasic />,
  },
  // 主应用路由（需要认证）
  {
    path: '/',
    element: (
      <ProtectedRoute>
        <MainLayout />
      </ProtectedRoute>
    ),
    children: [
      {
        index: true,
        element: <TaskIndex />, // Default to task reminder
      },
      // Task Routes
      {
        path: 'task',
        children: [
          {
            index: true,
            element: <TaskIndex />,
          },
          {
            path: 'add',
            element: <TaskAdd />,
          },

          {
            path: 'verify',
            element: <Verify />,
          },
          {
            path: 'verify-tasks',
            element: <VerifyTasks />,
          },
          {
            path: 'investigation',
            element: <Investigation />,
          },
        ],
      },
      // Company Change Confirmation Route
      {
        path: 'company-change-confirmation',
        element: <CompanyChangeConfirmation />,
      },

      // Companies Route (plural form)
      {
        path: 'companies/:id',
        element: <CompanyDetail />,
      },
      // Company Routes
      {
        path: 'company',
        children: [
          {
            index: true,
            element: <CompanyInfo />,
          },

          {
            path: 'info',
            element: <CompanyInfo />,
          },
          {
            path: 'add',
            element: <CompanyAddWorking />,
          },
          {
            path: 'detail',
            element: <CompanyDetail />,
          },
          {
            path: ':id',
            element: <CompanyDetail />,
          },
          {
            path: 'equity-chart',
            element: <EquityChart />,
          },
          {
            path: 'finance',
            element: <CompanyFinanceAdd />,
          },

          {
            path: 'change',
            children: [
              {
                index: true,
                element: <ChangeIndex />,
              },
              {
                path: 'basic-info',
                element: <CompanyChangeBasic />,
              },
              {
                path: 'executive-info',
                element: <CompanyChangeExecutive />,
              },
              {
                path: 'shareholder-info',
                element: <CompanyChangeShareholder />,
              },
              {
                path: 'shareholder-info',
                element: <ChangeShareholderInfo />,
              },
              {
                path: 'investment-info',
                element: <ChangeInvestmentInfo />,
              },
              // Legacy routes
              {
                path: 'basic',
                element: <BasicChange />,
              },
              {
                path: 'executive',
                element: <ExecutiveChange />,
              },
              {
                path: 'shareholder',
                element: <ShareholderChange />,
              },
            ],
          },
        ],
      },

      // Business Routes
      {
        path: 'business',
        children: [
          {
            index: true,
            element: <BusinessSegmentPage />,
          },
          {
            path: 'equity-chart',
            element: <BusinessIndex />,
          },
        ],
      },
      // Shareholder Routes
      {
        path: 'shareholder',
        element: <ShareholderIndex />,
      },
      // Basic Data Routes
      {
        path: 'basic-data',
        children: [
          {
            index: true,
            element: <BasicDataIndexPage />,
          },
          {
            path: 'region',
            element: <RegionManagement />,
          },
          {
            path: 'agency',
            element: <AgencyManagement />,
          },
          {
            path: 'dictionary',
            element: <DataDictionary />,
          },
        ],
      },
      // Archive Routes
      {
        path: 'archive',
        children: [
          {
            index: true,
            element: <ArchiveIndex />,
          },
          {
            path: 'update-rules',
            element: <ArchiveUpdateRuleList />,
          },
          {
            path: 'update-rule/add',
            element: <ArchiveUpdateRuleAdd />,
          },
          {
            path: 'edit/:id',
            element: <ArchiveUpdateRuleEdit />,
          },
        ],
      },

      // Employment Routes
      {
        path: 'employment',
        element: <EmploymentArchive />,
      },
      // Employment Archive Route (alternative path)
      {
        path: 'employment-archive',
        element: <EmploymentArchive />,
      },

      // System Routes
      {
        path: 'system',
        children: [
          {
            index: true,
            element: <SystemIndex />,
          },
          {
            path: 'user/add',
            element: <UserAdd />,
          },
          {
            path: 'user/edit/:id',
            element: <UserEdit />,
          },
          {
            path: 'user/management',
            element: <UserManagement />,
          },
          {
            path: 'database/config',
            element: <DatabaseConfig />,
          },
        ],
      },
      // Legacy routes for backward compatibility
      {
        path: 'company/info',
        element: <CompanyInfo />,
      },
    ],
  },
]);

export default router;