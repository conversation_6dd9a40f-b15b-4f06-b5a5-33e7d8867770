* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
  color: rgba(0, 0, 0, 0.85);
  background-color: #f0f2f5;
}

#root {
  width: 100%;
  height: 100vh;
}

.ant-layout {
  background: #f0f2f5;
}

.logo {
  height: 64px;
  margin: 0;
  background: #001529;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.logo img {
  height: 32px;
}

.logo span {
  color: white;
  margin-left: 10px;
  font-size: 18px;
  font-weight: 600;
}

.trigger {
  padding: 0 24px;
  font-size: 18px;
  line-height: 64px;
  cursor: pointer;
  transition: color 0.3s;
}

.trigger:hover {
  color: #1890ff;
}

.site-layout .site-layout-background {
  background: #fff;
}

.ant-table-wrapper {
  background: white;
  border-radius: 8px;
}

.ant-pagination {
  margin-right: 16px !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ant-layout-sider {
    max-width: 80px !important;
    min-width: 80px !important;
    width: 80px !important;
  }
  
  .ant-layout-sider-collapsed {
    max-width: 0 !important;
    min-width: 0 !important;
    width: 0 !important;
  }
}

.ant-layout-sider {
  position: relative;
  transition: width 0.2s;
}

.sider-resizer {
  position: absolute;
  top: 0;
  right: 0;
  width: 5px;
  height: 100%;
  cursor: col-resize;
  z-index: 100;
}

.sider-resizer:hover,
.sider-resizer:active {
  background-color: rgba(255, 255, 255, 0.2);
}

/* 当用户正在调整大小时添加这个类 */
.resizing {
  user-select: none;
  cursor: col-resize;
}

/* 确保菜单项文本在宽度变化时不会被截断 */
.ant-menu-item, .ant-menu-submenu-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 固定布局样式 */
.ant-layout-sider-fixed {
  position: fixed !important;
  left: 0;
  top: 0;
  bottom: 0;
  height: 100vh;
  z-index: 1000;
}

.ant-layout-header-fixed {
  position: fixed !important;
  top: 0;
  right: 0;
  z-index: 999;
  border-bottom: 1px solid #f0f0f0;
}

.ant-layout-content-with-fixed-header {
  margin-top: 64px;
  padding: 24px;
  min-height: calc(100vh - 64px);
  background: #f5f5f5;
  overflow-y: auto;
}

/* 确保菜单可以滚动 */
.ant-menu-inline {
  border-right: none;
}

/* 优化滚动条样式 */
.ant-layout-content-with-fixed-header::-webkit-scrollbar {
  width: 6px;
}

.ant-layout-content-with-fixed-header::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.ant-layout-content-with-fixed-header::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.ant-layout-content-with-fixed-header::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 自定义菜单样式 */
.menu-item {
  transition: all 0.2s ease;
  user-select: none;
}

.menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
}

.menu-item.active {
  background-color: rgba(24, 144, 255, 0.1) !important;
  color: #1890ff !important;
}

.menu-item.level-0 {
  font-weight: 500;
}

.menu-item.level-1 {
  font-size: 14px;
}

.menu-item.level-2 {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.7);
}

.menu-item.level-3 {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}
