import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requiredPermissions?: Array<{ module: string; action: string }>;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requireAuth = true,
  requiredPermissions = []
}) => {
  const { isAuthenticated, isLoading, canAccessPage, user, getDefaultPageForUser } = useAuth();
  const location = useLocation();

  // 如果正在加载认证状态，显示加载页面
  if (isLoading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column'
      }}>
        <Spin size="large" />
        <div style={{ marginTop: 16, color: '#666' }}>
          正在验证登录状态...
        </div>
      </div>
    );
  }

  // 如果需要认证但用户未登录，重定向到登录页面
  if (requireAuth && !isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // 如果用户已登录但访问登录页面，重定向到用户的默认页面
  if (isAuthenticated && location.pathname === '/login') {
    const defaultPage = getDefaultPageForUser();
    return <Navigate to={defaultPage} replace />;
  }

  // 检查页面访问权限
  if (isAuthenticated && !canAccessPage(location.pathname)) {
    console.warn(`用户 ${user?.username} 无权访问页面: ${location.pathname}`);
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column'
      }}>
        <div style={{ fontSize: 48, marginBottom: 16 }}>🚫</div>
        <h2>访问被拒绝</h2>
        <p style={{ color: '#666', textAlign: 'center' }}>
          您没有权限访问此页面<br />
          请联系管理员获取相应权限
        </p>
        <div style={{ marginTop: 16 }}>
          <a href={getDefaultPageForUser()}>返回首页</a>
        </div>
      </div>
    );
  }

  // 检查特定权限要求
  if (requiredPermissions.length > 0 && isAuthenticated) {
    const hasRequiredPermissions = requiredPermissions.every(({ module, action }) => 
      canAccessPage(`/${module}/${action}`)
    );

    if (!hasRequiredPermissions) {
      return (
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          flexDirection: 'column'
        }}>
          <div style={{ fontSize: 48, marginBottom: 16 }}>⚠️</div>
          <h2>权限不足</h2>
          <p style={{ color: '#666', textAlign: 'center' }}>
            您没有执行此操作的权限<br />
            请联系管理员获取相应权限
          </p>
        </div>
      );
    }
  }

  return <>{children}</>;
};

export default ProtectedRoute;
