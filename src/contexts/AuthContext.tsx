import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { apiService } from '../services/api-simple';

// 用户信息接口
export interface User {
  id: number;
  username: string;
  realName: string;
  email: string;
  phone: string;
  department: string;
  position: string;
  roleId: number;
  roleName: string;
  status: string;
}

// 权限信息接口
export interface Permission {
  id: number;
  module: string;
  action: string;
  description: string;
}

// 认证上下文接口
interface AuthContextType {
  user: User | null;
  permissions: Permission[];
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  hasPermission: (module: string, action: string) => boolean;
  canAccessPage: (pagePath: string) => boolean;
  canPerformAction: (module: string, action: string) => boolean;
  getDefaultPageForUser: () => string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// 页面权限映射
const PAGE_PERMISSIONS: Record<string, { module: string; action: string }[]> = {
  // 系统设置页面
  '/system': [{ module: 'system', action: 'user_management' }],
  '/system/user/add': [{ module: 'system', action: 'user_add' }],
  '/system/user/management': [{ module: 'system', action: 'user_management' }],
  '/system/user/edit': [{ module: 'system', action: 'user_management' }],
  '/system/database': [{ module: 'system', action: 'database_config' }],
  '/system/database/config': [{ module: 'system', action: 'database_config' }],
  
  // 任务管理页面
  '/task': [{ module: 'task', action: 'view' }],
  '/task/add': [{ module: 'task', action: 'add' }],
  '/task/verify': [{ module: 'task', action: 'verify' }],
  '/task/investigation': [{ module: 'task', action: 'investigation' }],
  
  // 公司信息页面
  '/company': [{ module: 'company', action: 'view' }],
  '/company/add': [{ module: 'company', action: 'add' }],
  '/company/edit': [{ module: 'company', action: 'edit' }],
  '/company/detail': [{ module: 'company', action: 'view' }],
  '/company/change': [{ module: 'company', action: 'change_confirm' }],
  
  // 任职档案页面
  '/employment': [{ module: 'employment', action: 'view' }],
  '/employment/add': [{ module: 'employment', action: 'add' }],
  '/employment/edit': [{ module: 'employment', action: 'edit' }],
  
  // 业务板块页面
  '/business': [{ module: 'business', action: 'view' }],
  '/business/edit': [{ module: 'business', action: 'edit' }],
  
  // 股东信息页面
  '/shareholder': [{ module: 'shareholder', action: 'view' }],
  '/shareholder/add': [{ module: 'shareholder', action: 'add' }],
  '/shareholder/edit': [{ module: 'shareholder', action: 'edit' }],
  
  // 基础数据页面
  '/basic-data': [{ module: 'basic_data', action: 'view' }],
  '/basic-data/edit': [{ module: 'basic_data', action: 'edit' }],
  
  // 档案规范页面
  '/archive': [{ module: 'archive', action: 'view' }],
  '/archive/add': [{ module: 'archive', action: 'add' }],
  '/archive/edit': [{ module: 'archive', action: 'edit' }]
};

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 检查是否已认证
  const isAuthenticated = !!user;

  // 初始化时检查本地存储的登录状态
  useEffect(() => {
    const initAuth = async () => {
      try {
        const savedUser = localStorage.getItem('user');
        const savedPermissions = localStorage.getItem('permissions');
        
        if (savedUser && savedPermissions) {
          const parsedUser = JSON.parse(savedUser);
          const parsedPermissions = JSON.parse(savedPermissions);
          console.log('🔍 从localStorage恢复用户信息:', {
            savedUser: parsedUser,
            savedPermissions: parsedPermissions
          });
          setUser(parsedUser);
          setPermissions(parsedPermissions);
        } else {
          console.log('🔍 localStorage中没有用户信息');
        }
      } catch (error) {
        console.error('初始化认证状态失败:', error);
        // 清除无效的本地存储
        localStorage.removeItem('user');
        localStorage.removeItem('permissions');
      } finally {
        setIsLoading(false);
      }
    };

    initAuth();
  }, []);

  // 登录函数
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      console.log('🔐 发起登录请求:', { username });
      
      // 调用登录API
      const response = await apiService.post('/api/auth/login', {
        username,
        password
      });

      if (response.data.success) {
        const { user: userData, permissions: userPermissions } = response.data.data;
        
        // 保存用户信息和权限
        setUser(userData);
        setPermissions(userPermissions);
        
        // 保存到本地存储
        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.setItem('permissions', JSON.stringify(userPermissions));
        
        console.log('✅ 登录成功:', { user: userData, permissions: userPermissions });
        return true;
      } else {
        console.error('❌ 登录失败:', response.data.message);
        return false;
      }
    } catch (error: any) {
      console.error('❌ 登录请求失败:', error);
      throw new Error(error.response?.data?.message || '登录失败，请检查网络连接');
    }
  };

  // 登出函数
  const logout = () => {
    setUser(null);
    setPermissions([]);
    localStorage.removeItem('user');
    localStorage.removeItem('permissions');
    console.log('🚪 用户已登出');
  };

  // 检查是否有特定权限
  const hasPermission = (module: string, action: string): boolean => {
    return permissions.some(permission => 
      permission.module === module && permission.action === action
    );
  };

  // 检查是否可以访问页面
  const canAccessPage = (pagePath: string): boolean => {
    // 如果未登录，只能访问登录页面
    if (!isAuthenticated) {
      return pagePath === '/login';
    }

    // 登录页面对已登录用户不可访问
    if (pagePath === '/login') {
      return false;
    }

    // 系统管理员（角色ID为1）可以访问所有页面
    if (user?.roleId === 1) {
      return true;
    }

    // 检查页面权限
    const pagePermissions = PAGE_PERMISSIONS[pagePath];
    if (!pagePermissions) {
      // 如果页面没有定义权限要求，默认允许访问
      return true;
    }

    // 检查是否有任一所需权限
    return pagePermissions.some(({ module, action }) =>
      hasPermission(module, action)
    );
  };

  // 获取用户登录后的默认页面
  const getDefaultPageForUser = (): string => {
    if (!user) return '/task';

    // 根据用户角色返回不同的默认页面
    switch (user.roleId) {
      case 1: // 系统管理员 - 跳转到用户管理页面
        return '/system/user/management';
      case 2: // 管理员 - 跳转到待办任务页面
      case 3: // 操作员 - 跳转到待办任务页面
      case 4: // 查看员 - 跳转到待办任务页面
      default:
        return '/task';
    }
  };

  // 检查是否可以执行操作
  const canPerformAction = (module: string, action: string): boolean => {
    if (!isAuthenticated) {
      return false;
    }

    // 系统管理员（角色ID为1）可以执行所有操作
    if (user?.roleId === 1) {
      return true;
    }

    return hasPermission(module, action);
  };

  const value: AuthContextType = {
    user,
    permissions,
    isAuthenticated,
    isLoading,
    login,
    logout,
    hasPermission,
    canAccessPage,
    canPerformAction,
    getDefaultPageForUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// 使用认证上下文的Hook
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
