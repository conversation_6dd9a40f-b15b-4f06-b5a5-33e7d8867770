// API Service for Stake/Equity Management System
import axios from 'axios';
import { message } from 'antd';

// API Configuration
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8080',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Create axios instance
const apiClient = axios.create(API_CONFIG);

// Request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // Add request ID for tracking
    config.headers['X-Request-ID'] = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
    
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`, config.data);
    return config;
  },
  (error) => {
    console.error('❌ Request Error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data);
    return response;
  },
  (error) => {
    console.error('❌ Response Error:', error);
    
    // Handle common error scenarios
    if (error.response) {
      const { status, data } = error.response;

      switch (status) {
        case 401:
          message.error('登录已过期，请重新登录');
          localStorage.removeItem('auth_token');
          window.location.href = '/login';
          break;
        case 403:
          message.error('权限不足，无法执行此操作');
          break;
        case 404:
          message.error('请求的资源不存在');
          break;
        case 422:
          message.error((data as any)?.message || '数据验证失败');
          break;
        case 500:
          message.error('服务器内部错误，请稍后重试');
          break;
        default:
          message.error((data as any)?.message || '请求失败，请稍后重试');
      }
    } else if (error.request) {
      message.error('网络连接失败，请检查网络设置');
    } else {
      message.error('请求配置错误');
    }
    
    return Promise.reject(error);
  }
);

// API Response Types
interface ApiResponse<T = any> {
  success: boolean;
  message?: string;
  data?: T;
  error?: string;
}

interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
}

// API Service Class
class ApiService {
  // Generic HTTP methods
  async get(url, params) {
    return apiClient.get(url, { params });
  }

  async post(url, data) {
    return apiClient.post(url, data);
  }

  async put(url, data) {
    return apiClient.put(url, data);
  }

  async patch(url, data) {
    return apiClient.patch(url, data);
  }

  async delete(url) {
    return apiClient.delete(url);
  }

  // Health Check
  async checkServerStatus() {
    return this.get('/api/test-db');
  }

  // Company APIs
  async getCompanies(params) {
    return this.get('/api/companies', params);
  }

  async getCompanyById(id) {
    return this.get(`/api/companies/${id}`);
  }

  async createCompany(data) {
    return this.post('/api/company/add', data);
  }

  async updateCompany(id, data) {
    return this.put(`/api/companies/${id}`, data);
  }

  async deleteCompany(id) {
    return this.delete(`/api/companies/${id}`);
  }

  async checkCompanyDuplicate(data) {
    return this.post('/api/company/check-duplicate', data);
  }

  // Basic Data APIs
  async getBusinessSegments() {
    return this.get('/api/business-segments');
  }

  async searchBusinessSegments(params) {
    return this.get('/api/business-segments/search', params);
  }

  async createBusinessSegment(data) {
    return this.post('/api/business-segments', data);
  }

  async deleteBusinessSegment(id) {
    return this.delete(`/api/business-segments/${id}`);
  }

  async getBusinessSegmentCompanies(id) {
    return this.get(`/api/business-segments/${id}/companies`);
  }

  async getCompaniesBySegment(segmentName) {
    return this.get('/api/companies/by-segment', { segment: segmentName });
  }

  async getRegions() {
    return this.get('/api/regions');
  }

  async createRegion(data) {
    return this.post('/api/regions', data);
  }

  async updateRegion(id, data) {
    return this.put(`/api/regions/${id}`, data);
  }

  async deleteRegion(id) {
    return this.delete(`/api/regions/${id}`);
  }

  async getAgencies() {
    return this.get('/api/agencies');
  }

  // User Management APIs
  async getCurrentUser() {
    return this.get('/api/auth/me');
  }

  async login(credentials) {
    return this.post('/api/auth/login', credentials);
  }

  async logout() {
    return this.post('/api/auth/logout');
  }

  // File Upload
  async uploadFile(file, type) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    return apiClient.post('/api/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  }
}

// Export singleton instance
export const apiService = new ApiService();

// Export types for use in components
export type { ApiResponse, PaginatedResponse };

// Export axios instance for direct use if needed
export { apiClient };

// Utility functions
export const handleApiError = (error: any, defaultMessage = '操作失败') => {
  if (error.response?.data?.message) {
    message.error(error.response.data.message);
  } else {
    message.error(defaultMessage);
  }
  console.error('API Error:', error);
};

export const isApiSuccess = (response: any): boolean => {
  return response?.data?.success === true;
};

// Constants
export const API_ENDPOINTS = {
  // Company
  COMPANIES: '/api/companies',
  COMPANY_ADD: '/api/company/add',
  COMPANY_CHECK_DUPLICATE: '/api/company/check-duplicate',

  // Basic Data
  BUSINESS_SEGMENTS: '/api/business-segments',
  BUSINESS_SEGMENTS_SEARCH: '/api/business-segments/search',
  BUSINESS_SEGMENT_COMPANIES: '/api/business-segments/:id/companies',
  REGIONS: '/api/regions',
  AGENCIES: '/api/agencies',

  // Auth
  LOGIN: '/api/auth/login',
  LOGOUT: '/api/auth/logout',
  ME: '/api/auth/me',

  // Health
  HEALTH_CHECK: '/api/test-db',
} as const;
