// Simple API Service for Stake/Equity Management System
import axios from 'axios';

// API Configuration
const API_CONFIG = {
  baseURL: 'http://localhost:8081',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Create axios instance
const apiClient = axios.create(API_CONFIG);

// API Service Class
class ApiService {
  // Generic HTTP methods
  async get(url, params) {
    try {
      console.log(`🚀 API GET: ${url}`, params);
      const response = await apiClient.get(url, { params });
      console.log(`✅ API Response:`, response.data);
      return response;
    } catch (error) {
      console.error(`❌ API GET Error:`, error);
      throw error;
    }
  }

  async post(url, data) {
    try {
      console.log(`🚀 API POST: ${url}`, data);
      const response = await apiClient.post(url, data);
      console.log(`✅ API Response:`, response.data);
      return response;
    } catch (error) {
      console.error(`❌ API POST Error:`, error);
      throw error;
    }
  }

  async put(url, data) {
    try {
      console.log(`🚀 API PUT: ${url}`, data);
      const response = await apiClient.put(url, data);
      console.log(`✅ API Response:`, response.data);
      return response;
    } catch (error) {
      console.error(`❌ API PUT Error:`, error);
      throw error;
    }
  }

  async delete(url) {
    try {
      console.log(`🚀 API DELETE: ${url}`);
      const response = await apiClient.delete(url);
      console.log(`✅ API Response:`, response.data);
      return response;
    } catch (error) {
      console.error(`❌ API DELETE Error:`, error);
      throw error;
    }
  }

  // Basic Data APIs
  async getBusinessSegments(params) {
    return this.get('/api/business-segments', params);
  }

  async createBusinessSegment(data) {
    return this.post('/api/business-segments', data);
  }

  async deleteBusinessSegment(id) {
    return apiClient.delete(`/api/business-segments/${id}`);
  }

  async getCompaniesBySegment(segmentName) {
    return this.get('/api/companies/by-segment', { segment: segmentName });
  }

  async getCompanyById(id) {
    return this.get(`/api/companies/${id}`);
  }

  // Health Check
  async checkServerStatus() {
    return this.get('/api/test-db');
  }

  // User Management APIs
  async getUsers(params) {
    return this.get('/api/users', params);
  }

  async createUser(data) {
    return this.post('/api/users', data);
  }

  async updateUser(id, data) {
    return apiClient.put(`/api/users/${id}`, data);
  }

  async deleteUser(id) {
    return apiClient.delete(`/api/users/${id}`);
  }

  async updateUserStatus(id, status) {
    return apiClient.put(`/api/users/${id}/status`, { status });
  }

  async resetUserPassword(id) {
    return this.post(`/api/users/${id}/reset-password`);
  }

  // Region Management APIs
  async getRegions() {
    return this.get('/api/regions');
  }

  async searchRegions(params) {
    return this.get('/api/regions/search', params);
  }

  async createRegion(data) {
    return this.post('/api/regions', data);
  }

  async updateRegion(id, data) {
    return this.put(`/api/regions/${id}`, data);
  }

  async deleteRegion(id) {
    return this.delete(`/api/regions/${id}`);
  }

  // Agency Management APIs
  async getAgencies() {
    return this.get('/api/agencies');
  }

  async createAgency(data) {
    return this.post('/api/agencies', data);
  }

  async updateAgency(id, data) {
    return apiClient.put(`/api/agencies/${id}`, data);
  }

  async deleteAgency(id) {
    return apiClient.delete(`/api/agencies/${id}`);
  }

  // Data Dictionary APIs
  async getPositions() {
    return this.get('/api/positions');
  }

  async searchPositions(params) {
    return this.get('/api/positions/search', params);
  }

  async createPosition(data) {
    return this.post('/api/positions', data);
  }

  async updatePosition(id, data) {
    return apiClient.put(`/api/positions/${id}`, data);
  }

  async deletePosition(id) {
    return apiClient.delete(`/api/positions/${id}`);
  }

  async getPaymentMethods() {
    return this.get('/api/payment-methods');
  }

  async searchPaymentMethods(params) {
    return this.get('/api/payment-methods/search', params);
  }

  async createPaymentMethod(data) {
    return this.post('/api/payment-methods', data);
  }

  async updatePaymentMethod(id, data) {
    return apiClient.put(`/api/payment-methods/${id}`, data);
  }

  async deletePaymentMethod(id) {
    return apiClient.delete(`/api/payment-methods/${id}`);
  }

  // Task Management APIs
  async getCompaniesList() {
    return this.get('/api/companies-list');
  }

  async createAnnualTasks(data) {
    return this.post('/api/task/create-annual-tasks', data);
  }

  async createAddressTasks(data) {
    return this.post('/api/task/create-address-tasks', data);
  }

  async createCustomTask(data) {
    return this.post('/api/task/create-custom-task', data);
  }

  async getTaskYears() {
    return this.get('/api/task/years');
  }

  async getTaskTypes() {
    return this.get('/api/task/types');
  }
}

// Export singleton instance
export const apiService = new ApiService();

// Export default
export default apiService;
