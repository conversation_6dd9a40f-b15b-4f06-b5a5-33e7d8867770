import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Select,
  DatePicker,
  Input,
  Button,
  message,
  Typography
} from 'antd';
import axios from 'axios';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;
const { Title } = Typography;

interface TaskItem {
  id: number;
  taskStatus: string;
  taskType: string;
  year: string;
  startDate: string;
  deadline: string;
  companyId: number;
  companyName: string;
  businessSegment: string;
  remarks: string;
  priority: number;
  progress: number;
  createTime: string;
}

interface Company {
  id: number;
  name: string;
}

interface EditTaskModalProps {
  visible: boolean;
  task: TaskItem | null;
  onCancel: () => void;
  onSuccess: () => void;
}

const EditTaskModal: React.FC<EditTaskModalProps> = ({
  visible,
  task,
  onCancel,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [companies, setCompanies] = useState<Company[]>([]);

  // 获取年份选项
  const getYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 2; i <= currentYear + 2; i++) {
      years.push(i.toString());
    }
    return years;
  };

  // 获取公司列表
  const fetchCompanies = async () => {
    try {
      const response = await axios.get('http://localhost:8080/api/companies-list');
      if (response.data.success) {
        setCompanies(response.data.data);
      }
    } catch (error) {
      console.error('获取公司列表失败:', error);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchCompanies();
    }
  }, [visible]);

  useEffect(() => {
    if (task && visible) {
      form.setFieldsValue({
        taskType: task.taskType,
        year: task.year,
        company: task.companyId,
        startDate: task.startDate ? dayjs(task.startDate) : null,
        deadline: task.deadline ? dayjs(task.deadline) : null,
        repeatCycle: 1, // 默认值
        remarks: task.remarks
      });
    }
  }, [task, visible, form]);

  const handleSubmit = async (values: any) => {
    if (!task) return;

    try {
      setLoading(true);
      const response = await axios.put(`http://localhost:8080/api/tasks/${task.id}`, {
        startDate: values.startDate ? values.startDate.format('YYYY-MM-DD') : null,
        deadline: values.deadline ? values.deadline.format('YYYY-MM-DD') : null,
        repeatCycle: values.repeatCycle,
        remarks: values.remarks
      });

      if (response.data.success) {
        message.success('任务更新成功');
        onSuccess();
        onCancel();
      } else {
        message.error(response.data.message || '任务更新失败');
      }
    } catch (error) {
      console.error('更新任务失败:', error);
      message.error('任务更新失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title={
        <div>
          <Title level={4} style={{ margin: 0 }}>
            编辑任务
          </Title>
          {task && (
            <div style={{ fontSize: '14px', color: '#666', marginTop: '4px' }}>
              {task.companyName} - {task.taskType}
            </div>
          )}
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        style={{ marginTop: '20px' }}
      >
        <Form.Item
          label="任务类型"
          name="taskType"
        >
          <Select disabled>
            <Option value="年审年报">年审年报</Option>
            <Option value="地址维护">地址维护</Option>
            <Option value="自定义任务">自定义任务</Option>
          </Select>
        </Form.Item>

        <Form.Item
          label="年度"
          name="year"
        >
          <Select disabled placeholder="请选择（单选）">
            {getYearOptions().map(year => (
              <Option key={year} value={year}>{year}</Option>
            ))}
          </Select>
        </Form.Item>

        <Form.Item
          label="主体"
          name="company"
        >
          <Select
            disabled
            placeholder="请选择（多选）"
            showSearch
            filterOption={(input, option) =>
              (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
            }
          >
            {companies.map(company => (
              <Option key={company.id} value={company.id}>
                {company.name}
              </Option>
            ))}
            {/* 显示当前任务的公司，即使不在companies列表中 */}
            {task && !companies.find(c => c.id === task.companyId) && (
              <Option key={task.companyId} value={task.companyId}>
                {task.companyName}
              </Option>
            )}
          </Select>
        </Form.Item>

        <Form.Item
          label="开始日期"
          name="startDate"
        >
          <DatePicker 
            placeholder="请输入"
            style={{ width: '100%' }}
            format="YYYY-MM-DD"
          />
        </Form.Item>

        <Form.Item
          label="结束日期"
          name="deadline"
        >
          <DatePicker 
            placeholder="请输入"
            style={{ width: '100%' }}
            format="YYYY-MM-DD"
          />
        </Form.Item>

        {task?.taskType === '地址维护' && (
          <Form.Item
            label="重复周期（年）"
            name="repeatCycle"
            rules={[{ required: true, message: '请输入重复周期' }]}
          >
            <Input 
              placeholder="1"
              type="number"
              min={1}
              max={10}
            />
          </Form.Item>
        )}

        <Form.Item
          label="备注"
          name="remarks"
        >
          <TextArea 
            placeholder="请输入"
            rows={4}
            maxLength={500}
            showCount
          />
        </Form.Item>

        <Form.Item style={{ marginBottom: 0, textAlign: 'right' }}>
          <Button onClick={handleCancel} style={{ marginRight: 8 }}>
            取消
          </Button>
          <Button 
            type="primary" 
            htmlType="submit" 
            loading={loading}
            style={{ backgroundColor: '#000', borderColor: '#000' }}
          >
            保存
          </Button>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default EditTaskModal;
