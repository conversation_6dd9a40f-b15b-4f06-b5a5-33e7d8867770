import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Select,
  DatePicker,
  Input,
  Button,
  message,
  Typography,
  Radio,
  Upload,
  Space
} from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import axios from 'axios';
import dayjs from 'dayjs';

const { Option } = Select;
const { TextArea } = Input;
const { Title } = Typography;

interface TaskItem {
  id: number;
  taskStatus: string;
  taskType: string;
  year: string;
  startDate: string;
  deadline: string;
  companyId: number;
  companyName: string;
  businessSegment: string;
  remarks: string;
  priority: number;
  progress: number;
}

interface Company {
  id: number;
  name: string;
}

interface UpdateTaskModalProps {
  visible: boolean;
  task: TaskItem | null;
  onCancel: () => void;
  onSuccess: () => void;
}

const UpdateTaskModal: React.FC<UpdateTaskModalProps> = ({
  visible,
  task,
  onCancel,
  onSuccess
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [companies, setCompanies] = useState<Company[]>([]);
  const [taskStatus, setTaskStatus] = useState<string>('未开始');
  const [nextCycleOption, setNextCycleOption] = useState<string>('不创建');
  const [fileList, setFileList] = useState<any[]>([]);

  // 获取年份选项
  const getYearOptions = () => {
    const currentYear = new Date().getFullYear();
    const years = [];
    for (let i = currentYear - 2; i <= currentYear + 2; i++) {
      years.push(i.toString());
    }
    return years;
  };

  // 获取公司列表
  const fetchCompanies = async () => {
    try {
      const response = await axios.get('http://localhost:8080/api/companies-list');
      if (response.data.success) {
        setCompanies(response.data.data);
      }
    } catch (error) {
      console.error('获取公司列表失败:', error);
    }
  };

  useEffect(() => {
    if (visible) {
      fetchCompanies();
    }
  }, [visible]);

  useEffect(() => {
    if (task && visible) {
      setTaskStatus(task.taskStatus);
      setNextCycleOption('不创建');
      setFileList([]);

      form.setFieldsValue({
        taskStatus: task.taskStatus,
        nextCycle: '不创建',
        taskType: task.taskType,
        year: (parseInt(task.year) + 1).toString(),
        company: task.companyId,
        startDate: null,
        deadline: null,
        repeatCycle: 1,
        remarks: ''
      });
    }
  }, [task, visible, form]);

  const handleTaskStatusChange = (value: string) => {
    setTaskStatus(value);
    if (value !== '已完成') {
      setNextCycleOption('不创建');
      form.setFieldValue('nextCycle', '不创建');
    }
  };

  const handleNextCycleChange = (value: string) => {
    setNextCycleOption(value);
  };

  const handleSubmit = async (values: any) => {
    if (!task) return;

    // 检查文件上传要求
    if ((values.taskStatus === '进行中' || values.taskStatus === '已完成') && fileList.length === 0) {
      message.error('请上传任务文件');
      return;
    }

    try {
      setLoading(true);
      
      // 准备更新数据
      const updateData: any = {
        taskStatus: values.taskStatus,
        uploadedFiles: fileList.map(file => file.name)
      };

      // 如果选择创建下一周期任务
      if (values.taskStatus === '已完成' && values.nextCycle === '创建') {
        updateData.createNextCycle = true;
        updateData.nextCycleData = {
          taskType: values.taskType,
          year: values.year,
          companyId: values.company,
          startDate: values.startDate ? values.startDate.format('YYYY-MM-DD') : null,
          deadline: values.deadline ? values.deadline.format('YYYY-MM-DD') : null,
          repeatCycle: values.repeatCycle,
          remarks: values.remarks
        };
      }

      const response = await axios.put(`http://localhost:8080/api/tasks/${task.id}/update-progress`, updateData);

      if (response.data.success) {
        message.success('任务更新成功');
        onSuccess();
        onCancel();
      } else {
        message.error(response.data.message || '任务更新失败');
      }
    } catch (error) {
      console.error('更新任务失败:', error);
      message.error('任务更新失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setTaskStatus('未开始');
    setNextCycleOption('不创建');
    setFileList([]);
    onCancel();
  };

  const uploadProps = {
    beforeUpload: (file: any) => {
      setFileList([...fileList, file]);
      return false; // 阻止自动上传
    },
    onRemove: (file: any) => {
      setFileList(fileList.filter(item => item.uid !== file.uid));
    },
    fileList,
  };

  // 判断任务信息部分是否可编辑
  const isTaskInfoEditable = taskStatus === '已完成' && nextCycleOption === '创建';
  // 判断下一周期任务选项是否可选择
  const isNextCycleSelectable = taskStatus === '已完成';

  return (
    <Modal
      title="更新进度"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={700}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        style={{ marginTop: '20px' }}
      >
        <Form.Item
          label="任务状态"
          name="taskStatus"
          rules={[{ required: true, message: '请选择任务状态' }]}
        >
          <Radio.Group onChange={(e) => handleTaskStatusChange(e.target.value)}>
            <Radio value="未开始">未开始</Radio>
            <Radio value="进行中">进行中</Radio>
            <Radio value="已完成">已完成</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item
          label="下一周期任务"
          name="nextCycle"
        >
          <Radio.Group 
            disabled={!isNextCycleSelectable}
            onChange={(e) => handleNextCycleChange(e.target.value)}
          >
            <Radio value="不创建">不创建</Radio>
            <Radio value="创建">创建</Radio>
          </Radio.Group>
        </Form.Item>

        <Form.Item label="上传">
          <Upload {...uploadProps}>
            <Button 
              icon={<UploadOutlined />}
              style={{ backgroundColor: '#000', color: 'white', borderColor: '#000' }}
            >
              上传文件
            </Button>
          </Upload>
          <div style={{ marginTop: 8, color: '#666', fontSize: '12px' }}>
            请上传任务完成或更新文件
          </div>
        </Form.Item>

        {isTaskInfoEditable && (
          <>
            <Title level={5} style={{ marginTop: 24, marginBottom: 16 }}>任务信息</Title>
            
            <Form.Item
              label="任务类型"
              name="taskType"
            >
              <Select disabled>
                <Option value="年审年报">年审年报</Option>
                <Option value="地址维护">地址维护</Option>
                <Option value="自定义任务">自定义任务</Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="年度"
              name="year"
              rules={[{ required: true, message: '请选择年度' }]}
            >
              <Select placeholder="请选择（单选）">
                {getYearOptions().map(year => (
                  <Option key={year} value={year}>{year}</Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="主体"
              name="company"
            >
              <Select
                disabled
                placeholder="请选择（多选）"
                showSearch
                filterOption={(input, option) =>
                  (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
                }
              >
                {companies.map(company => (
                  <Option key={company.id} value={company.id}>
                    {company.name}
                  </Option>
                ))}
                {/* 显示当前任务的公司，即使不在companies列表中 */}
                {task && !companies.find(c => c.id === task.companyId) && (
                  <Option key={task.companyId} value={task.companyId}>
                    {task.companyName}
                  </Option>
                )}
              </Select>
            </Form.Item>

            <Form.Item
              label="开始日期"
              name="startDate"
            >
              <DatePicker 
                placeholder="请输入"
                style={{ width: '100%' }}
                format="YYYY-MM-DD"
              />
            </Form.Item>

            <Form.Item
              label="结束日期"
              name="deadline"
              rules={[{ required: true, message: '请选择结束日期' }]}
            >
              <DatePicker 
                placeholder="请输入"
                style={{ width: '100%' }}
                format="YYYY-MM-DD"
              />
            </Form.Item>

            {task?.taskType === '地址维护' && (
              <Form.Item
                label="重复周期（年）"
                name="repeatCycle"
                rules={[{ required: true, message: '请输入重复周期' }]}
              >
                <Input 
                  placeholder="1"
                  type="number"
                  min={1}
                  max={10}
                />
              </Form.Item>
            )}

            <Form.Item
              label="备注"
              name="remarks"
            >
              <TextArea 
                placeholder="请输入"
                rows={4}
                maxLength={500}
                showCount
              />
            </Form.Item>
          </>
        )}

        <Form.Item style={{ marginBottom: 0, textAlign: 'right', marginTop: 24 }}>
          <Space>
            <Button onClick={handleCancel}>
              取消
            </Button>
            <Button 
              type="primary" 
              htmlType="submit" 
              loading={loading}
              style={{ backgroundColor: '#000', borderColor: '#000' }}
            >
              保存
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default UpdateTaskModal;
