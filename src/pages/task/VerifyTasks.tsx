import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, message, Modal, Form, Input, Select, DatePicker, Radio, Typography } from 'antd';
import { CheckOutlined } from '@ant-design/icons';
import axios from 'axios';
import dayjs from 'dayjs';

const { Text } = Typography;
const { Option } = Select;

interface Task {
  id: number;
  taskStatus: string;
  taskType: string;
  year: string;
  startDate: string;
  deadline: string;
  companyId: number;
  companyName: string;
  businessSegment: string;
  remarks: string;
  priority: number;
  progress: number;
  createdAt: string;
  updatedAt: string;
}

interface TaskType {
  value: string;
  label: string;
}

const VerifyTasks: React.FC = () => {
  const [taskList, setTaskList] = useState<Task[]>([]);
  const [loading, setLoading] = useState(false);
  const [verifyModalVisible, setVerifyModalVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [form] = Form.useForm();
  const [taskTypes, setTaskTypes] = useState<TaskType[]>([]);
  const [isVerified, setIsVerified] = useState(true);
  const [shouldCreateNext, setShouldCreateNext] = useState(false);
  const [verifyLoading, setVerifyLoading] = useState(false);

  // 获取已完成的任务列表
  const fetchCompletedTasks = async () => {
    try {
      setLoading(true);
      const response = await axios.get('http://localhost:8081/api/tasks', {
        params: {
          status: '已完成',
          page: 1,
          limit: 1000
        }
      });

      if (response.data.success) {
        setTaskList(response.data.data.list || []);
      } else {
        message.error('获取任务列表失败');
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取任务类型列表
  const fetchTaskTypes = async () => {
    try {
      const response = await axios.get('http://localhost:8081/api/task/types');
      if (response.data.success) {
        setTaskTypes(response.data.data);
      }
    } catch (error) {
      console.error('获取任务类型失败:', error);
    }
  };

  useEffect(() => {
    fetchCompletedTasks();
    fetchTaskTypes();
  }, []);

  // 打开核实弹窗
  const handleVerify = (task: Task) => {
    setSelectedTask(task);
    setVerifyModalVisible(true);
    setIsVerified(true);
    setShouldCreateNext(false);
    
    // 设置表单默认值，下一周期任务年度为当前任务年度+1
    const nextYear = parseInt(task.year) + 1;
    form.setFieldsValue({
      taskType: task.taskType,
      year: nextYear.toString(),
      companyName: task.companyName,
      startDate: null,
      deadline: null,
      repeatCycle: '',
      yearlyReminder: '否',
      remarks: ''
    });
  };

  // 处理核实提交
  const handleVerifySubmit = async () => {
    if (!isVerified) {
      message.error('请选择已核实');
      return;
    }

    if (shouldCreateNext) {
      try {
        await form.validateFields();
      } catch (error) {
        message.error('请填写完整的任务信息');
        return;
      }
    }

    setVerifyLoading(true);
    try {
      const formValues = shouldCreateNext ? form.getFieldsValue() : null;

      const requestData = {
        taskId: selectedTask!.id,
        isVerified: true,
        shouldCreateNext,
        nextTaskData: shouldCreateNext ? {
          taskType: formValues.taskType,
          year: formValues.year,
          startDate: formValues.startDate ? formValues.startDate.format('YYYY-MM-DD') : null,
          deadline: formValues.deadline ? formValues.deadline.format('YYYY-MM-DD') : null,
          companyId: selectedTask!.companyId,
          companyName: formValues.companyName,
          businessSegment: selectedTask!.businessSegment,
          repeatCycle: formValues.repeatCycle,
          yearlyReminder: formValues.yearlyReminder || '否',
          remarks: formValues.remarks || ''
        } : null
      };

      const response = await axios.post('http://localhost:8080/api/task/verify', requestData);

      if (response.data.success) {
        message.success(response.data.message);
        setVerifyModalVisible(false);
        fetchCompletedTasks(); // 重新获取任务列表
      } else {
        message.error(response.data.message);
      }
    } catch (error) {
      console.error('核实任务失败:', error);
      message.error('核实任务失败');
    } finally {
      setVerifyLoading(false);
    }
  };

  // 处理取消
  const handleCancel = () => {
    setVerifyModalVisible(false);
    setSelectedTask(null);
    form.resetFields();
  };

  const columns = [
    {
      title: '任务状态',
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      width: 100,
      align: 'center' as const,
      render: (status: string) => (
        <div style={{
          backgroundColor: '#52c41a',
          color: 'white',
          padding: '4px 8px',
          borderRadius: '4px',
          textAlign: 'center',
          fontSize: '12px'
        }}>
          {status}
        </div>
      )
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      key: 'taskType',
      width: 120,
      align: 'center' as const
    },
    {
      title: '年度',
      dataIndex: 'year',
      key: 'year',
      width: 80,
      align: 'center' as const
    },
    {
      title: '开始日期',
      dataIndex: 'startDate',
      key: 'startDate',
      width: 120,
      align: 'center' as const
    },
    {
      title: '截止日期',
      dataIndex: 'deadline',
      key: 'deadline',
      width: 120,
      align: 'center' as const,
      render: (deadline: string) => (
        <div style={{
          backgroundColor: '#52c41a',
          color: 'white',
          padding: '4px 8px',
          borderRadius: '4px',
          textAlign: 'center',
          fontSize: '12px'
        }}>
          {deadline}
        </div>
      )
    },
    {
      title: '主体名称',
      dataIndex: 'companyName',
      key: 'companyName',
      width: 200,
      align: 'center' as const
    },
    {
      title: '业务板块',
      dataIndex: 'businessSegment',
      key: 'businessSegment',
      width: 120,
      align: 'center' as const
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      align: 'center' as const,
      render: (_, record: Task) => (
        <Button
          type="link"
          size="small"
          icon={<CheckOutlined />}
          onClick={() => handleVerify(record)}
        >
          核实
        </Button>
      )
    }
  ];

  return (
    <div style={{ padding: '24px' }}>
      <Card title="核实任务">
        <Table
          columns={columns}
          dataSource={taskList}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            pageSizeOptions: ['10', '20', '50', '100']
          }}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 核实弹窗 */}
      <Modal
        title="核实任务"
        open={verifyModalVisible}
        onCancel={handleCancel}
        footer={[
          <Button key="cancel" onClick={handleCancel}>
            取消
          </Button>,
          <Button
            key="verify"
            type="primary"
            loading={verifyLoading}
            onClick={handleVerifySubmit}
          >
            核实
          </Button>
        ]}
        width={700}
        destroyOnClose
      >
        {selectedTask && (
          <>
            {/* 当前处理的任务信息 */}
            <div style={{ marginBottom: '24px', padding: '16px', backgroundColor: '#f5f5f5', borderRadius: '6px' }}>
              <Text strong style={{ fontSize: '16px' }}>
                当前处理：{selectedTask.companyName} - {selectedTask.taskType}（{selectedTask.year}年度）
              </Text>
              <div style={{ marginTop: '12px' }}>
                <div><Text>开始日期：{selectedTask.startDate}</Text></div>
                <div><Text>截止日期：{selectedTask.deadline}</Text></div>
                <div><Text>业务板块：{selectedTask.businessSegment}</Text></div>
                <div><Text>备注：{selectedTask.remarks || '无'}</Text></div>
              </div>

              {/* 文件列表 */}
              <div style={{ marginTop: '16px' }}>
                <Text strong>相关文件：</Text>
                <div style={{ marginTop: '8px' }}>
                  <Button type="link" size="small" onClick={() => message.info('文件功能开发中')}>
                    📄 任务相关文档.pdf
                  </Button>
                  <Button type="link" size="small" onClick={() => message.info('文件功能开发中')}>
                    📄 完成证明.jpg
                  </Button>
                </div>
              </div>
            </div>

            {/* 核实任务完成情况 */}
            <div style={{ marginBottom: '24px' }}>
              <h3 style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '16px' }}>
                核实任务完成情况
              </h3>

              <div style={{ marginBottom: '16px' }}>
                <Text style={{ marginRight: '20px', fontSize: '14px' }}>任务状态</Text>
                <Radio.Group
                  value={isVerified}
                  onChange={(e) => setIsVerified(e.target.value)}
                >
                  <Radio value={true}>已核实</Radio>
                </Radio.Group>
              </div>

              <div>
                <Text style={{ marginRight: '20px', fontSize: '14px' }}>下一周期任务</Text>
                <Radio.Group
                  value={shouldCreateNext}
                  onChange={(e) => setShouldCreateNext(e.target.value)}
                >
                  <Radio value={false}>不创建</Radio>
                  <Radio value={true}>创建</Radio>
                </Radio.Group>
              </div>
            </div>

            {/* 任务信息表单 */}
            {shouldCreateNext && (
              <Form
                form={form}
                layout="vertical"
                style={{ maxWidth: '600px' }}
              >
                <Form.Item
                  label="任务类型"
                  name="taskType"
                >
                  <Input disabled value={selectedTask.taskType} />
                </Form.Item>

                <Form.Item
                  label="主体公司"
                  name="companyName"
                >
                  <Input disabled value={selectedTask.companyName} />
                </Form.Item>

                <Form.Item
                  label="年度"
                  name="year"
                  rules={[{ required: true, message: '请选择年度' }]}
                >
                  <Select placeholder="请选择年度">
                    {[2024, 2025, 2026].map(year => (
                      <Option key={year} value={year.toString()}>
                        {year}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>

                <Form.Item
                  label="开始日期"
                  name="startDate"
                  rules={[{ required: true, message: '请选择开始日期' }]}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>

                <Form.Item
                  label="结束日期"
                  name="deadline"
                  rules={[{ required: true, message: '请选择结束日期' }]}
                >
                  <DatePicker style={{ width: '100%' }} />
                </Form.Item>

                {selectedTask.taskType === '地址维护' ? (
                  <Form.Item
                    label="每年提醒"
                    name="yearlyReminder"
                    rules={[{ required: true, message: '请选择每年提醒' }]}
                  >
                    <Select placeholder="请选择">
                      <Option value="是">是</Option>
                      <Option value="否">否</Option>
                    </Select>
                  </Form.Item>
                ) : (
                  <Form.Item
                    label="重复周期（年）"
                    name="repeatCycle"
                    rules={[{ required: true, message: '请输入重复周期' }]}
                  >
                    <Input placeholder="请输入重复周期" type="number" />
                  </Form.Item>
                )}

                <Form.Item
                  label="备注"
                  name="remarks"
                >
                  <Input.TextArea
                    placeholder="请输入备注"
                    maxLength={500}
                    showCount
                    rows={3}
                  />
                </Form.Item>
              </Form>
            )}
          </>
        )}
      </Modal>
    </div>
  );
};

export default VerifyTasks;
