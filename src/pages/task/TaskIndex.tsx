import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Table,
  Button,
  Input,
  Select,
  Space,
  Tag,
  Modal,
  message,
  Pagination,
  Menu,
  Dropdown
} from 'antd';
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined, CheckOutlined, FileSearchOutlined, DownOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import EditTaskModal from './components/EditTaskModal';
import UpdateTaskModal from './components/UpdateTaskModal';

const { Option } = Select;

interface TaskStatistics {
  typeStats: {
    全部: number;
    年审年报: number;
    地址维护: number;
    自定义任务: number;
  };
  statusStats: {
    已逾期: number;
    未开始: number;
    进行中: number;
    待核实: number;
    已完成: number;
  };
  typeStatusStats: {
    年审年报: {
      已逾期: number;
      未开始: number;
      进行中: number;
      待核实: number;
      已完成: number;
    };
    地址维护: {
      已逾期: number;
      未开始: number;
      进行中: number;
      待核实: number;
      已完成: number;
    };
    自定义任务: {
      已逾期: number;
      未开始: number;
      进行中: number;
      待核实: number;
      已完成: number;
    };
  };
}

interface TaskItem {
  id: number;
  taskStatus: string;
  taskType: string;
  year: string;
  startDate: string;
  deadline: string;
  companyId: number;
  companyName: string;
  businessSegment: string;
  remarks: string;
  priority: number;
  progress: number;
  createTime: string;
}

interface TaskListResponse {
  list: TaskItem[];
  total: number;
  page: number;
  limit: number;
}

const TaskIndex: React.FC = () => {
  const navigate = useNavigate();
  const [statistics, setStatistics] = useState<TaskStatistics | null>(null);
  const [statisticsError, setStatisticsError] = useState<string | null>(null);
  const [taskList, setTaskList] = useState<TaskItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 查询条件
  const [filters, setFilters] = useState({
    status: '',
    type: '',
    year: '',
    keyword: ''
  });

  // 可用年份列表
  const [availableYears, setAvailableYears] = useState<string[]>([]);

  // 弹窗状态
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [updateModalVisible, setUpdateModalVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState<TaskItem | null>(null);

  // 获取年份列表
  const fetchAvailableYears = async () => {
    console.log('开始获取年份列表...');
    try {
      const response = await fetch('http://localhost:8080/api/task/years');
      console.log('年份列表响应状态:', response.status);
      const result = await response.json();
      console.log('年份列表结果:', result);
      if (result.success) {
        setAvailableYears(result.data);
        console.log('年份列表设置成功:', result.data);
      } else {
        console.error('获取年份列表失败:', result.message);
      }
    } catch (error) {
      console.error('获取年份列表失败:', error);
    }
  };

  // 获取统计数据
  const fetchStatistics = async () => {
    console.log('开始获取统计数据...');
    try {
      setStatisticsError(null);
      const response = await fetch('http://localhost:8080/api/tasks/statistics');
      console.log('统计数据响应状态:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      console.log('统计数据结果:', result);

      if (result.success) {
        // 动态生成统计结构
        const dynamicStats = generateStatsFromAPI(result.data);
        setStatistics(dynamicStats);
        console.log('✅ 统计数据设置成功');
      } else {
        throw new Error(result.message || '统计数据API返回失败');
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      const errorMessage = `获取统计数据失败: ${error.message}`;
      setStatisticsError(errorMessage);
      message.error(errorMessage);
    }
  };

  // 动态生成统计结构
  const generateStatsFromAPI = (apiData: any): TaskStatistics => {
    return {
      typeStats: apiData.typeStats || { 全部: 0, 年审年报: 0, 地址维护: 0, 自定义任务: 0 },
      statusStats: apiData.statusStats || { 已逾期: 0, 未开始: 0, 进行中: 0, 待核实: 0, 已完成: 0 },
      typeStatusStats: apiData.typeStatusStats || {
        年审年报: { 已逾期: 0, 未开始: 0, 进行中: 0, 待核实: 0, 已完成: 0 },
        地址维护: { 已逾期: 0, 未开始: 0, 进行中: 0, 待核实: 0, 已完成: 0 },
        自定义任务: { 已逾期: 0, 未开始: 0, 进行中: 0, 待核实: 0, 已完成: 0 }
      }
    };
  };

  // 获取任务列表（使用当前filters）
  const fetchTaskList = async (page = 1, pageSize = 10) => {
    console.log('开始获取任务列表...', { page, pageSize, filters });
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pageSize.toString(),
        ...(filters.status && { status: filters.status }),
        ...(filters.type && { type: filters.type }),
        ...(filters.year && { year: filters.year })
      });

      const url = `http://localhost:8080/api/tasks?${params}`;
      console.log('请求URL:', url);
      const response = await fetch(url);
      console.log('任务列表响应状态:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('任务列表结果:', result);

      if (result.success) {
        setTaskList(result.data.list);
        setPagination({
          current: result.data.page,
          pageSize: result.data.limit,
          total: result.data.total
        });
        console.log('任务列表设置成功，共', result.data.total, '条记录');
      } else {
        console.error('获取任务列表失败:', result.message);
        message.error(result.message || '获取任务列表失败');
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 使用指定筛选条件获取任务列表
  const fetchTaskListWithFilters = async (customFilters: any, page = 1, pageSize = 10) => {
    console.log('使用自定义筛选条件获取任务列表...', { page, pageSize, customFilters });
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: pageSize.toString(),
        ...(customFilters.status && { status: customFilters.status }),
        ...(customFilters.type && { type: customFilters.type }),
        ...(customFilters.year && { year: customFilters.year }),
        ...(customFilters.keyword && { keyword: customFilters.keyword })
      });

      const url = `http://localhost:8080/api/tasks?${params}`;
      console.log('请求URL:', url);
      const response = await fetch(url);
      console.log('任务列表响应状态:', response.status);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();
      console.log('任务列表结果:', result);

      if (result.success) {
        setTaskList(result.data.list);
        setPagination({
          current: result.data.page,
          pageSize: result.data.limit,
          total: result.data.total
        });
        console.log('任务列表设置成功，共', result.data.total, '条记录');
      } else {
        console.error('获取任务列表失败:', result.message);
        message.error(result.message || '获取任务列表失败');
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    console.log('TaskIndex组件加载，开始获取数据...');
    fetchAvailableYears();
    fetchStatistics();
    fetchTaskList();
  }, []);

  // 移除自动更新useEffect，改为手动触发

  // 查询处理
  const handleSearch = async () => {
    await fetchTaskListWithFilters(filters, 1, pagination.pageSize);
  };

  // 重置查询条件
  const handleReset = async () => {
    const resetFilters = {
      status: '',
      type: '',
      year: '',
      keyword: ''
    };
    setFilters(resetFilters);
    setTimeout(async () => {
      await fetchTaskListWithFilters(resetFilters, 1, pagination.pageSize);
    }, 100);
  };

  // 删除任务
  const handleDelete = (id: number) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个任务吗？删除后无法恢复。',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await fetch(`http://localhost:8080/api/tasks/${id}`, {
            method: 'DELETE'
          });
          const result = await response.json();

          if (result.success) {
            message.success('删除成功');
            fetchTaskList(pagination.current, pagination.pageSize);
            fetchStatistics();
          } else {
            message.error(result.message || '删除失败');
          }
        } catch (error) {
          console.error('删除任务失败:', error);
          message.error('删除失败');
        }
      }
    });
  };

  // 获取状态标签颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case '已逾期': return 'red';
      case '未开始': return 'default';
      case '进行中': return 'blue';
      case '已完成': return 'green';
      case '已核实': return 'green';
      default: return 'default';
    }
  };

  // 重试统计数据
  const handleRetryStatistics = () => {
    setStatisticsError(null);
    fetchStatistics();
  };

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计数据错误状态 */}
      {statisticsError && (
        <Card style={{ marginBottom: 24 }}>
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <h4>统计数据加载失败</h4>
            <p style={{ color: '#ff4d4f', marginBottom: '16px' }}>{statisticsError}</p>
            <Button type="primary" onClick={handleRetryStatistics}>
              重试
            </Button>
          </div>
        </Card>
      )}

      {/* 顶部统计卡片 - 按照图片样式 */}
      {statistics && (
        <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <div style={{
            backgroundColor: '#8c8c8c',
            color: 'white',
            padding: '16px',
            textAlign: 'center',
            borderRadius: '4px'
          }}>
            <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: 12 }}>全部</div>
            <Row gutter={8}>
              <Col span={8}>
                <div style={{ fontSize: '12px' }}>未开始</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold' }}>{statistics?.statusStats?.未开始 || 0}</div>
              </Col>
              <Col span={8}>
                <div style={{ fontSize: '12px' }}>进行中</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                  {statistics?.statusStats?.进行中 || 0}
                </div>
              </Col>
              <Col span={8}>
                <div style={{ fontSize: '12px' }}>已完成</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold' }}>{statistics?.statusStats?.已完成 || 0}</div>
              </Col>
            </Row>
          </div>
        </Col>
        <Col span={6}>
          <div style={{
            backgroundColor: '#d9d9d9',
            color: '#333',
            padding: '16px',
            textAlign: 'center',
            borderRadius: '4px'
          }}>
            <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: 12 }}>年审年报</div>
            <Row gutter={8}>
              <Col span={8}>
                <div style={{ fontSize: '12px' }}>未开始</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                  {statistics?.typeStatusStats?.年审年报?.未开始 || 0}
                </div>
              </Col>
              <Col span={8}>
                <div style={{ fontSize: '12px' }}>进行中</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                  {statistics?.typeStatusStats?.年审年报?.进行中 || 0}
                </div>
              </Col>
              <Col span={8}>
                <div style={{ fontSize: '12px' }}>已完成</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                  {statistics?.typeStatusStats?.年审年报?.已完成 || 0}
                </div>
              </Col>
            </Row>
          </div>
        </Col>
        <Col span={6}>
          <div style={{
            backgroundColor: '#d9d9d9',
            color: '#333',
            padding: '16px',
            textAlign: 'center',
            borderRadius: '4px'
          }}>
            <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: 12 }}>地址维护</div>
            <Row gutter={8}>
              <Col span={8}>
                <div style={{ fontSize: '12px' }}>未开始</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                  {statistics?.typeStatusStats?.地址维护?.未开始 || 0}
                </div>
              </Col>
              <Col span={8}>
                <div style={{ fontSize: '12px' }}>进行中</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                  {statistics?.typeStatusStats?.地址维护?.进行中 || 0}
                </div>
              </Col>
              <Col span={8}>
                <div style={{ fontSize: '12px' }}>已完成</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                  {statistics?.typeStatusStats?.地址维护?.已完成 || 0}
                </div>
              </Col>
            </Row>
          </div>
        </Col>
        <Col span={6}>
          <div style={{
            backgroundColor: '#d9d9d9',
            color: '#333',
            padding: '16px',
            textAlign: 'center',
            borderRadius: '4px'
          }}>
            <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: 12 }}>自定义任务</div>
            <Row gutter={8}>
              <Col span={8}>
                <div style={{ fontSize: '12px' }}>未开始</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                  {statistics?.typeStatusStats?.自定义任务?.未开始 || 0}
                </div>
              </Col>
              <Col span={8}>
                <div style={{ fontSize: '12px' }}>进行中</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                  {statistics?.typeStatusStats?.自定义任务?.进行中 || 0}
                </div>
              </Col>
              <Col span={8}>
                <div style={{ fontSize: '12px' }}>已完成</div>
                <div style={{ fontSize: '18px', fontWeight: 'bold' }}>
                  {statistics?.typeStatusStats?.自定义任务?.已完成 || 0}
                </div>
              </Col>
            </Row>
          </div>
        </Col>
      </Row>
      )}

      {/* 状态统计条和搜索区域 - 两行布局（交换位置） */}
      {statistics && (
        <div style={{ marginBottom: 16 }}>
          {/* 第一行：搜索框和操作按钮 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px', marginBottom: 12 }}>
            <Input
              placeholder="搜索主体名称"
              value={filters.keyword}
              onChange={(e) => setFilters({ ...filters, keyword: e.target.value })}
              suffix={<SearchOutlined />}
              style={{ width: '200px' }}
            />
            <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
              查询
            </Button>
            <Button onClick={handleReset}>
              重置
            </Button>
            <Button
              type="default"
              onClick={() => navigate('/task/investigation')}
            >
              任务排查
            </Button>
            <Button
              type="primary"
              onClick={() => navigate('/task/add')}
            >
              新增
            </Button>
          </div>

          {/* 第二行：状态筛选按钮 */}
          <div style={{ display: 'flex', gap: '8px' }}>
            <div
              style={{
                textAlign: 'center',
                padding: '8px 12px',
                border: '1px solid #d9d9d9',
                backgroundColor: filters.status === '' ? '#1890ff' : '#f5f5f5',
                color: filters.status === '' ? 'white' : '#333',
                cursor: 'pointer',
                borderRadius: '4px',
                fontSize: '14px',
                minWidth: '100px'
              }}
              onClick={async () => {
                const newFilters = { ...filters, status: '' };
                setFilters(newFilters);
                await fetchTaskListWithFilters(newFilters, 1, pagination.pageSize);
              }}
            >
              全部（{(statistics?.statusStats?.未开始 || 0) + (statistics?.statusStats?.进行中 || 0) + (statistics?.statusStats?.已完成 || 0) + (statistics?.statusStats?.已核实 || 0)}）
            </div>
            <div
              style={{
                textAlign: 'center',
                padding: '8px 12px',
                border: '1px solid #d9d9d9',
                backgroundColor: filters.status === '未开始' ? '#1890ff' : '#f5f5f5',
                color: filters.status === '未开始' ? 'white' : '#333',
                cursor: 'pointer',
                borderRadius: '4px',
                fontSize: '14px',
                minWidth: '100px'
              }}
              onClick={async () => {
                const newFilters = { ...filters, status: '未开始' };
                setFilters(newFilters);
                await fetchTaskListWithFilters(newFilters, 1, pagination.pageSize);
              }}
            >
              未开始（{statistics?.statusStats?.未开始 || 0}）
            </div>
            <div
              style={{
                textAlign: 'center',
                padding: '8px 12px',
                border: '1px solid #d9d9d9',
                backgroundColor: filters.status === '进行中' ? '#1890ff' : '#f5f5f5',
                color: filters.status === '进行中' ? 'white' : '#333',
                cursor: 'pointer',
                borderRadius: '4px',
                fontSize: '14px',
                minWidth: '100px'
              }}
              onClick={async () => {
                const newFilters = { ...filters, status: '进行中' };
                setFilters(newFilters);
                await fetchTaskListWithFilters(newFilters, 1, pagination.pageSize);
              }}
            >
              进行中（{statistics?.statusStats?.进行中 || 0}）
            </div>

            <div
              style={{
                textAlign: 'center',
                padding: '8px 12px',
                border: '1px solid #d9d9d9',
                backgroundColor: filters.status === '已完成' ? '#1890ff' : '#f5f5f5',
                color: filters.status === '已完成' ? 'white' : '#333',
                cursor: 'pointer',
                borderRadius: '4px',
                fontSize: '14px',
                minWidth: '100px'
              }}
              onClick={async () => {
                const newFilters = { ...filters, status: '已完成' };
                setFilters(newFilters);
                await fetchTaskListWithFilters(newFilters, 1, pagination.pageSize);
              }}
            >
              已完成（{statistics?.statusStats?.已完成 || 0}）
            </div>
            <div
              style={{
                textAlign: 'center',
                padding: '8px 12px',
                border: '1px solid #d9d9d9',
                backgroundColor: filters.status === '已核实' ? '#1890ff' : '#f5f5f5',
                color: filters.status === '已核实' ? 'white' : '#333',
                cursor: 'pointer',
                borderRadius: '4px',
                fontSize: '14px',
                minWidth: '100px'
              }}
              onClick={async () => {
                const newFilters = { ...filters, status: '已核实' };
                setFilters(newFilters);
                await fetchTaskListWithFilters(newFilters, 1, pagination.pageSize);
              }}
            >
              已核实（{statistics?.statusStats?.已核实 || 0}）
            </div>
          </div>
        </div>
      )}

      {/* 任务列表 */}
      <Card>
        <Table
          columns={[
            {
              title: '任务状态',
              dataIndex: 'taskStatus',
              key: 'taskStatus',
              width: 100,
              align: 'center',
              render: (status: string, record: any) => {
                // 检查是否逾期：未开始或进行中且当前日期>截止日期
                const isOverdue = (status === '未开始' || status === '进行中') &&
                                 new Date() > new Date(record.deadline);

                if (isOverdue) {
                  return (
                    <div style={{
                      backgroundColor: '#ff4d4f',
                      color: 'white',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      textAlign: 'center',
                      fontSize: '12px'
                    }}>
                      {status}
                    </div>
                  );
                }

                if (status === '已完成') {
                  return (
                    <div style={{
                      backgroundColor: '#52c41a',
                      color: 'white',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      textAlign: 'center',
                      fontSize: '12px'
                    }}>
                      {status}
                    </div>
                  );
                }

                return <Tag color={getStatusColor(status)}>{status}</Tag>;
              }
            },
            {
              title: (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  任务类型
                  <Dropdown
                    overlay={
                      <Menu onClick={async ({ key }) => {
                        const newFilters = { ...filters, type: key === 'all' ? '' : key };
                        setFilters(newFilters);
                        await fetchTaskListWithFilters(newFilters, 1, pagination.pageSize);
                      }}>
                        <Menu.Item key="all">全部</Menu.Item>
                        <Menu.Item key="年审年报">年审年报</Menu.Item>
                        <Menu.Item key="地址维护">地址维护</Menu.Item>
                        <Menu.Item key="自定义任务">自定义任务</Menu.Item>
                      </Menu>
                    }
                    trigger={['click']}
                  >
                    <DownOutlined style={{ marginLeft: 4, cursor: 'pointer', fontSize: '12px' }} />
                  </Dropdown>
                </div>
              ),
              dataIndex: 'taskType',
              key: 'taskType',
              width: 120,
              align: 'center'
            },
            {
              title: (
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  年度
                  <Dropdown
                    overlay={
                      <Menu onClick={async ({ key }) => {
                        const newFilters = { ...filters, year: key === 'all' ? '' : key };
                        setFilters(newFilters);
                        await fetchTaskListWithFilters(newFilters, 1, pagination.pageSize);
                      }}>
                        <Menu.Item key="all">全部</Menu.Item>
                        {availableYears.map(year => (
                          <Menu.Item key={year}>{year}</Menu.Item>
                        ))}
                      </Menu>
                    }
                    trigger={['click']}
                  >
                    <DownOutlined style={{ marginLeft: 4, cursor: 'pointer', fontSize: '12px' }} />
                  </Dropdown>
                </div>
              ),
              dataIndex: 'year',
              key: 'year',
              width: 80,
              align: 'center'
            },
            {
              title: '开始日期',
              dataIndex: 'startDate',
              key: 'startDate',
              width: 120,
              align: 'center',
              sorter: (a, b) => new Date(a.startDate).getTime() - new Date(b.startDate).getTime(),
              sortDirections: ['ascend', 'descend']
            },
            {
              title: '截止日期',
              dataIndex: 'deadline',
              key: 'deadline',
              width: 120,
              align: 'center',
              sorter: (a, b) => new Date(a.deadline).getTime() - new Date(b.deadline).getTime(),
              sortDirections: ['ascend', 'descend'],
              render: (deadline: string, record: any) => {
                // 检查是否逾期：未开始或进行中且当前日期>截止日期
                const isOverdue = (record.taskStatus === '未开始' || record.taskStatus === '进行中') &&
                                 new Date() > new Date(deadline);

                if (isOverdue) {
                  return (
                    <div style={{
                      backgroundColor: '#ff4d4f',
                      color: 'white',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      textAlign: 'center',
                      fontSize: '12px'
                    }}>
                      {deadline}
                    </div>
                  );
                }

                if (record.taskStatus === '已完成') {
                  return (
                    <div style={{
                      backgroundColor: '#52c41a',
                      color: 'white',
                      padding: '4px 8px',
                      borderRadius: '4px',
                      textAlign: 'center',
                      fontSize: '12px'
                    }}>
                      {deadline}
                    </div>
                  );
                }

                return deadline;
              }
            },
            {
              title: '主体名称',
              dataIndex: 'companyName',
              key: 'companyName',
              width: 200,
              align: 'center'
            },
            {
              title: '备注',
              dataIndex: 'remarks',
              key: 'remarks',
              ellipsis: true,
              align: 'center'
            },
            {
              title: '操作',
              key: 'action',
              width: 200,
              align: 'center',
              render: (_, record) => (
                <Space size="small">
                  <Button
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => {
                      setSelectedTask(record);
                      setUpdateModalVisible(true);
                    }}
                  >
                    更新
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => {
                      setSelectedTask(record);
                      setEditModalVisible(true);
                    }}
                  >
                    编辑
                  </Button>
                  <Button
                    type="link"
                    size="small"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleDelete(record.id)}
                  >
                    删除
                  </Button>
                </Space>
              )
            }
          ]}
          dataSource={taskList}
          rowKey="id"
          loading={loading}
          pagination={false}
          scroll={{ x: 1200 }}
        />

        {/* 分页 */}
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Pagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            showSizeChanger
            showQuickJumper
            showTotal={(total, range) => `共 ${total} 条记录`}
            onChange={(page, pageSize) => {
              fetchTaskList(page, pageSize);
            }}
          />
        </div>
      </Card>

      {/* 编辑任务弹窗 */}
      <EditTaskModal
        visible={editModalVisible}
        task={selectedTask}
        onCancel={() => {
          setEditModalVisible(false);
          setSelectedTask(null);
        }}
        onSuccess={() => {
          fetchTaskList(pagination.current, pagination.pageSize);
          fetchStatistics();
        }}
      />

      {/* 更新任务进度弹窗 */}
      <UpdateTaskModal
        visible={updateModalVisible}
        task={selectedTask}
        onCancel={() => {
          setUpdateModalVisible(false);
          setSelectedTask(null);
        }}
        onSuccess={() => {
          fetchTaskList(pagination.current, pagination.pageSize);
          fetchStatistics();
        }}
      />
    </div>
  );
};

export default TaskIndex;
