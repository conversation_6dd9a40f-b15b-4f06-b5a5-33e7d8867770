import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation, useSearchParams } from 'react-router-dom';
import { Button, Select, DatePicker, Table, Upload, message, Input, Row, Col } from 'antd';
import { UploadOutlined, DownloadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import './index.css';

const { Option } = Select;

interface CompanyChangeExecutiveProps {}

interface ChangeRecord {
  field: string;
  fieldName: string;
  oldValue: string;
  newValue: string;
}

interface CompanyInfo {
  id: number;
  chineseName: string;
  englishName: string;
}

interface Person {
  id: number;
  name: string;
  idType: string;
  idNumber: string;
  maskedIdNumber: string;
  phone: string | null;
  email: string | null;
}

interface ExecutiveRecord {
  id?: number;
  position: string;
  personId: number | null;
  personName: string;
}

const CompanyChangeExecutive: React.FC<CompanyChangeExecutiveProps> = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const [loading, setLoading] = useState(false);
  
  // 表单状态
  const [changeType, setChangeType] = useState<string>('');
  const [changeContent, setChangeContent] = useState<string>('');
  const [changeDate, setChangeDate] = useState<dayjs.Dayjs>(dayjs());
  const [changeRecords, setChangeRecords] = useState<ChangeRecord[]>([]);
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);

  // 公司和人员信息状态
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null);
  const [persons, setPersons] = useState<Person[]>([]);
  const [executives, setExecutives] = useState<ExecutiveRecord[]>([]);
  const [originalExecutives, setOriginalExecutives] = useState<ExecutiveRecord[]>([]);

  // 从路由参数获取公司信息，支持URL参数和state参数
  const companyId = location.state?.companyId || searchParams.get('companyId');
  const companyName = location.state?.companyName || '';
  const isConfirmMode = location.state?.isConfirmMode || false;

  // 变更类型选项
  const changeTypeOptions = [
    { value: 'basic', label: '基础信息变更' },
    { value: 'executive', label: '高管信息变更' },
    { value: 'shareholder', label: '股东信息变更' },
  ];

  // 变更内容选项（根据变更类型动态变化）
  const getChangeContentOptions = (type: string) => {
    switch (type) {
      case 'executive':
        return [
          { value: 'add_executive', label: '新增高管' },
          { value: 'remove_executive', label: '删除高管' },
          { value: 'change_position', label: '职位变更' },
          { value: 'change_person', label: '任职人员变更' },
        ];
      default:
        return [];
    }
  };

  // 高管职位选项
  const positionOptions = [
    '董事长',
    '副董事长',
    '董事',
    '总经理',
    '副总经理',
    '财务总监',
    '财务负责人',
    '监事',
    '监事会主席',
    '执行董事',
    '经理',
    '法定代表人',
  ];

  // 高管信息表格列配置
  const executiveColumns = [
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
      width: '30%',
      render: (text: string, record: ExecutiveRecord, index: number) => (
        <Select
          value={text}
          onChange={(value) => handleExecutiveChange(index, 'position', value)}
          placeholder="请选择职位"
          style={{ width: '100%' }}
          disabled={isConfirmMode}
        >
          {positionOptions.map(position => (
            <Option key={position} value={position}>{position}</Option>
          ))}
        </Select>
      ),
    },
    {
      title: '任职人员',
      dataIndex: 'personId',
      key: 'personId',
      width: '50%',
      render: (personId: number | null, record: ExecutiveRecord, index: number) => (
        <Select
          value={personId}
          onChange={(value) => handleExecutiveChange(index, 'personId', value)}
          placeholder="请选择任职人员"
          style={{ width: '100%' }}
          disabled={isConfirmMode}
          showSearch
          filterOption={(input, option) =>
            (option?.children as string)?.toLowerCase().includes(input.toLowerCase())
          }
        >
          {persons.map(person => (
            <Option key={person.id} value={person.id}>{person.name}</Option>
          ))}
        </Select>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: '20%',
      render: (_, record: ExecutiveRecord, index: number) => (
        <Button
          type="link"
          danger
          onClick={() => handleRemoveExecutive(index)}
          disabled={isConfirmMode}
          style={{ color: '#ff4d4f' }}
        >
          🗑️
        </Button>
      ),
    },
  ];

  useEffect(() => {
    if (companyId) {
      loadInitialData();
    }
  }, [companyId]);

  useEffect(() => {
    // 根据变更内容生成变更记录
    if (changeContent) {
      const records: ChangeRecord[] = [];
      // 这里可以根据实际的变更内容生成记录
      setChangeRecords(records);
    }
  }, [changeContent]);

  // 加载初始数据
  const loadInitialData = async () => {
    try {
      // 加载公司信息
      if (companyId) {
        const companyResponse = await fetch(`http://localhost:8080/api/companies/${companyId}`);
        const companyResult = await companyResponse.json();
        if (companyResult.success) {
          setCompanyInfo(companyResult.data);
        }
      }

      // 加载人员列表
      const personsResponse = await fetch('http://localhost:8080/api/persons');
      const personsResult = await personsResponse.json();
      if (personsResult.success) {
        setPersons(personsResult.data);
      }

      // 加载公司现有高管信息
      if (companyId) {
        const employmentResponse = await fetch('http://localhost:8080/api/employment-records');
        const employmentResult = await employmentResponse.json();
        if (employmentResult.success) {
          // 筛选当前公司的在职高管
          const currentExecutives = employmentResult.data.filter((record: any) =>
            record.companyId === parseInt(companyId) &&
            record.isActive === 1 &&
            record.endDate === null
          );

          // 转换为页面需要的格式
          const executiveRecords = currentExecutives.map((record: any) => ({
            id: record.id,
            position: record.position,
            personId: record.personId,
            personName: record.personName
          }));

          // 如果有现有高管，显示现有高管；否则添加一行空记录
          if (executiveRecords.length > 0) {
            setExecutives(executiveRecords);
            setOriginalExecutives(executiveRecords); // 保存原始数据
          } else {
            setExecutives([{ position: '', personId: null, personName: '' }]);
            setOriginalExecutives([]); // 保存原始数据（空数组）
          }
        } else {
          // 如果获取失败，添加一行空记录
          setExecutives([{ position: '', personId: null, personName: '' }]);
          setOriginalExecutives([]); // 保存原始数据（空数组）
        }
      } else {
        // 如果没有公司ID，添加一行空记录
        setExecutives([{ position: '', personId: null, personName: '' }]);
        setOriginalExecutives([]); // 保存原始数据（空数组）
      }

    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    }
  };

  // 检查任职冲突
  const checkEmploymentConflict = (personId: number, currentIndex: number) => {
    // 检查同一人是否在当前公司已有其他在职记录
    const conflicts = executives.filter((exec, index) =>
      index !== currentIndex &&
      exec.personId === personId &&
      exec.personId !== null
    );

    if (conflicts.length > 0) {
      const conflictPositions = conflicts.map(exec => exec.position).join('、');
      message.warning(`该人员已在当前公司担任：${conflictPositions}，同一人不能在同一时间任职同一公司的多个职位`);
      return true;
    }
    return false;
  };

  // 处理高管信息变更
  const handleExecutiveChange = (index: number, field: string, value: any) => {
    const newExecutives = [...executives];

    if (field === 'personId') {
      // 检查任职冲突
      if (value && checkEmploymentConflict(value, index)) {
        return; // 如果有冲突，不进行更新
      }

      const selectedPerson = persons.find(p => p.id === value);
      newExecutives[index] = {
        ...newExecutives[index],
        personId: value,
        personName: selectedPerson?.name || ''
      };
    } else {
      newExecutives[index] = {
        ...newExecutives[index],
        [field]: value
      };
    }
    setExecutives(newExecutives);
  };

  // 添加高管记录
  const handleAddExecutive = () => {
    setExecutives([...executives, { position: '', personId: null, personName: '' }]);
  };

  // 删除高管记录
  const handleRemoveExecutive = (index: number) => {
    if (executives.length > 1) {
      const newExecutives = executives.filter((_, i) => i !== index);
      setExecutives(newExecutives);
    }
  };

  // 文件上传处理
  const handleFileUpload = (file: any, type: string) => {
    const newFile = {
      type: type,
      name: file.name,
      file: file,
    };
    
    setUploadedFiles(prev => {
      const filtered = prev.filter(f => f.type !== type);
      return [...filtered, newFile];
    });
    
    message.success(`${file.name} 上传成功`);
    return false; // 阻止自动上传
  };

  // 下载模板
  const handleDownloadTemplate = (type: string) => {
    message.info(`下载 ${type} 模板`);
  };

  // 提交处理
  const handleSubmit = async () => {
    if (!companyInfo) {
      message.error('公司信息加载中，请稍后再试');
      return;
    }

    // 验证高管信息
    const validExecutives = executives.filter(exec => exec.position && exec.personId);
    if (validExecutives.length === 0) {
      message.error('请至少添加一条有效的高管信息');
      return;
    }

    // 检查是否有重复的人员
    const personIds = validExecutives.map(exec => exec.personId);
    const duplicatePersonIds = personIds.filter((id, index) => personIds.indexOf(id) !== index);
    if (duplicatePersonIds.length > 0) {
      const duplicatePersons = duplicatePersonIds.map(id => {
        const person = persons.find(p => p.id === id);
        return person?.name || '未知';
      }).join('、');
      message.error(`同一人不能在同一公司担任多个职位：${duplicatePersons}`);
      return;
    }

    // 检查是否有重复的职位
    const positions = validExecutives.map(exec => exec.position);
    const duplicatePositions = positions.filter((pos, index) => positions.indexOf(pos) !== index);
    if (duplicatePositions.length > 0) {
      message.error(`同一职位不能有多人担任：${duplicatePositions.join('、')}`);
      return;
    }

    // 生成变更内容描述
    const changes = [];

    // 比较原始数据和当前数据
    const originalMap = new Map(originalExecutives.map(exec => [exec.position, exec.personName]));
    const currentMap = new Map(validExecutives.map(exec => [exec.position, exec.personName]));

    // 检查新增和变更
    for (const [position, personName] of currentMap) {
      if (!originalMap.has(position)) {
        changes.push(`新增高管: ${position} - ${personName}`);
      } else if (originalMap.get(position) !== personName) {
        changes.push(`高管变更: ${position} - ${originalMap.get(position)} → ${personName}`);
      }
    }

    // 检查删除
    for (const [position, personName] of originalMap) {
      if (!currentMap.has(position)) {
        changes.push(`删除高管: ${position} - ${personName}`);
      }
    }

    if (changes.length === 0) {
      message.warning('没有检测到任何变更');
      return;
    }

    setLoading(true);
    try {
      // 创建变更记录
      const changeResponse = await fetch('http://localhost:8080/api/company-change-confirmation/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          company_id: parseInt(companyId),
          change_type: 'executive',
          change_content: changes.join('; '),
          old_value: JSON.stringify(originalExecutives),
          new_value: JSON.stringify(validExecutives),
          change_date: changeDate.format('YYYY-MM-DD'),
          operator: '当前用户' // 这里应该从用户上下文获取
        }),
      });

      const changeResult = await changeResponse.json();

      if (changeResult.success) {
        message.success('高管信息变更提交成功，等待确认');
        navigate('/company');
      } else {
        message.error(changeResult.message || '提交失败');
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败');
    } finally {
      setLoading(false);
    }
  };

  // 确认处理
  const handleConfirm = async () => {
    setLoading(true);
    try {
      // 这里应该调用确认API
      message.success('变更确认成功');
      navigate('/company-change-confirmation');
    } catch (error) {
      console.error('确认失败:', error);
      message.error('确认失败');
    } finally {
      setLoading(false);
    }
  };

  // 取消处理
  const handleCancel = () => {
    navigate('/company');
  };

  // 生成变更内容描述
  const generateChangeDescription = () => {
    return executives.map(exec => `${exec.position} - ${exec.personName}`).join(', ');
  };

  // 文件数据
  const fileData = [
    { key: 1, type: 'C规范', name: 'C规范模板.docx' },
    { key: 2, type: 'D规范', name: 'D规范模板.docx' },
  ];

  // 如果没有公司ID，显示错误信息
  if (!companyId) {
    return (
      <div className="company-change-executive">
        <div className="page-header">
          <h2>股权管理</h2>
        </div>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <h3>访问错误</h3>
          <p>请通过正确的入口访问此页面：</p>
          <ul style={{ textAlign: 'left', display: 'inline-block' }}>
            <li>从公司信息页面点击公司卡片的"变更"按钮，然后选择"变更高管信息"</li>
            <li>从公司详情页面点击右上角的"变更高管信息"按钮</li>
          </ul>
          <div style={{ marginTop: '20px' }}>
            <Button type="primary" onClick={() => navigate('/company')}>
              返回公司信息页面
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="company-change-executive">
      {/* 顶部按钮 */}
      <div className="top-buttons">
        <Button 
          type="primary" 
          className="submit-btn"
          onClick={handleSubmit}
          loading={loading}
          disabled={isConfirmMode}
        >
          提交
        </Button>
        {isConfirmMode && (
          <Button 
            type="primary" 
            className="confirm-btn"
            onClick={handleConfirm}
            loading={loading}
          >
            确认
          </Button>
        )}
        <Button
          className="cancel-btn"
          onClick={handleCancel}
        >
          取消
        </Button>
      </div>

      {/* 表单区域 */}
      <div className="form-section">
        <div className="form-row">
          <label className="form-label">变更日期</label>
          <DatePicker
            value={changeDate}
            onChange={(date) => setChangeDate(date || dayjs())}
            style={{ width: 300 }}
            disabled={isConfirmMode}
          />
        </div>
      </div>

      {/* 显示公司名称 */}
      {companyInfo && (
        <div style={{ marginBottom: '20px', padding: '10px', background: '#f0f0f0', borderRadius: '4px' }}>
          <strong>变更公司：{companyInfo.chineseName}</strong>
        </div>
      )}

      {/* 高管信息编辑区域 */}
      <div className="executive-info-section">
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <h3>高管信息</h3>
          {!isConfirmMode && (
            <Button type="primary" onClick={handleAddExecutive}>
              添加高管
            </Button>
          )}
        </div>

        <Table
          columns={executiveColumns}
          dataSource={executives}
          pagination={false}
          rowKey={(record, index) => `executive-${index}`}
          bordered
          size="middle"
        />
      </div>

      {/* 参考规范 */}
      <div className="reference-section">
        <h3>参考规范</h3>
        <div className="reference-links">
          <div className="reference-item">
            <span>A规范</span>
            <a href="#" className="reference-link" onClick={() => handleDownloadTemplate('A规范')}>
              A规范.docx
            </a>
          </div>
          <div className="reference-item">
            <span>B规范</span>
            <a href="#" className="reference-link" onClick={() => handleDownloadTemplate('B规范')}>
              B规范.docx
            </a>
          </div>
        </div>
      </div>

      {/* 具体变更 */}
      <div className="change-details-section">
        <h3>具体变更</h3>
        <div style={{ padding: '10px', background: '#f9f9f9', borderRadius: '4px', minHeight: '60px' }}>
          {generateChangeDescription() || '请添加高管信息'}
        </div>
      </div>

      {/* 上传文件 */}
      <div className="upload-section">
        <h3>上传文件</h3>
        <Table
          columns={[
            {
              title: '文件类型',
              dataIndex: 'type',
              key: 'type',
              width: '20%',
              align: 'center',
            },
            {
              title: '文件名',
              dataIndex: 'name',
              key: 'name',
              width: '60%',
              align: 'center',
            },
            {
              title: '操作',
              key: 'action',
              width: '20%',
              align: 'center',
              render: (_, record) => (
                <div style={{ display: 'flex', gap: '8px', justifyContent: 'center' }}>
                  <Upload
                    beforeUpload={(file) => handleFileUpload(file, record.type)}
                    showUploadList={false}
                    disabled={isConfirmMode}
                  >
                    <Button type="link" icon={<UploadOutlined />} disabled={isConfirmMode}>
                      下载模板
                    </Button>
                  </Upload>
                  <Button
                    type="link"
                    icon={<DownloadOutlined />}
                    onClick={() => handleDownloadTemplate(record.type)}
                  >
                    重新上传
                  </Button>
                </div>
              ),
            },
          ]}
          dataSource={fileData}
          pagination={false}
          bordered
          size="small"
        />
      </div>
    </div>
  );
};

export default CompanyChangeExecutive;
