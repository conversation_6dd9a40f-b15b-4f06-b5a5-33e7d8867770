.agency-management-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 查询条件卡片 */
.search-card {
  margin-bottom: 16px;
}

.search-row {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.search-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8px;
}

.search-label {
  font-weight: 500;
  color: #262626;
  font-size: 14px;
  text-align: left;
}

.search-input-group {
  display: flex;
  align-items: center;
}

/* 操作按钮卡片 */
.action-card {
  margin-bottom: 16px;
}

/* 表格样式 */
.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .agency-management-container {
    padding: 16px;
  }

  .search-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .search-item {
    width: 100%;
  }

  .search-item .ant-input,
  .search-item .ant-select {
    width: 100%;
  }
}

@media (max-width: 576px) {
  .agency-management-container {
    padding: 12px;
  }

  .search-item .ant-input,
  .search-item .ant-select {
    width: 100%;
  }
}

/* 按钮样式优化 */
.ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 表格操作按钮 */
.ant-btn-link {
  padding: 0;
  height: auto;
}

.ant-btn-link:hover {
  background-color: transparent;
}

/* 卡片间距 */
.ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.ant-card + .ant-card {
  margin-top: 16px;
}

/* 表格分页样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: center;
}

/* 表格加载状态 */
.ant-spin-container {
  min-height: 200px;
}

/* 空数据状态 */
.ant-empty {
  margin: 40px 0;
}

/* 表单项间距 */
.ant-form-item {
  margin-bottom: 16px;
}

.ant-form-item:last-child {
  margin-bottom: 0;
}

/* 输入框样式 */
.ant-input {
  border-radius: 6px;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 选择器样式 */
.ant-select-dropdown {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 表格行样式 */
.ant-table-tbody > tr > td {
  padding: 12px 16px;
}

.ant-table-thead > tr > th {
  padding: 12px 16px;
}

/* 操作按钮间距 */
.ant-space-item {
  display: flex;
  align-items: center;
}

/* 确保表格在小屏幕上的可读性 */
@media (max-width: 768px) {
  .ant-table-tbody > tr > td,
  .ant-table-thead > tr > th {
    padding: 8px 12px;
    font-size: 13px;
  }

  .ant-btn {
    font-size: 12px;
    padding: 4px 8px;
  }
}

/* 模态框在小屏幕上的适配 */
@media (max-width: 576px) {
  .ant-modal {
    margin: 0;
    padding: 0;
    max-width: 100vw;
  }

  .ant-modal-content {
    border-radius: 0;
  }
}
