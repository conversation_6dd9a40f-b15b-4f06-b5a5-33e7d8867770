.employment-archive-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.archive-layout {
  display: flex;
  gap: 16px;
  height: calc(100vh - 48px);
}

/* 左侧人员列表面板 */
.person-list-panel {
  width: 300px;
  flex-shrink: 0;
}

.person-list-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.person-list-card .ant-card-body {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.person-list-header {
  margin-bottom: 16px;
}

.person-list {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: white;
}

.person-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.person-item:last-child {
  border-bottom: none;
}

.person-item:hover {
  background-color: #f5f5f5;
}

.person-item.selected {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.person-name {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
  flex: 1;
}

.person-actions {
  display: flex;
  align-items: center;
  opacity: 0.8;
}

.person-actions .anticon {
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.person-actions .anticon:hover {
  transform: scale(1.1);
}

/* 右侧任职记录面板 */
.employment-records-panel {
  flex: 1;
}

.employment-records-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.employment-records-card .ant-card-body {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.employment-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.employment-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.empty-selection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8c8c8c;
  font-size: 16px;
}

/* 可排序表头样式 */
.sortable-header {
  cursor: pointer;
  user-select: none;
  transition: color 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.sortable-header:hover {
  color: #1890ff;
}

/* 表格样式 */
.ant-table {
  flex: 1;
}

.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #f0f0f0;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.ant-table-tbody > tr > td {
  border-bottom: 1px solid #f0f0f0;
}

/* 分页样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: center;
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
}

/* 表单样式 */
.ant-form-item {
  margin-bottom: 16px;
}

.ant-form-item:last-child {
  margin-bottom: 0;
}

/* 输入框样式 */
.ant-input {
  border-radius: 6px;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 选择器样式 */
.ant-select-dropdown {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 按钮样式 */
.ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .archive-layout {
    flex-direction: column;
    height: auto;
  }
  
  .person-list-panel {
    width: 100%;
    height: 300px;
  }
  
  .employment-records-panel {
    height: 500px;
  }
}

@media (max-width: 768px) {
  .employment-archive-container {
    padding: 16px;
  }
  
  .person-list-panel {
    height: 250px;
  }
  
  .employment-records-panel {
    height: 400px;
  }
  
  .person-item {
    padding: 8px 12px;
  }
  
  .person-name {
    font-size: 13px;
  }
  
  .person-actions .anticon {
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .employment-archive-container {
    padding: 12px;
  }
  
  .archive-layout {
    gap: 12px;
  }
  
  .person-list-panel {
    height: 200px;
  }
  
  .employment-records-panel {
    height: 350px;
  }
}
