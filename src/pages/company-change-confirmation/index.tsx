import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Modal, message, Tabs, Input } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { useAuth } from '../../contexts/AuthContext';
import './index.css';

// 统计数据接口
interface StatisticsData {
  basicInfo: number;
  executiveInfo: number;
  shareholderInfo: number;
  investmentInfo: number;
}

// 变更记录接口
interface ChangeRecord {
  id: number;
  changeType: 'basic' | 'executive' | 'shareholder' | 'investment';
  changeTypeText: string;
  content: string;
  changeDate: string;
  operator: string;
  operateDate: string;
  status: 'pending' | 'confirmed' | 'rejected';
  statusText: string;
  details?: any;
}

// 添加样式
const styles = `
  .pending-row {
    background-color: #fff2f0 !important;
  }
  .pending-row:hover {
    background-color: #ffe7e0 !important;
  }
  .ant-table-thead > tr > th {
    font-size: 13px !important;
    font-weight: bold !important;
    padding: 8px 6px !important;
  }
  .ant-table-tbody > tr > td {
    padding: 6px 6px !important;
    line-height: 1.3 !important;
  }
  .ant-table-cell {
    word-break: break-word !important;
  }
`;

const CompanyChangeConfirmation: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(false);

  // 权限判断函数
  const getUserRole = () => {
    if (!user) return 'viewer';
    switch (user.roleId) {
      case 1: return 'admin'; // 系统管理员
      case 2: return 'manager'; // 管理员
      case 3: return 'operator'; // 操作员
      case 4: return 'viewer'; // 查看员
      default: return 'viewer';
    }
  };

  const userRole = getUserRole();

  // 判断是否可以确认变更（系统管理员和管理员）
  const canConfirmChange = () => {
    return userRole === 'admin' || userRole === 'manager';
  };

  // 判断是否可以编辑异常记录（操作员）
  const canEditException = () => {
    return userRole === 'operator';
  };

  // 判断是否可以撤销变更（操作员对待确认记录）
  const canCancelChange = () => {
    return userRole === 'operator';
  };

  // 撤销变更（删除记录）
  const handleCancelChange = async () => {
    if (!selectedRecord) return;

    try {
      setLoading(true);
      const response = await fetch(`http://localhost:8081/api/company-change-logs/${selectedRecord.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        message.success('变更记录已撤销');
        setDetailModalVisible(false);
        fetchData();
      } else {
        const errorData = await response.json();
        message.error(`撤销失败: ${errorData.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('撤销变更失败:', error);
      message.error('撤销失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 统一的数据获取函数
  const fetchData = async () => {
    await Promise.all([
      fetchStatistics(),
      fetchChangeRecords()
    ]);
  };

  const [statistics, setStatistics] = useState<StatisticsData>({
    basicInfo: 0,
    executiveInfo: 0,
    shareholderInfo: 0,
    investmentInfo: 0
  });
  const [changeRecords, setChangeRecords] = useState<ChangeRecord[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<ChangeRecord[]>([]);
  const [activeTab, setActiveTab] = useState('all');
  const [searchText, setSearchText] = useState('');
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<ChangeRecord | null>(null);
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingRecord, setEditingRecord] = useState<ChangeRecord | null>(null);
  const [editForm, setEditForm] = useState({
    companyName: '',
    changeType: '',
    content: '',
    oldValue: '',
    newValue: '',
    changeDate: '',
    operator: ''
  });

  // 排序函数
  const sortRecords = (records: ChangeRecord[]) => {
    return records.sort((a, b) => {
      // 1. 按状态排序：有异常(rejected) → 待确认(pending) → 已确认(confirmed)
      const statusOrder = { 'rejected': 0, 'pending': 1, 'confirmed': 2 };
      const statusDiff = statusOrder[a.status] - statusOrder[b.status];
      if (statusDiff !== 0) return statusDiff;

      // 2. 按变更类型排序：基础信息变更 → 高管信息变更 → 股东信息变更
      const typeOrder = { 'basic': 0, 'executive': 1, 'shareholder': 2, 'investment': 3 };
      const typeDiff = typeOrder[a.changeType] - typeOrder[b.changeType];
      if (typeDiff !== 0) return typeDiff;

      // 3. 按变更时间排序（最新的在前）
      return new Date(b.changeDate).getTime() - new Date(a.changeDate).getTime();
    });
  };

  // 获取统计数据
  const fetchStatistics = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8081/api/company-change-logs/statistics');
      const result = await response.json();
      if (result.success) {
        setStatistics(result.data);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取变更记录
  const fetchChangeRecords = async () => {
    try {
      setLoading(true);
      const response = await fetch('http://localhost:8081/api/company-change-logs');
      const result = await response.json();
      if (result.success) {
        const sortedData = sortRecords([...result.data]);
        setChangeRecords(sortedData);
        setFilteredRecords(sortedData);
      }
    } catch (error) {
      console.error('获取变更记录失败:', error);
      message.error('获取变更记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 过滤数据
  const filterRecords = (status: string, search: string = searchText) => {
    let filtered = changeRecords;
    
    // 按状态过滤
    if (status === 'pending') {
      filtered = filtered.filter(record => record.status === 'pending');
    } else if (status === 'confirmed') {
      filtered = filtered.filter(record => record.status === 'confirmed');
    } else if (status === 'rejected') {
      filtered = filtered.filter(record => record.status === 'rejected');
    }
    
    // 按搜索文本过滤
    if (search) {
      filtered = filtered.filter(record => 
        record.content.toLowerCase().includes(search.toLowerCase()) ||
        record.operator.toLowerCase().includes(search.toLowerCase())
      );
    }
    
    setFilteredRecords(sortRecords(filtered));
  };

  // 处理标签页切换
  const handleTabChange = (key: string) => {
    setActiveTab(key);
    filterRecords(key);
  };

  // 处理搜索
  const handleSearch = (value: string) => {
    setSearchText(value);
    filterRecords(activeTab, value);
  };

  // 确认变更
  const handleConfirm = async (record: ChangeRecord) => {
    try {
      const response = await fetch(`http://localhost:8081/api/company-change-logs/confirm/${record.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      const result = await response.json();
      if (result.success) {
        message.success('确认成功');
        setDetailModalVisible(false);
        fetchData();
      } else {
        message.error(result.message || '确认失败');
      }
    } catch (error) {
      console.error('确认失败:', error);
      message.error('确认失败');
    }
  };

  // 退回变更
  const handleReject = async (record: ChangeRecord) => {
    try {
      const response = await fetch(`http://localhost:8081/api/company-change-logs/reject/${record.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      const result = await response.json();
      if (result.success) {
        message.success('退回成功');
        setDetailModalVisible(false);
        fetchData();
      } else {
        message.error(result.message || '退回失败');
      }
    } catch (error) {
      console.error('退回失败:', error);
      message.error('退回失败');
    }
  };

  // 格式化变更数据显示
  const formatChangeData = (data: string, changeType: string) => {
    if (!data || data === '无') return '无';

    try {
      // 尝试解析JSON数据
      const parsedData = JSON.parse(data);

      if (Array.isArray(parsedData)) {
        // 处理数组格式的数据（如高管信息）
        return parsedData.map((item, index) => (
          <div key={index} style={{ marginBottom: '4px' }}>
            {item.position ? `${item.position}: ${item.personName}` :
             item.shareholderName ? `${item.shareholderName}: ${item.shareholdingPercentage}%` :
             item.investmentSubject ? `${item.investmentSubject}: ${item.investmentAmount}万元 (${item.shareholdingPercentage}%)` :
             JSON.stringify(item)}
          </div>
        ));
      } else if (typeof parsedData === 'object') {
        // 处理对象格式的数据
        return Object.entries(parsedData).map(([key, value], index) => (
          <div key={index} style={{ marginBottom: '4px' }}>
            {key}: {String(value)}
          </div>
        ));
      } else {
        return String(parsedData);
      }
    } catch (error) {
      // 如果不是JSON格式，直接返回原始数据
      return data;
    }
  };

  // 显示详情
  const showDetails = (record: ChangeRecord) => {
    setSelectedRecord(record);
    setDetailModalVisible(true);
  };

  // 格式化JSON数据显示（用于编辑表单）
  const formatJsonValue = (value: string) => {
    if (!value) return '';

    try {
      const parsed = JSON.parse(value);
      if (Array.isArray(parsed)) {
        return parsed.map((item, index) => {
          if (typeof item === 'object') {
            // 针对不同类型的对象进行特殊格式化
            if (item.investmentSubject) {
              return `投资主体: ${item.investmentSubject}, 投资金额: ${item.investmentAmount}万元, 持股比例: ${item.shareholdingPercentage}%`;
            } else if (item.shareholderName) {
              return `股东姓名: ${item.shareholderName}, 持股比例: ${item.shareholdingPercentage}%`;
            } else if (item.position) {
              return `职位: ${item.position}, 姓名: ${item.personName}`;
            } else {
              return Object.entries(item)
                .map(([key, val]) => `${key}: ${val}`)
                .join(', ');
            }
          }
          return item;
        }).join('\n');
      } else if (typeof parsed === 'object') {
        return Object.entries(parsed)
          .map(([key, val]) => `${key}: ${val}`)
          .join('\n');
      }
      return value;
    } catch {
      return value;
    }
  };

  // 显示编辑弹窗
  const showEditModal = (record: ChangeRecord) => {
    setEditingRecord(record);
    setEditForm({
      companyName: record.companyName,
      changeType: record.changeTypeText,
      content: record.content,
      oldValue: formatJsonValue(record.oldValue || ''),
      newValue: formatJsonValue(record.newValue || ''),
      changeDate: record.changeDate,
      operator: record.operator
    });
    setEditModalVisible(true);
  };

  // 处理编辑表单变化
  const handleEditFormChange = (field: string, value: string) => {
    setEditForm(prev => ({
      ...prev,
      [field]: value
    }));
  };



  // 提交编辑
  const handleSubmitEdit = async () => {
    if (!editingRecord) return;

    // 检查是否有修改
    const hasChanges =
      editForm.content !== editingRecord.content ||
      editForm.oldValue !== (editingRecord.oldValue || '') ||
      editForm.newValue !== (editingRecord.newValue || '') ||
      editForm.changeDate !== editingRecord.changeDate ||
      editForm.operator !== editingRecord.operator;

    if (!hasChanges) {
      message.error('请先修改内容再提交');
      return;
    }

    try {
      const response = await fetch(`http://localhost:8081/api/company-change-logs/${editingRecord.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          change_content: editForm.content,
          old_value: editForm.oldValue,
          new_value: editForm.newValue,
          change_date: editForm.changeDate,
          operator: editForm.operator,
          status: 'pending'
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        message.success('变更记录已更新');
        setEditModalVisible(false);
        setEditingRecord(null);
        // 刷新数据
        await fetchData();
      } else {
        console.error('更新失败:', result);
        message.error(result.message || '更新失败');
      }
    } catch (error) {
      console.error('更新变更失败:', error);
      message.error('网络错误，更新失败');
    }
  };



  useEffect(() => {
    fetchData();
  }, []);

  // 表格列定义
  const columns: ColumnsType<ChangeRecord> = [
    {
      title: '任务状态',
      dataIndex: 'statusText',
      key: 'status',
      width: 80,
      align: 'center',
      render: (text: string, record: ChangeRecord) => {
        let color = '#52c41a'; // 默认绿色（已确认）
        if (record.status === 'pending') {
          color = '#ff4d4f'; // 红色（待确认）
        } else if (record.status === 'rejected') {
          color = '#fa8c16'; // 橙色（有异常）
        }

        return (
          <span style={{
            color: color,
            fontWeight: 'bold',
            fontSize: '12px'
          }}>
            {text}
          </span>
        );
      },
    },
    {
      title: '公司名称',
      dataIndex: 'companyName',
      key: 'companyName',
      width: 140,
      ellipsis: true,
      align: 'center',
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '变更类型',
      dataIndex: 'changeTypeText',
      key: 'changeType',
      width: 80,
      align: 'center',
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '变更内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      align: 'center',
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '档案文件',
      key: 'files',
      width: 70,
      align: 'center',
      render: () => (
        <span style={{ fontSize: '12px' }}>3</span>
      ),
    },
    {
      title: '变更时间',
      dataIndex: 'changeDate',
      key: 'changeDate',
      width: 90,
      align: 'center',
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
      width: 70,
      align: 'center',
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '操作时间',
      dataIndex: 'operateDate',
      key: 'operateDate',
      width: 130,
      align: 'center',
      render: (text: string) => (
        <span style={{ fontSize: '12px' }}>
          {text}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 60,
      align: 'center',
      render: (_, record: ChangeRecord) => {
        // 系统管理员和管理员：可以确认待确认的记录
        if (record.status === 'pending' && canConfirmChange()) {
          return (
            <Button
              type="primary"
              size="small"
              style={{ fontSize: '12px', padding: '2px 8px' }}
              onClick={() => showDetails(record)}
            >
              确认
            </Button>
          );
        }

        // 操作员：可以编辑有异常的记录
        if (record.status === 'rejected' && canEditException()) {
          return (
            <Button
              type="default"
              size="small"
              style={{ fontSize: '12px', padding: '2px 8px' }}
              onClick={() => showEditModal(record)}
            >
              编辑
            </Button>
          );
        }

        // 所有角色：可以查看已确认的记录
        if (record.status === 'confirmed') {
          return (
            <Button
              type="link"
              size="small"
              style={{ fontSize: '12px', padding: '2px 8px' }}
              onClick={() => showDetails(record)}
            >
              查看
            </Button>
          );
        }

        return null;
      },
    },
  ];

  return (
    <div style={{ padding: '24px' }}>
      <style>{styles}</style>
      {/* 统计卡片 */}
      <div style={{ display: 'flex', gap: '12px', marginBottom: '24px' }}>
        <div style={{
          width: '23%',
          textAlign: 'center',
          backgroundColor: '#8c8c8c',
          padding: '20px',
          borderRadius: '4px'
        }}>
          <div style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>基础信息</div>
          <div style={{ color: 'white', fontSize: '32px', fontWeight: 'bold', marginTop: '8px' }}>
            {statistics.basicInfo}
          </div>
        </div>
        <div style={{
          width: '23%',
          textAlign: 'center',
          backgroundColor: '#bfbfbf',
          padding: '20px',
          borderRadius: '4px'
        }}>
          <div style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>高管信息</div>
          <div style={{ color: 'white', fontSize: '32px', fontWeight: 'bold', marginTop: '8px' }}>
            {statistics.executiveInfo}
          </div>
        </div>
        <div style={{
          width: '23%',
          textAlign: 'center',
          backgroundColor: '#bfbfbf',
          padding: '20px',
          borderRadius: '4px'
        }}>
          <div style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>股东信息</div>
          <div style={{ color: 'white', fontSize: '32px', fontWeight: 'bold', marginTop: '8px' }}>
            {statistics.shareholderInfo}
          </div>
        </div>
        <div style={{
          width: '23%',
          textAlign: 'center',
          backgroundColor: '#bfbfbf',
          padding: '20px',
          borderRadius: '4px'
        }}>
          <div style={{ color: 'white', fontSize: '16px', fontWeight: 'bold' }}>对外投资</div>
          <div style={{ color: 'white', fontSize: '32px', fontWeight: 'bold', marginTop: '8px' }}>
            {statistics.investmentInfo}
          </div>
        </div>
      </div>

      {/* 标签页和搜索 */}
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '16px' }}>
          <div style={{ display: 'flex', gap: '24px' }}>
            <span
              style={{
                cursor: 'pointer',
                fontWeight: activeTab === 'all' ? 'bold' : 'normal',
                color: activeTab === 'all' ? '#1890ff' : '#000',
                borderBottom: activeTab === 'all' ? '2px solid #1890ff' : 'none',
                paddingBottom: '4px'
              }}
              onClick={() => handleTabChange('all')}
            >
              全部
            </span>
            <span
              style={{
                cursor: 'pointer',
                fontWeight: activeTab === 'pending' ? 'bold' : 'normal',
                color: activeTab === 'pending' ? '#1890ff' : '#000',
                borderBottom: activeTab === 'pending' ? '2px solid #1890ff' : 'none',
                paddingBottom: '4px'
              }}
              onClick={() => handleTabChange('pending')}
            >
              待确认
            </span>
            <span
              style={{
                cursor: 'pointer',
                fontWeight: activeTab === 'rejected' ? 'bold' : 'normal',
                color: activeTab === 'rejected' ? '#1890ff' : '#000',
                borderBottom: activeTab === 'rejected' ? '2px solid #1890ff' : 'none',
                paddingBottom: '4px'
              }}
              onClick={() => handleTabChange('rejected')}
            >
              有异常
            </span>
            <span
              style={{
                cursor: 'pointer',
                fontWeight: activeTab === 'confirmed' ? 'bold' : 'normal',
                color: activeTab === 'confirmed' ? '#1890ff' : '#000',
                borderBottom: activeTab === 'confirmed' ? '2px solid #1890ff' : 'none',
                paddingBottom: '4px'
              }}
              onClick={() => handleTabChange('confirmed')}
            >
              已确认
            </span>
          </div>
          <Input.Search
            placeholder="搜索股东主体信息"
            allowClear
            style={{ width: 300 }}
            onSearch={handleSearch}
            onChange={(e) => handleSearch(e.target.value)}
            prefix={<SearchOutlined />}
          />
        </div>

        {/* 数据表格 */}
        <Table
          columns={columns}
          dataSource={filteredRecords}
          rowKey="id"
          loading={loading}
          rowClassName={(record) => {
            if (record.status === 'pending') return 'pending-row';
            if (record.status === 'rejected') return 'rejected-row';
            return '';
          }}
          onRow={(record) => ({
            onClick: () => {
              // 系统管理员和管理员：点击待确认记录显示确认弹窗
              if (record.status === 'pending' && canConfirmChange()) {
                showDetails(record);
                return;
              }

              // 系统管理员和管理员：点击有异常记录显示详情弹窗
              if (record.status === 'rejected' && canConfirmChange()) {
                showDetails(record);
                return;
              }

              // 操作员：点击有异常记录显示编辑弹窗
              if (record.status === 'rejected' && canEditException()) {
                showEditModal(record);
                return;
              }

              // 操作员：点击待确认记录显示详情（包含撤销按钮）
              if (record.status === 'pending' && canCancelChange()) {
                showDetails(record);
                return;
              }

              // 所有角色：点击已确认记录显示详情
              if (record.status === 'confirmed') {
                showDetails(record);
                return;
              }

              // 查看员：点击任何记录都显示详情
              if (userRole === 'viewer') {
                showDetails(record);
                return;
              }
            },
            style: { cursor: 'pointer' }
          })}
          pagination={{
            total: filteredRecords.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          scroll={{ x: 1000 }}
          size="small"
        />
      </Card>

      {/* 详情确认弹窗 */}
      <Modal
        title={selectedRecord?.status === 'confirmed' ? '变更详情' : '变更详情确认'}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={(() => {
          if (!selectedRecord) return [];

          // 系统管理员和管理员：对待确认记录显示确认/退回按钮
          if (selectedRecord.status === 'pending' && canConfirmChange()) {
            return [
              <Button key="cancel" onClick={() => setDetailModalVisible(false)}>
                取消
              </Button>,
              <Button key="reject" onClick={() => selectedRecord && handleReject(selectedRecord)}>
                退回
              </Button>,
              <Button
                key="confirm"
                type="primary"
                onClick={() => selectedRecord && handleConfirm(selectedRecord)}
              >
                确定
              </Button>,
            ];
          }

          // 操作员：对待确认记录显示返回和撤销变更按钮
          if (selectedRecord.status === 'pending' && canCancelChange()) {
            return [
              <Button key="back" onClick={() => setDetailModalVisible(false)}>
                返回
              </Button>,
              <Button key="cancel-change" danger onClick={handleCancelChange}>
                撤销变更
              </Button>,
            ];
          }

          // 其他情况：只显示返回按钮
          return [
            <Button key="back" onClick={() => setDetailModalVisible(false)}>
              返回
            </Button>
          ];
        })()}
        width={800}
      >
        {selectedRecord && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <strong>变更类型：</strong>
              <div style={{ marginTop: '4px' }}>{selectedRecord.changeTypeText}</div>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>变更内容：</strong>
              <div style={{ marginTop: '8px', padding: '12px', backgroundColor: '#f5f5f5', borderRadius: '4px' }}>
                {selectedRecord.content.split('；').map((item, index) => (
                  <div key={index} style={{ marginBottom: '4px' }}>
                    {item.trim()}
                  </div>
                ))}
              </div>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>变更前：</strong>
              <div style={{ marginTop: '8px', padding: '12px', backgroundColor: '#fff2e8', borderRadius: '4px', border: '1px solid #ffb366' }}>
                {formatChangeData(selectedRecord.oldValue || '无', selectedRecord.changeType)}
              </div>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>变更后：</strong>
              <div style={{ marginTop: '8px', padding: '12px', backgroundColor: '#f6ffed', borderRadius: '4px', border: '1px solid #b7eb8f' }}>
                {formatChangeData(selectedRecord.newValue || '无', selectedRecord.changeType)}
              </div>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>变更时间：</strong>
              <div style={{ marginTop: '4px' }}>{selectedRecord.changeDate}</div>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>操作人：</strong>
              <div style={{ marginTop: '4px' }}>{selectedRecord.operator}</div>
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>操作时间：</strong>
              <div style={{ marginTop: '4px' }}>{selectedRecord.operateDate}</div>
            </div>

            {selectedRecord.status === 'confirmed' && selectedRecord.confirmedAt && (
              <div style={{ marginBottom: '16px' }}>
                <strong>确认时间：</strong>
                <div style={{ marginTop: '4px', color: '#52c41a' }}>{selectedRecord.confirmedAt}</div>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 编辑弹窗 */}
      <Modal
        title="编辑变更记录"
        open={editModalVisible}
        onCancel={() => setEditModalVisible(false)}
        width={800}
        footer={(() => {
          // 操作员编辑有异常记录：显示取消和提交按钮
          if (editingRecord?.status === 'rejected' && canEditException()) {
            return [
              <Button key="cancel" onClick={() => setEditModalVisible(false)}>
                取消
              </Button>,
              <Button key="submit" type="primary" onClick={handleSubmitEdit}>
                提交
              </Button>,
            ];
          }

          // 其他情况：只显示返回按钮
          return [
            <Button key="back" onClick={() => setEditModalVisible(false)}>
              返回
            </Button>
          ];
        })()}
      >
        {editingRecord && (
          <div>
            <div style={{ marginBottom: '16px' }}>
              <strong>公司名称：</strong>
              <Input
                value={editForm.companyName}
                disabled
                style={{ marginTop: '4px' }}
              />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>变更类型：</strong>
              <Input
                value={editForm.changeType}
                disabled
                style={{ marginTop: '4px' }}
              />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>变更内容：</strong>
              <Input.TextArea
                value={editForm.content}
                onChange={(e) => handleEditFormChange('content', e.target.value)}
                rows={4}
                style={{ marginTop: '4px' }}
                placeholder="请输入变更内容，多个变更用分号分隔"
                disabled={!canEditException() || editingRecord?.status !== 'rejected'}
              />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>原值：</strong>
              <Input.TextArea
                value={editForm.oldValue}
                onChange={(e) => handleEditFormChange('oldValue', e.target.value)}
                style={{ marginTop: '4px' }}
                placeholder="请输入原值"
                disabled={!canEditException() || editingRecord?.status !== 'rejected'}
                rows={3}
                autoSize={{ minRows: 3, maxRows: 6 }}
              />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>新值：</strong>
              <Input.TextArea
                value={editForm.newValue}
                onChange={(e) => handleEditFormChange('newValue', e.target.value)}
                style={{ marginTop: '4px' }}
                placeholder="请输入新值"
                disabled={!canEditException() || editingRecord?.status !== 'rejected'}
                rows={3}
                autoSize={{ minRows: 3, maxRows: 6 }}
              />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>变更时间：</strong>
              <Input
                value={editForm.changeDate}
                onChange={(e) => handleEditFormChange('changeDate', e.target.value)}
                style={{ marginTop: '4px' }}
                placeholder="YYYY-MM-DD"
                disabled={!canEditException() || editingRecord?.status !== 'rejected'}
              />
            </div>

            <div style={{ marginBottom: '16px' }}>
              <strong>操作人：</strong>
              <Input
                value={editForm.operator}
                onChange={(e) => handleEditFormChange('operator', e.target.value)}
                style={{ marginTop: '4px' }}
                placeholder="请输入操作人"
                disabled={!canEditException() || editingRecord?.status !== 'rejected'}
              />
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default CompanyChangeConfirmation;
