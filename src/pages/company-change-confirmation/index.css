/* 公司信息变更确认页面样式 */

.pending-row {
  background-color: #fff7e6;
}

.pending-row:hover {
  background-color: #ffe7ba !important;
}

.rejected-row {
  color: #fa8c16;
}

.rejected-row:hover {
  background-color: #fff7e6 !important;
}

.rejected-row td {
  color: #fa8c16 !important;
}

/* 表格行悬停效果 */
.ant-table-tbody > tr:hover > td {
  cursor: pointer;
}

/* 状态标签样式 */
.status-pending {
  color: #faad14;
  font-weight: 500;
}

.status-confirmed {
  color: #52c41a;
  font-weight: 500;
}

.status-rejected {
  color: #fa8c16;
  font-weight: 500;
}

/* 统计卡片样式 */
.statistics-card {
  margin-bottom: 16px;
}

.statistics-card .ant-card-body {
  padding: 16px;
}

/* 搜索框样式 */
.search-container {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 弹窗内容样式 */
.detail-modal .ant-modal-body {
  max-height: 60vh;
  overflow-y: auto;
}

.detail-item {
  margin-bottom: 16px;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-label {
  font-weight: 600;
  color: #262626;
  margin-bottom: 4px;
}

.detail-value {
  color: #595959;
  line-height: 1.5;
}

.change-content-box {
  background-color: #f5f5f5;
  border-radius: 4px;
  padding: 12px;
  margin-top: 8px;
}

.change-content-item {
  margin-bottom: 4px;
  line-height: 1.4;
}

.change-content-item:last-child {
  margin-bottom: 0;
}

/* 确认时间特殊样式 */
.confirmed-time {
  color: #52c41a;
  font-weight: 500;
}
