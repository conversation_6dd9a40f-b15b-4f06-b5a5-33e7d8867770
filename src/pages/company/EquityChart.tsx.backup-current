import React, { useState, useEffect } from 'react';
import { Card, Select, Button, message, Spin, Alert } from 'antd';
import { useSearchParams } from 'react-router-dom';
import axios from 'axios';

const { Option } = Select;

interface Company {
  id: number;
  name: string;
}

interface ShareholderNode {
  id: number;
  shareholderId: number;
  shareholderName: string;
  shareholderType: 'person' | 'company';
  companyId: number;
  companyName: string;
  percentage: number;
  isProxy: boolean;
  actualShareholderId?: number;
  actualShareholderName?: string;
  level?: number; // 层级信息
}

interface InvestmentNode {
  id: number;
  investorId: number;
  investorName: string;
  investeeId?: number;
  investeeName: string;
  percentage: number;
  level?: number; // 层级信息
}

interface EquityChartData {
  centerCompany: Company;
  upstreamShareholders: ShareholderNode[];
  downstreamInvestments: InvestmentNode[];
  relatedCompanies: Company[];
}

interface NodePosition {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  name: string;
  type: 'company' | 'person';
  isCenter?: boolean;
  collapsed?: boolean;
  children?: string[];
  level?: number;
  isProxy?: boolean;
  targetCompanyId?: number; // 股东的目标公司ID
  sourceInvestorId?: number; // 投资的来源投资方ID
  actualShareholderId?: number; // 实际股东ID
  actualShareholderName?: string; // 实际股东名称
}

const EquityChart: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [companies, setCompanies] = useState<Company[]>([]);
  const [selectedCompanyId, setSelectedCompanyId] = useState<number | null>(null);
  const [selectedCompanyIds, setSelectedCompanyIds] = useState<number[]>([]);
  const [version, setVersion] = useState<'legal' | 'investor'>('investor');
  const [equityData, setEquityData] = useState<EquityChartData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hiddenCompanies, setHiddenCompanies] = useState<Set<number>>(new Set());
  const [collapsedNodes, setCollapsedNodes] = useState<Set<string>>(new Set());

  // 从URL参数获取公司ID
  useEffect(() => {
    const companyId = searchParams.get('companyId');
    if (companyId) {
      setSelectedCompanyId(parseInt(companyId));
    }
  }, [searchParams]);

  // 加载公司列表
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        const response = await axios.get('http://localhost:8080/api/companies');
        if (response.data.success) {
          const companiesList = response.data.data.map((company: any) => ({
            id: company.id,
            name: company.chineseName || company.company_name_cn
          }));
          setCompanies(companiesList);
        }
      } catch (error) {
        console.error('获取公司列表失败:', error);
        message.error('获取公司列表失败');
      }
    };

    fetchCompanies();
  }, []);

  // 自动选择第一个公司（仅在投资者版本且没有选择公司时）
  useEffect(() => {
    if (!selectedCompanyId && version === 'investor' && companies.length > 0) {
      setSelectedCompanyId(companies[0].id);
    }
  }, [companies, selectedCompanyId, version]);

  // 加载股权图数据
  useEffect(() => {
    if (version === 'investor' && selectedCompanyId) {
      fetchEquityData(selectedCompanyId);
    } else if (version === 'legal' && selectedCompanyIds.length > 0) {
      fetchMultipleEquityData(selectedCompanyIds);
    }
  }, [selectedCompanyId, selectedCompanyIds, version]);

  const fetchEquityData = async (companyId: number) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get(`http://localhost:8080/api/equity-chart/${companyId}`);
      if (response.data.success) {
        setEquityData(response.data.data);
      } else {
        throw new Error(response.data.message || '获取股权图数据失败');
      }
    } catch (error: any) {
      console.error('获取股权图数据失败:', error);
      setError(error.message || '获取股权图数据失败');
      message.error(error.message || '获取股权图数据失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchMultipleEquityData = async (companyIds: number[]) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.post(`http://localhost:8080/api/equity-chart/multiple`, {
        companyIds
      });
      if (response.data.success) {
        setEquityData(response.data.data);
      } else {
        throw new Error(response.data.message || '获取股权图数据失败');
      }
    } catch (error: any) {
      console.error('获取股权图数据失败:', error);
      setError(error.message || '获取股权图数据失败');
      message.error(error.message || '获取股权图数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 计算节点位置 - 新的层级系统布局算法
  const calculateNodePositions = (): NodePosition[] => {
    if (!equityData) return [];

    const positions: NodePosition[] = [];

    // 优化的节点和布局参数 - 参考参考图的整齐布局
    const nodeWidth = 120;         // 统一节点宽度（减小以提高整齐度）
    const nodeHeight = 40;         // 统一节点高度（减小以提高整齐度）
    const horizontalSpacing = 200; // 层级之间的水平间距（适中间距）
    const verticalSpacing = 60;    // 同层节点之间的垂直间距（减小避免过于分散）

    // 组织数据按新的层级系统 - 重新计算层级
    const upstreamLevels = organizeUpstreamByLevels(equityData.upstreamShareholders);
    const downstreamLevels = organizeDownstreamByLevels(equityData.downstreamInvestments);

    // 计算画布尺寸 - 确保有足够空间显示所有层级
    const totalLevels = upstreamLevels.length + 1 + downstreamLevels.length; // 上游 + 中心 + 下游
    const canvasWidth = Math.max(1400, totalLevels * horizontalSpacing + 400);

    const maxUpstreamNodes = upstreamLevels.length > 0 ? Math.max(...upstreamLevels.map(level => level.length)) : 0;
    const maxDownstreamNodes = downstreamLevels.length > 0 ? Math.max(...downstreamLevels.map(level => level.length)) : 0;
    const maxNodesInLevel = Math.max(maxUpstreamNodes, maxDownstreamNodes, 1);
    const canvasHeight = Math.max(800, maxNodesInLevel * verticalSpacing + 300);

    const centerY = canvasHeight / 2;

    // 中心公司位置 (层级0) - 在所有上游层级之后
    const centerCompanyX = 200 + upstreamLevels.length * horizontalSpacing;

    if (version === 'investor') {
      // 投资者版：中心公司位置 - 根据名称长度调整宽度
      const centerNodeWidth = Math.max(140, equityData.centerCompany.name.length * 12);
      positions.push({
        id: `company-${equityData.centerCompany.id}`,
        x: centerCompanyX - centerNodeWidth / 2,
        y: centerY - nodeHeight / 2,
        width: centerNodeWidth,  // 根据名称长度调整
        height: nodeHeight,
        name: equityData.centerCompany.name,
        type: 'company',
        isCenter: true,
        level: 0
      });

      // 上游股东（层级1,2,3...从右到左排列）
      upstreamLevels.forEach((level, levelIndex) => {
        // 层级编号：1, 2, 3... (levelIndex + 1)
        const actualLevel = levelIndex + 1;
        // X坐标：从右到左，层级越高越靠左
        const x = centerCompanyX - actualLevel * horizontalSpacing;

        // 改进的垂直分布算法：按目标公司分组，每组内部垂直平均分布
        const shareholdersByTarget = new Map<number, typeof level>();
        level.forEach(shareholder => {
          const targetCompanyId = shareholder.companyId;
          if (!shareholdersByTarget.has(targetCompanyId)) {
            shareholdersByTarget.set(targetCompanyId, []);
          }
          shareholdersByTarget.get(targetCompanyId)!.push(shareholder);
        });

        // 计算所有组的总高度，确保垂直居中
        const totalGroups = shareholdersByTarget.size;
        const totalGroupHeight = (totalGroups - 1) * verticalSpacing * 1.2;
        let currentGroupStartY = centerY - totalGroupHeight / 2;

        shareholdersByTarget.forEach((shareholders, targetCompanyId) => {
          // 每组内部的垂直分布 - 确保真正的平均分布
          if (shareholders.length === 1) {
            // 单个股东，直接居中
            const shareholder = shareholders[0];
            const displayName = shareholder.shareholderName;
            let nodeLevel = actualLevel;

            // 代持关系处理
            if (shareholder.isProxy && shareholder.actualShareholderName) {
              nodeLevel = actualLevel;

              // 添加实际股东节点（在代持人左侧）
              positions.push({
                id: `actual-${shareholder.actualShareholderId}-${shareholder.level || 0}`,
                x: x - horizontalSpacing / 2 - nodeWidth / 2,
                y: currentGroupStartY - nodeHeight / 2,
                width: nodeWidth,
                height: nodeHeight,
                name: shareholder.actualShareholderName!,
                type: 'person',
                level: nodeLevel + 1,
                isProxy: false,
                targetCompanyId: targetCompanyId
              });
            }

            positions.push({
              id: `${shareholder.shareholderType}-${shareholder.shareholderId}-${shareholder.level || 0}`,
              x: x - nodeWidth / 2,
              y: currentGroupStartY - nodeHeight / 2,
              width: nodeWidth,
              height: nodeHeight,
              name: displayName,
              type: shareholder.shareholderType,
              level: nodeLevel,
              isProxy: shareholder.isProxy,
              targetCompanyId: targetCompanyId,
              actualShareholderId: shareholder.actualShareholderId,
              actualShareholderName: shareholder.actualShareholderName
            });
          } else {
            // 多个股东，垂直平均分布
            const groupHeight = (shareholders.length - 1) * verticalSpacing * 0.7;
            const groupStartY = currentGroupStartY - groupHeight / 2;

            shareholders.forEach((shareholder, index) => {
              const y = groupStartY + index * verticalSpacing * 0.7;
              const displayName = shareholder.shareholderName;
              let nodeLevel = actualLevel;

              // 代持关系处理
              if (shareholder.isProxy && shareholder.actualShareholderName) {
                nodeLevel = actualLevel;

                // 添加实际股东节点（在代持人左侧）
                positions.push({
                  id: `actual-${shareholder.actualShareholderId}-${shareholder.level || 0}`,
                  x: x - horizontalSpacing / 2 - nodeWidth / 2,
                  y: y - nodeHeight / 2,
                  width: nodeWidth,
                  height: nodeHeight,
                  name: shareholder.actualShareholderName!,
                  type: 'person',
                  level: nodeLevel + 1,
                  isProxy: false,
                  targetCompanyId: targetCompanyId
                });
              }

              positions.push({
                id: `${shareholder.shareholderType}-${shareholder.shareholderId}-${shareholder.level || 0}`,
                x: x - nodeWidth / 2,
                y: y - nodeHeight / 2,
                width: nodeWidth,
                height: nodeHeight,
                name: displayName,
                type: shareholder.shareholderType,
                level: nodeLevel,
                isProxy: shareholder.isProxy,
                targetCompanyId: targetCompanyId,
                actualShareholderId: shareholder.actualShareholderId,
                actualShareholderName: shareholder.actualShareholderName
              });
            });
          }

          // 移动到下一组的位置
          currentGroupStartY += verticalSpacing * 1.2;
        });
      });

      // 下游投资（层级-1,-2,-3...从左到右排列）
      downstreamLevels.forEach((level, levelIndex) => {
        // 层级编号：-1, -2, -3... (-(levelIndex + 1))
        const actualLevel = -(levelIndex + 1);
        // X坐标：从左到右，层级越低越靠右
        const x = centerCompanyX + (levelIndex + 1) * horizontalSpacing;

        // 改进的垂直分布算法：按投资方分组，每组内部垂直平均分布
        const investmentsBySource = new Map<number, typeof level>();
        level.forEach(investment => {
          const sourceInvestorId = investment.investorId;
          if (!investmentsBySource.has(sourceInvestorId)) {
            investmentsBySource.set(sourceInvestorId, []);
          }
          investmentsBySource.get(sourceInvestorId)!.push(investment);
        });

        // 为每个投资方的投资组分配垂直位置
        let currentGroupStartY = centerY - ((investmentsBySource.size - 1) * verticalSpacing * 1.5) / 2;

        investmentsBySource.forEach((investments, sourceInvestorId) => {
          // 每组内部的垂直分布
          const groupHeight = (investments.length - 1) * (verticalSpacing * 0.8);
          const groupStartY = currentGroupStartY - groupHeight / 2;

          investments.forEach((investment, index) => {
            const y = investments.length === 1 ? currentGroupStartY : groupStartY + index * (verticalSpacing * 0.8);

            positions.push({
              id: `investment-${investment.investeeId || investment.id}-${investment.level || 0}`,
              x: x - nodeWidth / 2,
              y: y - nodeHeight / 2,
              width: nodeWidth,  // 统一宽度
              height: nodeHeight, // 统一高度
              name: investment.investeeName,
              type: 'company',
              level: actualLevel, // 使用负数层级编号
              sourceInvestorId: sourceInvestorId // 添加投资方ID用于连线
            });
          });

          // 移动到下一组的位置
          currentGroupStartY += verticalSpacing * 1.5;
        });
      });
    } else {
      // 法务版：多个中心公司 - 确保使用统一尺寸
      const centerCompanies = selectedCompanyIds.map(id =>
        companies.find(c => c.id === id)
      ).filter(Boolean) as Company[];

      centerCompanies.forEach((company, index) => {
        const totalWidth = (centerCompanies.length - 1) * horizontalSpacing;
        const startX = centerCompanyX - totalWidth / 2;
        const x = Math.max(nodeWidth / 2, startX + index * horizontalSpacing);

        positions.push({
          id: `company-${company.id}`,
          x: x - nodeWidth / 2,
          y: centerY - nodeHeight / 2,
          width: nodeWidth,  // 统一宽度
          height: nodeHeight, // 统一高度
          name: company.name,
          type: 'company',
          isCenter: true,
          level: 0 // 中心公司层级为0
        });
      });
    }

    return positions;
  };



  // 组织上游股东按层级
  const organizeUpstreamByLevels = (shareholders: ShareholderNode[]): ShareholderNode[][] => {
    // 按层级分组
    const levelMap = new Map<number, ShareholderNode[]>();

    shareholders.forEach(shareholder => {
      const level = shareholder.level || 0;
      if (!levelMap.has(level)) {
        levelMap.set(level, []);
      }
      levelMap.get(level)!.push(shareholder);
    });

    // 转换为数组，按层级排序
    const levels: ShareholderNode[][] = [];
    const sortedLevels = Array.from(levelMap.keys()).sort((a, b) => a - b);

    sortedLevels.forEach(level => {
      const shareholdersAtLevel = levelMap.get(level) || [];
      levels.push(shareholdersAtLevel);
    });

    return levels;
  };

  // 组织下游投资按层级
  const organizeDownstreamByLevels = (investments: InvestmentNode[]): InvestmentNode[][] => {
    // 按层级分组
    const levelMap = new Map<number, InvestmentNode[]>();

    investments.forEach(investment => {
      const level = investment.level || 0;
      if (!levelMap.has(level)) {
        levelMap.set(level, []);
      }
      levelMap.get(level)!.push(investment);
    });

    // 转换为数组，按层级排序
    const levels: InvestmentNode[][] = [];
    const sortedLevels = Array.from(levelMap.keys()).sort((a, b) => a - b);

    sortedLevels.forEach(level => {
      const investmentsAtLevel = levelMap.get(level) || [];
      levels.push(investmentsAtLevel);
    });

    return levels;
  };

  // 渲染连接线
  const renderConnections = (positions: NodePosition[]) => {
    if (!equityData || version !== 'investor') return null;

    const connections: JSX.Element[] = [];
    const centerPos = positions.find(p => p.isCenter);
    if (!centerPos) {
      console.log('❌ 未找到中心公司位置');
      return null;
    }

    // 统一的持股份额显示框尺寸 - 缩小尺寸
    const percentageBoxWidth = 70;  // 缩小持股份额框宽度
    const percentageBoxHeight = 18; // 缩小持股份额框高度

    // 上游连接线（股东 -> 其持股的公司）
    // 每个股东连接到其持股的公司
    equityData.upstreamShareholders.forEach((shareholder) => {
      const shareholderPos = positions.find(p =>
        p.id === `${shareholder.shareholderType}-${shareholder.shareholderId}-${shareholder.level || 0}`
      );

      if (shareholderPos) {
        // 处理代持关系：先绘制实际股东到代持人的连接线
        if (shareholder.isProxy && shareholder.actualShareholderName) {
          const actualShareholderPos = positions.find(p =>
            p.id === `actual-${shareholder.actualShareholderId}-${shareholder.level || 0}`
          );

          if (actualShareholderPos) {
            // 实际股东到代持人的连接线（实线）
            const actualStartX = actualShareholderPos.x + actualShareholderPos.width;
            const actualStartY = actualShareholderPos.y + actualShareholderPos.height / 2;
            const proxyEndX = shareholderPos.x;
            const proxyEndY = shareholderPos.y + shareholderPos.height / 2;

            const actualPathData = `M ${actualStartX} ${actualStartY} L ${proxyEndX} ${proxyEndY}`;

            connections.push(
              <g key={`actual-${shareholder.actualShareholderId}-to-proxy-${shareholder.shareholderId}`}>
                <path
                  d={actualPathData}
                  stroke="#666666"
                  strokeWidth="2"
                  fill="none"
                  markerEnd="url(#arrowhead)"
                />
              </g>
            );
          }
        }

        // 找到股东持股的目标公司
        let targetPos = null;

        // 如果股东持股的是中心公司
        if (shareholder.companyId === equityData.centerCompany.id) {
          targetPos = centerPos;
        } else {
          // 如果股东持股的是其他公司，找到对应的公司节点
          // 在上游股东中查找对应的企业股东节点
          const targetCompany = equityData.upstreamShareholders.find(s =>
            s.shareholderType === 'company' && s.shareholderId === shareholder.companyId
          );
          if (targetCompany) {
            targetPos = positions.find(p =>
              p.id === `company-${targetCompany.shareholderId}-${targetCompany.level || 0}`
            );
          }
        }

        if (targetPos) {
          // 从股东节点的右边缘到目标公司的左边缘
          const startX = shareholderPos.x + shareholderPos.width;
          const startY = shareholderPos.y + shareholderPos.height / 2;
          const endX = targetPos.x;
          const endY = targetPos.y + targetPos.height / 2;

          // 使用贝塞尔曲线连接，更美观的折线效果
          const controlX1 = startX + (endX - startX) * 0.6;
          const controlY1 = startY;
          const controlX2 = startX + (endX - startX) * 0.4;
          const controlY2 = endY;

          const pathData = `M ${startX} ${startY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${endX} ${endY}`;

          // 精确计算当前连接线的持股份额框位置
          const midX = (startX + endX) / 2;

          // 根据当前连接线的高度动态调整Y位置
          const lineTopY = Math.min(startY, endY);
          const baseOffsetY = 25;

          // 为同一目标公司的股东错开显示位置
          const shareholdersToSameTarget = equityData.upstreamShareholders.filter(s =>
            s.companyId === shareholder.companyId
          );
          const shareholderIndex = shareholdersToSameTarget.findIndex(s =>
            s.shareholderId === shareholder.shareholderId
          );

          const verticalOffset = shareholderIndex * (percentageBoxHeight + 5);
          const boxY = lineTopY - baseOffsetY - verticalOffset;
          const boxX = midX - percentageBoxWidth / 2;

          // 确定线条样式：代持关系使用虚线
          const strokeDashArray = shareholder.isProxy ? "5,5" : "none";
          const strokeColor = shareholder.isProxy ? "#ff7875" : "#1890ff";

          connections.push(
            <g key={`upstream-${shareholder.shareholderId}-${shareholder.level || 0}-to-company-${shareholder.companyId}`}>
              <path
                d={pathData}
                stroke={strokeColor}
                strokeWidth="2"
                strokeDasharray={strokeDashArray}
                fill="none"
                markerEnd="url(#arrowhead)"
              />
              {/* 持股份额显示框 - 精确位于当前连接线上方 */}
              <rect
                x={boxX}
                y={boxY}
                width={percentageBoxWidth}
                height={percentageBoxHeight}
                fill="white"
                stroke={strokeColor}
                strokeWidth="1"
                rx="4"
              />
              <text
                x={midX}
                y={boxY + percentageBoxHeight / 2 + 3}
                textAnchor="middle"
                fontSize="9"
                fill={strokeColor}
                fontWeight="bold"
              >
                {shareholder.percentage}%{shareholder.isProxy ? ' (代持)' : ''}
              </text>
            </g>
          );
        }
      }
    });

    // 下游连接线（投资公司 -> 被投资公司）
    // 每个投资关系连接投资方到被投资方
    equityData.downstreamInvestments.forEach((investment) => {
      const investmentPos = positions.find(p =>
        p.id === `investment-${investment.investeeId || investment.id}-${investment.level || 0}`
      );

      if (investmentPos) {
        let sourcePos = null;

        // 找到投资方公司
        if (investment.investorId === equityData.centerCompany.id) {
          // 投资方是中心公司
          sourcePos = centerPos;
        } else {
          // 投资方是其他公司，在下游投资中查找
          const sourceInvestment = equityData.downstreamInvestments.find(i =>
            (i.investeeId || i.id) === investment.investorId
          );
          if (sourceInvestment) {
            sourcePos = positions.find(p =>
              p.id === `investment-${sourceInvestment.investeeId || sourceInvestment.id}-${sourceInvestment.level || 0}`
            );
          }
        }

        if (sourcePos) {
          // 从投资方节点的右边缘到被投资方节点的左边缘
          const startX = sourcePos.x + sourcePos.width;
          const startY = sourcePos.y + sourcePos.height / 2;
          const endX = investmentPos.x;
          const endY = investmentPos.y + investmentPos.height / 2;

          // 使用贝塞尔曲线连接，更美观的折线效果
          const controlX1 = startX + (endX - startX) * 0.6;
          const controlY1 = startY;
          const controlX2 = startX + (endX - startX) * 0.4;
          const controlY2 = endY;

          const pathData = `M ${startX} ${startY} C ${controlX1} ${controlY1}, ${controlX2} ${controlY2}, ${endX} ${endY}`;

          // 改进的持股份额框位置计算 - 确保在线条上方且不重叠
          const midX = (startX + endX) / 2;
          const midY = (startY + endY) / 2;

          // 计算更精确的位置，确保框位于线条上方
          const boxX = midX - percentageBoxWidth / 2;

          // 根据线条的高度动态调整Y位置，确保框始终在线条上方
          const lineTopY = Math.min(startY, endY);
          const baseOffsetY = 25; // 基础向上偏移距离

          // 为避免重叠，根据投资在同一投资方组中的索引调整位置
          const investmentsFromSameSource = equityData.downstreamInvestments.filter(i =>
            i.investorId === investment.investorId
          );
          const investmentIndex = investmentsFromSameSource.findIndex(i =>
            (i.investeeId || i.id) === (investment.investeeId || investment.id)
          );

          // 如果有多个投资来自同一投资方，错开显示位置避免重叠
          const verticalOffset = investmentIndex * (percentageBoxHeight + 5);
          const boxY = lineTopY - baseOffsetY - verticalOffset;

          connections.push(
            <g key={`downstream-${investment.investorId}-to-${investment.investeeId || investment.id}-${investment.level || 0}`}>
              <path
                d={pathData}
                stroke="#52c41a"
                strokeWidth="2"
                fill="none"
                markerEnd="url(#arrowhead-green)"
              />
              {/* 统一尺寸的持股份额显示框 - 确保在曲线上方 */}
              <rect
                x={boxX}
                y={boxY}
                width={percentageBoxWidth}
                height={percentageBoxHeight}
                fill="white"
                stroke="#52c41a"
                strokeWidth="1"
                rx="4"
              />
              <text
                x={midX}
                y={boxY + percentageBoxHeight / 2 + 3}
                textAnchor="middle"
                fontSize="9"
                fill="#52c41a"
                fontWeight="bold"
              >
                {investment.percentage}%
              </text>
            </g>
          );
        }
      }
    });

    return connections;
  };

  // 渲染节点
  const renderNodes = (positions: NodePosition[]) => {
    return positions.map((pos) => (
      <g key={pos.id}>
        {/* 节点阴影 */}
        <rect
          x={pos.x + 2}
          y={pos.y + 2}
          width={pos.width}
          height={pos.height}
          fill="rgba(0,0,0,0.1)"
          rx="6"
        />

        {/* 节点主体 */}
        <rect
          x={pos.x}
          y={pos.y}
          width={pos.width}
          height={pos.height}
          fill={pos.isCenter ? '#1890ff' : pos.type === 'person' ? '#f6ffed' : '#fff'}
          stroke={pos.isCenter ? '#1890ff' : pos.type === 'person' ? '#52c41a' : '#d9d9d9'}
          strokeWidth={pos.isCenter ? "3" : "2"}
          rx="6"
          style={{ cursor: 'pointer' }}
        />

        {/* 节点类型图标 */}
        <circle
          cx={pos.x + 15}
          cy={pos.y + 15}
          r="8"
          fill={pos.type === 'person' ? '#52c41a' : '#1890ff'}
        />
        <text
          x={pos.x + 15}
          y={pos.y + 19}
          textAnchor="middle"
          fontSize="10"
          fill="white"
          fontWeight="bold"
        >
          {pos.type === 'person' ? '人' : '企'}
        </text>

        {/* 节点名称 */}
        <text
          x={pos.x + pos.width / 2}
          y={pos.y + pos.height / 2 - 5}
          textAnchor="middle"
          dominantBaseline="middle"
          fontSize="12"
          fill={pos.isCenter ? '#fff' : '#333'}
          fontWeight={pos.isCenter ? 'bold' : 'normal'}
        >
          {pos.name.length > 10 ? `${pos.name.substring(0, 10)}...` : pos.name}
        </text>

        {/* 代持关系标识 */}
        {pos.id.includes('person') && equityData?.upstreamShareholders.some(s =>
          s.shareholderType === 'person' &&
          s.shareholderId.toString() === pos.id.split('-')[1] &&
          s.isProxy
        ) && (
          <g>
            <rect
              x={pos.x + pos.width - 25}
              y={pos.y + 5}
              width="20"
              height="15"
              fill="#ff4d4f"
              rx="2"
            />
            <text
              x={pos.x + pos.width - 15}
              y={pos.y + 14}
              textAnchor="middle"
              fontSize="9"
              fill="white"
              fontWeight="bold"
            >
              代
            </text>
          </g>
        )}

        {/* 中心标识 */}
        {pos.isCenter && (
          <text
            x={pos.x + pos.width / 2}
            y={pos.y + pos.height / 2 + 12}
            textAnchor="middle"
            fontSize="10"
            fill="#fff"
            fontWeight="bold"
          >
            [中心]
          </text>
        )}
      </g>
    ));
  };

  // 处理公司筛选变化
  const handleCompanyFilterChange = (companyIds: number[]) => {
    const hiddenSet = new Set<number>();
    equityData?.relatedCompanies.forEach(company => {
      if (!companyIds.includes(company.id)) {
        hiddenSet.add(company.id);
      }
    });
    setHiddenCompanies(hiddenSet);
  };

  // 清理重复数据
  const handleCleanupDuplicates = async () => {
    try {
      setLoading(true);
      const response = await axios.post('http://localhost:8080/api/equity-chart/cleanup-duplicates');
      if (response.data.success) {
        message.success(`数据清理完成！清理了 ${response.data.data.totalCleaned} 条重复记录`);
        // 重新加载数据
        if (version === 'investor' && selectedCompanyId) {
          await fetchEquityData(selectedCompanyId);
        } else if (version === 'legal' && selectedCompanyIds.length > 0) {
          await fetchMultipleEquityData(selectedCompanyIds);
        }
      } else {
        throw new Error(response.data.message || '数据清理失败');
      }
    } catch (error: any) {
      console.error('数据清理失败:', error);
      message.error(error.message || '数据清理失败');
    } finally {
      setLoading(false);
    }
  };

  const positions = calculateNodePositions();

  // 调试信息
  console.log('🔍 股权图调试信息:', {
    hasEquityData: !!equityData,
    positionsCount: positions.length,
    version,
    selectedCompanyId,
    loading,
    error
  });

  if (positions.length > 0) {
    console.log('📍 节点位置信息:', {
      minX: Math.min(...positions.map(p => p.x)),
      maxX: Math.max(...positions.map(p => p.x + p.width)),
      minY: Math.min(...positions.map(p => p.y)),
      maxY: Math.max(...positions.map(p => p.y + p.height))
    });

    // 按层级分组显示节点
    const nodesByLevel = positions.reduce((acc, pos) => {
      const level = pos.level || 0;
      if (!acc[level]) acc[level] = [];
      acc[level].push(pos.name);
      return acc;
    }, {} as Record<number, string[]>);

    console.log('🏢 按层级分组的节点:', nodesByLevel);
  }

  // 动态计算SVG画布尺寸
  const calculateCanvasSize = () => {
    if (!equityData || positions.length === 0) {
      return { width: 1200, height: 600, viewBox: '0 0 1200 600' };
    }

    const maxX = Math.max(...positions.map(p => p.x + p.width));
    const maxY = Math.max(...positions.map(p => p.y + p.height));
    const minX = Math.min(...positions.map(p => p.x));
    const minY = Math.min(...positions.map(p => p.y));

    // 确保有足够的边距
    const padding = 100;
    const viewBoxX = Math.min(0, minX - padding);
    const viewBoxY = Math.min(0, minY - padding);
    const width = Math.max(1200, maxX - viewBoxX + padding);
    const height = Math.max(600, maxY - viewBoxY + padding);

    return {
      width,
      height,
      viewBox: `${viewBoxX} ${viewBoxY} ${width} ${height}`
    };
  };

  const canvasSize = calculateCanvasSize();

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px' }}>
          <h2>股权图-主体公司</h2>
          
          {/* 版本选择 */}
          <div style={{ marginBottom: '16px' }}>
            <label style={{ marginRight: '8px' }}>选择版本：</label>
            <Select
              style={{ width: 200 }}
              value={version}
              onChange={(value) => {
                setVersion(value);
                setSelectedCompanyId(null);
                setSelectedCompanyIds([]);
                setEquityData(null);
              }}
            >
              <Option value="investor">投资者版</Option>
              <Option value="legal">法务版</Option>
            </Select>
          </div>

          {/* 公司选择下拉框 */}
          <div style={{ marginBottom: '16px' }}>
            <label style={{ marginRight: '8px' }}>选择主体公司：</label>
            {version === 'investor' ? (
              <Select
                style={{ width: 300 }}
                placeholder="请选择公司"
                value={selectedCompanyId}
                onChange={setSelectedCompanyId}
                showSearch
                filterOption={(input, option) =>
                  option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
                }
              >
                {companies.map(company => (
                  <Option key={company.id} value={company.id}>
                    {company.name}
                  </Option>
                ))}
              </Select>
            ) : (
              <Select
                mode="multiple"
                style={{ width: 400 }}
                placeholder="请选择多个公司"
                value={selectedCompanyIds}
                onChange={setSelectedCompanyIds}
                showSearch
                filterOption={(input, option) =>
                  option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
                }
              >
                {companies.map(company => (
                  <Option key={company.id} value={company.id}>
                    {company.name}
                  </Option>
                ))}
              </Select>
            )}
          </div>

          {/* 数据清理按钮 */}
          <div style={{ marginBottom: '16px' }}>
            <Button
              type="default"
              onClick={handleCleanupDuplicates}
              loading={loading}
              style={{ marginRight: '8px' }}
            >
              🧹 清理重复数据
            </Button>
            <span style={{ fontSize: '12px', color: '#666' }}>
              清理数据库中重复的股权关系记录
            </span>
          </div>
        </div>

        {/* 加载状态 */}
        {loading && (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>正在加载股权图数据...</div>
          </div>
        )}

        {/* 错误状态 */}
        {error && (
          <Alert
            message="加载失败"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        )}

        {/* 无数据提示 */}
        {!loading && !error && !equityData && (
          <div style={{
            textAlign: 'center',
            padding: '50px',
            backgroundColor: '#fafafa',
            border: '1px solid #d9d9d9',
            borderRadius: '4px'
          }}>
            <div style={{ fontSize: '16px', color: '#666', marginBottom: '8px' }}>
              📊 请选择公司查看股权关系图
            </div>
            <div style={{ fontSize: '14px', color: '#999' }}>
              {version === 'investor' ? '选择一个公司查看其股权结构' : '选择多个公司查看综合股权关系'}
            </div>
          </div>
        )}

        {/* 股权图显示 */}
        {!loading && !error && equityData && (
          <div style={{
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            backgroundColor: '#fafafa',
            minHeight: `${canvasSize.height}px`,
            position: 'relative',
            overflow: 'auto'
          }}>
            <svg
              width={canvasSize.width}
              height={canvasSize.height}
              viewBox={canvasSize.viewBox}
              style={{ display: 'block' }}
            >
              {/* 定义箭头标记 */}
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon
                    points="0 0, 10 3.5, 0 7"
                    fill="#1890ff"
                  />
                </marker>
                <marker
                  id="arrowhead-green"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon
                    points="0 0, 10 3.5, 0 7"
                    fill="#52c41a"
                  />
                </marker>
              </defs>

              {/* 渲染连接线 */}
              {renderConnections(positions)}

              {/* 渲染节点 */}
              {renderNodes(positions)}
            </svg>

            {/* 滚动提示 - 移到左上角避免与图例重叠 */}
            {canvasSize.width > 1400 && (
              <div style={{
                position: 'absolute',
                top: '10px',
                left: '10px',
                backgroundColor: '#fff3cd',
                padding: '8px 12px',
                border: '1px solid #ffeaa7',
                borderRadius: '4px',
                fontSize: '12px',
                color: '#856404',
                zIndex: 10
              }}>
                💡 图表较宽，可以左右滚动查看完整内容
              </div>
            )}

            {/* 图例说明 - 固定在画布左下角，滚动时保持显示 */}
            <div style={{
              position: 'fixed',
              bottom: '20px',
              left: '20px',
              backgroundColor: '#fff',
              padding: '12px',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              fontSize: '11px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              maxWidth: '200px',
              zIndex: 1000
            }}>
              <div style={{ marginBottom: '12px', fontWeight: 'bold', fontSize: '14px' }}>图例说明</div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '20px',
                  height: '3px',
                  backgroundColor: '#1890ff',
                  marginRight: '8px'
                }}></div>
                <span>股东持股关系</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '20px',
                  height: '3px',
                  backgroundColor: '#52c41a',
                  marginRight: '8px'
                }}></div>
                <span>对外投资关系</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '16px',
                  backgroundColor: '#1890ff',
                  borderRadius: '3px',
                  marginRight: '8px',
                  border: '2px solid #1890ff'
                }}></div>
                <span>中心公司</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '16px',
                  backgroundColor: '#f6ffed',
                  borderRadius: '3px',
                  marginRight: '8px',
                  border: '2px solid #52c41a'
                }}></div>
                <span>个人股东</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '16px',
                  backgroundColor: '#fff',
                  borderRadius: '3px',
                  marginRight: '8px',
                  border: '2px solid #d9d9d9'
                }}></div>
                <span>企业主体</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '12px',
                  backgroundColor: '#ff4d4f',
                  borderRadius: '2px',
                  marginRight: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '8px'
                }}>代</div>
                <span>代持关系</span>
              </div>

              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '16px',
                  backgroundColor: '#52c41a',
                  borderRadius: '50%',
                  marginRight: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '10px'
                }}>↑</div>
                <span>展开/收起按钮</span>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default EquityChart;
