import React, { useState } from 'react';
import { Card, Form, Input, Button, Select, DatePicker, Row, Col, Space, Table, Upload, message } from 'antd';
import { SaveOutlined, RollbackOutlined, UploadOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import type { ColumnsType } from 'antd/es/table';
import type { UploadProps } from 'antd';

const { Option } = Select;

interface ExecutiveRecord {
  id: string;
  name: string;
  position: string;
  idType: string;
  idNumber: string;
  appointmentDate: string;
  dismissalDate?: string;
  status: string;
}

const ExecutiveChange: React.FC = () => {
  const [form] = Form.useForm();
  const navigate = useNavigate();
  const [executives, setExecutives] = useState<ExecutiveRecord[]>([
    {
      id: '1',
      name: '张三',
      position: '董事长',
      idType: '身份证',
      idNumber: '110101199001011234',
      appointmentDate: '2020-01-15',
      status: '在职',
    },
    {
      id: '2',
      name: '李四',
      position: '总经理',
      idType: '身份证',
      idNumber: '110101199002022345',
      appointmentDate: '2020-01-15',
      status: '在职',
    },
  ]);

  const onFinish = (values: any) => {
    console.log('Form values:', values);
    // 这里添加提交表单的逻辑
    message.success('提交成功');
    form.resetFields();
  };

  const onReset = () => {
    form.resetFields();
  };

  const uploadProps: UploadProps = {
    name: 'file',
    action: 'https://run.mocky.io/v3/435e224c-44fb-4773-9faf-380c5e6a2188',
    headers: {
      authorization: 'authorization-text',
    },
    onChange(info) {
      if (info.file.status !== 'uploading') {
        console.log(info.file, info.fileList);
      }
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };

  const columns: ColumnsType<ExecutiveRecord> = [
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
    },
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
      align: 'center',
    },
    {
      title: '证件类型',
      dataIndex: 'idType',
      key: 'idType',
      align: 'center',
    },
    {
      title: '证件号码',
      dataIndex: 'idNumber',
      key: 'idNumber',
      align: 'center',
    },
    {
      title: '任职日期',
      dataIndex: 'appointmentDate',
      key: 'appointmentDate',
      align: 'center',
    },
    {
      title: '离职日期',
      dataIndex: 'dismissalDate',
      key: 'dismissalDate',
      align: 'center',
      render: (text) => text || '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (text) => {
        let color = text === '在职' ? 'green' : 'red';
        return <span style={{ color }}>{text}</span>;
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleEdit(record)}>编辑</Button>
          <Button type="link" danger onClick={() => handleDelete(record.id)}>删除</Button>
        </Space>
      ),
    },
  ];

  const handleEdit = (record: ExecutiveRecord) => {
    form.setFieldsValue({
      name: record.name,
      position: record.position,
      idType: record.idType,
      idNumber: record.idNumber,
      appointmentDate: record.appointmentDate,
      dismissalDate: record.dismissalDate,
      status: record.status,
    });
  };

  const handleDelete = (id: string) => {
    setExecutives(executives.filter(item => item.id !== id));
    message.success('删除成功');
  };

  const handleAdd = () => {
    const values = form.getFieldsValue();
    const newExecutive: ExecutiveRecord = {
      id: Date.now().toString(),
      name: values.name,
      position: values.position,
      idType: values.idType,
      idNumber: values.idNumber,
      appointmentDate: values.appointmentDate,
      dismissalDate: values.dismissalDate,
      status: values.status,
    };
    setExecutives([...executives, newExecutive]);
    message.success('添加成功');
    form.resetFields();
  };

  return (
    <div>
      <Card title="高管信息变更" style={{ marginBottom: 20 }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
        >
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                name="name"
                label="姓名"
                rules={[{ required: true, message: '请输入姓名' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="position"
                label="职位"
                rules={[{ required: true, message: '请输入职位' }]}
              >
                <Select>
                  <Option value="董事长">董事长</Option>
                  <Option value="董事">董事</Option>
                  <Option value="总经理">总经理</Option>
                  <Option value="副总经理">副总经理</Option>
                  <Option value="财务负责人">财务负责人</Option>
                  <Option value="监事">监事</Option>
                  <Option value="监事会主席">监事会主席</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="idType"
                label="证件类型"
                rules={[{ required: true, message: '请选择证件类型' }]}
              >
                <Select>
                  <Option value="身份证">身份证</Option>
                  <Option value="护照">护照</Option>
                  <Option value="军官证">军官证</Option>
                  <Option value="港澳通行证">港澳通行证</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                name="idNumber"
                label="证件号码"
                rules={[{ required: true, message: '请输入证件号码' }]}
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="appointmentDate"
                label="任职日期"
                rules={[{ required: true, message: '请选择任职日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="dismissalDate"
                label="离职日期"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select>
                  <Option value="在职">在职</Option>
                  <Option value="离职">离职</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={16}>
              <Form.Item
                name="upload"
                label="上传证明材料"
              >
                <Upload {...uploadProps}>
                  <Button icon={<UploadOutlined />}>点击上传</Button>
                </Upload>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item>
            <Space>
              <Button type="primary" onClick={handleAdd} icon={<PlusOutlined />}>
                添加
              </Button>
              <Button htmlType="button" onClick={onReset} icon={<RollbackOutlined />}>
                重置
              </Button>
              <Button htmlType="button" onClick={() => navigate('/company/info')}>
                返回
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>

      <Card title="高管信息列表">
        <Table columns={columns} dataSource={executives} rowKey="id" />
      </Card>
    </div>
  );
};

export default ExecutiveChange;