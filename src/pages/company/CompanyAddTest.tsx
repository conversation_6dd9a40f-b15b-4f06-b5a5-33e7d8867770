// Simple test component to verify routing and basic rendering
import React from 'react';
import { Card, Button, Typography } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;

const CompanyAddTest: React.FC = () => {
  const navigate = useNavigate();

  const handleBack = () => {
    navigate(-1);
  };

  return (
    <div style={{ padding: 24, backgroundColor: '#f5f5f5', minHeight: '100vh' }}>
      <Card title="测试页面 - 新增公司">
        <div style={{ textAlign: 'center', padding: 40 }}>
          <Title level={3}>🎉 页面加载成功！</Title>
          <Text>
            这是一个测试页面，用于验证路由和基本渲染是否正常工作。
          </Text>
          <div style={{ marginTop: 24 }}>
            <Button 
              type="primary" 
              icon={<ArrowLeftOutlined />} 
              onClick={handleBack}
            >
              返回
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default CompanyAddTest;
