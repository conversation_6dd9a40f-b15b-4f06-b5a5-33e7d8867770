import React, { useState, useEffect } from 'react';
import { Card, Button, Table, Tabs, Modal, Select, message } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';
import axios from 'axios';
import { apiService } from '../../services/api-simple';
import './CompanyDetail.css';

const { TabPane } = Tabs;
const { Option } = Select;

interface CompanyInfo {
  id: number;
  chineseName: string;
  englishName: string;
  registeredCapital: number;
  establishmentDate: string;
  businessScope: string;
  region: string;
  agentOrganization: string;
  annualReport: string;
  registrationAddress: string;
}

interface ExecutiveInfo {
  position: string;
  personName: string;
}

interface ShareholderInfo {
  shareholderName: string;
  subscriptionAmount: number;
  shareholdingRatio: number;
  representative: string;
  actualController: string;
  openingTime: string;
}

interface InvestmentInfo {
  targetCompany: string;
  investmentAmount: number;
  shareholdingRatio: number;
  openingTime: string;
}

interface FinancialInfo {
  year: number;
  totalAssets: number;
  totalLiabilities: number;
  ownerEquity: number;
  mainBusinessIncome: number;
  netProfit: number;
  totalProfit: number;
  netAssets: number;
  notes: string;
}

interface StockInfo {
  shareholderName: string;
  actualAmount: number;
  paymentMethod: string;
  actualPaymentTime: string;
}

interface ChangeHistory {
  status: string;
  changeContent: string;
  content: string;
  changeDocuments: number;
  changeTime: string;
  operator: string;
  operationTime: string;
  reviewer: string;
  reviewTime: string;
}

const CompanyDetail: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo | null>(null);
  const [executives, setExecutives] = useState<ExecutiveInfo[]>([]);
  const [shareholders, setShareholders] = useState<ShareholderInfo[]>([]);
  const [investments, setInvestments] = useState<InvestmentInfo[]>([]);
  const [financialInfo, setFinancialInfo] = useState<FinancialInfo[]>([]);
  const [stockInfo, setStockInfo] = useState<StockInfo[]>([]);
  const [changeHistory, setChangeHistory] = useState<ChangeHistory[]>([]);
  const [isFinanceModalVisible, setIsFinanceModalVisible] = useState(false);
  const [selectedYear, setSelectedYear] = useState<number>();
  const [activeSection, setActiveSection] = useState<string>('basic-info');

  // 模块化错误状态
  const [executiveError, setExecutiveError] = useState<string | null>(null);
  const [shareholderError, setShareholderError] = useState<string | null>(null);
  const [investmentError, setInvestmentError] = useState<string | null>(null);
  const [financialError, setFinancialError] = useState<string | null>(null);
  const [stockError, setStockError] = useState<string | null>(null);
  const [changeHistoryError, setChangeHistoryError] = useState<string | null>(null);

  // 从路由状态获取公司ID
  const companyId = location.state?.companyId;

  useEffect(() => {
    if (companyId) {
      fetchCompanyData(companyId);
    } else {
      console.error('❌ 未获取到公司ID');
      message.error('未获取到公司ID，请从公司列表页面进入');
    }
  }, [companyId]);

  const fetchCompanyData = async (id: number) => {
    try {
      console.log('🔄 获取公司详情数据, ID:', id);

      // 获取公司基本信息
      try {
        console.log('🔄 开始获取公司基本信息，ID:', id);
        const companyResponse = await apiService.getCompanyById(id);
        console.log('📊 公司基本信息响应:', companyResponse);

        if (companyResponse.data && companyResponse.data.success) {
          setCompanyInfo(companyResponse.data.data);
          console.log('✅ 公司基本信息设置成功');
        } else if (companyResponse.data) {
          setCompanyInfo(companyResponse.data);
          console.log('✅ 公司基本信息设置成功（直接数据）');
        } else {
          throw new Error('公司基本信息为空');
        }
      } catch (error) {
        console.error('❌ 获取公司基本信息失败:', error);
        message.error(`获取公司基本信息失败: ${error.message}`);
        // 即使基本信息获取失败，也继续加载其他模块数据
      }

      // 并行加载各模块数据
      await Promise.all([
        loadExecutiveData(id.toString()),
        loadShareholderData(id.toString()),
        loadInvestmentData(id.toString()),
        loadFinancialData(id.toString()),
        loadStockData(id.toString()),
        loadChangeHistoryData(id.toString())
      ]);

    } catch (error) {
      console.error('❌ 获取公司数据失败:', error);
      message.error('获取公司数据失败');
    }
  };

  // 加载高管信息
  const loadExecutiveData = async (companyId: string) => {
    try {
      setExecutiveError(null);
      const response = await axios.get('http://localhost:8080/api/employment-records');
      if (response.data.success) {
        // 筛选当前公司的在职高管
        const currentExecutives = response.data.data.filter((record: any) =>
          record.companyId === parseInt(companyId) &&
          record.isActive === 1 &&
          record.endDate === null
        );

        // 转换为页面需要的格式
        const executiveRecords = currentExecutives.map((record: any) => ({
          position: record.position,
          personName: record.personName
        }));

        setExecutives(executiveRecords);
        console.log('✅ 高管信息加载成功，共', executiveRecords.length, '条记录');
      } else {
        throw new Error(response.data.message || '任职记录API返回失败');
      }
    } catch (error) {
      console.error('获取高管信息失败:', error);
      setExecutiveError(`获取高管信息失败: ${error.message}`);
      setExecutives([]);
    }
  };

  // 加载股东信息
  const loadShareholderData = async (companyId: string) => {
    try {
      setShareholderError(null);
      const response = await axios.get('http://localhost:8080/api/shareholdings');
      if (response.data.success) {
        // 筛选当前公司的在职股东
        const currentShareholders = response.data.data.filter((record: any) =>
          record.companyId === parseInt(companyId) &&
          record.isActive === 1 &&
          record.endDate === null
        );

        // 转换为页面需要的格式
        const shareholderRecords = currentShareholders.map((record: any) => ({
          shareholderName: record.personName,
          registeredAmount: record.investmentAmount,
          percentage: record.percentage,
          isProxy: record.isProxy ? '是' : '否',
          actualShareholder: record.actualShareholderName || '-',
          startDate: record.startDate ? new Date(record.startDate).toLocaleDateString() : '-'
        }));

        setShareholders(shareholderRecords);
        console.log('✅ 股东信息加载成功，共', shareholderRecords.length, '条记录');
      } else {
        throw new Error(response.data.message || '股东信息API返回失败');
      }
    } catch (error) {
      console.error('获取股东信息失败:', error);
      setShareholderError(`获取股东信息失败: ${error.message}`);
      setShareholders([]);
    }
  };

  // 加载投资信息
  const loadInvestmentData = async (companyId: string) => {
    try {
      setInvestmentError(null);
      const response = await axios.get('http://localhost:8080/api/debug/investments-data');
      if (response.data.success) {
        // 筛选当前公司的投资记录
        const currentInvestments = response.data.data.filter((record: any) =>
          record.investor_company_id === parseInt(companyId) &&
          record.is_active === 1 &&
          record.end_date === null
        );

        // 转换为页面需要的格式
        const investmentRecords = currentInvestments.map((record: any) => ({
          targetCompany: record.investee_company_name,
          investmentAmount: record.investment_amount,
          percentage: record.percentage,
          startDate: record.start_date ? new Date(record.start_date).toLocaleDateString() : '-'
        }));

        setInvestments(investmentRecords);
        console.log('✅ 投资信息加载成功，共', investmentRecords.length, '条记录');
      } else {
        throw new Error(response.data.message || '投资信息API返回失败');
      }
    } catch (error) {
      console.error('获取投资信息失败:', error);
      setInvestmentError(`获取投资信息失败: ${error.message}`);
      setInvestments([]);
    }
  };

  // 加载财务信息
  const loadFinancialData = async (companyId: string) => {
    try {
      setFinancialError(null);
      const response = await axios.get(`http://localhost:8080/api/companies/${companyId}/financial`);
      if (response.data.success) {
        setFinancialInfo(response.data.data || []);
        console.log('✅ 财务信息加载成功');
      } else {
        throw new Error(response.data.message || '财务信息API返回失败');
      }
    } catch (error) {
      console.error('获取财务信息失败:', error);
      setFinancialError(`获取财务信息失败: ${error.message}`);
      setFinancialInfo([]);
    }
  };

  // 加载股本信息
  const loadStockData = async (companyId: string) => {
    try {
      setStockError(null);
      const response = await axios.get(`http://localhost:8080/api/companies/${companyId}/stock`);
      if (response.data.success) {
        setStockInfo(response.data.data || []);
        console.log('✅ 股本信息加载成功');
      } else {
        throw new Error(response.data.message || '股本信息API返回失败');
      }
    } catch (error) {
      console.error('获取股本信息失败:', error);
      setStockError(`获取股本信息失败: ${error.message}`);
      setStockInfo([]);
    }
  };

  // 加载变更历史
  const loadChangeHistoryData = async (companyId: string) => {
    try {
      setChangeHistoryError(null);
      const response = await axios.get(`http://localhost:8080/api/companies/${companyId}/change-history`);
      if (response.data.success) {
        setChangeHistory(response.data.data || []);
        console.log('✅ 变更历史加载成功');
      } else {
        throw new Error(response.data.message || '变更历史API返回失败');
      }
    } catch (error) {
      console.error('获取变更历史失败:', error);
      setChangeHistoryError(`获取变更历史失败: ${error.message}`);
      setChangeHistory([]);
    }
  };

  const handleBack = () => {
    navigate('/company/list');
  };

  const handlePersonClick = (personName: string) => {
    navigate('/archive/employment', { state: { personName } });
  };

  const handleFinanceEdit = () => {
    setIsFinanceModalVisible(true);
  };

  const handleFinanceModalOk = () => {
    if (selectedYear && companyId) {
      navigate('/company/finance', {
        state: {
          companyId,
          year: selectedYear,
          editMode: true
        }
      });
    } else {
      message.warning('请选择年度');
    }
  };

  const handleFinanceModalCancel = () => {
    setIsFinanceModalVisible(false);
    setSelectedYear(undefined);
  };

  // 高管信息表格列
  const executiveColumns = [
    {
      title: '职位',
      dataIndex: 'position',
      key: 'position',
      width: '50%',
      align: 'center' as const,
    },
    {
      title: '任职人员',
      dataIndex: 'personName',
      key: 'personName',
      width: '50%',
      align: 'center' as const,
      render: (text: string) => (
        <a onClick={() => handlePersonClick(text)} style={{ color: '#1890ff' }}>
          {text}
        </a>
      ),
    },
  ];

  // 股东信息表格列
  const shareholderColumns = [
    {
      title: '身份股东',
      dataIndex: 'shareholderName',
      key: 'shareholderName',
      align: 'center' as const,
    },
    {
      title: '认缴出资额（万元人民币）',
      dataIndex: 'registeredAmount',
      key: 'registeredAmount',
      align: 'center' as const,
    },
    {
      title: '持股比例',
      dataIndex: 'percentage',
      key: 'percentage',
      align: 'center' as const,
      render: (value: string) => `${value}%`,
    },
    {
      title: '代持',
      dataIndex: 'isProxy',
      key: 'isProxy',
      align: 'center' as const,
    },
    {
      title: '实际股东',
      dataIndex: 'actualShareholder',
      key: 'actualShareholder',
      align: 'center' as const,
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      key: 'startDate',
      align: 'center' as const,
    },
  ];

  // 对外投资信息表格列
  const investmentColumns = [
    {
      title: '主体名称',
      dataIndex: 'targetCompany',
      key: 'targetCompany',
      align: 'center' as const,
    },
    {
      title: '投资金额（万元人民币）',
      dataIndex: 'investmentAmount',
      key: 'investmentAmount',
      align: 'center' as const,
    },
    {
      title: '持股比例',
      dataIndex: 'percentage',
      key: 'percentage',
      align: 'center' as const,
      render: (value: string) => `${value}%`,
    },
    {
      title: '开始时间',
      dataIndex: 'startDate',
      key: 'startDate',
      align: 'center' as const,
    },
  ];

  // 股本及出资信息表格列
  const stockColumns = [
    {
      title: '股东名称',
      dataIndex: 'shareholderName',
      key: 'shareholderName',
      align: 'center' as const,
    },
    {
      title: '实缴出资额（万元人民币）',
      dataIndex: 'actualAmount',
      key: 'actualAmount',
      align: 'center' as const,
    },
    {
      title: '实缴出资方式',
      dataIndex: 'paymentMethod',
      key: 'paymentMethod',
      align: 'center' as const,
    },
    {
      title: '实缴出资时间',
      dataIndex: 'actualPaymentTime',
      key: 'actualPaymentTime',
      align: 'center' as const,
    },
  ];

  // 变更历史表格列
  const changeColumns = [
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      align: 'center' as const,
    },
    {
      title: '变更内容',
      dataIndex: 'changeContent',
      key: 'changeContent',
      align: 'center' as const,
    },
    {
      title: '内容',
      dataIndex: 'content',
      key: 'content',
      align: 'center' as const,
    },
    {
      title: '变更档案文件',
      dataIndex: 'changeDocuments',
      key: 'changeDocuments',
      align: 'center' as const,
    },
    {
      title: '变更时间',
      dataIndex: 'changeTime',
      key: 'changeTime',
      align: 'center' as const,
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
      align: 'center' as const,
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      key: 'operationTime',
      align: 'center' as const,
    },
    {
      title: '审核人',
      dataIndex: 'reviewer',
      key: 'reviewer',
      align: 'center' as const,
    },
    {
      title: '审核时间',
      dataIndex: 'reviewTime',
      key: 'reviewTime',
      align: 'center' as const,
    },
  ];

  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 10 }, (_, i) => currentYear - i);

  // 页内导航菜单项
  const navigationItems = [
    { key: 'basic-info', label: '基础信息' },
    { key: 'executive-info', label: '高管信息' },
    { key: 'shareholder-info', label: '股东信息' },
    { key: 'investment-info', label: '对外投资信息' },
    { key: 'financial-info', label: '财务信息与股本及出资信息' },
    { key: 'change-history', label: '变更历史' },
  ];

  // 滚动到指定段落
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
      setActiveSection(sectionId);
    }
  };

  // 监听滚动事件，更新当前激活的段落
  useEffect(() => {
    const handleScroll = () => {
      const sections = navigationItems.map(item => item.key);
      const scrollPosition = window.scrollY + 100; // 偏移量

      for (let i = sections.length - 1; i >= 0; i--) {
        const element = document.getElementById(sections[i]);
        if (element && element.offsetTop <= scrollPosition) {
          setActiveSection(sections[i]);
          break;
        }
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <div className="company-detail">
      {/* 固定操作按钮 */}
      <div
        className="fixed-action-buttons"
        style={{
          position: 'fixed',
          top: '80px',
          right: '24px',
          zIndex: 1000,
          display: 'flex',
          gap: '8px'
        }}
      >
        <Button
          onClick={() => navigate('/company/change/basic-info', {
            state: {
              companyId: companyInfo?.id,
              companyName: companyInfo?.chineseName
            }
          })}
          type="primary"
          className="change-button"
        >
          变更基础信息
        </Button>
        <Button
          onClick={() => navigate('/company/change/executive-info', {
            state: {
              companyId: companyInfo?.id,
              companyName: companyInfo?.chineseName
            }
          })}
          type="primary"
          className="change-button"
          style={{ marginLeft: '8px' }}
        >
          变更高管信息
        </Button>
        <Button
          onClick={() => navigate('/company/change/shareholder-info', {
            state: {
              companyId: companyInfo?.id,
              companyName: companyInfo?.chineseName
            }
          })}
          type="primary"
          className="change-button"
          style={{ marginLeft: '8px' }}
        >
          变更股东信息
        </Button>
        <Button onClick={handleBack} className="back-button">
          返回
        </Button>
      </div>

      {/* 页内导航菜单 */}
      <div
        className="page-navigation"
        style={{
          position: 'fixed',
          top: '80px',
          left: '240px',
          zIndex: 1000,
          background: 'white',
          border: '1px solid #d9d9d9',
          borderRadius: '8px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
          padding: '8px 0',
          minWidth: '140px'
        }}
      >
        <div className="navigation-menu">
          {navigationItems.map(item => (
            <div
              key={item.key}
              className={`navigation-item ${activeSection === item.key ? 'active' : ''}`}
              onClick={() => scrollToSection(item.key)}
              style={{
                padding: '8px 16px',
                cursor: 'pointer',
                fontSize: '14px',
                color: activeSection === item.key ? '#1890ff' : '#595959',
                backgroundColor: activeSection === item.key ? '#e6f7ff' : 'transparent',
                borderLeft: activeSection === item.key ? '3px solid #1890ff' : '3px solid transparent',
                fontWeight: activeSection === item.key ? 500 : 'normal'
              }}
            >
              {item.label}
            </div>
          ))}
        </div>
      </div>

      <div className="company-detail-content">
        <Card title="基础信息" className="info-card" id="basic-info">
          <div className="basic-info-grid">
            <div className="info-row">
              <div className="info-item">
                <span className="label">公司中文名</span>
                <span className="value">{companyInfo?.chineseName || ''}</span>
              </div>
              <div className="info-item">
                <span className="label">公司英文名</span>
                <span className="value">{companyInfo?.englishName || ''}</span>
              </div>
            </div>
            <div className="info-row">
              <div className="info-item">
                <span className="label">注册资本（万元人民币）</span>
                <span className="value">{companyInfo?.registeredCapital || ''}</span>
              </div>
              <div className="info-item">
                <span className="label">设立日期</span>
                <span className="value">{companyInfo?.establishmentDate || ''}</span>
              </div>
            </div>
            <div className="info-row">
              <div className="info-item">
                <span className="label">业务板块</span>
                <span className="value">{companyInfo?.businessScope || ''}</span>
              </div>
              <div className="info-item">
                <span className="label">地区</span>
                <span className="value">{companyInfo?.region || ''}</span>
              </div>
            </div>
            <div className="info-row">
              <div className="info-item">
                <span className="label">代理机构</span>
                <span className="value">{companyInfo?.agentOrganization || ''}</span>
              </div>
              <div className="info-item">
                <span className="label">年审要求</span>
                <span className="value">{companyInfo?.annualReport || ''}</span>
              </div>
            </div>
            <div className="info-row">
              <div className="info-item full-width">
                <span className="label">注册地址</span>
                <span className="value">{companyInfo?.registrationAddress || ''}</span>
              </div>
            </div>
          </div>
        </Card>

        <Card title="高管信息" className="info-card" id="executive-info">
          {executiveError ? (
            <div style={{ textAlign: 'center', padding: '20px', color: '#ff4d4f' }}>
              {executiveError}
            </div>
          ) : (
            <Table
              columns={executiveColumns}
              dataSource={executives}
              pagination={false}
              rowKey={(record, index) => `${record.position}-${index}`}
              size="small"
              locale={{ emptyText: '暂无高管信息' }}
            />
          )}
        </Card>

        <Card title="股东信息" className="info-card" id="shareholder-info">
          <Table
            columns={shareholderColumns}
            dataSource={shareholders}
            pagination={false}
            rowKey={(record, index) => `${record.shareholderName}-${index}`}
            size="small"
          />
        </Card>

        <Card title="对外投资信息" className="info-card" id="investment-info">
          <Table
            columns={investmentColumns}
            dataSource={investments}
            pagination={false}
            rowKey={(record, index) => `${record.targetCompany}-${index}`}
            size="small"
          />
        </Card>

        <Card
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              财务信息与股本及出资信息
              <span
                onClick={handleFinanceEdit}
                style={{ cursor: 'pointer', fontSize: '16px' }}
                title="编辑财务信息"
              >
                ✏️
              </span>
            </div>
          }
          className="info-card"
          id="financial-info"
        >
          <Tabs defaultActiveKey="2021">
            {financialInfo.map((finance) => (
              <TabPane tab={finance.year.toString()} key={finance.year.toString()}>
                <div className="financial-info">
                  <h4>资产状况信息（万元人民币）</h4>
                  <div className="financial-grid">
                    <div className="financial-row">
                      <div className="financial-item">
                        <span className="label">资产总额</span>
                        <span className="value">{finance.totalAssets}</span>
                      </div>
                      <div className="financial-item">
                        <span className="label">所有者权益合计</span>
                        <span className="value">{finance.ownerEquity}</span>
                      </div>
                    </div>
                    <div className="financial-row">
                      <div className="financial-item">
                        <span className="label">负债总额</span>
                        <span className="value">{finance.totalLiabilities}</span>
                      </div>
                      <div className="financial-item">
                        <span className="label">营业总收入</span>
                        <span className="value">{finance.mainBusinessIncome}</span>
                      </div>
                    </div>
                    <div className="financial-row">
                      <div className="financial-item">
                        <span className="label">主营业务收入</span>
                        <span className="value">{finance.mainBusinessIncome}</span>
                      </div>
                      <div className="financial-item">
                        <span className="label">利润总额</span>
                        <span className="value">{finance.totalProfit}</span>
                      </div>
                    </div>
                    <div className="financial-row">
                      <div className="financial-item">
                        <span className="label">净利润</span>
                        <span className="value">{finance.netProfit}</span>
                      </div>
                      <div className="financial-item">
                        <span className="label">纳税总额</span>
                        <span className="value">{finance.netAssets}</span>
                      </div>
                    </div>
                  </div>
                  <div className="notes">
                    <span className="label">备注</span>
                    <span className="value">{finance.notes}</span>
                  </div>

                  <h4 style={{ marginTop: '24px', marginBottom: '16px' }}>股东及出资信息</h4>
                  <Table
                    columns={stockColumns}
                    dataSource={stockInfo}
                    pagination={false}
                    rowKey={(record, index) => `${record.shareholderName}-${index}`}
                    size="small"
                    locale={{ emptyText: '暂无股本及出资信息' }}
                  />
                </div>
              </TabPane>
            ))}
          </Tabs>
        </Card>



        <Card title="变更历史" className="info-card" id="change-history">
          <Table
            columns={changeColumns}
            dataSource={changeHistory}
            pagination={false}
            rowKey={(record, index) => `${record.changeContent}-${index}`}
            size="small"
            scroll={{ x: 1200 }}
          />
        </Card>
      </div>

      <Modal
        title="选择年度"
        visible={isFinanceModalVisible}
        onOk={handleFinanceModalOk}
        onCancel={handleFinanceModalCancel}
        okText="确定"
        cancelText="取消"
      >
        <div style={{ marginBottom: 16 }}>
          <span>请选择要编辑的财务信息年度：</span>
        </div>
        <Select
          style={{ width: '100%' }}
          placeholder="请选择年度"
          value={selectedYear}
          onChange={setSelectedYear}
        >
          {years.map(year => (
            <Option key={year} value={year}>{year}</Option>
          ))}
        </Select>
      </Modal>
    </div>
  );
};

export default CompanyDetail;
