import React, { useState, useEffect } from 'react';
import { Card, Select, Button, message, Spin, Alert } from 'antd';
import { useSearchParams } from 'react-router-dom';
import axios from 'axios';

const { Option } = Select;

interface Company {
  id: number;
  name: string;
}

interface ShareholderNode {
  id: number;
  shareholderId: number;
  shareholderName: string;
  shareholderType: 'person' | 'company';
  companyId: number;
  companyName: string;
  percentage: number;
  isProxy: boolean;
  actualShareholderId?: number;
  actualShareholderName?: string;
  level?: number; // 层级信息
  proxyType?: 'proxy' | 'actual'; // 代持关系类型：代持者或被代持者
  proxyRelationId?: number; // 代持关系ID，用于关联代持者和被代持者
}

interface InvestmentNode {
  id: number;
  investorId: number;
  investorName: string;
  investeeId?: number;
  investeeName: string;
  percentage: number;
  level?: number; // 层级信息
}

// 基于reference.md的层级化数据结构
interface HierarchicalNode {
  id: string;
  name: string;
  type: 'company' | 'person';
  percentage?: string;
  isProxy?: boolean;
  actualShareholderName?: string;
  level?: number;
  children?: HierarchicalNode[];
  parents?: HierarchicalNode[];
  collapsed?: boolean;
  _children?: HierarchicalNode[]; // 用于展开/收缩功能
}

interface EquityChartData {
  centerCompany: Company;
  upstreamShareholders: ShareholderNode[];
  downstreamInvestments: InvestmentNode[];
  relatedCompanies: Company[];
  // 新增层级化数据结构
  hierarchicalData?: HierarchicalNode;
}

interface NodePosition {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  name: string;
  type: 'company' | 'person';
  isCenter?: boolean;
  collapsed?: boolean;
  children?: string[];
  level?: number;
  isProxy?: boolean;
  percentage?: number;
  proxyType?: 'proxy' | 'actual'; // 代持关系类型
  proxyRelationId?: number; // 代持关系ID
}

const EquityChart: React.FC = () => {
  const [searchParams] = useSearchParams();
  const [companies, setCompanies] = useState<Company[]>([]);
  const [selectedCompanyId, setSelectedCompanyId] = useState<number | null>(null);
  const [selectedCompanyIds, setSelectedCompanyIds] = useState<number[]>([]);
  const [version, setVersion] = useState<'legal' | 'investor'>('investor');
  const [equityData, setEquityData] = useState<EquityChartData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hiddenCompanies, setHiddenCompanies] = useState<Set<number>>(new Set());
  // 节点展开/收缩状态管理
  const [collapsedNodes, setCollapsedNodes] = useState<Set<string>>(new Set());
  // 缩放控制
  const [zoomLevel, setZoomLevel] = useState<number>(1);

  // 基于reference.md的直角连接线绘制函数
  const drawRectangularConnection = ({ source, target }: {
    source: { x: number, y: number },
    target: { x: number, y: number }
  }): string => {
    const halfDistance = (target.y - source.y) / 2;
    const halfY = source.y + halfDistance;
    return `M${source.x},${source.y} L${source.x},${halfY} ${target.x},${halfY} ${target.x},${target.y}`;
  };

  // 节点展开/收缩处理函数
  const handleNodeToggle = (nodeId: string) => {
    setCollapsedNodes(prev => {
      const newSet = new Set(prev);
      if (newSet.has(nodeId)) {
        newSet.delete(nodeId);
        console.log(`🔓 展开节点: ${nodeId}`);
      } else {
        newSet.add(nodeId);
        console.log(`🔒 收缩节点: ${nodeId}`);
      }
      return newSet;
    });
  };

  // 基于reference.md的数据转换函数 - 将平面数据转换为层级化结构
  const transformToHierarchicalData = (equityData: EquityChartData): HierarchicalNode => {
    if (!equityData) return null;

    // 创建中心公司节点（根节点）
    const centerNode: HierarchicalNode = {
      id: `company-${equityData.centerCompany.id}`,
      name: equityData.centerCompany.name,
      type: 'company',
      level: 0,
      children: [],
      parents: []
    };

    // 处理下游投资（children）
    if (equityData.downstreamInvestments && equityData.downstreamInvestments.length > 0) {
      centerNode.children = equityData.downstreamInvestments.map(investment => ({
        id: `company-${investment.investeeId || investment.id}`,
        name: investment.investeeName || investment.investorName || '未知公司',
        type: 'company' as const,
        percentage: investment.percentage?.toString(),
        level: investment.level || 1,
        children: [], // 可以进一步递归展开
        parents: [centerNode]
      }));
    }

    // 处理上游股东（parents）- 按层级组织
    if (equityData.upstreamShareholders && equityData.upstreamShareholders.length > 0) {
      // 按层级分组
      const shareholdersByLevel = new Map<number, ShareholderNode[]>();
      equityData.upstreamShareholders.forEach(shareholder => {
        const level = shareholder.level || 0;
        if (!shareholdersByLevel.has(level)) {
          shareholdersByLevel.set(level, []);
        }
        shareholdersByLevel.get(level)!.push(shareholder);
      });

      // 构建层级化的父节点结构
      const buildParentHierarchy = (level: number): HierarchicalNode[] => {
        const currentLevelShareholders = shareholdersByLevel.get(level) || [];

        return currentLevelShareholders.map(shareholder => {
          const displayName = shareholder.isProxy && shareholder.actualShareholderName
            ? shareholder.actualShareholderName
            : shareholder.shareholderName;

          const parentNode: HierarchicalNode = {
            id: `${shareholder.shareholderType}-${shareholder.shareholderId}-${level}`,
            name: displayName,
            type: shareholder.shareholderType,
            percentage: shareholder.percentage?.toString(),
            isProxy: shareholder.isProxy,
            actualShareholderName: shareholder.actualShareholderName,
            level: level,
            children: [],
            parents: []
          };

          // 递归构建更上层的父节点
          if (shareholdersByLevel.has(level + 1)) {
            parentNode.parents = buildParentHierarchy(level + 1);
          }

          return parentNode;
        });
      };

      // 从第0层开始构建
      centerNode.parents = buildParentHierarchy(0);
    }

    return centerNode;
  };

  // 从URL参数获取公司ID
  useEffect(() => {
    const companyId = searchParams.get('companyId');
    if (companyId) {
      setSelectedCompanyId(parseInt(companyId));
    }
  }, [searchParams]);

  // 加载公司列表
  useEffect(() => {
    const fetchCompanies = async () => {
      try {
        const response = await axios.get('http://localhost:8080/api/companies');
        if (response.data.success) {
          const companiesList = response.data.data.map((company: any) => ({
            id: company.id,
            name: company.chineseName || company.company_name_cn
          }));
          setCompanies(companiesList);
        }
      } catch (error) {
        console.error('获取公司列表失败:', error);
        message.error('获取公司列表失败');
      }
    };

    fetchCompanies();
  }, []);

  // 自动选择第一个公司（投资者版和法务版都支持）
  useEffect(() => {
    if (!selectedCompanyId && (version === 'investor' || version === 'legal') && companies.length > 0) {
      setSelectedCompanyId(companies[0].id);
    }
  }, [companies, selectedCompanyId, version]);

  // 加载股权图数据
  useEffect(() => {
    if (selectedCompanyId && (version === 'investor' || version === 'legal')) {
      fetchEquityData(selectedCompanyId);
    }
  }, [selectedCompanyId, version]);

  const fetchEquityData = async (companyId: number) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get(`http://localhost:8080/api/equity-chart/${companyId}`);
      if (response.data.success) {
        const rawData = response.data.data;
        // 转换为层级化数据结构
        const hierarchicalData = transformToHierarchicalData(rawData);
        const enhancedData = {
          ...rawData,
          hierarchicalData
        };
        setEquityData(enhancedData);
        console.log('🌳 层级化数据结构:', hierarchicalData);
      } else {
        throw new Error(response.data.message || '获取股权图数据失败');
      }
    } catch (error: any) {
      console.error('获取股权图数据失败:', error);
      setError(error.message || '获取股权图数据失败');
      message.error(error.message || '获取股权图数据失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchMultipleEquityData = async (companyIds: number[]) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.post(`http://localhost:8080/api/equity-chart/multiple`, {
        companyIds
      });
      if (response.data.success) {
        setEquityData(response.data.data);
      } else {
        throw new Error(response.data.message || '获取股权图数据失败');
      }
    } catch (error: any) {
      console.error('获取股权图数据失败:', error);
      setError(error.message || '获取股权图数据失败');
      message.error(error.message || '获取股权图数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 基于参考代码的D3树布局算法重新实现
  const calculateNodePositions = (): NodePosition[] => {
    if (!equityData) return [];

    // 调试：打印关键的股权关系信息
    console.log('🔍 上游股东详细信息:');
    equityData.upstreamShareholders?.forEach((shareholder, index) => {
      console.log(`股东 ${index} - ${shareholder.shareholderName}:`, {
        id: shareholder.shareholderId,
        name: shareholder.shareholderName,
        type: shareholder.shareholderType,
        level: shareholder.level,
        percentage: shareholder.percentage,
        companyId: shareholder.companyId,
        companyName: shareholder.companyName,
        isProxy: shareholder.isProxy,
        actualShareholderId: shareholder.actualShareholderId,
        actualShareholderName: shareholder.actualShareholderName,
        allKeys: Object.keys(shareholder)
      });
    });

    const positions: NodePosition[] = [];

    // 动态计算布局配置 - 根据数据层级自动调整画布尺寸
    const maxUpstreamLevel = Math.max(0, ...equityData.upstreamShareholders.map(s => s.level || 0));
    const maxDownstreamLevel = Math.max(0, ...equityData.downstreamInvestments.map(i => i.level || 0));

    console.log(`📊 层级统计: 上游最大层级=${maxUpstreamLevel}, 下游最大层级=${maxDownstreamLevel}`);

    const config = {
      centralWidth: 1000,   // 画布中心X坐标 - 增加以容纳更多节点
      centralHeight: Math.max(800, (maxUpstreamLevel + 1) * 170 + 200), // 动态计算中心高度，确保上游不被截断
      linkLength: 170,      // 层级间距 - 参考reference.md中的170
      nodeSpace: 200,       // 节点间距 - 参考reference.md中的200
      rootRectWidth: Math.max(170, equityData.centerCompany.name.length * 15), // 根据名称长度动态调整
      nodeWidth: 170,       // 普通节点宽度 - 参考reference.md
      nodeHeight: 70        // 节点高度 - 参考reference.md
    };

    console.log(`📐 动态配置: 中心高度=${config.centralHeight}, 上游层级=${maxUpstreamLevel}, 下游层级=${maxDownstreamLevel}`);

    // 真正的多层级股权穿透布局算法
    const calculateTreeNodes = (data: any[], direction: 'upward' | 'downward') => {
      // 按层级分组数据，确保层级正确
      const levelGroups = new Map<number, any[]>();
      data.forEach(item => {
        const level = item.level || 0;
        if (!levelGroups.has(level)) {
          levelGroups.set(level, []);
        }
        levelGroups.get(level)!.push(item);
      });

      const nodes: any[] = [];

      // 按层级顺序处理，确保层级分明
      const sortedLevels = Array.from(levelGroups.keys()).sort((a, b) => {
        // 上游：层级从高到低排序（level 2 -> 1 -> 0）
        // 下游：层级从低到高排序（level 0 -> 1 -> 2）
        return direction === 'upward' ? b - a : a - b;
      });

      console.log(`🔍 ${direction} 层级排序:`, sortedLevels);

      sortedLevels.forEach((level, levelIndex) => {
        const levelItems = levelGroups.get(level) || [];
        const totalNodes = levelItems.length;
        const spacing = config.nodeSpace; // 使用reference.md中的200间距

        // 修复节点分布算法 - 确保节点不超出边界
        const totalWidth = Math.max((totalNodes - 1) * spacing, 0);
        const minStartX = config.nodeWidth / 2 + 50; // 最小起始X坐标，留出边距
        const maxEndX = 2200; // 最大结束X坐标，确保不超出画布

        let startX;
        if (totalNodes === 1) {
          startX = config.centralWidth;
        } else {
          // 确保节点分布在合理范围内
          const idealStartX = config.centralWidth - totalWidth / 2;
          const idealEndX = idealStartX + totalWidth;

          if (idealEndX > maxEndX) {
            // 如果超出右边界，从右边界往左排列
            startX = maxEndX - totalWidth;
          } else if (idealStartX < minStartX) {
            // 如果超出左边界，从左边界开始排列
            startX = minStartX;
          } else {
            // 正常居中排列
            startX = idealStartX;
          }
        }

        const itemNames = levelItems.map(i => i.shareholderName || i.investeeName || i.name || '未知').join(', ');
        console.log(`📊 ${direction} Level ${level} (第${levelIndex}层): ${itemNames}`);

        levelItems.forEach((item, index) => {
          const node = {
            ...item,
            x: startX + index * spacing,
            depth: level,
            level: level
          };

          // 修复Y坐标计算 - 直接使用level而不是levelIndex
          if (direction === 'upward') {
            // 上游：level越高，Y坐标越小（越往上）
            // Level 2 -> 最上层（孙八、周九）
            // Level 1 -> 中间层（上海金融服务有限公司）
            // Level 0 -> 最下层（赵六、钱七、王五等直接股东）
            node.y = config.centralHeight - ((level + 1) * config.linkLength);
          } else {
            // 下游：level越高，Y坐标越大（越往下）
            node.y = config.centralHeight + ((level + 1) * config.linkLength);
          }

          const itemName = item.shareholderName || item.investeeName || item.name || '未知';
          console.log(`📍 ${itemName} -> x: ${node.x}, y: ${node.y}, level: ${level}, levelIndex: ${levelIndex}`);
          nodes.push(node);
        });
      });

      return nodes;
    };

    // 节点重叠检测函数
    const checkNodeOverlap = (node1: any, node2: any, margin: number = 20): boolean => {
      const node1Right = node1.x + config.nodeWidth + margin;
      const node1Left = node1.x - margin;
      const node1Bottom = node1.y + config.nodeHeight + margin;
      const node1Top = node1.y - margin;

      const node2Right = node2.x + config.nodeWidth + margin;
      const node2Left = node2.x - margin;
      const node2Bottom = node2.y + config.nodeHeight + margin;
      const node2Top = node2.y - margin;

      // 检查是否重叠
      return !(node1Right < node2Left ||
               node1Left > node2Right ||
               node1Bottom < node2Top ||
               node1Top > node2Bottom);
    };

    // 代持关系X轴对齐函数（水平位置对齐）
    const applyProxyXAxisAlignment = (nodes: any[]): any[] => {
      // 找到所有代持关系对（跨层级）
      const proxyPairs = new Map<number, { proxy: any, actual: any }>();

      // 调试：打印所有节点信息
      console.log('🔍 所有节点信息:');
      nodes.forEach(node => {
        if (node.proxyType) {
          console.log(`  - ${node.shareholderName}: proxyType=${node.proxyType}, shareholderId=${node.shareholderId}, proxyRelationId=${node.proxyRelationId}, level=${node.level}`);
        }
      });

      nodes.forEach(node => {
        if (node.proxyType === 'proxy') {
          // 找到对应的被代持者（可能在不同层级）
          const actualNode = nodes.find(n =>
            n.proxyType === 'actual' &&
            n.proxyRelationId === node.shareholderId
          );
          if (actualNode) {
            proxyPairs.set(node.shareholderId, {
              proxy: node,
              actual: actualNode
            });
            console.log(`🔗 找到代持关系对: ${node.shareholderName} (代持者) <-> ${actualNode.shareholderName} (被代持者)`);
          } else {
            console.log(`❌ 未找到代持者 ${node.shareholderName} (ID: ${node.shareholderId}) 对应的被代持者`);
          }
        }
      });

      console.log(`🔍 发现 ${proxyPairs.size} 对代持关系`);

      // 为每对代持关系应用X轴对齐
      proxyPairs.forEach(({ proxy, actual }, proxyId) => {
        console.log(`📐 处理代持关系X轴对齐: ${proxy.shareholderName} (Level ${proxy.level}, X=${proxy.x}) -> ${actual.shareholderName} (Level ${actual.level}, X=${actual.x})`);

        // 计算X轴对齐位置（以代持者的X坐标为准）
        const alignedX = proxy.x;

        // 创建临时节点用于重叠检测
        const tempActual = { ...actual, x: alignedX };

        // 检查被代持者调整位置后是否与其他节点重叠
        let hasOverlap = false;

        for (const otherNode of nodes) {
          // 跳过当前代持关系的两个节点
          if (otherNode === proxy || otherNode === actual) continue;

          // 检查被代持者是否重叠
          if (checkNodeOverlap(tempActual, otherNode)) {
            console.log(`  ❌ ${actual.shareholderName} X轴对齐后与 ${otherNode.shareholderName} 重叠`);
            hasOverlap = true;
            break;
          }
        }

        // 如果没有重叠，应用X轴对齐
        if (!hasOverlap) {
          const oldX = actual.x;
          actual.x = alignedX;
          console.log(`  ✅ 应用X轴对齐: ${proxy.shareholderName} & ${actual.shareholderName} (${oldX} -> ${alignedX})`);
        } else {
          console.log(`  ⚠️ 跳过X轴对齐: 存在重叠风险`);
        }
      });

      return nodes;
    };

    // 安全的水平平均分布函数（避免重叠优先）
    const applySafeHorizontalDistribution = (nodes: any[], direction: 'upward' | 'downward') => {
      // 按层级分组
      const levelGroups = new Map<number, any[]>();
      nodes.forEach(node => {
        const level = node.level || 0;
        if (!levelGroups.has(level)) {
          levelGroups.set(level, []);
        }
        levelGroups.get(level)!.push(node);
      });

      // 为每个层级应用安全的水平分布
      levelGroups.forEach((levelNodes, level) => {
        // 按投资目标分组
        const targetGroups = new Map<string, any[]>();
        levelNodes.forEach(node => {
          const targetKey = node.companyName || 'center';
          if (!targetGroups.has(targetKey)) {
            targetGroups.set(targetKey, []);
          }
          targetGroups.get(targetKey)!.push(node);
        });

        // 为每个目标公司的股东组应用安全分布
        targetGroups.forEach((shareholders, targetCompanyName) => {
          if (shareholders.length <= 1) return; // 单个股东无需调整

          // 优化节点排序
          if (targetCompanyName === 'center' || targetCompanyName === '深圳科技有限公司') {
            shareholders.sort((a, b) => {
              if (a.shareholderName === '李四') return -1;
              if (b.shareholderName === '李四') return 1;
              if (a.shareholderName === '张三') return 1;
              if (b.shareholderName === '张三') return -1;
              return 0;
            });
          }

          // 查找目标公司的位置
          let targetCenterX = config.centralWidth;

          if (targetCompanyName !== 'center') {
            const targetNode = nodes.find(n =>
              n.shareholderName === targetCompanyName &&
              n.shareholderType === 'company' &&
              n.level === (direction === 'upward' ? level - 1 : level + 1)
            );
            if (targetNode) {
              targetCenterX = targetNode.x + config.nodeWidth / 2;
            }
          }

          // 🎯 安全的水平分布算法
          const nodeCount = shareholders.length;
          const minSpacing = config.nodeWidth + 20; // 最小间距：节点宽度 + 20px安全距离

          // 计算所需的最小总宽度
          const minRequiredWidth = (nodeCount - 1) * minSpacing;

          // 计算理想的分布宽度（可以更宽，但不能更窄）
          const idealWidth = Math.max(
            minRequiredWidth,
            (nodeCount - 1) * config.nodeSpace // 使用标准间距
          );

          // 计算分布范围（以目标公司为中心）
          let startX = targetCenterX - idealWidth / 2;
          let endX = targetCenterX + idealWidth / 2;

          // 边界检查和调整
          const minStartX = config.nodeWidth / 2 + 50;
          const maxEndX = 2200 - config.nodeWidth / 2;

          if (startX < minStartX) {
            startX = minStartX;
            endX = startX + idealWidth;
          }
          if (endX > maxEndX) {
            endX = maxEndX;
            startX = endX - idealWidth;
          }

          // 最终安全检查：确保有足够空间
          const actualWidth = endX - startX;
          const actualSpacing = actualWidth / (nodeCount - 1);

          if (actualSpacing < minSpacing) {
            // 如果空间不足，使用最小间距
            const finalSpacing = minSpacing;
            const finalWidth = (nodeCount - 1) * finalSpacing;
            startX = Math.max(targetCenterX - finalWidth / 2, minStartX);

            shareholders.forEach((shareholder, index) => {
              shareholder.x = startX + index * finalSpacing;
            });

            console.log(`⚠️ 空间不足，使用最小间距: ${targetCompanyName}`);
          } else {
            // 空间充足，使用平均分布
            shareholders.forEach((shareholder, index) => {
              shareholder.x = startX + index * actualSpacing;
            });

            console.log(`✅ 水平平均分布: ${targetCompanyName}`);
          }

          console.log(`   ${shareholders.length}个股东, 间距=${Math.round(actualSpacing)}, 中心X=${targetCenterX}`);
          console.log(`   位置: ${shareholders.map(s => `${s.shareholderName}(${Math.round(s.x)})`).join(', ')}`);
        });
      });

      return nodes;
    };

    // 中心公司（origin节点）- 参考代码第502-504行
    positions.push({
      id: `company-${equityData.centerCompany.id}`,
      x: config.centralWidth - config.rootRectWidth / 2,  // 参考代码第542行
      y: config.centralHeight - config.nodeHeight / 2,    // 参考代码第545行
      width: config.rootRectWidth,
      height: config.nodeHeight,
      name: equityData.centerCompany.name,
      type: 'company',
      isCenter: true
    });

    if (version === 'investor') {
      // 投资者版：完整的代持关系处理

      // 上游股东树（upward方向）
      if (equityData.upstreamShareholders && equityData.upstreamShareholders.length > 0) {
        // 先处理代持关系，创建代持者和被代持者节点
        const processedShareholders = processProxyRelationships(equityData.upstreamShareholders, version);

        // 过滤掉被收缩节点的子节点
        const visibleShareholders = processedShareholders.filter(shareholder => {
          // 检查其父节点是否被收缩
          const parentNodeId = `${shareholder.shareholderType}-${shareholder.shareholderId}-${(shareholder.level || 0) - 1}`;
          return !collapsedNodes.has(parentNodeId);
        });

        console.log(`📊 处理代持关系后的股东数量: ${processedShareholders.length}`);
        processedShareholders.forEach((s, i) => {
          console.log(`  ${i + 1}. ${s.shareholderName} (Level ${s.level}, ProxyType: ${(s as any).proxyType || 'none'})`);
        });

        const upstreamNodes = calculateTreeNodes(visibleShareholders, 'upward');

        // 应用安全的水平平均分布
        const alignedUpstreamNodes = applySafeHorizontalDistribution(upstreamNodes, 'upward');

        // 应用代持关系X轴对齐（在水平对齐之后）
        const finalUpstreamNodes = applyProxyXAxisAlignment(alignedUpstreamNodes);

        finalUpstreamNodes.forEach((node) => {
          // node对象包含了原始的股东数据，因为在calculateTreeNodes中使用了...item
          const shareholder = node;
          // 修复代持关系的显示名称逻辑
          let displayName;
          if ((shareholder as any).proxyType === 'proxy') {
            // 代持者显示代持者的名称（李四）
            displayName = shareholder.shareholderName;
          } else if ((shareholder as any).proxyType === 'actual') {
            // 被代持者显示被代持者的名称（王五）
            displayName = shareholder.shareholderName;
          } else {
            // 普通股东：非代持关系直接显示股东名称
            displayName = shareholder.shareholderName;
          }

          const nodeId = `${shareholder.shareholderType}-${shareholder.shareholderId}-${shareholder.level || 0}`;

          console.log(`📍 创建节点:`, {
            id: nodeId,
            name: displayName,
            level: shareholder.level,
            proxyType: (shareholder as any).proxyType,
            isProxy: shareholder.isProxy
          });

          positions.push({
            id: nodeId,
            x: node.x - config.nodeWidth / 2,  // 居中对齐
            y: node.y - config.nodeHeight / 2, // 居中对齐
            width: config.nodeWidth,
            height: config.nodeHeight,
            name: displayName,
            type: shareholder.shareholderType,
            level: shareholder.level || 0,
            isProxy: shareholder.isProxy,
            percentage: shareholder.percentage,
            collapsed: collapsedNodes.has(nodeId),
            children: [], // 可以根据需要添加子节点信息
            proxyType: (shareholder as any).proxyType, // 代持关系类型
            proxyRelationId: (shareholder as any).proxyRelationId // 代持关系ID
          });
        });
      }

      // 下游投资树（downward方向）
      if (equityData.downstreamInvestments && equityData.downstreamInvestments.length > 0) {
        const downstreamNodes = calculateTreeNodes(equityData.downstreamInvestments, 'downward');

        // 应用安全的水平平均分布
        const alignedDownstreamNodes = applySafeHorizontalDistribution(downstreamNodes, 'downward');

        alignedDownstreamNodes.forEach((node) => {
          // node对象包含了原始的投资数据
          const investment = node;

          positions.push({
            id: `company-${investment.investeeId || investment.id}-${investment.level || 0}`,
            x: node.x - config.nodeWidth / 2,  // 居中对齐
            y: node.y - config.nodeHeight / 2, // 居中对齐
            width: config.nodeWidth,
            height: config.nodeHeight,
            name: investment.investeeName || investment.investorName || '未知公司',
            type: 'company',
            level: investment.level || 1,
            percentage: investment.percentage
          });
        });
      }
    } else if (version === 'legal') {
      // 法务版：简化的代持关系处理

      // 上游股东树（upward方向）
      if (equityData.upstreamShareholders && equityData.upstreamShareholders.length > 0) {
        // 法务版：只显示代持者，不显示被代持者，不显示代持标记
        const legalShareholders = equityData.upstreamShareholders.map(shareholder => {
          if (shareholder.isProxy && shareholder.actualShareholderId && shareholder.actualShareholderName) {
            // 代持关系：只保留代持者，去掉代持标记
            return {
              ...shareholder,
              isProxy: false, // 法务版不显示代持标记
              proxyType: undefined // 不设置代持类型
            };
          } else {
            // 非代持关系，直接返回
            return shareholder;
          }
        });

        // 过滤掉被收缩节点的子节点
        const visibleShareholders = legalShareholders.filter(shareholder => {
          const parentNodeId = `${shareholder.shareholderType}-${shareholder.shareholderId}-${(shareholder.level || 0) - 1}`;
          return !collapsedNodes.has(parentNodeId);
        });

        console.log(`📊 法务版处理后的股东数量: ${legalShareholders.length}`);
        legalShareholders.forEach((s, i) => {
          console.log(`  ${i + 1}. ${s.shareholderName} (Level ${s.level})`);
        });

        const upstreamNodes = calculateTreeNodes(visibleShareholders, 'upward');

        // 应用安全的水平平均分布
        const alignedUpstreamNodes = applySafeHorizontalDistribution(upstreamNodes, 'upward');

        alignedUpstreamNodes.forEach((node) => {
          const shareholder = node;
          const displayName = shareholder.shareholderName;
          const nodeId = `${shareholder.shareholderType}-${shareholder.shareholderId}-${shareholder.level || 0}`;

          console.log(`📍 法务版创建节点:`, {
            id: nodeId,
            name: displayName,
            level: shareholder.level,
            isProxy: false // 法务版不显示代持标记
          });

          positions.push({
            id: nodeId,
            x: node.x - config.nodeWidth / 2,
            y: node.y - config.nodeHeight / 2,
            width: config.nodeWidth,
            height: config.nodeHeight,
            name: displayName,
            type: shareholder.shareholderType,
            level: shareholder.level || 0,
            isProxy: false, // 法务版不显示代持标记
            percentage: shareholder.percentage,
            collapsed: collapsedNodes.has(nodeId),
            children: []
          });
        });
      }

      // 下游投资树（downward方向）- 与投资者版相同
      if (equityData.downstreamInvestments && equityData.downstreamInvestments.length > 0) {
        const downstreamNodes = calculateTreeNodes(equityData.downstreamInvestments, 'downward');
        const alignedDownstreamNodes = applySafeHorizontalDistribution(downstreamNodes, 'downward');

        alignedDownstreamNodes.forEach((node) => {
          const investment = node;

          positions.push({
            id: `company-${investment.investeeId || investment.id}-${investment.level || 0}`,
            x: node.x - config.nodeWidth / 2,
            y: node.y - config.nodeHeight / 2,
            width: config.nodeWidth,
            height: config.nodeHeight,
            name: investment.investeeName || investment.investorName || '未知公司',
            type: 'company',
            level: investment.level || 1,
            percentage: investment.percentage
          });
        });
      }
    } else {
      // 法务版：多个中心公司
      const centerCompanies = selectedCompanyIds.map(id => 
        companies.find(c => c.id === id)
      ).filter(Boolean) as Company[];

      centerCompanies.forEach((company, index) => {
        const totalWidth = (centerCompanies.length - 1) * config.nodeSpace;
        const startX = config.centralWidth - totalWidth / 2;
        const x = Math.max(60, startX + index * config.nodeSpace);

        positions.push({
          id: `company-${company.id}`,
          x: x - config.rootRectWidth / 2,
          y: config.centralHeight - config.nodeHeight / 2,
          width: config.rootRectWidth,
          height: config.nodeHeight,
          name: company.name,
          type: 'company',
          isCenter: true
        });
      });
    }

    return positions;
  };

  // 处理代持关系 - 根据版本创建不同的显示方式
  const processProxyRelationships = (shareholders: ShareholderNode[], version: string): ShareholderNode[] => {
    const processedShareholders: ShareholderNode[] = [];

    shareholders.forEach(shareholder => {
      if (shareholder.isProxy && shareholder.actualShareholderId && shareholder.actualShareholderName) {
        if (version === 'legal') {
          // 法务版：只显示代持者，不显示被代持者，不显示代持标记
          const legalProxyNode: ShareholderNode = {
            ...shareholder,
            // 保持代持者的原始信息和层级，但不标记为代持
            isProxy: false, // 法务版不显示代持标记
            proxyType: undefined // 不设置代持类型
          };

          processedShareholders.push(legalProxyNode);

          console.log(`🏛️ 法务版代持处理: 只显示代持者 ${legalProxyNode.shareholderName} (Level ${legalProxyNode.level})`);
        } else {
          // 投资者版：创建代持者和被代持者两个节点

          // 1. 代持者节点（保持原层级）
          const proxyNode: ShareholderNode = {
            ...shareholder,
            // 保持代持者的原始信息和层级
            isProxy: true,
            proxyType: 'proxy' as any // 标记为代持者
          };

          // 2. 被代持者节点（在上一层级）
          const actualNode: ShareholderNode = {
            ...shareholder,
            id: shareholder.actualShareholderId * 10000 + shareholder.shareholderId, // 生成唯一ID
            shareholderId: shareholder.actualShareholderId,
            shareholderName: shareholder.actualShareholderName,
            level: (shareholder.level || 0) + 1, // 被代持者在上一层级（王五在Level 1）
            isProxy: false,
            proxyType: 'actual' as any, // 标记为被代持者
            proxyRelationId: shareholder.shareholderId as any // 关联到代持者
          };

          processedShareholders.push(proxyNode, actualNode);

          console.log(`🔗 投资者版代持关系处理:`, {
            proxy: `${proxyNode.shareholderName} (Level ${proxyNode.level}, ID: ${proxyNode.shareholderId})`,
            actual: `${actualNode.shareholderName} (Level ${actualNode.level}, ID: ${actualNode.shareholderId})`,
            percentage: shareholder.percentage,
            proxyNodeId: proxyNode.id,
            actualNodeId: actualNode.id
          });
        }
      } else {
        // 非代持关系，直接添加
        processedShareholders.push(shareholder);
      }
    });

    return processedShareholders;
  };

  // 合并同一个人的重复记录
  const mergeDuplicatePersons = (shareholders: ShareholderNode[]): ShareholderNode[] => {
    const personMap = new Map<number, ShareholderNode>();
    const companyList: ShareholderNode[] = [];

    shareholders.forEach(shareholder => {
      if (shareholder.shareholderType === 'person' && !(shareholder as any).proxyType) {
        // 只合并非代持关系的个人股东
        const personId = shareholder.actualShareholderId || shareholder.shareholderId;
        const personName = shareholder.actualShareholderName || shareholder.shareholderName;

        if (personMap.has(personId)) {
          // 合并持股比例
          const existing = personMap.get(personId)!;
          const existingPercentage = parseFloat(existing.percentage.toString());
          const currentPercentage = parseFloat(shareholder.percentage.toString());
          existing.percentage = (existingPercentage + currentPercentage).toFixed(2);

          // 更新名称为实际股东名称（如果有代持关系）
          if (shareholder.actualShareholderName) {
            existing.shareholderName = shareholder.actualShareholderName;
          }
        } else {
          // 创建新的合并记录
          const mergedShareholder = {
            ...shareholder,
            shareholderId: personId,
            shareholderName: personName,
            isProxy: false // 合并后不再显示代持标识
          };
          personMap.set(personId, mergedShareholder);
        }
      } else {
        // 企业股东和代持关系节点直接添加
        companyList.push(shareholder);
      }
    });

    return [...Array.from(personMap.values()), ...companyList];
  };

  // 组织上游股东按层级 - 支持代持关系的分层显示
  const organizeUpstreamByLevels = (shareholders: ShareholderNode[]): ShareholderNode[][] => {
    // 代持关系已经在更早阶段处理，这里直接按层级分组
    const levelMap = new Map<number, ShareholderNode[]>();

    shareholders.forEach(shareholder => {
      const level = shareholder.level || 0;
      if (!levelMap.has(level)) {
        levelMap.set(level, []);
      }
      levelMap.get(level)!.push(shareholder);
    });

    // 转换为数组，按层级排序
    const levels: ShareholderNode[][] = [];
    const sortedLevels = Array.from(levelMap.keys()).sort((a, b) => a - b);

    sortedLevels.forEach(level => {
      const shareholdersAtLevel = levelMap.get(level) || [];
      // 对每一层的股东进行去重处理（但保留代持关系）
      const mergedShareholders = mergeDuplicatePersons(shareholdersAtLevel);
      levels.push(mergedShareholders);
    });

    return levels;
  };

  // 组织下游投资按层级
  const organizeDownstreamByLevels = (investments: InvestmentNode[]): InvestmentNode[][] => {
    // 按层级分组
    const levelMap = new Map<number, InvestmentNode[]>();

    investments.forEach(investment => {
      const level = investment.level || 0;
      if (!levelMap.has(level)) {
        levelMap.set(level, []);
      }
      levelMap.get(level)!.push(investment);
    });

    // 转换为数组，按层级排序
    const levels: InvestmentNode[][] = [];
    const sortedLevels = Array.from(levelMap.keys()).sort((a, b) => a - b);

    sortedLevels.forEach(level => {
      const investmentsAtLevel = levelMap.get(level) || [];
      levels.push(investmentsAtLevel);
    });

    return levels;
  };

  // 渲染连接线 - 修复层级关系和连接点
  const renderConnections = (positions: NodePosition[]) => {
    if (!equityData || (version !== 'investor' && version !== 'legal')) return null;

    const connections: JSX.Element[] = [];
    const renderedConnections = new Set(); // 用于去重

    console.log('🔗 开始渲染连接线...');
    console.log('📍 所有节点位置:', positions.map(p => ({ id: p.id, name: p.name, level: p.level })));
    console.log('📊 股权数据:', equityData);

    // 渲染代持关系连接线 - 虚线连接代持者到被持股公司，实线连接被代持者到代持者
    equityData.upstreamShareholders?.forEach((shareholder) => {
      if (shareholder.isProxy && shareholder.actualShareholderId && shareholder.actualShareholderName) {
        // 查找代持者节点
        const proxyPos = positions.find(p =>
          p.id === `${shareholder.shareholderType}-${shareholder.shareholderId}-${shareholder.level || 0}`
        );

        // 查找被代持者节点（在下一层级）
        const actualPos = positions.find(p =>
          p.proxyType === 'actual' && p.proxyRelationId === shareholder.shareholderId
        );

        // 查找被持股公司（目标公司）
        const targetCompanyPos = shareholder.companyName === equityData.centerCompany?.name
          ? positions.find(p => p.isCenter)
          : positions.find(p => p.name === shareholder.companyName);

        if (proxyPos && targetCompanyPos) {
          // 1. 代持者到被持股公司的连接线（根据版本使用不同样式）
          const proxyConnectionKey = `proxy-${shareholder.shareholderId}-${shareholder.level}-to-company`;
          if (!renderedConnections.has(proxyConnectionKey)) {
            renderedConnections.add(proxyConnectionKey);

            const startX = proxyPos.x + proxyPos.width / 2;
            const startY = proxyPos.y + proxyPos.height;
            const endX = targetCompanyPos.x + targetCompanyPos.width / 2;
            const endY = targetCompanyPos.y;

            const pathData = drawRectangularConnection({
              source: { x: startX, y: startY },
              target: { x: endX, y: endY }
            });

            const midY = startY + (endY - startY) / 2;

            // 根据版本决定连接线样式
            const isLegalVersion = version === 'legal';
            const lineStyle = isLegalVersion
              ? { stroke: "#7A9EFF", strokeWidth: "1", strokeDasharray: undefined } // 法务版：正常蓝色实线
              : { stroke: "#FF6B6B", strokeWidth: "1", strokeDasharray: "5,5" };    // 投资者版：红色虚线

            const textStyle = isLegalVersion
              ? { fill: "#7A9EFF", text: `${Number(shareholder.percentage).toFixed(2)}%` }  // 法务版：正常持股比例
              : { fill: "#FF6B6B", text: `代持 ${Number(shareholder.percentage).toFixed(2)}%` }; // 投资者版：代持标识

            connections.push(
              <g key={proxyConnectionKey}>
                <path
                  d={pathData}
                  stroke={lineStyle.stroke}
                  strokeWidth={lineStyle.strokeWidth}
                  strokeDasharray={lineStyle.strokeDasharray}
                  fill="none"
                  markerEnd="url(#arrowhead)"
                />
                <text
                  x={(startX + endX) / 2}
                  y={midY - 5}
                  textAnchor="middle"
                  fontSize="10"
                  fill={textStyle.fill}
                  fontWeight="normal"
                >
                  {textStyle.text}
                </text>
              </g>
            );
          }
        }

        // 2. 被代持者到代持者的连接线（仅在投资者版显示）
        if (actualPos && proxyPos && version === 'investor') {
          const actualConnectionKey = `actual-${shareholder.actualShareholderId}-${(shareholder.level || 0) + 1}-to-proxy`;
          if (!renderedConnections.has(actualConnectionKey)) {
            renderedConnections.add(actualConnectionKey);

            const startX = actualPos.x + actualPos.width / 2;
            const startY = actualPos.y + actualPos.height;
            const endX = proxyPos.x + proxyPos.width / 2;
            const endY = proxyPos.y;

            const pathData = drawRectangularConnection({
              source: { x: startX, y: startY },
              target: { x: endX, y: endY }
            });

            const midY = startY + (endY - startY) / 2;

            connections.push(
              <g key={actualConnectionKey}>
                <path
                  d={pathData}
                  stroke="#7A9EFF"
                  strokeWidth="1"
                  fill="none"
                  markerEnd="url(#arrowhead)"
                />
                <text
                  x={(startX + endX) / 2}
                  y={midY - 5}
                  textAnchor="middle"
                  fontSize="10"
                  fill="#7A9EFF"
                  fontWeight="normal"
                >
                  实际持股
                </text>
              </g>
            );
          }
        }
      }
    });

    // 渲染上游连接线 - 基于层级的股权穿透逻辑
    equityData.upstreamShareholders?.forEach((shareholder) => {
      // 在投资者版中跳过代持关系（因为已经单独处理）
      if (shareholder.isProxy && version === 'investor') {
        return;
      }

      // 在法务版中，代持者作为普通股东处理，正常渲染连接线

      const shareholderPos = positions.find(p =>
        p.id === `${shareholder.shareholderType}-${shareholder.shareholderId}-${shareholder.level || 0}`
      );

      if (shareholderPos) {
        let targetPos;

        // 🎯 根据数据库中的实际投资关系确定连接目标
        console.log(`🔍 分析股东 ${shareholder.shareholderName}:`, {
          companyName: shareholder.companyName,
          level: shareholder.level,
          percentage: shareholder.percentage,
          shareholderType: shareholder.shareholderType
        });

        // 查找股东投资的目标公司
        if (shareholder.companyName === equityData.centerCompany?.name) {
          // 直接投资中心公司
          targetPos = positions.find(p => p.isCenter);
          console.log(`  -> 直接投资中心公司: ${equityData.centerCompany?.name}`);
        } else {
          // 投资其他公司，查找该公司在股东列表中的节点
          // 根据companyName查找目标公司作为股东的节点
          const targetShareholder = equityData.upstreamShareholders.find(s =>
            s.shareholderName === shareholder.companyName &&
            s.shareholderType === 'company'
          );

          if (targetShareholder) {
            targetPos = positions.find(p =>
              p.id === `${targetShareholder.shareholderType}-${targetShareholder.shareholderId}-${targetShareholder.level || 0}`
            );
            console.log(`  -> 投资股东公司: ${targetShareholder.shareholderName}`, targetPos ? '✅找到' : '❌未找到');
          } else {
            // 如果找不到对应的股东，可能是投资中心公司
            targetPos = positions.find(p => p.isCenter);
            console.log(`  -> 未找到目标公司，连接到中心公司`);
          }
        }

        console.log(`🔗 连接 ${shareholder.shareholderName} (level ${shareholder.level}) -> ${targetPos?.name || 'NOT FOUND'}`);

        if (!targetPos) {
          console.warn(`❌ 找不到股东 ${shareholder.shareholderName} (level: ${shareholder.level}) 的连接目标`);
          return;
        }

        if (!targetPos) {
          console.warn(`找不到股东 ${shareholder.shareholderName} (level: ${shareholder.level}) 的连接目标`);
          return;
        }

        // 连接点：从股东节点底部到目标公司顶部
        const startX = shareholderPos.x + shareholderPos.width / 2;
        const startY = shareholderPos.y + shareholderPos.height; // 从底部出
        const endX = targetPos.x + targetPos.width / 2;
        const endY = targetPos.y; // 到顶部进

        // 基于reference.md的直角连接线算法
        const pathData = drawRectangularConnection({
          source: { x: startX, y: startY },
          target: { x: endX, y: endY }
        });

        // 计算文本标签位置 - 放在垂直线段上
        const verticalSegmentX = startX; // 垂直线段的X坐标
        const verticalSegmentY = startY + 30; // 垂直线段上的位置

        // 生成唯一的key，避免重复
        const connectionKey = `upstream-${shareholder.shareholderType}-${shareholder.shareholderId || shareholder.id}-${shareholder.level}-${shareholder.shareholderName.replace(/\s+/g, '')}`;

        // 检查是否已经渲染过这个连接
        if (renderedConnections.has(connectionKey)) {
          console.log(`⚠️ 跳过重复连接: ${connectionKey}`);
          return;
        }
        renderedConnections.add(connectionKey);

        connections.push(
          <g
            key={connectionKey}
            style={{
              transition: 'all 0.5s ease-in-out',
              opacity: 1
            }}
          >
            <path
              className="equity-connection"
              d={pathData}
              stroke="#7A9EFF"
              strokeWidth="1"
              fill="none"
              markerEnd="url(#arrowhead)"
            />
            {/* 持股比例显示在垂直线段上 */}
            <text
              x={verticalSegmentX}
              y={verticalSegmentY}
              textAnchor="middle"
              fontSize="10"
              fill="#7A9EFF"
              fontWeight="normal"
            >
              {shareholder.percentage ? `${Number(shareholder.percentage).toFixed(2)}%` : '0.00%'}
            </text>
          </g>
        );
      }
    });

    // 渲染下游连接线（投资公司 -> 被投资公司）- 修复多余连接线问题
    equityData.downstreamInvestments?.forEach((investment) => {
      const investmentPos = positions.find(p =>
        p.id === `company-${investment.investeeId || investment.id}-${investment.level || 0}`
      );

      if (investmentPos) {
        // 找到源公司（投资方）- 只渲染正确的投资关系
        let sourcePos;

        console.log(`🔍 分析投资关系:`, {
          investorName: investment.investorName,
          investeeName: investment.investeeName,
          level: investment.level,
          percentage: investment.percentage
        });

        // 根据投资方名称精确匹配源节点
        if (investment.investorName) {
          // 优先查找投资方对应的节点
          sourcePos = positions.find(p => p.name === investment.investorName);

          // 如果找不到，可能是中心公司
          if (!sourcePos && investment.investorName === equityData.centerCompany?.name) {
            sourcePos = positions.find(p => p.isCenter);
          }

          console.log(`📊 投资关系: ${investment.investorName} -> ${investment.investeeName}`,
                     sourcePos ? '✅找到投资方' : '❌未找到投资方');
        } else {
          // 如果没有投资方名称，跳过这个连接（避免错误连接）
          console.log(`⚠️ 跳过无投资方信息的投资: ${investment.investeeName}`);
          return;
        }

        // 只有找到正确的投资方才渲染连接线
        if (sourcePos) {
          // 连接点：从投资公司底部到被投资公司顶部
          const startX = sourcePos.x + sourcePos.width / 2;
          const startY = sourcePos.y + sourcePos.height; // 从底部出
          const endX = investmentPos.x + investmentPos.width / 2;
          const endY = investmentPos.y; // 到顶部进

          // 基于reference.md的直角连接线算法
          const pathData = drawRectangularConnection({
            source: { x: startX, y: startY },
            target: { x: endX, y: endY }
          });

          // 计算文本标签位置 - 放在靠近箭头的垂直线段上
          const verticalSegmentX = endX; // 靠近目标的垂直线段X坐标
          const verticalSegmentY = endY - 30; // 靠近箭头的垂直线段位置

          // 生成唯一的key，避免重复
          const downstreamKey = `downstream-${investment.investeeId || investment.id}-${investment.level}-${investment.investeeName?.replace(/\s+/g, '') || 'unknown'}`;

          // 检查是否已经渲染过这个连接
          if (renderedConnections.has(downstreamKey)) {
            console.log(`⚠️ 跳过重复下游连接: ${downstreamKey}`);
            return;
          }
          renderedConnections.add(downstreamKey);

          connections.push(
            <g
              key={downstreamKey}
              style={{
                transition: 'all 0.5s ease-in-out',
                opacity: 1
              }}
            >
              <path
                className="equity-connection"
                d={pathData}
                stroke="#7A9EFF"
                strokeWidth="1"
                fill="none"
                markerEnd="url(#arrowhead)"
              />
              {/* 持股比例显示在靠近箭头的垂直线段上 */}
              <text
                x={verticalSegmentX}
                y={verticalSegmentY}
                textAnchor="middle"
                fontSize="10"
                fill="#7A9EFF"
                fontWeight="normal"
              >
                {investment.percentage}%
              </text>
            </g>
          );
        } else {
          // 找不到投资方，记录日志但不渲染连接线
          console.log(`❌ 找不到投资方节点: ${investment.investorName} -> ${investment.investeeName}`);
        }
      } else {
        console.log(`❌ 找不到被投资方节点: ${investment.investeeName}`);
      }
    });

    return connections;
  };

  // 渲染节点 - 基于reference.md的优化设计
  const renderNodes = (positions: NodePosition[]) => {
    return positions.map((pos) => {
      // 根据节点类型确定样式
      const nodeStyle = {
        fill: pos.isCenter ? '#128bed' : '#ffffff',
        stroke: pos.isCenter ? '#128bed' : pos.type === 'person' ? '#FF6B6B' : '#7A9EFF',
        strokeWidth: pos.isCenter ? 2 : 1,
        textColor: pos.isCenter ? '#ffffff' : '#000000'
      };

      // 计算文本位置
      const textX = pos.x + pos.width / 2;
      const textY = pos.y + pos.height / 2;

      // 处理长文本
      const displayName = pos.name && pos.name.length > 11
        ? pos.name.substring(0, 11) + '...'
        : (pos.name || '未知');

      return (
        <g
          key={pos.id}
          className="equity-node equity-node-enter"
          style={{
            cursor: 'pointer'
          }}
        >
          {/* 节点主体 - 圆角矩形设计 */}
          <rect
            x={pos.x}
            y={pos.y}
            width={pos.width}
            height={pos.height}
            fill={nodeStyle.fill}
            stroke={nodeStyle.stroke}
            strokeWidth={nodeStyle.strokeWidth}
            rx="5" // 圆角半径
            ry="5"
            style={{
              filter: pos.isCenter ? 'drop-shadow(2px 2px 4px rgba(0,0,0,0.1))' : 'none'
            }}
            onClick={() => {
              if (pos.isCenter) {
                console.log('点击中心公司:', pos.name);
              } else {
                console.log('点击节点:', pos.name, pos.type);
              }
            }}
          />

          {/* 节点名称 - 优化字体和布局 */}
          <text
            x={textX}
            y={pos.isCenter ? textY - 2 : textY}
            textAnchor="middle"
            dominantBaseline="middle"
            fontSize={pos.isCenter ? "14" : "12"}
            fill={nodeStyle.textColor}
            fontWeight={pos.isCenter ? "bold" : "normal"}
            fontFamily="'Microsoft YaHei', '微软雅黑', sans-serif"
          >
            {displayName}
          </text>



          {/* 代持关系标识 - 区分代持者和被代持者 */}
          {pos.proxyType === 'proxy' && (
            <g>
              <rect
                x={pos.x + pos.width - 25}
                y={pos.y + 2}
                width="20"
                height="12"
                fill="#FF6B6B"
                rx="2"
                ry="2"
              />
              <text
                x={pos.x + pos.width - 15}
                y={pos.y + 8}
                textAnchor="middle"
                dominantBaseline="middle"
                fontSize="8"
                fill="#ffffff"
                fontWeight="bold"
              >
                代持
              </text>
            </g>
          )}

          {pos.proxyType === 'actual' && (
            <g>
              <rect
                x={pos.x + pos.width - 30}
                y={pos.y + 2}
                width="25"
                height="12"
                fill="#52C41A"
                rx="2"
                ry="2"
              />
              <text
                x={pos.x + pos.width - 17.5}
                y={pos.y + 8}
                textAnchor="middle"
                dominantBaseline="middle"
                fontSize="8"
                fill="#ffffff"
                fontWeight="bold"
              >
                实际
              </text>
            </g>
          )}

          {/* 展开/收缩按钮 - 为有子节点的节点添加 */}
          {pos.children && pos.children.length > 0 && (
            <g
              style={{ cursor: 'pointer' }}
              onClick={(e) => {
                e.stopPropagation();
                handleNodeToggle(pos.id);
              }}
            >
              <circle
                cx={pos.x + pos.width / 2}
                cy={pos.y + pos.height + 8}
                r="8"
                fill="#7A9EFF"
                stroke="#ffffff"
                strokeWidth="1"
              />
              <text
                x={pos.x + pos.width / 2}
                y={pos.y + pos.height + 13}
                textAnchor="middle"
                dominantBaseline="middle"
                fontSize="12"
                fill="#ffffff"
                fontWeight="bold"
              >
                {collapsedNodes.has(pos.id) ? "+" : "-"}
              </text>
            </g>
          )}
        </g>
      );
    });
  };

  // 处理公司筛选变化
  const handleCompanyFilterChange = (companyIds: number[]) => {
    const hiddenSet = new Set<number>();
    equityData?.relatedCompanies?.forEach(company => {
      if (!companyIds.includes(company.id)) {
        hiddenSet.add(company.id);
      }
    });
    setHiddenCompanies(hiddenSet);
  };



  const positions = calculateNodePositions();

  // 调试信息
  console.log('🔍 股权图调试信息:', {
    equityData,
    positions,
    version,
    selectedCompanyId,
    loading,
    error
  });

  // 动态计算SVG画布尺寸 - 修复上游股东显示不完整问题
  const calculateCanvasSize = () => {
    if (!equityData || positions.length === 0) {
      return { width: 2400, height: 2000 }; // 增加默认高度
    }

    const maxX = Math.max(...positions.map(p => p.x + p.width));
    const maxY = Math.max(...positions.map(p => p.y + p.height));
    const minX = Math.min(...positions.map(p => p.x));
    const minY = Math.min(...positions.map(p => p.y));

    // 特别处理负Y坐标（上游股东可能在画布顶部之外）
    const paddingX = 300; // 水平边距
    const paddingY = 300; // 增加垂直边距，特别是顶部

    // 如果有负Y坐标，需要调整画布起始位置
    const adjustedMinY = Math.min(0, minY); // 确保包含负Y坐标
    const totalHeight = maxY - adjustedMinY + paddingY * 2;

    const width = Math.max(2400, maxX + paddingX, Math.abs(minX) + maxX + paddingX * 2);
    const height = Math.max(2000, totalHeight);

    console.log('📐 画布尺寸计算（修复上游截断）:', {
      positions: positions.length,
      bounds: { minX, maxX, minY, maxY },
      adjustedMinY,
      totalHeight,
      calculated: { width, height },
      padding: { paddingX, paddingY },
      upstreamNodes: positions.filter(p => p.y < 500).map(p => ({ name: p.name, y: p.y }))
    });

    return { width, height };
  };

  const canvasSize = calculateCanvasSize();

  // 计算viewBox以处理负Y坐标
  const calculateViewBox = () => {
    if (positions.length === 0) {
      return "0 0 2400 2000";
    }

    const minX = Math.min(...positions.map(p => p.x));
    const minY = Math.min(...positions.map(p => p.y));
    const maxX = Math.max(...positions.map(p => p.x + p.width));
    const maxY = Math.max(...positions.map(p => p.y + p.height));

    // 添加边距
    const padding = 300;
    const viewBoxX = Math.min(0, minX - padding);
    const viewBoxY = Math.min(0, minY - padding);
    const viewBoxWidth = Math.max(canvasSize.width, maxX - viewBoxX + padding);
    const viewBoxHeight = Math.max(canvasSize.height, maxY - viewBoxY + padding);

    console.log('📐 ViewBox计算:', {
      bounds: { minX, minY, maxX, maxY },
      viewBox: `${viewBoxX} ${viewBoxY} ${viewBoxWidth} ${viewBoxHeight}`
    });

    return `${viewBoxX} ${viewBoxY} ${viewBoxWidth} ${viewBoxHeight}`;
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '16px' }}>
          <h2>股权图-主体公司</h2>
          
          {/* 版本选择 */}
          <div style={{ marginBottom: '16px' }}>
            <label style={{ marginRight: '8px' }}>选择版本：</label>
            <Select
              style={{ width: 200 }}
              value={version}
              onChange={(value) => {
                setVersion(value);
                setSelectedCompanyId(null);
                setSelectedCompanyIds([]);
                setEquityData(null);
              }}
            >
              <Option value="investor">投资者版</Option>
              <Option value="legal">法务版</Option>
            </Select>
          </div>

          {/* 公司选择下拉框 */}
          <div style={{ marginBottom: '16px' }}>
            <label style={{ marginRight: '8px' }}>选择主体公司：</label>
            {version === 'investor' ? (
              <Select
                style={{ width: 300 }}
                placeholder="请选择公司"
                value={selectedCompanyId}
                onChange={setSelectedCompanyId}
                showSearch
                filterOption={(input, option) =>
                  option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
                }
              >
                {companies.map(company => (
                  <Option key={company.id} value={company.id}>
                    {company.name}
                  </Option>
                ))}
              </Select>
            ) : (
              <Select
                style={{ width: 300 }}
                placeholder="请选择公司"
                value={selectedCompanyId}
                onChange={setSelectedCompanyId}
                showSearch
                filterOption={(input, option) =>
                  option?.children?.toString().toLowerCase().includes(input.toLowerCase()) ?? false
                }
              >
                {companies.map(company => (
                  <Option key={company.id} value={company.id}>
                    {company.name}
                  </Option>
                ))}
              </Select>
            )}
          </div>

          {/* 控制按钮组 */}
          <div style={{ marginBottom: '16px' }}>
            <div style={{ display: 'flex', gap: '16px', alignItems: 'center', flexWrap: 'wrap' }}>

              {/* 缩放控制 */}
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <span style={{ fontSize: '14px', color: '#666' }}>缩放:</span>
                <Button
                  size="small"
                  onClick={() => setZoomLevel(prev => Math.max(0.5, prev - 0.1))}
                  disabled={zoomLevel <= 0.5}
                >
                  -
                </Button>
                <span style={{ minWidth: '50px', textAlign: 'center', fontSize: '12px' }}>
                  {Math.round(zoomLevel * 100)}%
                </span>
                <Button
                  size="small"
                  onClick={() => setZoomLevel(prev => Math.min(2, prev + 0.1))}
                  disabled={zoomLevel >= 2}
                >
                  +
                </Button>
                <Button
                  size="small"
                  onClick={() => setZoomLevel(1)}
                  type="link"
                >
                  重置
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* 加载状态 */}
        {loading && (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" />
            <div style={{ marginTop: '16px' }}>正在加载股权图数据...</div>
          </div>
        )}

        {/* 错误状态 */}
        {error && (
          <Alert
            message="加载失败"
            description={error}
            type="error"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        )}

        {/* 无数据提示 */}
        {!loading && !error && !equityData && (
          <div style={{
            textAlign: 'center',
            padding: '50px',
            backgroundColor: '#fafafa',
            border: '1px solid #d9d9d9',
            borderRadius: '4px'
          }}>
            <div style={{ fontSize: '16px', color: '#666', marginBottom: '8px' }}>
              📊 请选择公司查看股权关系图
            </div>
            <div style={{ fontSize: '14px', color: '#999' }}>
              {version === 'investor' ? '选择一个公司查看其股权结构' : '选择一个公司查看其法务版股权关系'}
            </div>
          </div>
        )}

        {/* 股权图显示 */}
        {!loading && !error && equityData && (
          <div style={{
            border: '1px solid #d9d9d9',
            borderRadius: '4px',
            backgroundColor: '#fafafa',
            width: '100%',
            height: '800px', // 固定高度，允许滚动
            position: 'relative',
            overflow: 'auto', // 允许滚动查看完整内容
            display: 'block'
          }}>
            <svg
              width={canvasSize.width}
              height={canvasSize.height}
              viewBox={calculateViewBox()}
              style={{
                display: 'block',
                transform: `scale(${zoomLevel})`,
                transformOrigin: 'top left'
              }}
            >
              {/* 定义箭头标记和动画 */}
              <defs>
                {/* 箭头标记 */}
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon
                    points="0 0, 10 3.5, 0 7"
                    fill="#7A9EFF"
                  />
                </marker>

                {/* 动画定义 - 基于reference.md的过渡效果 */}
                <style>
                  {`
                    .equity-node {
                      transition: all 0.5s ease-in-out;
                      transform-origin: center;
                    }

                    .equity-connection {
                      transition: all 0.5s ease-in-out;
                      stroke-dasharray: 1000;
                      stroke-dashoffset: 1000;
                      animation: drawLine 0.8s ease-in-out forwards;
                    }

                    @keyframes drawLine {
                      to {
                        stroke-dashoffset: 0;
                      }
                    }

                    .equity-node-enter {
                      opacity: 0;
                      transform: scale(0.1);
                      animation: nodeEnter 0.5s ease-out forwards;
                    }

                    @keyframes nodeEnter {
                      to {
                        opacity: 1;
                        transform: scale(1);
                      }
                    }

                    .equity-node-exit {
                      animation: nodeExit 0.3s ease-in forwards;
                    }

                    @keyframes nodeExit {
                      to {
                        opacity: 0;
                        transform: scale(0.1);
                      }
                    }
                  `}
                </style>
              </defs>

              {/* 渲染连接线 */}
              {renderConnections(positions)}

              {/* 渲染节点 */}
              {renderNodes(positions)}
            </svg>



            {/* 图例说明 - 固定在视口左下角 */}
            <div style={{
              position: 'fixed',
              bottom: '20px',
              left: '20px',
              backgroundColor: '#fff',
              padding: '12px',
              border: '1px solid #d9d9d9',
              borderRadius: '6px',
              fontSize: '11px',
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              maxWidth: '180px',
              zIndex: 1000
            }}>
              <div style={{ marginBottom: '12px', fontWeight: 'bold', fontSize: '14px' }}>图例说明</div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '20px',
                  height: '3px',
                  backgroundColor: '#1890ff',
                  marginRight: '8px'
                }}></div>
                <span>股东持股关系</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '20px',
                  height: '3px',
                  backgroundColor: '#52c41a',
                  marginRight: '8px'
                }}></div>
                <span>对外投资关系</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '16px',
                  backgroundColor: '#1890ff',
                  borderRadius: '3px',
                  marginRight: '8px',
                  border: '2px solid #1890ff'
                }}></div>
                <span>中心公司</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '16px',
                  backgroundColor: '#f6ffed',
                  borderRadius: '3px',
                  marginRight: '8px',
                  border: '2px solid #52c41a'
                }}></div>
                <span>个人股东</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '16px',
                  backgroundColor: '#fff',
                  borderRadius: '3px',
                  marginRight: '8px',
                  border: '2px solid #d9d9d9'
                }}></div>
                <span>企业主体</span>
              </div>

              <div style={{ marginBottom: '8px', display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '12px',
                  backgroundColor: '#ff4d4f',
                  borderRadius: '2px',
                  marginRight: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '8px'
                }}>代</div>
                <span>代持关系</span>
              </div>

              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '16px',
                  height: '16px',
                  backgroundColor: '#52c41a',
                  borderRadius: '50%',
                  marginRight: '8px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: 'white',
                  fontSize: '10px'
                }}>↑</div>
                <span>展开/收起按钮</span>
              </div>
            </div>
          </div>
        )}
      </Card>
    </div>
  );
};

export default EquityChart;
