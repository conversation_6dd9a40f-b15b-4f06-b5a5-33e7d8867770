import React from 'react';

const CompanyAddBasic: React.FC = () => {
  return (
    <div style={{ padding: '20px', backgroundColor: '#f0f0f0', minHeight: '100vh' }}>
      <h1 style={{ color: '#1890ff', fontSize: '24px' }}>🎉 测试页面加载成功！</h1>
      <p style={{ fontSize: '16px', marginTop: '20px' }}>
        如果您能看到这个页面，说明路由和基本渲染都正常工作。
      </p>
      <div style={{ 
        backgroundColor: 'white', 
        padding: '20px', 
        borderRadius: '8px', 
        marginTop: '20px',
        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
      }}>
        <h2>基本信息</h2>
        <p>这是一个最简单的React组件，用于测试页面是否能正常显示。</p>
        <button 
          style={{
            backgroundColor: '#1890ff',
            color: 'white',
            border: 'none',
            padding: '10px 20px',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
          onClick={() => alert('按钮点击成功！')}
        >
          点击测试
        </button>
      </div>
    </div>
  );
};

export default CompanyAddBasic;
