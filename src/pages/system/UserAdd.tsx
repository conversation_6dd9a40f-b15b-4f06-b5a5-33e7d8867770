import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Space,
  Typography,
  message,
  Divider,
  Avatar,
  Upload
} from 'antd';
import {
  UserOutlined,
  ArrowLeftOutlined,
  SaveOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';

const { Title, Text } = Typography;
const { Option } = Select;

interface UserFormData {
  username: string;
  password: string;
  confirmPassword: string;
  realName: string;
  email: string;
  phone: string;
  department: string;
  position: string;
  userType: string;
  status: string;
}

const UserAdd: React.FC = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 部门选项
  const departments = [
    '管理部', '财务部', '法务部', '投资部', '人力资源部', '信息技术部', '审计部'
  ];

  // 职位选项
  const positions = [
    '总经理', '副总经理', '部门经理', '主管', '专员', '助理', '实习生'
  ];

  // 用户类型选项
  const userTypes = [
    { value: '1', label: '系统管理员', description: '可以操作系统设置-新增用户、用户管理、数据库配置等三个页面，可以查看其他所有页面，但不可操作' },
    { value: '2', label: '管理员', description: '可以操作除了系统设置菜单下新增用户、用户管理、数据库配置等三个页面以外的其他所有页面，但不可访问系统设置菜单' },
    { value: '3', label: '操作员', description: '初步保持和管理员一致，但会在具体个别页面根据需要进行差异化' },
    { value: '4', label: '查看员', description: '可以查看除了系统设置菜单下新增用户、用户管理、数据库配置等三个页面以外的其他所有页面，但不可进行任何操作' }
  ];



  // 提交表单
  const handleSubmit = async (values: UserFormData) => {
    try {
      setLoading(true);

      // 验证密码
      if (values.password !== values.confirmPassword) {
        message.error('两次输入的密码不一致');
        return;
      }

      // 准备提交数据
      const submitData = {
        username: values.username,
        password: values.password,
        realName: values.realName,
        email: values.email,
        phone: values.phone,
        department: values.department,
        position: values.position,
        roleId: parseInt(values.userType), // 转换为数字类型的角色ID
        status: values.status || 'active',
        createdBy: 'Admin User' // TODO: 从当前登录用户获取
      };

      console.log('📤 提交新增用户数据:', submitData);

      // 调用后端API
      const response = await axios.post('http://localhost:8080/api/users', submitData, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      });

      if (response.data.success) {
        message.success('用户创建成功！');
        navigate('/system/user/management');
      } else {
        throw new Error(response.data.message || '创建用户失败');
      }
    } catch (error: any) {
      console.error('❌ 创建用户失败:', error);
      if (error.response?.status === 409) {
        message.error('用户名已存在，请使用其他用户名');
      } else {
        message.error(`创建用户失败: ${error.message || '请稍后重试'}`);
      }
    } finally {
      setLoading(false);
    }
  };



  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/system/user/management')}
            style={{ marginRight: 12 }}
          >
            返回
          </Button>
          <div>
            <Title level={3} style={{ margin: 0 }}>
              新增用户
            </Title>
            <Text type="secondary">
              添加新的系统用户并分配相应权限
            </Text>
          </div>
        </div>
      </div>

      {/* 主表单 */}
      <Card style={{ maxWidth: 1000, margin: '0 auto' }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            status: 'active',
            userType: '4' // 默认为查看员
          }}
        >
          {/* 基本信息 */}
          <Title level={4}>基本信息</Title>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="用户名"
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' },
                  { max: 20, message: '用户名最多20个字符' },
                  { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' }
                ]}
              >
                <Input placeholder="请输入用户名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="真实姓名"
                name="realName"
                rules={[{ required: true, message: '请输入真实姓名' }]}
              >
                <Input placeholder="请输入真实姓名" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="密码"
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' }
                ]}
              >
                <Input.Password placeholder="请输入密码" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="确认密码"
                name="confirmPassword"
                rules={[
                  { required: true, message: '请确认密码' },
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (!value || getFieldValue('password') === value) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('两次输入的密码不一致'));
                    },
                  }),
                ]}
              >
                <Input.Password placeholder="请再次输入密码" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="邮箱"
                name="email"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="手机号"
                name="phone"
                rules={[
                  { required: true, message: '请输入手机号' },
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                ]}
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          {/* 职位信息 */}
          <Title level={4}>职位信息</Title>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="部门"
                name="department"
                rules={[{ required: true, message: '请选择部门' }]}
              >
                <Select placeholder="请选择部门">
                  {departments.map(dept => (
                    <Option key={dept} value={dept}>{dept}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="职位"
                name="position"
                rules={[{ required: true, message: '请选择职位' }]}
              >
                <Select placeholder="请选择职位">
                  {positions.map(pos => (
                    <Option key={pos} value={pos}>{pos}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          {/* 权限设置 */}
          <Title level={4}>权限设置</Title>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="用户类型"
                name="userType"
                rules={[{ required: true, message: '请选择用户类型' }]}
              >
                <Select placeholder="请选择用户类型">
                  {userTypes.map(userType => (
                    <Option key={userType.value} value={userType.value}>
                      <div>
                        <div>{userType.label}</div>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {userType.description}
                        </Text>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="账户状态"
                name="status"
                rules={[{ required: true, message: '请选择账户状态' }]}
              >
                <Select placeholder="请选择账户状态">
                  <Option value="active">启用</Option>
                  <Option value="inactive">禁用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* 操作按钮 */}
          <Row justify="center" style={{ marginTop: 32 }}>
            <Space size="large">
              <Button
                size="large"
                onClick={() => navigate('/system/user/management')}
              >
                取消
              </Button>
              <Button
                type="primary"
                size="large"
                htmlType="submit"
                loading={loading}
                icon={<SaveOutlined />}
              >
                创建用户
              </Button>
            </Space>
          </Row>
        </Form>
      </Card>
    </div>
  );
};

export default UserAdd;
