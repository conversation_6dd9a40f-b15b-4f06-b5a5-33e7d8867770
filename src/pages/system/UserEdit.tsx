import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Button,
  Row,
  Col,
  Space,
  Typography,
  message,
  Divider,
  Spin
} from 'antd';
import {
  UserOutlined,
  ArrowLeftOutlined,
  SaveOutlined
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { apiService } from '../../services/api-simple';

const { Title, Text } = Typography;
const { Option } = Select;

interface UserFormData {
  username: string;
  realName: string;
  email: string;
  phone: string;
  department: string;
  position: string;
  userType: string;
  status: string;
}

const UserEdit: React.FC = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [pageLoading, setPageLoading] = useState(true);
  const [userTypes, setUserTypes] = useState<any[]>([]);

  // 部门选项
  const departments = [
    '管理部', '财务部', '法务部', '投资部', '人力资源部', '信息技术部', '审计部'
  ];

  // 职位选项
  const positions = [
    '总经理', '副总经理', '部门经理', '主管', '专员', '助理', '实习生'
  ];

  // 加载用户角色
  const loadUserTypes = async () => {
    try {
      const response = await apiService.get('/api/user-roles');
      if (response.data.success) {
        const types = response.data.data.map((role: any) => ({
          value: role.id.toString(),
          label: role.name,
          description: role.description
        }));
        setUserTypes(types);
      }
    } catch (error) {
      console.error('加载用户角色失败:', error);
      message.error('加载用户角色失败');
    }
  };

  // 加载用户详情
  const loadUserDetail = async () => {
    if (!id) return;
    
    try {
      setPageLoading(true);
      // 由于后端没有单个用户详情API，我们通过用户列表API获取
      const response = await apiService.getUsers({ page: 1, pageSize: 1000 });
      
      if (response.data.success) {
        const user = response.data.data.find((u: any) => u.id.toString() === id);
        if (user) {
          form.setFieldsValue({
            username: user.username,
            realName: user.realName,
            email: user.email,
            phone: user.phone,
            department: user.department,
            position: user.position,
            userType: user.roleId.toString(),
            status: user.status
          });
        } else {
          message.error('用户不存在');
          navigate('/system/user/management');
        }
      }
    } catch (error) {
      console.error('加载用户详情失败:', error);
      message.error('加载用户详情失败');
    } finally {
      setPageLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    loadUserTypes();
    loadUserDetail();
  }, [id]);

  // 提交表单
  const handleSubmit = async (values: UserFormData) => {
    if (!id) return;
    
    try {
      setLoading(true);

      // 准备提交数据
      const submitData = {
        realName: values.realName,
        email: values.email,
        phone: values.phone,
        department: values.department,
        position: values.position,
        roleId: parseInt(values.userType),
        status: values.status
      };

      console.log('📤 提交编辑用户数据:', submitData);

      // 调用后端API
      const response = await apiService.updateUser(parseInt(id), submitData);

      if (response.data.success) {
        message.success('用户更新成功！');
        navigate('/system/user/management');
      } else {
        throw new Error(response.data.message || '更新用户失败');
      }
    } catch (error: any) {
      console.error('❌ 更新用户失败:', error);
      message.error(`更新用户失败: ${error.message || '请稍后重试'}`);
    } finally {
      setLoading(false);
    }
  };

  if (pageLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载用户信息中...</div>
      </div>
    );
  }

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/system/user/management')}
            style={{ marginRight: 12 }}
          >
            返回
          </Button>
          <div>
            <Title level={3} style={{ margin: 0 }}>
              编辑用户
            </Title>
            <Text type="secondary">
              修改用户信息和权限设置
            </Text>
          </div>
        </div>
      </div>

      {/* 主表单 */}
      <Card style={{ maxWidth: 1000, margin: '0 auto' }}>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          {/* 基本信息 */}
          <Title level={4}>基本信息</Title>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="用户名"
                name="username"
              >
                <Input placeholder="用户名" disabled />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="真实姓名"
                name="realName"
                rules={[{ required: true, message: '请输入真实姓名' }]}
              >
                <Input placeholder="请输入真实姓名" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="邮箱"
                name="email"
                rules={[
                  { required: true, message: '请输入邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input placeholder="请输入邮箱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="手机号"
                name="phone"
                rules={[
                  { required: true, message: '请输入手机号' },
                  { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                ]}
              >
                <Input placeholder="请输入手机号" />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          {/* 职位信息 */}
          <Title level={4}>职位信息</Title>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="部门"
                name="department"
                rules={[{ required: true, message: '请选择部门' }]}
              >
                <Select placeholder="请选择部门">
                  {departments.map(dept => (
                    <Option key={dept} value={dept}>{dept}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="职位"
                name="position"
                rules={[{ required: true, message: '请选择职位' }]}
              >
                <Select placeholder="请选择职位">
                  {positions.map(pos => (
                    <Option key={pos} value={pos}>{pos}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          {/* 权限设置 */}
          <Title level={4}>权限设置</Title>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="用户类型"
                name="userType"
                rules={[{ required: true, message: '请选择用户类型' }]}
              >
                <Select placeholder="请选择用户类型">
                  {userTypes.map(userType => (
                    <Option key={userType.value} value={userType.value}>
                      <div>
                        <div>{userType.label}</div>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {userType.description}
                        </Text>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="账户状态"
                name="status"
                rules={[{ required: true, message: '请选择账户状态' }]}
              >
                <Select placeholder="请选择账户状态">
                  <Option value="active">启用</Option>
                  <Option value="inactive">禁用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* 操作按钮 */}
          <Row justify="center" style={{ marginTop: 32 }}>
            <Space size="large">
              <Button
                size="large"
                onClick={() => navigate('/system/user/management')}
              >
                取消
              </Button>
              <Button
                type="primary"
                size="large"
                htmlType="submit"
                loading={loading}
                icon={<SaveOutlined />}
              >
                保存修改
              </Button>
            </Space>
          </Row>
        </Form>
      </Card>
    </div>
  );
};

export default UserEdit;
