import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  InputNumber,
  Button,
  Row,
  Col,
  Space,
  Typography,
  message,
  Al<PERSON>,
  Divider,
  Spin,
  Badge,
  Modal
} from 'antd';
// 移除图标组件导入，使用emoji替代

const { Title, Text, Paragraph } = Typography;
const { confirm } = Modal;

interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  username: string;
  password: string;
  charset: string;
  timezone: string;
  connectionLimit: number;
  acquireTimeout: number;
  timeout: number;
}

interface ConnectionStatus {
  connected: boolean;
  message: string;
  lastChecked: string;
  version?: string;
  uptime?: string;
}

const DatabaseConfig: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [configLoading, setConfigLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState<ConnectionStatus | null>(null);

  // 加载当前配置
  const loadConfig = async () => {
    try {
      setConfigLoading(true);
      console.log('📊 加载数据库配置...');

      // 暂时注释掉API调用，使用默认配置
      // const response = await axios.get('http://localhost:8080/api/database/config', {
      //   timeout: 10000
      // });

      // if (response.data.success) {
      //   const config = response.data.data;
      //   form.setFieldsValue(config);
      //   console.log('✅ 加载数据库配置成功:', config);
      // } else {
      //   throw new Error(response.data.message || '加载配置失败');
      // }

      // 设置默认配置
      form.setFieldsValue({
        host: 'localhost',
        port: 3306,
        database: 'equity_management',
        username: 'root',
        password: '',
        charset: 'utf8mb4',
        timezone: '+08:00',
        connectionLimit: 10,
        acquireTimeout: 60000,
        timeout: 60000
      });
      console.log('✅ 使用默认数据库配置');
    } catch (error: any) {
      console.error('❌ 加载数据库配置失败:', error);
      message.error('加载配置失败，将使用默认配置');

      // 设置默认配置
      form.setFieldsValue({
        host: 'localhost',
        port: 3306,
        database: 'equity_management',
        username: 'root',
        password: '',
        charset: 'utf8mb4',
        timezone: '+08:00',
        connectionLimit: 10,
        acquireTimeout: 60000,
        timeout: 60000
      });
    } finally {
      setConfigLoading(false);
    }
  };

  // 检查连接状态
  const checkConnection = async () => {
    try {
      setTestLoading(true);
      console.log('🔍 检查数据库连接状态...');

      // 调用真实的数据库状态API
      const response = await fetch('http://localhost:8080/api/test-db', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 15000
      });

      const data = await response.json();

      if (data.success) {
        setConnectionStatus({
          connected: true,
          message: data.message || '数据库连接正常',
          lastChecked: new Date().toISOString(),
          version: data.data?.version || 'MySQL',
          uptime: data.data?.uptime || '未知'
        });
        console.log('✅ 数据库连接状态检查成功:', data);
      } else {
        throw new Error(data.message || '连接检查失败');
      }
    } catch (error: any) {
      console.error('❌ 数据库连接状态检查失败:', error);
      setConnectionStatus({
        connected: false,
        message: error.message || '连接检查失败',
        lastChecked: new Date().toISOString()
      });
    } finally {
      setTestLoading(false);
    }
  };

  // 测试连接
  const testConnection = async () => {
    try {
      const values = await form.validateFields();
      setTestLoading(true);
      console.log('🧪 测试数据库连接...', values);

      // 模拟测试连接
      setTimeout(() => {
        message.success('数据库连接测试成功！');
        setConnectionStatus({
          connected: true,
          message: '连接测试成功',
          lastChecked: new Date().toISOString(),
          version: 'MySQL 8.0.33',
          uptime: '15 days 3 hours'
        });
        console.log('✅ 数据库连接测试成功');
        setTestLoading(false);
      }, 2000);

      // const response = await axios.post('http://localhost:8080/api/database/test', values, {
      //   headers: {
      //     'Content-Type': 'application/json'
      //   },
      //   timeout: 15000
      // });

      // if (response.data.success) {
      //   message.success('数据库连接测试成功！');
      //   setConnectionStatus({
      //     connected: true,
      //     message: '连接测试成功',
      //     lastChecked: new Date().toISOString(),
      //     version: response.data.data?.version,
      //     uptime: response.data.data?.uptime
      //   });
      //   console.log('✅ 数据库连接测试成功:', response.data.data);
      // } else {
      //   throw new Error(response.data.message || '连接测试失败');
      // }
    } catch (error: any) {
      console.error('❌ 数据库连接测试失败:', error);
      message.error(`连接测试失败: ${error.message || '请检查配置参数'}`);
      setConnectionStatus({
        connected: false,
        message: error.message || '连接测试失败',
        lastChecked: new Date().toISOString()
      });
      setTestLoading(false);
    }
  };

  // 保存配置
  const handleSubmit = async (values: DatabaseConfig) => {
    confirm({
      title: '确认保存配置',
      content: '保存数据库配置后，系统将重新连接数据库。确定要继续吗？',
      // icon: <WarningOutlined />,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          setLoading(true);
          console.log('💾 保存数据库配置...', values);

          // 模拟保存配置
          setTimeout(() => {
            message.success('数据库配置保存成功！系统正在重新连接数据库...');
            console.log('✅ 数据库配置保存成功');

            // 延迟检查连接状态
            setTimeout(() => {
              checkConnection();
            }, 2000);
            setLoading(false);
          }, 1000);

          // const response = await axios.post('http://localhost:8080/api/database/config', values, {
          //   headers: {
          //     'Content-Type': 'application/json'
          //   },
          //   timeout: 15000
          // });

          // if (response.data.success) {
          //   message.success('数据库配置保存成功！系统正在重新连接数据库...');
          //   console.log('✅ 数据库配置保存成功');

          //   // 延迟检查连接状态
          //   setTimeout(() => {
          //     checkConnection();
          //   }, 2000);
          // } else {
          //   throw new Error(response.data.message || '保存配置失败');
          // }
        } catch (error: any) {
          console.error('❌ 保存数据库配置失败:', error);
          message.error(`保存配置失败: ${error.message || '请稍后重试'}`);
          setLoading(false);
        }
      }
    });
  };

  // 初始化
  useEffect(() => {
    loadConfig();
    checkConnection();
  }, []);

  return (
    <div>
      {/* 页面头部 */}
      <div style={{ marginBottom: 24 }}>
        <Title level={3} style={{ margin: 0, marginBottom: 8 }}>
          🗄️ 数据库配置
        </Title>
        <Text type="secondary">
          配置本地MySQL数据库连接信息，确保系统能够正常访问数据库
        </Text>
      </div>

      {/* 连接状态 */}
      <Card
        title="连接状态"
        extra={
          <Button
            onClick={checkConnection}
            loading={testLoading}
            size="small"
          >
            🔄 刷新状态
          </Button>
        }
        style={{ marginBottom: 24 }}
      >
        {connectionStatus ? (
          <div>
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
              <Badge
                status={connectionStatus.connected ? 'success' : 'error'}
                text={
                  <span style={{ fontSize: 16, fontWeight: 500 }}>
                    {connectionStatus.connected ? '已连接' : '连接失败'}
                  </span>
                }
              />
              <span style={{ marginLeft: 8 }}>
                {connectionStatus.connected ? '✅' : '❌'}
              </span>
            </div>

            <Row gutter={24}>
              <Col span={12}>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>状态信息：</Text>
                  <Text style={{ marginLeft: 8 }}>{connectionStatus.message}</Text>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>检查时间：</Text>
                  <Text style={{ marginLeft: 8 }}>
                    {new Date(connectionStatus.lastChecked).toLocaleString()}
                  </Text>
                </div>
                <div>
                  <Text strong>字符集：</Text>
                  <Text style={{ marginLeft: 8 }}>utf8mb4</Text>
                </div>
              </Col>
              {connectionStatus.connected && (
                <Col span={12}>
                  {connectionStatus.version && (
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>数据库版本：</Text>
                      <Text style={{ marginLeft: 8 }}>{connectionStatus.version}</Text>
                    </div>
                  )}
                  {connectionStatus.uptime && (
                    <div>
                      <Text strong>运行时间：</Text>
                      <Text style={{ marginLeft: 8 }}>{connectionStatus.uptime}</Text>
                    </div>
                  )}
                </Col>
              )}
            </Row>
          </div>
        ) : (
          <Spin>
            <div style={{ height: 60 }}>检查连接状态中...</div>
          </Spin>
        )}
      </Card>

      {/* 配置表单 */}
      <Card title="数据库配置" loading={configLoading}>
        <Alert
          message="注意事项"
          description="修改数据库配置前，请确保新的数据库服务器正常运行，并且具有相应的访问权限。配置保存后系统将立即尝试重新连接。"
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          {/* 基本连接信息 */}
          <Title level={4}>基本连接信息</Title>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="主机地址"
                name="host"
                rules={[{ required: true, message: '请输入主机地址' }]}
              >
                <Input placeholder="localhost 或 IP 地址" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="端口号"
                name="port"
                rules={[
                  { required: true, message: '请输入端口号' },
                  { type: 'number', min: 1, max: 65535, message: '端口号范围：1-65535' }
                ]}
              >
                <InputNumber
                  placeholder="3306"
                  style={{ width: '100%' }}
                  min={1}
                  max={65535}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="数据库名"
                name="database"
                rules={[{ required: true, message: '请输入数据库名' }]}
              >
                <Input placeholder="equity_management" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="字符集"
                name="charset"
                rules={[{ required: true, message: '请输入字符集' }]}
              >
                <Input placeholder="utf8mb4" />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          {/* 认证信息 */}
          <Title level={4}>认证信息</Title>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="用户名"
                name="username"
                rules={[{ required: true, message: '请输入用户名' }]}
              >
                <Input placeholder="root" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="密码"
                name="password"
              >
                <Input.Password placeholder="数据库密码" />
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          {/* 高级设置 */}
          <Title level={4}>高级设置</Title>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                label="时区"
                name="timezone"
                rules={[{ required: true, message: '请输入时区' }]}
              >
                <Input placeholder="+08:00" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="连接池大小"
                name="connectionLimit"
                rules={[
                  { required: true, message: '请输入连接池大小' },
                  { type: 'number', min: 1, max: 100, message: '连接池大小范围：1-100' }
                ]}
              >
                <InputNumber
                  placeholder="10"
                  style={{ width: '100%' }}
                  min={1}
                  max={100}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="获取连接超时(ms)"
                name="acquireTimeout"
                rules={[
                  { required: true, message: '请输入获取连接超时时间' },
                  { type: 'number', min: 1000, max: 300000, message: '超时时间范围：1000-300000ms' }
                ]}
              >
                <InputNumber
                  placeholder="60000"
                  style={{ width: '100%' }}
                  min={1000}
                  max={300000}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                label="查询超时(ms)"
                name="timeout"
                rules={[
                  { required: true, message: '请输入查询超时时间' },
                  { type: 'number', min: 1000, max: 300000, message: '超时时间范围：1000-300000ms' }
                ]}
              >
                <InputNumber
                  placeholder="60000"
                  style={{ width: '100%' }}
                  min={1000}
                  max={300000}
                />
              </Form.Item>
            </Col>
          </Row>

          {/* 操作按钮 */}
          <Row justify="center" style={{ marginTop: 32 }}>
            <Space size="large">
              <Button
                onClick={testConnection}
                loading={testLoading}
              >
                🧪 测试连接
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
              >
                💾 保存配置
              </Button>
            </Space>
          </Row>
        </Form>
      </Card>
    </div>
  );
};

export default DatabaseConfig;
