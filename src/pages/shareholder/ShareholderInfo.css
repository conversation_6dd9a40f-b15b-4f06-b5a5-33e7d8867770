.shareholder-info-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.shareholder-layout {
  display: flex;
  gap: 16px;
  height: calc(100vh - 48px);
}

/* 左侧股东列表面板 */
.shareholder-list-panel {
  width: 300px;
  flex-shrink: 0;
}

.shareholder-list-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.shareholder-list-card .ant-card-body {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.shareholder-list-header {
  margin-bottom: 16px;
}

.shareholder-list {
  flex: 1;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: white;
}

.shareholder-item {
  padding: 12px 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.shareholder-item:last-child {
  border-bottom: none;
}

.shareholder-item:hover {
  background-color: #f5f5f5;
}

.shareholder-item.selected {
  background-color: #e6f7ff;
  border-left: 3px solid #1890ff;
}

.shareholder-name {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
  flex: 1;
}

.shareholder-actions {
  display: flex;
  align-items: center;
  opacity: 0.8;
}

.shareholder-actions span {
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.shareholder-actions span:hover {
  transform: scale(1.1);
}

/* 右侧投资记录面板 */
.investment-records-panel {
  flex: 1;
}

.investment-records-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.investment-records-card .ant-card-body {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.investment-header {
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.investment-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.empty-selection {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8c8c8c;
  font-size: 16px;
}

/* 可排序表头样式 */
.sortable-header {
  cursor: pointer;
  user-select: none;
  transition: color 0.2s ease;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.sortable-header:hover {
  color: #1890ff;
}

/* 表格样式 */
.ant-table {
  flex: 1;
}

.ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
  border-bottom: 2px solid #f0f0f0;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.ant-table-tbody > tr > td {
  border-bottom: 1px solid #f0f0f0;
}

/* 分页样式 */
.ant-pagination {
  margin-top: 16px;
  text-align: center;
}

/* 模态框样式 */
.ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.ant-modal-footer {
  border-top: 1px solid #f0f0f0;
}

/* 表单样式 */
.ant-form-item {
  margin-bottom: 16px;
}

.ant-form-item:last-child {
  margin-bottom: 0;
}

/* 输入框样式 */
.ant-input {
  border-radius: 6px;
}

.ant-input:focus,
.ant-input-focused {
  border-color: #40a9ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 选择器样式 */
.ant-select-dropdown {
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 按钮样式 */
.ant-btn-primary {
  background-color: #1890ff;
  border-color: #1890ff;
}

.ant-btn-primary:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .shareholder-layout {
    flex-direction: column;
    height: auto;
  }
  
  .shareholder-list-panel {
    width: 100%;
    height: 300px;
  }
  
  .investment-records-panel {
    height: 500px;
  }
}

@media (max-width: 768px) {
  .shareholder-info-container {
    padding: 16px;
  }
  
  .shareholder-list-panel {
    height: 250px;
  }
  
  .investment-records-panel {
    height: 400px;
  }
  
  .shareholder-item {
    padding: 8px 12px;
  }
  
  .shareholder-name {
    font-size: 13px;
  }
  
  .shareholder-actions span {
    font-size: 14px;
  }
}

@media (max-width: 576px) {
  .shareholder-info-container {
    padding: 12px;
  }
  
  .shareholder-layout {
    gap: 12px;
  }
  
  .shareholder-list-panel {
    height: 200px;
  }
  
  .investment-records-panel {
    height: 350px;
  }
}
