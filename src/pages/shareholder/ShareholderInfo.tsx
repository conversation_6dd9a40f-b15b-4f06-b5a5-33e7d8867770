import React, { useState, useEffect } from 'react';
import { Card, Button, Input, Table, Modal, Form, message, Space, Select, Pagination } from 'antd';
import axios from 'axios';
import './ShareholderInfo.css';

// 配置axios默认URL
axios.defaults.baseURL = 'http://localhost:8080';

interface ShareholderEntity {
  id: string;
  name: string;
  type: 'individual' | 'company' | 'external'; // 个人股东、内部主体、外部投资主体
  createTime: string;
}

interface InvestmentRecord {
  id: string;
  shareholderId: string;
  companyName: string;
  investmentAmount: string;
  percentage: string;
  startDate: string;
  endDate: string;
  createTime: string;
}

const ShareholderInfo: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [shareholderData, setShareholderData] = useState<ShareholderEntity[]>([]);
  const [investmentData, setInvestmentData] = useState<InvestmentRecord[]>([]);
  const [selectedShareholder, setSelectedShareholder] = useState<ShareholderEntity | null>(null);
  const [hoveredShareholderId, setHoveredShareholderId] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingShareholder, setEditingShareholder] = useState<ShareholderEntity | null>(null);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'asc' | 'desc';
  }>({
    key: 'companyName',
    direction: 'asc'
  });
  const [form] = Form.useForm();

  // 获取所有持股的股东数据（个人和企业股东）
  useEffect(() => {
    const fetchShareholderData = async () => {
      try {
        setLoading(true);
        setError(null);

        // 获取所有股东数据，只显示持有股份的股东，排除已在公司信息页面出现的企业
        const response = await axios.get('http://localhost:8080/api/shareholders?hasShares=true&excludeCompanies=true');
        if (response.data.success) {
          const transformedData = response.data.data.map((item: any) => ({
            id: item.id.toString(),
            name: item.name || item.shareholder_name,
            type: item.type || (item.shareholder_type === 'individual' ? 'individual' : 'company'),
            createTime: item.created_at || item.createTime
          }));

          setShareholderData(transformedData);
          console.log('✅ 股东数据加载成功，共', transformedData.length, '条记录');
        } else {
          throw new Error(response.data.message || '股东数据API返回失败');
        }

        setLoading(false);
      } catch (error) {
        console.error('获取股东数据失败:', error);
        setError(`获取股东数据失败: ${error.message}`);
        message.error(`获取股东数据失败: ${error.message}`);
        setLoading(false);
      }
    };

    fetchShareholderData();
  }, []);

  // 获取投资记录数据
  const fetchInvestmentData = async (shareholderId: string) => {
    try {
      // 尝试多个可能的API端点
      let response;
      try {
        response = await axios.get(`http://localhost:8080/api/shareholders/${shareholderId}/investments`);
      } catch (error) {
        // 如果第一个API失败，尝试备用API
        response = await axios.get(`http://localhost:8080/api/investments?shareholderId=${shareholderId}`);
      }

      if (response.data.success) {
        const transformedData = response.data.data.map((item: any) => ({
          id: item.id.toString(),
          shareholderId: shareholderId,
          companyName: item.company_name || item.companyName || item.target_company,
          investmentAmount: item.investment_amount || item.investmentAmount || item.amount,
          percentage: item.percentage || item.shareholding_ratio,
          startDate: item.start_date || item.startDate || item.investment_date,
          endDate: item.end_date || item.endDate,
          createTime: item.created_at || item.createTime
        }));

        setInvestmentData(transformedData);
        console.log('✅ 投资记录加载成功，共', transformedData.length, '条记录');
      } else {
        throw new Error(response.data.message || '投资记录API返回失败');
      }
    } catch (error) {
      console.error('获取投资记录失败:', error);
      message.error(`获取投资记录失败: ${error.message}`);
      setInvestmentData([]);
    }
  };



  // 处理新增外部投资主体
  const handleAddShareholder = () => {
    setEditingShareholder(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 处理编辑外部投资主体
  const handleEditShareholder = (shareholder: ShareholderEntity) => {
    setEditingShareholder(shareholder);
    form.setFieldsValue({
      name: shareholder.name,
      type: shareholder.type
    });
    setIsModalVisible(true);
  };

  // 处理删除外部投资主体
  const handleDeleteShareholder = (shareholder: ShareholderEntity) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除外部投资主体"${shareholder.name}"吗？此操作不可逆。`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      async onOk() {
        try {
          const response = await axios.delete(`/api/shareholders/${shareholder.id}`);
          if (response.data.success) {
            message.success('删除成功');
            fetchShareholderData();
            if (selectedShareholder?.id === shareholder.id) {
              setSelectedShareholder(null);
            }
          } else {
            message.error('删除失败');
          }
        } catch (error) {
          console.error('删除失败:', error);
          const newData = shareholderData.filter(item => item.id !== shareholder.id);
          setShareholderData(newData);
          if (selectedShareholder?.id === shareholder.id) {
            setSelectedShareholder(null);
          }
          message.warning('数据库删除失败，仅本地删除');
        }
      },
    });
  };

  // 处理模态框确认
  const handleModalOk = async () => {
    try {
      const values = await form.validateFields();

      // 检查是否重复
      const isDuplicate = shareholderData.some(item =>
        item.name === values.name &&
        (!editingShareholder || item.id !== editingShareholder.id)
      );

      if (isDuplicate) {
        message.error('已存在该外部投资主体');
        return;
      }

      if (editingShareholder) {
        // 编辑模式
        try {
          const response = await axios.put(`/api/shareholders/${editingShareholder.id}`, values);
          if (response.data.success) {
            message.success('更新成功');
            fetchShareholderData();
          } else {
            message.error('更新失败');
            return;
          }
        } catch (error) {
          console.error('更新失败:', error);
          const newData = shareholderData.map(item =>
            item.id === editingShareholder.id
              ? { ...item, ...values }
              : item
          );
          setShareholderData(newData);
          message.warning('数据库更新失败，仅本地更新');
        }
      } else {
        // 新增模式
        try {
          const response = await axios.post('/api/shareholders', { ...values, type: 'external' });
          if (response.data.success) {
            message.success('创建成功');
            fetchShareholderData();
          } else {
            message.error('创建失败');
            return;
          }
        } catch (error) {
          console.error('创建失败:', error);
          const newShareholder: ShareholderEntity = {
            id: Date.now().toString(),
            ...values,
            type: 'external',
            createTime: new Date().toLocaleString()
          };
          setShareholderData([newShareholder, ...shareholderData]);
          message.warning('数据库保存失败，仅本地添加');
        }
      }

      setIsModalVisible(false);
      form.resetFields();
    } catch (error) {
      console.error('操作失败:', error);
    }
  };

  // 处理模态框取消
  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  // 处理股东选择
  const handleShareholderSelect = (shareholder: ShareholderEntity) => {
    setSelectedShareholder(shareholder);
    // 加载该股东的投资记录
    fetchInvestmentData(shareholder.id);
  };

  // 处理排序
  const handleSort = (key: string) => {
    setSortConfig(prev => ({
      key,
      direction: prev.key === key && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  // 过滤股东数据
  const filteredShareholderData = shareholderData.filter(shareholder =>
    shareholder.name.toLowerCase().includes(searchKeyword.toLowerCase())
  );

  // 获取选中股东的投资记录
  const selectedShareholderInvestments = selectedShareholder
    ? investmentData.filter(record => record.shareholderId === selectedShareholder.id)
    : [];

  // 排序投资记录
  const sortedInvestmentData = [...selectedShareholderInvestments].sort((a, b) => {
    const { key, direction } = sortConfig;
    let aValue = a[key as keyof InvestmentRecord];
    let bValue = b[key as keyof InvestmentRecord];

    // 特殊处理日期字段
    if (key === 'startDate' || key === 'endDate') {
      aValue = aValue || '9999-12-31'; // 空日期排在最后
      bValue = bValue || '9999-12-31';
    }

    if (aValue < bValue) return direction === 'asc' ? -1 : 1;
    if (aValue > bValue) return direction === 'asc' ? 1 : -1;
    
    // 如果主要字段相同，按公司名称排序
    if (key !== 'companyName') {
      if (a.companyName < b.companyName) return -1;
      if (a.companyName > b.companyName) return 1;
    }
    
    // 如果公司名称也相同，按开始日期排序
    if (key !== 'startDate') {
      const aStart = a.startDate || '9999-12-31';
      const bStart = b.startDate || '9999-12-31';
      if (aStart < bStart) return -1;
      if (aStart > bStart) return 1;
    }
    
    return 0;
  });

  return (
    <div className="shareholder-info-container">
      <div className="shareholder-layout">
        {/* 左侧股东列表 */}
        <div className="shareholder-list-panel">
          <Card className="shareholder-list-card">
            <div className="shareholder-list-header">
              <Input
                placeholder="搜索股东"
                value={searchKeyword}
                onChange={(e) => setSearchKeyword(e.target.value)}
                style={{ marginBottom: 16 }}
              />
              <Button 
                type="primary" 
                onClick={handleAddShareholder}
                style={{ width: '100%' }}
              >
                新增
              </Button>
            </div>
            
            <div className="shareholder-list">
              {filteredShareholderData.map(shareholder => (
                <div
                  key={shareholder.id}
                  className={`shareholder-item ${selectedShareholder?.id === shareholder.id ? 'selected' : ''}`}
                  onClick={() => handleShareholderSelect(shareholder)}
                  onMouseEnter={() => setHoveredShareholderId(shareholder.id)}
                  onMouseLeave={() => setHoveredShareholderId(null)}
                >
                  <div className="shareholder-name">
                    {shareholder.name}
                  </div>
                  {hoveredShareholderId === shareholder.id && (
                    <div className="shareholder-actions">
                      <span 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditShareholder(shareholder);
                        }}
                        style={{ marginRight: 8, color: '#1890ff', cursor: 'pointer' }}
                      >
                        ✏️
                      </span>
                      <span 
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteShareholder(shareholder);
                        }}
                        style={{ color: '#ff4d4f', cursor: 'pointer' }}
                      >
                        🗑️
                      </span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </Card>
        </div>

        {/* 右侧投资记录 */}
        <div className="investment-records-panel">
          <Card className="investment-records-card">
            {selectedShareholder ? (
              <>
                <div className="investment-header">
                  <h3>{selectedShareholder.name} 的投资记录</h3>
                </div>
                <Table
                  columns={[
                    {
                      title: (
                        <span
                          className="sortable-header"
                          onClick={() => handleSort('companyName')}
                        >
                          公司 {sortConfig.key === 'companyName' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                        </span>
                      ),
                      dataIndex: 'companyName',
                      key: 'companyName',
                      align: 'center',
                      render: (text: string) => (
                        <span style={{ color: '#1890ff', cursor: 'pointer' }}>
                          {text}
                        </span>
                      )
                    },
                    {
                      title: (
                        <span
                          className="sortable-header"
                          onClick={() => handleSort('percentage')}
                        >
                          持股比例 {sortConfig.key === 'percentage' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                        </span>
                      ),
                      dataIndex: 'percentage',
                      key: 'percentage',
                      align: 'center'
                    },
                    {
                      title: (
                        <span
                          className="sortable-header"
                          onClick={() => handleSort('startDate')}
                        >
                          开始日期 {sortConfig.key === 'startDate' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                        </span>
                      ),
                      dataIndex: 'startDate',
                      key: 'startDate',
                      align: 'center',
                      render: (text: string) => {
                        if (!text) return '';
                        const date = new Date(text);
                        return date.toLocaleDateString('zh-CN', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit'
                        });
                      }
                    },
                    {
                      title: (
                        <span
                          className="sortable-header"
                          onClick={() => handleSort('endDate')}
                        >
                          结束日期 {sortConfig.key === 'endDate' && (sortConfig.direction === 'asc' ? '↑' : '↓')}
                        </span>
                      ),
                      dataIndex: 'endDate',
                      key: 'endDate',
                      align: 'center',
                      render: (text: string) => {
                        if (!text) return '至今';
                        const date = new Date(text);
                        return date.toLocaleDateString('zh-CN', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit'
                        });
                      }
                    }
                  ]}
                  dataSource={sortedInvestmentData}
                  rowKey="id"
                  pagination={{
                    pageSize: 10,
                    showTotal: (total) => `共 ${total} 条记录`,
                  }}
                  locale={{ emptyText: '暂无投资记录' }}
                />
              </>
            ) : (
              <div className="empty-selection">
                <p>请选择左侧股东查看投资记录</p>
              </div>
            )}
          </Card>
        </div>
      </div>

      {/* 新增/编辑外部投资主体模态框 */}
      <Modal
        title={editingShareholder ? '编辑外部投资主体' : '新增外部投资主体'}
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        okText="确认"
        cancelText="取消"
      >
        <Form
          form={form}
          layout="vertical"
          style={{ marginTop: 16 }}
        >
          <Form.Item
            label="外部投资主体名称"
            name="name"
            rules={[
              { required: true, message: '请输入外部投资主体名称' },
              { min: 2, message: '名称至少2个字符' }
            ]}
          >
            <Input placeholder="请输入外部投资主体公司名字" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ShareholderInfo;
