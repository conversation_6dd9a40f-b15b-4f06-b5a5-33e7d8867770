import React, { useState } from 'react';
import { Table, Card, Tabs, Button, Input, Space, Tag, Modal, message, Row, Col } from 'antd';
import { SearchOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import type { TabsProps } from 'antd';

const { TabPane } = Tabs;
const { confirm } = Modal;

interface StockItem {
  key: string;
  taskType: string;
  taskStatus: string;
  year: number;
  startDate: string;
  endDate: string;
  company: string;
  remark: string; // 将handler改为remark
}

const StockList: React.FC = () => {
  const [activeTab, setActiveTab] = useState('all');
  const [stockData, setStockData] = useState<StockItem[]>([
    {
      key: '1',
      taskType: '年审年报',
      taskStatus: '已逾期',
      year: 2024,
      startDate: '2024-09-11',
      endDate: '2024-11-11',
      company: '深圳A公司',
      remark: 'A备注信息'
    },
    {
      key: '2',
      taskType: '地址维护',
      taskStatus: '未开始',
      year: 2024,
      startDate: '2024-09-11',
      endDate: '2024-11-11',
      company: '深圳A公司',
      remark: 'B备注信息'
    },
    {
      key: '3',
      taskType: '自定义任务',
      taskStatus: '未开始',
      year: 2024,
      startDate: '2024-09-11',
      endDate: '2024-11-11',
      company: '深圳A公司',
      remark: 'A备注信息'
    },
    {
      key: '4',
      taskType: '自定义任务',
      taskStatus: '进行中',
      year: 2024,
      startDate: '2024-09-11',
      endDate: '2024-11-11',
      company: '深圳A公司',
      remark: 'B备注信息'
    },
    {
      key: '5',
      taskType: '自定义任务',
      taskStatus: '待核实',
      year: 2024,
      startDate: '2024-09-11',
      endDate: '2024-11-11',
      company: '深圳A公司',
      remark: 'C备注信息'
    },
    {
      key: '6',
      taskType: '自定义任务',
      taskStatus: '已核实',
      year: 2024,
      startDate: '2024-09-11',
      endDate: '2024-11-11',
      company: '深圳A公司',
      remark: 'D备注信息'
    }
  ]);

  // 定义表格列
  const columns = [
    {
      title: '任务状态', // 将任务优先级改为任务状态
      dataIndex: 'taskStatus',
      key: 'taskStatus',
      align: 'center',
      render: (text: string) => {
        let color = 'blue';
        if (text === '已逾期') color = 'error';
        if (text === '未开始') color = 'default';
        if (text === '进行中') color = 'processing';
        if (text === '待核实') color = 'warning';
        if (text === '已核实') color = 'success';
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '任务类型',
      dataIndex: 'taskType',
      key: 'taskTypeDisplay',
      align: 'center',
    },
    {
      title: '年度',
      dataIndex: 'year',
      key: 'year',
      align: 'center',
    },
    {
      title: '开始日期',
      dataIndex: 'startDate',
      key: 'startDate',
      align: 'center',
    },
    {
      title: '截止日期',
      dataIndex: 'endDate',
      key: 'endDate',
      align: 'center',
    },
    {
      title: '主体名称',
      dataIndex: 'company',
      key: 'company',
      align: 'center',
    },
    {
      title: '备注', // 将经办改为备注
      dataIndex: 'remark',
      key: 'remark',
      align: 'center',
    },
    {
      title: '操作',
      key: 'action',
      align: 'center',
      render: (_, record) => (
        <Space size="middle">
          <a onClick={() => handleUpdate(record)}>更新</a>
          <a onClick={() => handleEdit(record)}>编辑</a>
          <a onClick={() => handleDelete(record)}>删除</a>
          <a onClick={() => handleVerify(record)}>核实</a>
        </Space>
      ),
    },
  ];

  // 处理更新操作
  const handleUpdate = (record: StockItem) => {
    console.log('更新操作', record);
    message.info(`进入更新功能，记录ID: ${record.key}`);
    // 这里可以添加导航到更新页面的逻辑
  };

  // 处理编辑操作
  const handleEdit = (record: StockItem) => {
    console.log('编辑操作', record);
    message.info(`进入编辑功能，记录ID: ${record.key}`);
    // 这里可以添加导航到编辑页面的逻辑
  };

  // 处理删除操作
  const handleDelete = (record: StockItem) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除这条记录吗？(ID: ${record.key})`,
      okText: '确认',
      cancelText: '取消',
      onOk() {
        console.log('删除操作', record);
        message.success(`已删除记录ID: ${record.key}`);
        // 这里可以添加实际删除数据的逻辑
      },
    });
  };

  // 处理核实操作
  const handleVerify = (record: StockItem) => {
    console.log('核实操作', record);
    message.info(`进入核实功能，记录ID: ${record.key}`);
    // 这里可以添加导航到核实页面的逻辑
  };

  // 处理查询操作
  const handleSearch = () => {
    message.info('执行查询操作');
    // 这里可以添加实际查询逻辑
  };

  // 处理任务排查操作
  const handleTaskInspection = () => {
    message.info('进入任务排查功能');
    // 这里可以添加导航到任务排查页面的逻辑
  };

  // 创建新记录的函数
  const createNewRecord = (record: Omit<StockItem, 'key' | 'taskStatus'>) => {
    const newKey = (stockData.length + 1).toString();
    // 当记录被创建时，任务状态默认为未开始
    const newRecord: StockItem = {
      ...record,
      key: newKey,
      taskStatus: '未开始',
    };
    setStockData([...stockData, newRecord]);
    return newRecord;
  };

  // 处理新增操作
  const handleAdd = () => {
    message.info('进入新增功能');
    // 这里可以添加导航到新增页面的逻辑，或者打开一个表单模态框
    // 示例：创建一个新记录
    // const newRecord = createNewRecord({
    //   taskType: '新任务',
    //   year: new Date().getFullYear(),
    //   startDate: new Date().toISOString().split('T')[0],
    //   endDate: new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString().split('T')[0],
    //   company: '待定',
    //   remark: '',
    // });
    // message.success(`已创建新记录，ID: ${newRecord.key}`);
  };

  // 过滤数据
  const getFilteredData = () => {
    if (activeTab === 'all') return stockData;
    return stockData.filter(item => item.taskStatus === activeTab);
  };

  // 标签页配置
  const tabItems: TabsProps['items'] = [
    {
      key: 'all',
      label: (
        <span>
          全部（{stockData.length}）
        </span>
      ),
    },
    {
      key: '已逾期',
      label: (
        <span>
          已逾期（{stockData.filter(item => item.taskStatus === '已逾期').length}）
        </span>
      ),
    },
    {
      key: '未开始',
      label: (
        <span>
          未开始（{stockData.filter(item => item.taskStatus === '未开始').length}）
        </span>
      ),
    },
    {
      key: '进行中',
      label: (
        <span>
          进行中（{stockData.filter(item => item.taskStatus === '进行中').length}）
        </span>
      ),
    },
    {
      key: '待核实',
      label: (
        <span>
          待核实（{stockData.filter(item => item.taskStatus === '待核实').length}）
        </span>
      ),
    },
    {
      key: '已核实',
      label: (
        <span>
          已核实（{stockData.filter(item => item.taskStatus === '已核实').length}）
        </span>
      ),
    },
  ];

  // 获取不同类型任务的统计数据
  const getTaskTypeStats = () => {
    const taskTypes = ['全部', '年审年报', '地址维护', '自定义任务'];
    return taskTypes.map(type => {
      const filteredData = type === '全部' ? stockData : stockData.filter(item => item.taskType === type);
      return {
        type,
        total: filteredData.length,
        overdue: filteredData.filter(item => item.taskStatus === '已逾期').length,
        inProgress: filteredData.filter(item => item.taskStatus === '进行中').length,
        toVerify: filteredData.filter(item => item.taskStatus === '待核实').length,
      };
    });
  };

  // 渲染统计卡片
  const renderStatsCards = () => {
    const stats = getTaskTypeStats();
    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        {stats.map(stat => (
          <Col span={6} key={stat.type}>
            <Card style={{ backgroundColor: '#9e9e9e', color: 'white', textAlign: 'center' }}>
              <div style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>{stat.type}</div>
              <div style={{ display: 'flex', justifyContent: 'space-between', borderTop: '1px solid #d9d9d9', paddingTop: 8 }}>
                <div style={{ flex: 1, borderRight: '1px solid #d9d9d9' }}>
                  <div>已逾期</div>
                  <div style={{ fontSize: 18, fontWeight: 'bold' }}>{stat.overdue}</div>
                </div>
                <div style={{ flex: 1, borderRight: '1px solid #d9d9d9' }}>
                  <div>进行中</div>
                  <div style={{ fontSize: 18, fontWeight: 'bold' }}>{stat.inProgress}</div>
                </div>
                <div style={{ flex: 1 }}>
                  <div>待核实</div>
                  <div style={{ fontSize: 18, fontWeight: 'bold' }}>{stat.toVerify}</div>
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    );
  };

  return (
    <div>
      {renderStatsCards()}
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
          <Tabs 
            activeKey={activeTab} 
            onChange={setActiveTab}
            items={tabItems}
          />
          <Space>
            <Input placeholder="搜索" prefix={<SearchOutlined />} />
            <Button type="primary" onClick={handleSearch}>查询</Button>
            <Button onClick={handleTaskInspection}>任务排查</Button>
            <Button type="primary" onClick={handleAdd}>新增</Button>
          </Space>
        </div>
        <Table 
          columns={columns} 
          dataSource={getFilteredData()} 
          pagination={{ 
            total: getFilteredData().length,
            showTotal: (total) => `共 ${total} 条`,
            showSizeChanger: true,
            showQuickJumper: true,
          }}
        />
      </Card>
    </div>
  );
};

export default StockList;