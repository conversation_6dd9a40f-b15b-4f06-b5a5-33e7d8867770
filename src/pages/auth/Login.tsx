import React, { useState } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Typography, 
  message, 
  Row, 
  Col,
  Space,
  Divider
} from 'antd';
import { 
  UserOutlined, 
  LockOutlined, 
  LoginOutlined 
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';

const { Title, Text } = Typography;

interface LoginFormData {
  username: string;
  password: string;
}

const Login: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();
  const { login, getDefaultPageForUser } = useAuth();

  // 处理登录提交
  const handleLogin = async (values: LoginFormData) => {
    try {
      setLoading(true);
      console.log('🔐 开始登录验证:', { username: values.username });

      // 调用登录API
      const success = await login(values.username, values.password);
      
      if (success) {
        message.success('登录成功！');
        // 登录成功后根据用户角色跳转到对应的默认页面
        const defaultPage = getDefaultPageForUser();
        navigate(defaultPage);
      } else {
        message.error('用户名或密码错误');
      }
    } catch (error: any) {
      console.error('❌ 登录失败:', error);
      message.error(`登录失败: ${error.message || '请稍后重试'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Card
        style={{
          width: '100%',
          maxWidth: 400,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
          borderRadius: 12
        }}
        bodyStyle={{ padding: '40px 32px' }}
      >
        {/* 系统标题 */}
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <div style={{
            width: 64,
            height: 64,
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            margin: '0 auto 16px',
            color: 'white',
            fontSize: 24
          }}>
            📊
          </div>
          <Title level={2} style={{ margin: 0, color: '#1f2937' }}>
            股权管理系统
          </Title>
          <Text type="secondary" style={{ fontSize: 14 }}>
            请使用您的账户登录系统
          </Text>
        </div>

        {/* 登录表单 */}
        <Form
          form={form}
          name="login"
          onFinish={handleLogin}
          autoComplete="off"
          size="large"
        >
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 2, message: '用户名至少2个字符' }
            ]}
          >
            <Input
              prefix={<UserOutlined style={{ color: '#9ca3af' }} />}
              placeholder="用户名"
              style={{ borderRadius: 8 }}
            />
          </Form.Item>

          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少6个字符' }
            ]}
          >
            <Input.Password
              prefix={<LockOutlined style={{ color: '#9ca3af' }} />}
              placeholder="密码"
              style={{ borderRadius: 8 }}
            />
          </Form.Item>

          <Form.Item style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              block
              size="large"
              icon={<LoginOutlined />}
              style={{
                borderRadius: 8,
                height: 48,
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                border: 'none',
                fontSize: 16,
                fontWeight: 500
              }}
            >
              {loading ? '登录中...' : '登录'}
            </Button>
          </Form.Item>
        </Form>

        <Divider style={{ margin: '24px 0' }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            测试账户
          </Text>
        </Divider>

        {/* 测试账户提示 */}
        <div style={{ textAlign: 'center' }}>
          <Space direction="vertical" size="small">
            <Text type="secondary" style={{ fontSize: 12 }}>
              <strong>系统管理员:</strong> admin / 123456
            </Text>
            <Text type="secondary" style={{ fontSize: 12 }}>
              <strong>管理员:</strong> manager / 123456
            </Text>
            <Text type="secondary" style={{ fontSize: 12 }}>
              <strong>操作员:</strong> operator / 123456
            </Text>
            <Text type="secondary" style={{ fontSize: 12 }}>
              <strong>查看员:</strong> viewer / 123456
            </Text>
          </Space>
        </div>

        {/* 版权信息 */}
        <div style={{ 
          textAlign: 'center', 
          marginTop: 24, 
          paddingTop: 16, 
          borderTop: '1px solid #f0f0f0' 
        }}>
          <Text type="secondary" style={{ fontSize: 12 }}>
            © 2024 股权管理系统 v1.0
          </Text>
        </div>
      </Card>
    </div>
  );
};

export default Login;
