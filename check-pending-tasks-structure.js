import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function checkPendingTasksStructure() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 查看pending_tasks表结构
    const [columns] = await connection.query(
      'DESCRIBE pending_tasks'
    );
    
    console.log('\n📋 pending_tasks表结构:');
    columns.forEach(column => {
      console.log(`  ${column.Field}: ${column.Type} ${column.Null === 'YES' ? '(可空)' : '(非空)'} ${column.Key ? `[${column.Key}]` : ''}`);
    });

  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
checkPendingTasksStructure();
