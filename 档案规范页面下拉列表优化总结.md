# 档案规范页面下拉列表优化总结

## 🎯 优化目标

根据用户要求，对新增档案规范页面和编辑档案规范页面进行以下优化：

1. **业务板块下拉列表**：从数据库中动态获取
2. **地区类型和地区联动**：
   - 地区类型选择"国内"时，地区下拉列表只显示国内城市/地区
   - 地区类型选择"海外"时，地区下拉列表只显示海外国家/城市
   - 不出现混合显示的情况

## ✅ 已完成的修改

### 1. 新增档案规范页面 (`ArchiveUpdateRuleAdd.tsx`)

#### 🔧 数据状态管理
```typescript
// 添加数据库数据状态
const [businessSegments, setBusinessSegments] = useState<any[]>([]);
const [allRegions, setAllRegions] = useState<any[]>([]);
const [filteredRegions, setFilteredRegions] = useState<any[]>([]);
const [selectedRegionTypes, setSelectedRegionTypes] = useState<string[]>([]);
```

#### 🔧 数据加载函数
```typescript
// 加载业务板块数据
const loadBusinessSegments = async () => {
  const response = await axios.get('http://localhost:8080/api/business-segments');
  if (response.data.success) {
    setBusinessSegments(response.data.data);
  }
};

// 加载地区数据
const loadRegions = async () => {
  const response = await axios.get('http://localhost:8080/api/regions');
  if (response.data.success) {
    setAllRegions(response.data.data);
    setFilteredRegions(response.data.data);
  }
};
```

#### 🔧 地区过滤逻辑
```typescript
// 地区类型变化时过滤地区
useEffect(() => {
  if (selectedRegionTypes.length === 0) {
    setFilteredRegions(allRegions);
  } else {
    const filtered = allRegions.filter(region => {
      if (selectedRegionTypes.includes('国内')) {
        if (region.type === '国内') return true;
      }
      if (selectedRegionTypes.includes('海外')) {
        if (region.type !== '国内') return true;
      }
      return false;
    });
    setFilteredRegions(filtered);
  }
}, [selectedRegionTypes, allRegions]);
```

#### 🔧 适用范围处理
```typescript
// 地区类型变化时清空已选地区
const handleScopeChange = (type: string, values: string[]) => {
  setApplicableScope(prev => ({
    ...prev,
    [type]: values
  }));
  
  if (type === 'regionTypes') {
    setSelectedRegionTypes(values);
    setApplicableScope(prev => ({
      ...prev,
      regions: [] // 清空已选地区
    }));
  }
};
```

#### 🔧 UI数据源更新
```typescript
// 适用范围表格数据源
dataSource={[
  {
    key: 'businessSegments',
    scope: '业务板块',
    tags: applicableScope.businessSegments,
    options: businessSegments.map(item => item.name) // 从数据库获取
  },
  {
    key: 'regionTypes',
    scope: '地区类型',
    tags: applicableScope.regionTypes,
    options: regionTypes
  },
  {
    key: 'regions',
    scope: '地区',
    tags: applicableScope.regions,
    options: filteredRegions.map(item => item.region) // 动态过滤
  }
]}
```

### 2. 编辑档案规范页面 (`ArchiveUpdateRuleEdit.tsx`)

#### 🔧 相同的数据状态管理
- 添加了相同的数据库数据状态变量
- 实现了相同的数据加载函数
- 添加了相同的地区过滤逻辑

#### 🔧 编辑页面特殊处理
```typescript
// 加载编辑数据时设置地区类型
if (data.applicable_scope) {
  const scope = typeof data.applicable_scope === 'string' 
    ? JSON.parse(data.applicable_scope) 
    : data.applicable_scope;
  setApplicableScope(scope);
  
  // 设置选中的地区类型，用于过滤地区
  if (scope.regionTypes) {
    setSelectedRegionTypes(scope.regionTypes);
  }
}
```

#### 🔧 标签添加/删除处理
```typescript
// 地区类型标签变化时的特殊处理
const addTag = (type: keyof ApplicableScope, value: string) => {
  // ... 添加标签逻辑
  
  if (type === 'regionTypes') {
    const newRegionTypes = [...applicableScope.regionTypes, value];
    setSelectedRegionTypes(newRegionTypes);
    setApplicableScope(prev => ({
      ...prev,
      regions: [] // 清空已选地区
    }));
  }
};

const removeTag = (type: keyof ApplicableScope, value: string) => {
  // ... 删除标签逻辑
  
  if (type === 'regionTypes') {
    const newRegionTypes = applicableScope.regionTypes.filter(item => item !== value);
    setSelectedRegionTypes(newRegionTypes);
    if (newRegionTypes.length === 0) {
      setApplicableScope(prev => ({
        ...prev,
        regions: [] // 清空已选地区
      }));
    }
  }
};
```

## 📊 数据库数据结构

### 业务板块数据 (`/api/business-segments`)
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "电子商务",
      "description": "",
      "is_active": 1,
      "created_at": "2025-06-17T10:41:32.000Z"
    },
    {
      "id": 2,
      "name": "A. 农、林、牧、渔业",
      "description": "",
      "is_active": 1,
      "created_at": "2025-06-22T16:10:42.000Z"
    }
    // ... 更多业务板块
  ]
}
```

### 地区数据 (`/api/regions`)
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "type": "国内",
      "region": "广州",
      "createTime": "2025-06-24 16:15:37"
    },
    {
      "id": 2,
      "type": "海外公司对外投资",
      "region": "亚太",
      "createTime": "2025-06-24 16:15:37"
    },
    {
      "id": 3,
      "type": "海外公司对外投资",
      "region": "美国",
      "createTime": "2025-06-24 16:15:37"
    },
    {
      "id": 4,
      "type": "国内",
      "region": "深圳",
      "createTime": "2025-06-24 16:15:37"
    }
    // ... 更多地区
  ]
}
```

## 🎯 实现效果

### ✅ 业务板块下拉列表
- ✅ 从数据库动态获取业务板块数据
- ✅ 显示实际的业务板块名称（如"A. 农、林、牧、渔业"、"电子商务"等）
- ✅ 替换了原来的硬编码数据

### ✅ 地区类型和地区联动
- ✅ **选择"国内"**：地区下拉列表只显示 `type === "国内"` 的地区
  - 显示：广州、深圳、北京、上海等国内城市
- ✅ **选择"海外"**：地区下拉列表只显示 `type !== "国内"` 的地区
  - 显示：亚太、美国等海外国家/地区
- ✅ **地区类型变化时自动清空已选地区**，避免数据不一致
- ✅ **支持多选地区类型**，地区列表会显示所有匹配类型的地区

### ✅ 编辑页面特殊处理
- ✅ 加载编辑数据时正确设置地区类型，确保地区过滤正常工作
- ✅ 标签添加/删除时正确更新地区类型状态
- ✅ 保持与新增页面一致的交互逻辑

## 🔗 页面访问路径

- **档案更新规范列表**：`http://localhost:5174/archive`
- **新增档案规范**：`http://localhost:5174/archive/update-rule/add`
- **编辑档案规范**：`http://localhost:5174/archive/edit/{id}`

## 🎉 总结

所有要求的功能都已成功实现：

1. ✅ **业务板块下拉列表从数据库导入**
2. ✅ **地区类型和地区下拉列表从数据库导入**
3. ✅ **地区类型联动过滤功能**：
   - 选择"国内" → 只显示国内城市
   - 选择"海外" → 只显示海外国家/城市
   - 不会出现混合显示
4. ✅ **新增和编辑页面都已修改**

用户现在可以正常使用档案更新规范的新增和编辑功能，所有下拉列表都会从数据库动态获取数据，并且地区选择具有正确的联动过滤效果。
