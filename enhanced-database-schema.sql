-- Enhanced Database Schema for Stake/Equity Management System
-- Includes user management, security, audit trails, and improved constraints

-- Create database
CREATE DATABASE IF NOT EXISTS stake_management_v2;
USE stake_management_v2;

-- Enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- USER MANAGEMENT TABLES
-- ============================================================================

-- Roles table for RBAC
CREATE TABLE roles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) UNIQUE NOT NULL COMMENT '角色名称',
  display_name VARCHAR(100) NOT NULL COMMENT '显示名称',
  description TEXT COMMENT '角色描述',
  permissions JSON COMMENT '权限配置',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_role_name (name),
  INDEX idx_role_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Users table
CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
  password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
  salt VARCHAR(255) NOT NULL COMMENT '密码盐值',
  role_id INT NOT NULL COMMENT '角色ID',
  full_name VARCHAR(100) COMMENT '全名',
  phone VARCHAR(20) COMMENT '电话',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  is_locked BOOLEAN DEFAULT FALSE COMMENT '是否锁定',
  failed_login_attempts INT DEFAULT 0 COMMENT '失败登录次数',
  last_login TIMESTAMP NULL COMMENT '最后登录时间',
  last_login_ip VARCHAR(45) COMMENT '最后登录IP',
  password_changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '密码修改时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE RESTRICT,
  INDEX idx_username (username),
  INDEX idx_email (email),
  INDEX idx_user_active (is_active),
  INDEX idx_user_role (role_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- User sessions table
CREATE TABLE user_sessions (
  id VARCHAR(128) PRIMARY KEY COMMENT '会话ID',
  user_id INT NOT NULL COMMENT '用户ID',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  expires_at TIMESTAMP NOT NULL COMMENT '过期时间',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
  INDEX idx_session_user (user_id),
  INDEX idx_session_expires (expires_at),
  INDEX idx_session_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- ENHANCED BUSINESS TABLES
-- ============================================================================

-- Enhanced companies table
CREATE TABLE companies (
  id INT AUTO_INCREMENT PRIMARY KEY,
  company_name_cn VARCHAR(255) NOT NULL COMMENT '公司中文名',
  company_name_en VARCHAR(255) NOT NULL COMMENT '公司英文名',
  company_code VARCHAR(50) UNIQUE COMMENT '公司代码',
  registered_capital DECIMAL(20,4) COMMENT '注册资本（万元）',
  paid_capital DECIMAL(20,4) COMMENT '实缴资本（万元）',
  establish_date DATE COMMENT '设立日期',
  business_segment VARCHAR(100) COMMENT '业务板块',
  region VARCHAR(100) COMMENT '地区',
  agency VARCHAR(100) COMMENT '代理机构',
  annual_update VARCHAR(50) COMMENT '年审更新',
  registered_address TEXT COMMENT '注册地址',
  business_address TEXT COMMENT '经营地址',
  operation_status VARCHAR(50) COMMENT '存续状况',
  legal_representative VARCHAR(100) COMMENT '法定代表人',
  unified_social_credit_code VARCHAR(50) COMMENT '统一社会信用代码',
  business_scope TEXT COMMENT '经营范围',
  company_type VARCHAR(100) COMMENT '公司类型',
  parent_company_id INT COMMENT '母公司ID',
  is_listed BOOLEAN DEFAULT FALSE COMMENT '是否上市',
  stock_code VARCHAR(20) COMMENT '股票代码',
  created_by INT COMMENT '创建人',
  updated_by INT COMMENT '更新人',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (parent_company_id) REFERENCES companies(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
  UNIQUE INDEX idx_company_name (company_name_cn, company_name_en),
  UNIQUE INDEX idx_company_code (company_code),
  INDEX idx_company_status (operation_status),
  INDEX idx_company_segment (business_segment),
  INDEX idx_company_region (region),
  INDEX idx_company_parent (parent_company_id),
  FULLTEXT INDEX idx_company_search (company_name_cn, company_name_en, business_scope)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced executives table
CREATE TABLE executives (
  id INT AUTO_INCREMENT PRIMARY KEY,
  company_id INT NOT NULL COMMENT '关联的公司ID',
  person_id INT COMMENT '人员ID（关联persons表）',
  position VARCHAR(100) COMMENT '职位',
  person_name VARCHAR(100) COMMENT '任职人员姓名',
  id_number VARCHAR(50) COMMENT '身份证号',
  appointment_date DATE COMMENT '任职日期',
  resignation_date DATE COMMENT '离职日期',
  is_current BOOLEAN DEFAULT TRUE COMMENT '是否现任',
  salary_range VARCHAR(50) COMMENT '薪资范围',
  created_by INT COMMENT '创建人',
  updated_by INT COMMENT '更新人',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_executive_company (company_id),
  INDEX idx_executive_person (person_id),
  INDEX idx_executive_current (is_current),
  INDEX idx_executive_position (position)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced shareholders table
CREATE TABLE shareholders (
  id INT AUTO_INCREMENT PRIMARY KEY,
  company_id INT NOT NULL COMMENT '关联的公司ID',
  shareholder_type ENUM('individual', 'company') NOT NULL COMMENT '股东类型',
  shareholder_name VARCHAR(255) NOT NULL COMMENT '股东名称',
  shareholder_id_number VARCHAR(50) COMMENT '股东身份证号/统一社会信用代码',
  investment_amount DECIMAL(20,4) COMMENT '认缴出资额（万元）',
  paid_amount DECIMAL(20,4) COMMENT '实缴出资额（万元）',
  percentage DECIMAL(8,4) COMMENT '持股比例（%）',
  share_type VARCHAR(50) COMMENT '股份类型',
  start_date DATE COMMENT '出资日期',
  end_date DATE COMMENT '退出日期',
  is_current BOOLEAN DEFAULT TRUE COMMENT '是否现任股东',
  voting_rights DECIMAL(8,4) COMMENT '表决权比例（%）',
  created_by INT COMMENT '创建人',
  updated_by INT COMMENT '更新人',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_shareholder_company (company_id),
  INDEX idx_shareholder_type (shareholder_type),
  INDEX idx_shareholder_current (is_current),
  INDEX idx_shareholder_name (shareholder_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Enhanced investments table
CREATE TABLE investments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  investor_company_id INT NOT NULL COMMENT '投资方公司ID',
  investee_company_id INT COMMENT '被投资方公司ID（如果是公司）',
  investee_name VARCHAR(255) NOT NULL COMMENT '被投资方名称',
  investment_amount DECIMAL(20,4) COMMENT '投资金额（万元）',
  percentage DECIMAL(8,4) COMMENT '持股比例（%）',
  investment_type VARCHAR(50) COMMENT '投资类型',
  start_date DATE COMMENT '投资日期',
  exit_date DATE COMMENT '退出日期',
  is_current BOOLEAN DEFAULT TRUE COMMENT '是否当前投资',
  valuation DECIMAL(20,4) COMMENT '估值（万元）',
  created_by INT COMMENT '创建人',
  updated_by INT COMMENT '更新人',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  FOREIGN KEY (investor_company_id) REFERENCES companies(id) ON DELETE CASCADE,
  FOREIGN KEY (investee_company_id) REFERENCES companies(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_investment_investor (investor_company_id),
  INDEX idx_investment_investee (investee_company_id),
  INDEX idx_investment_current (is_current),
  INDEX idx_investment_type (investment_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ============================================================================
-- AUDIT AND LOGGING TABLES
-- ============================================================================

-- Audit logs table
CREATE TABLE audit_logs (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  user_id INT COMMENT '操作用户ID',
  action VARCHAR(100) NOT NULL COMMENT '操作类型',
  table_name VARCHAR(50) NOT NULL COMMENT '表名',
  record_id INT COMMENT '记录ID',
  old_values JSON COMMENT '修改前的值',
  new_values JSON COMMENT '修改后的值',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  user_agent TEXT COMMENT '用户代理',
  request_id VARCHAR(100) COMMENT '请求ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_audit_user (user_id),
  INDEX idx_audit_table (table_name),
  INDEX idx_audit_record (table_name, record_id),
  INDEX idx_audit_action (action),
  INDEX idx_audit_created (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- System logs table
CREATE TABLE system_logs (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  level ENUM('DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL') NOT NULL COMMENT '日志级别',
  message TEXT NOT NULL COMMENT '日志消息',
  context JSON COMMENT '上下文信息',
  user_id INT COMMENT '相关用户ID',
  ip_address VARCHAR(45) COMMENT 'IP地址',
  request_id VARCHAR(100) COMMENT '请求ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL,
  INDEX idx_log_level (level),
  INDEX idx_log_user (user_id),
  INDEX idx_log_created (created_at),
  INDEX idx_log_request (request_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
