import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function checkCompanies() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 查看所有公司
    const [companies] = await connection.query(
      'SELECT id, company_name_cn, created_by FROM companies ORDER BY id'
    );
    
    console.log('\n📊 所有公司列表:');
    companies.forEach(company => {
      console.log(`ID: ${company.id}, 名称: ${company.company_name_cn}, 创建者: ${company.created_by}`);
    });

    // 查看测试公司
    const [testCompanies] = await connection.query(
      'SELECT id, company_name_cn FROM companies WHERE created_by = 1 ORDER BY id'
    );
    
    console.log('\n🧪 测试公司列表:');
    testCompanies.forEach(company => {
      console.log(`ID: ${company.id}, 名称: ${company.company_name_cn}`);
    });

    // 查看股东关系
    const [shareholdings] = await connection.query(
      'SELECT s.*, p.name as person_name, c.company_name_cn FROM shareholdings s LEFT JOIN persons p ON s.person_id = p.id LEFT JOIN companies c ON s.company_id = c.id WHERE s.created_by = 1'
    );
    
    console.log('\n👥 股东关系:');
    shareholdings.forEach(sh => {
      console.log(`${sh.person_name} 持股 ${sh.company_name_cn} ${sh.percentage}%`);
    });

    // 查看投资关系
    const [investments] = await connection.query(
      'SELECT i.*, ic.company_name_cn as investor_name, ec.company_name_cn as investee_name FROM investments i LEFT JOIN companies ic ON i.investor_company_id = ic.id LEFT JOIN companies ec ON i.investee_company_id = ec.id WHERE i.created_by = 1'
    );
    
    console.log('\n💰 投资关系:');
    investments.forEach(inv => {
      console.log(`${inv.investor_name} 投资 ${inv.investee_name || inv.investee_company_name} ${inv.percentage}%`);
    });

  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
checkCompanies();
