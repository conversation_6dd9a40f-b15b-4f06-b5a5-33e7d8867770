# 任务排查功能实现说明

## 功能概述

任务排查功能用于检查指定年度的任务完成情况，识别缺失的任务，并提供一键补全功能。

## 主要功能

### 1. 年度选择
- 支持选择当前年份及前4年的任务排查
- 默认选择当前年份

### 2. 统计数据显示
- **存续公司统计**：显示总公司数、不管年审公司数、管年审（固定周期）公司数、管年审（滚动周期）公司数
- **年审年报统计**：显示理论数、实际数、缺失数
- **地址维护统计**：显示理论数、实际数、缺失数

### 3. 缺失任务列表
- 显示所有缺失任务的公司名单，每个公司一行
- 同一公司的不同缺失任务合并显示在同一行
- 年审年报和地址维护列用"×"标记缺失状态
- 支持分页显示，每页可选择10/20/50/100条
- 显示缺失任务总数和分类统计

### 4. 补全缺失任务
- 一键补全所有缺失的任务
- 自动创建年审年报和地址维护任务
- 设置合理的默认截止日期

## 技术实现

### 后端API

#### 1. 任务排查API
```
POST /api/task/investigation
参数: { year: "2024" }
```

**功能逻辑：**
- 统计存续公司数量
- 计算年审年报理论数和实际数
- 计算地址维护理论数和实际数
- 查询缺失任务的公司列表
- 按公司分组合并缺失任务类型
- 返回完整的排查结果

#### 2. 补全缺失任务API
```
POST /api/task/complete-missing
参数: { year: "2024" }
```

**功能逻辑：**
- 查找年审年报缺失的公司
- 查找地址维护缺失的公司
- 批量创建缺失的任务记录
- 使用事务确保数据一致性

### 前端实现

#### 1. 页面结构
- 年度选择和排查按钮
- 统计数据展示卡片
- 缺失任务列表表格
- 补全缺失任务按钮

#### 2. 交互功能
- 年度选择触发重新排查
- 排查按钮显示加载状态
- 补全按钮在无缺失任务时禁用
- 表格支持分页和滚动

## 数据计算逻辑

### 理论数计算
- **年审年报理论数** = 实际已有任务数 + 缺失任务数
- **地址维护理论数** = 实际已有任务数 + 缺失任务数

### 缺失数计算
- **年审年报缺失数** = 需要年审年报但未创建任务的公司数
- **地址维护缺失数** = 需要地址维护但未创建任务的公司数

### 公司分类
- **存续公司**：所有在数据库中的公司（简化处理）
- **管年审公司**：按固定周期和滚动周期分类（模拟数据）

## 使用说明

1. **选择年度**：在下拉框中选择要排查的年度
2. **开始排查**：点击"开始排查"按钮执行排查
3. **查看结果**：查看统计数据和缺失任务列表
4. **补全任务**：如有缺失任务，点击"补全缺失任务"按钮
5. **重新排查**：补全后可重新排查验证结果

## 注意事项

1. **数据一致性**：补全任务使用数据库事务确保数据一致性
2. **性能优化**：大量数据时表格支持分页显示
3. **用户体验**：操作过程中显示加载状态，防止重复操作
4. **错误处理**：网络错误时显示友好的错误提示

## 数据结构优化

### 缺失任务数据结构
```javascript
// 优化前（每个任务一行）
[
  { companyName: "公司A", taskType: "年审年报" },
  { companyName: "公司A", taskType: "地址维护" },
  { companyName: "公司B", taskType: "地址维护" }
]

// 优化后（每个公司一行）
[
  {
    companyName: "公司A",
    missingAnnualReport: true,
    missingAddressMaintenance: true
  },
  {
    companyName: "公司B",
    missingAnnualReport: false,
    missingAddressMaintenance: true
  }
]
```

### 优化效果
- **数据压缩**：减少了重复的公司名称
- **显示优化**：同一公司的缺失任务合并在一行显示
- **统计准确**：更容易统计缺失任务的公司数量
- **用户体验**：表格更简洁，信息更清晰

## 扩展功能建议

1. **导出功能**：支持导出缺失任务列表到Excel
2. **筛选功能**：支持按公司名称、业务板块筛选
3. **批量操作**：支持选择性补全特定公司的任务
4. **历史记录**：记录排查历史和补全操作日志
