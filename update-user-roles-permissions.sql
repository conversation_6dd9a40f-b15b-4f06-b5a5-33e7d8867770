-- 更新用户权限管理系统数据库结构
-- 设立checkpoint前的数据库结构更新

USE stake_management_v2;

-- 1. 先删除角色权限关联
DELETE FROM role_permissions;

-- 2. 更新现有角色而不是删除重建
UPDATE user_roles SET
  name = '系统管理员',
  description = '可以操作系统设置-新增用户、用户管理、数据库配置等三个页面，可以查看其他所有页面，但不可操作'
WHERE id = 1;

UPDATE user_roles SET
  name = '管理员',
  description = '可以操作除了系统设置菜单下新增用户、用户管理、数据库配置等三个页面以外的其他所有页面，但不可访问系统设置菜单'
WHERE id = 2;

UPDATE user_roles SET
  name = '操作员',
  description = '初步保持和管理员一致，但会在具体个别页面根据需要进行差异化'
WHERE id = 3;

UPDATE user_roles SET
  name = '查看员',
  description = '可以查看除了系统设置菜单下新增用户、用户管理、数据库配置等三个页面以外的其他所有页面，但不可进行任何操作'
WHERE id = 4;

-- 3. 如果角色不存在则插入
INSERT IGNORE INTO user_roles (id, name, description, is_active, created_at) VALUES
(1, '系统管理员', '可以操作系统设置-新增用户、用户管理、数据库配置等三个页面，可以查看其他所有页面，但不可操作', 1, NOW()),
(2, '管理员', '可以操作除了系统设置菜单下新增用户、用户管理、数据库配置等三个页面以外的其他所有页面，但不可访问系统设置菜单', 1, NOW()),
(3, '操作员', '初步保持和管理员一致，但会在具体个别页面根据需要进行差异化', 1, NOW()),
(4, '查看员', '可以查看除了系统设置菜单下新增用户、用户管理、数据库配置等三个页面以外的其他所有页面，但不可进行任何操作', 1, NOW());

-- 4. 更新权限表，定义详细的权限项
DELETE FROM permissions WHERE id > 0;

INSERT INTO permissions (id, module, action, description, created_at) VALUES
-- 系统设置权限
(1, 'system', 'user_add', '新增用户', NOW()),
(2, 'system', 'user_management', '用户管理', NOW()),
(3, 'system', 'database_config', '数据库配置', NOW()),

-- 任务管理权限
(4, 'task', 'view', '查看任务', NOW()),
(5, 'task', 'add', '新增任务', NOW()),
(6, 'task', 'edit', '编辑任务', NOW()),
(7, 'task', 'delete', '删除任务', NOW()),
(8, 'task', 'verify', '核实任务', NOW()),
(9, 'task', 'investigation', '任务排查', NOW()),

-- 公司信息权限
(10, 'company', 'view', '查看公司信息', NOW()),
(11, 'company', 'add', '新增公司', NOW()),
(12, 'company', 'edit', '编辑公司信息', NOW()),
(13, 'company', 'delete', '删除公司', NOW()),
(14, 'company', 'change_confirm', '公司信息变更确认', NOW()),
(15, 'company', 'finance', '财务信息管理', NOW()),
(16, 'company', 'equity_chart', '股权图查看', NOW()),

-- 任职档案权限
(17, 'employment', 'view', '查看任职档案', NOW()),
(18, 'employment', 'add', '新增任职记录', NOW()),
(19, 'employment', 'edit', '编辑任职记录', NOW()),
(20, 'employment', 'delete', '删除任职记录', NOW()),

-- 业务板块权限
(21, 'business', 'view', '查看业务板块', NOW()),
(22, 'business', 'edit', '编辑业务板块', NOW()),
(23, 'business', 'equity_chart', '业务板块股权图', NOW()),

-- 股东信息权限
(24, 'shareholder', 'view', '查看股东信息', NOW()),
(25, 'shareholder', 'add', '新增股东', NOW()),
(26, 'shareholder', 'edit', '编辑股东信息', NOW()),
(27, 'shareholder', 'delete', '删除股东', NOW()),

-- 基础数据权限
(28, 'basic_data', 'view', '查看基础数据', NOW()),
(29, 'basic_data', 'edit', '编辑基础数据', NOW()),

-- 档案更新规范权限
(30, 'archive', 'view', '查看档案规范', NOW()),
(31, 'archive', 'add', '新增档案规范', NOW()),
(32, 'archive', 'edit', '编辑档案规范', NOW()),
(33, 'archive', 'delete', '删除档案规范', NOW());

-- 5. 设置角色权限关联
-- 系统管理员权限：系统设置操作权限 + 其他所有查看权限
INSERT INTO role_permissions (role_id, permission_id, created_at) VALUES
-- 系统设置操作权限
(1, 1, NOW()), (1, 2, NOW()), (1, 3, NOW()),
-- 其他模块查看权限
(1, 4, NOW()), (1, 10, NOW()), (1, 16, NOW()), (1, 17, NOW()), (1, 21, NOW()), (1, 23, NOW()),
(1, 24, NOW()), (1, 28, NOW()), (1, 30, NOW());

-- 管理员权限：除系统设置外的所有操作权限
INSERT INTO role_permissions (role_id, permission_id, created_at) VALUES
-- 任务管理
(2, 4, NOW()), (2, 5, NOW()), (2, 6, NOW()), (2, 7, NOW()), (2, 8, NOW()), (2, 9, NOW()),
-- 公司信息
(2, 10, NOW()), (2, 11, NOW()), (2, 12, NOW()), (2, 13, NOW()), (2, 14, NOW()), (2, 15, NOW()), (2, 16, NOW()),
-- 任职档案
(2, 17, NOW()), (2, 18, NOW()), (2, 19, NOW()), (2, 20, NOW()),
-- 业务板块
(2, 21, NOW()), (2, 22, NOW()), (2, 23, NOW()),
-- 股东信息
(2, 24, NOW()), (2, 25, NOW()), (2, 26, NOW()), (2, 27, NOW()),
-- 基础数据
(2, 28, NOW()), (2, 29, NOW()),
-- 档案规范
(2, 30, NOW()), (2, 31, NOW()), (2, 32, NOW()), (2, 33, NOW());

-- 操作员权限：初步与管理员一致
INSERT INTO role_permissions (role_id, permission_id, created_at) VALUES
-- 任务管理
(3, 4, NOW()), (3, 5, NOW()), (3, 6, NOW()), (3, 7, NOW()), (3, 8, NOW()), (3, 9, NOW()),
-- 公司信息
(3, 10, NOW()), (3, 11, NOW()), (3, 12, NOW()), (3, 13, NOW()), (3, 14, NOW()), (3, 15, NOW()), (3, 16, NOW()),
-- 任职档案
(3, 17, NOW()), (3, 18, NOW()), (3, 19, NOW()), (3, 20, NOW()),
-- 业务板块
(3, 21, NOW()), (3, 22, NOW()), (3, 23, NOW()),
-- 股东信息
(3, 24, NOW()), (3, 25, NOW()), (3, 26, NOW()), (3, 27, NOW()),
-- 基础数据
(3, 28, NOW()), (3, 29, NOW()),
-- 档案规范
(3, 30, NOW()), (3, 31, NOW()), (3, 32, NOW()), (3, 33, NOW());

-- 查看员权限：除系统设置外的所有查看权限
INSERT INTO role_permissions (role_id, permission_id, created_at) VALUES
-- 任务管理查看
(4, 4, NOW()),
-- 公司信息查看
(4, 10, NOW()), (4, 16, NOW()),
-- 任职档案查看
(4, 17, NOW()),
-- 业务板块查看
(4, 21, NOW()), (4, 23, NOW()),
-- 股东信息查看
(4, 24, NOW()),
-- 基础数据查看
(4, 28, NOW()),
-- 档案规范查看
(4, 30, NOW());

-- 6. 更新现有用户的角色（保持现有用户不变，只更新角色ID映射）
-- 注意：这里需要根据实际情况调整，暂时保持现有数据
UPDATE users SET role_id = 1 WHERE role_id = 1; -- 超级管理员 -> 系统管理员
UPDATE users SET role_id = 2 WHERE role_id = 2; -- 普通管理员 -> 管理员
UPDATE users SET role_id = 4 WHERE role_id = 3; -- 只读用户 -> 查看员
UPDATE users SET role_id = 3 WHERE role_id = 4; -- 财务专员 -> 操作员

-- 7. 删除临时角色
DELETE FROM user_roles WHERE id = 5;

SELECT '数据库权限结构更新完成' as message;
