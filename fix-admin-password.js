import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function fixAdminPassword() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 生成正确的密码hash
    const password = '123456';
    const passwordHash = Buffer.from(password).toString('base64');
    console.log('新密码hash:', passwordHash);

    // 更新admin用户的密码
    await connection.query(
      'UPDATE users SET password_hash = ? WHERE username = ?',
      [passwordHash, 'admin']
    );

    console.log('✅ admin用户密码已更新');

    // 同时更新其他测试用户的密码
    const testUsers = ['manager', 'operator', 'viewer'];
    for (const username of testUsers) {
      await connection.query(
        'UPDATE users SET password_hash = ? WHERE username = ?',
        [passwordHash, username]
      );
      console.log(`✅ ${username}用户密码已更新`);
    }

    // 验证更新结果
    const [users] = await connection.query(`
      SELECT username, password_hash FROM users 
      WHERE username IN ('admin', 'manager', 'operator', 'viewer')
    `);

    console.log('\n📊 验证更新结果:');
    users.forEach(user => {
      console.log(`  ${user.username}: ${user.password_hash}`);
    });

  } catch (error) {
    console.error('❌ 修复密码失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行修复
fixAdminPassword().catch(console.error);
