-- 创建财务信息表
CREATE TABLE IF NOT EXISTS company_finances (
    id INT AUTO_INCREMENT PRIMARY KEY,
    company_id INT NOT NULL,
    year VARCHAR(4) NOT NULL,
    total_assets DECIMAL(15,4) DEFAULT 0 COMMENT '资产总额（万元人民币）',
    total_liabilities DECIMAL(15,4) DEFAULT 0 COMMENT '负债总额（万元人民币）',
    total_equity DECIMAL(15,4) DEFAULT 0 COMMENT '所有者权益合计（万元人民币）',
    business_income DECIMAL(15,4) DEFAULT 0 COMMENT '营业总收入（万元人民币）',
    main_business_income DECIMAL(15,4) DEFAULT 0 COMMENT '主营业务收入（万元人民币）',
    profit_before_tax DECIMAL(15,4) DEFAULT 0 COMMENT '利润总额（万元人民币）',
    net_profit DECIMAL(15,4) DEFAULT 0 COMMENT '净利润（万元人民币）',
    tax_payable DECIMAL(15,4) DEFAULT 0 COMMENT '纳税总额（万元人民币）',
    remarks TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    UNIQUE KEY unique_company_year (company_id, year)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司财务信息表';

-- 创建股东及出资信息表
CREATE TABLE IF NOT EXISTS shareholder_contributions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    finance_id INT NOT NULL,
    shareholder_name VARCHAR(255) NOT NULL COMMENT '股东名称',
    contribution_amount DECIMAL(15,4) DEFAULT 0 COMMENT '实缴出资额（万元人民币）',
    contribution_method VARCHAR(100) DEFAULT '' COMMENT '实缴出资方式',
    contribution_time DATE NULL COMMENT '实缴出资时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (finance_id) REFERENCES company_finances(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股东及出资信息表';

-- 创建索引以提高查询性能
CREATE INDEX idx_company_finances_company_id ON company_finances(company_id);
CREATE INDEX idx_company_finances_year ON company_finances(year);
CREATE INDEX idx_shareholder_contributions_finance_id ON shareholder_contributions(finance_id);
CREATE INDEX idx_shareholder_contributions_shareholder_name ON shareholder_contributions(shareholder_name);
