# API接口适配指南

## 📋 概述
本文档说明如何将现有API接口适配到新的数据库结构，确保前端页面功能正常。

## 🔄 主要变更

### 1. 股东信息相关API

#### 原接口结构
```javascript
// GET /api/shareholders
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "张三",
      "type": "external",
      "createTime": "2025-06-23 10:00:00"
    }
  ]
}

// GET /api/investment-records
{
  "success": true,
  "data": [
    {
      "id": 1,
      "shareholderId": 1,
      "companyName": "公司A",
      "investmentAmount": "100万",
      "percentage": "10%",
      "startDate": "2023-01-01"
    }
  ]
}
```

#### 新接口结构
```javascript
// GET /api/persons (替代 /api/shareholders)
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "张三",
      "idType": "身份证",
      "idNumber": "123456789012345678",
      "phone": "13800138000",
      "email": "zhang<PERSON>@example.com",
      "createTime": "2025-06-23 10:00:00"
    }
  ]
}

// GET /api/shareholdings (替代 /api/investment-records)
{
  "success": true,
  "data": [
    {
      "id": 1,
      "personId": 1,
      "personName": "张三",
      "companyId": 1,
      "companyName": "公司A",
      "investmentAmount": 100.0000,
      "percentage": 10.00,
      "isProxy": false,
      "actualShareholderId": null,
      "actualShareholderName": null,
      "startDate": "2023-01-01",
      "endDate": null,
      "isActive": true,
      "createTime": "2025-06-23 10:00:00"
    }
  ]
}
```

### 2. 任职档案相关API

#### 新接口结构
```javascript
// GET /api/employments (替代 /api/employment-records)
{
  "success": true,
  "data": [
    {
      "id": 1,
      "personId": 1,
      "personName": "张三",
      "companyId": 1,
      "companyName": "公司A",
      "position": "董事",
      "startDate": "2023-01-01",
      "endDate": null,
      "isActive": true,
      "createTime": "2025-06-23 10:00:00"
    }
  ]
}
```

### 3. 公司信息相关API

#### 新接口结构
```javascript
// GET /api/companies
{
  "success": true,
  "data": [
    {
      "id": 1,
      "companyNameCn": "公司A",
      "companyNameEn": "Company A",
      "companyCode": "CA001",
      "registeredCapital": 1000.0000,
      "establishDate": "2020-01-01",
      "businessSegmentId": 1,
      "businessSegmentName": "制造业",
      "regionId": 1,
      "regionName": "深圳",
      "regionType": "国内",
      "agencyId": 1,
      "agencyName": "代理机构A",
      "annualUpdateStatusId": 1,
      "annualUpdateStatusName": "管年审",
      "operationStatusId": 1,
      "operationStatusName": "正常经营",
      "registeredAddress": "深圳市南山区...",
      "createTime": "2025-06-23 10:00:00"
    }
  ]
}
```

## 🛠️ 具体适配步骤

### 步骤1: 更新服务器端API

#### 1.1 股东信息API适配
```javascript
// 新增获取人员列表API
app.get('/api/persons', async (req, res) => {
  try {
    const [rows] = await pool.query(`
      SELECT 
        id, name, id_type as idType, id_number as idNumber,
        phone, email,
        DATE_FORMAT(created_at, "%Y-%m-%d %H:%i:%s") as createTime
      FROM persons 
      ORDER BY created_at DESC
    `);
    
    res.json({ success: true, data: rows });
  } catch (error) {
    console.error('获取人员数据失败:', error);
    res.status(500).json({ success: false, message: '获取人员数据失败' });
  }
});

// 新增获取股东关系API
app.get('/api/shareholdings', async (req, res) => {
  try {
    const [rows] = await pool.query(`
      SELECT 
        s.id,
        s.person_id as personId,
        p.name as personName,
        s.company_id as companyId,
        c.company_name_cn as companyName,
        s.investment_amount as investmentAmount,
        s.percentage,
        s.is_proxy as isProxy,
        s.actual_shareholder_id as actualShareholderId,
        ap.name as actualShareholderName,
        s.start_date as startDate,
        s.end_date as endDate,
        s.is_active as isActive,
        DATE_FORMAT(s.created_at, "%Y-%m-%d %H:%i:%s") as createTime
      FROM shareholdings s
      LEFT JOIN persons p ON s.person_id = p.id
      LEFT JOIN companies c ON s.company_id = c.id
      LEFT JOIN persons ap ON s.actual_shareholder_id = ap.id
      WHERE s.is_active = true
      ORDER BY c.company_name_cn ASC, s.start_date ASC
    `);
    
    res.json({ success: true, data: rows });
  } catch (error) {
    console.error('获取股东关系失败:', error);
    res.status(500).json({ success: false, message: '获取股东关系失败' });
  }
});
```

#### 1.2 任职档案API适配
```javascript
// 更新任职记录API
app.get('/api/employments', async (req, res) => {
  try {
    const [rows] = await pool.query(`
      SELECT 
        e.id,
        e.person_id as personId,
        p.name as personName,
        e.company_id as companyId,
        c.company_name_cn as companyName,
        e.position,
        e.start_date as startDate,
        e.end_date as endDate,
        e.is_active as isActive,
        DATE_FORMAT(e.created_at, "%Y-%m-%d %H:%i:%s") as createTime
      FROM employments e
      LEFT JOIN persons p ON e.person_id = p.id
      LEFT JOIN companies c ON e.company_id = c.id
      WHERE e.is_active = true
      ORDER BY c.company_name_cn ASC, e.start_date ASC
    `);
    
    res.json({ success: true, data: rows });
  } catch (error) {
    console.error('获取任职记录失败:', error);
    res.status(500).json({ success: false, message: '获取任职记录失败' });
  }
});
```

#### 1.3 公司信息API适配
```javascript
// 更新公司信息API，包含关联数据
app.get('/api/companies', async (req, res) => {
  try {
    const [rows] = await pool.query(`
      SELECT 
        c.id,
        c.company_name_cn as companyNameCn,
        c.company_name_en as companyNameEn,
        c.company_code as companyCode,
        c.registered_capital as registeredCapital,
        c.establish_date as establishDate,
        c.business_segment_id as businessSegmentId,
        bs.name as businessSegmentName,
        c.region_id as regionId,
        r.name as regionName,
        r.type as regionType,
        c.agency_id as agencyId,
        a.name as agencyName,
        c.annual_update_status_id as annualUpdateStatusId,
        aus.name as annualUpdateStatusName,
        c.operation_status_id as operationStatusId,
        os.name as operationStatusName,
        c.registered_address as registeredAddress,
        DATE_FORMAT(c.created_at, "%Y-%m-%d %H:%i:%s") as createTime
      FROM companies c
      LEFT JOIN business_segments bs ON c.business_segment_id = bs.id
      LEFT JOIN regions r ON c.region_id = r.id
      LEFT JOIN agencies a ON c.agency_id = a.id
      LEFT JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      LEFT JOIN operation_status os ON c.operation_status_id = os.id
      ORDER BY c.created_at DESC
    `);
    
    res.json({ success: true, data: rows });
  } catch (error) {
    console.error('获取公司数据失败:', error);
    res.status(500).json({ success: false, message: '获取公司数据失败' });
  }
});
```

### 步骤2: 更新前端页面

#### 2.1 股东信息页面适配
```javascript
// ShareholderInfo.tsx 中的API调用更新
const fetchShareholders = async () => {
  try {
    const response = await axios.get('/api/persons'); // 改为persons
    if (response.data.success) {
      setShareholders(response.data.data);
    }
  } catch (error) {
    console.error('获取股东数据失败:', error);
    message.error('连接数据库失败');
  }
};

const fetchInvestmentRecords = async () => {
  try {
    const response = await axios.get('/api/shareholdings'); // 改为shareholdings
    if (response.data.success) {
      setInvestmentRecords(response.data.data);
    }
  } catch (error) {
    console.error('获取投资记录失败:', error);
    message.error('连接数据库失败');
  }
};

// 更新表格列定义
const columns = [
  {
    title: '公司名称',
    dataIndex: 'companyName', // 保持不变
    key: 'companyName'
  },
  {
    title: '投资金额',
    dataIndex: 'investmentAmount', // 保持不变
    key: 'investmentAmount',
    render: (text) => text ? `${text}万元` : ''
  },
  {
    title: '持股比例',
    dataIndex: 'percentage', // 保持不变
    key: 'percentage',
    render: (text) => text ? `${text}%` : ''
  },
  {
    title: '是否代持',
    dataIndex: 'isProxy', // 新增字段
    key: 'isProxy',
    render: (isProxy) => isProxy ? '是' : '否'
  },
  {
    title: '实际股东',
    dataIndex: 'actualShareholderName', // 新增字段
    key: 'actualShareholderName',
    render: (text) => text || '-'
  }
  // ... 其他列
];
```

#### 2.2 任职档案页面适配
```javascript
// EmploymentArchive.tsx 中的API调用更新
const fetchPersons = async () => {
  try {
    const response = await axios.get('/api/persons'); // 统一使用persons
    if (response.data.success) {
      setPersons(response.data.data);
    }
  } catch (error) {
    console.error('获取人员数据失败:', error);
    message.error('连接数据库失败');
  }
};

const fetchEmploymentRecords = async () => {
  try {
    const response = await axios.get('/api/employments'); // 改为employments
    if (response.data.success) {
      setEmploymentRecords(response.data.data);
    }
  } catch (error) {
    console.error('获取任职记录失败:', error);
    message.error('连接数据库失败');
  }
};
```

### 步骤3: 添加基础数据API

#### 3.1 基础数据获取API
```javascript
// 获取所有基础数据的API
app.get('/api/base-data', async (req, res) => {
  try {
    const [segments] = await pool.query('SELECT id, name FROM business_segments WHERE is_active = true ORDER BY name');
    const [regions] = await pool.query('SELECT id, type, name FROM regions WHERE is_active = true ORDER BY type, name');
    const [agencies] = await pool.query('SELECT id, name FROM agencies WHERE is_active = true ORDER BY name');
    const [annualStatus] = await pool.query('SELECT id, name FROM annual_update_status WHERE is_active = true ORDER BY name');
    const [operationStatus] = await pool.query('SELECT id, name FROM operation_status WHERE is_active = true ORDER BY name');
    const [contributionMethods] = await pool.query('SELECT id, name FROM contribution_methods WHERE is_active = true ORDER BY name');
    const [changeTypes] = await pool.query('SELECT id, name FROM change_types WHERE is_active = true ORDER BY name');
    
    res.json({
      success: true,
      data: {
        businessSegments: segments,
        regions: regions,
        agencies: agencies,
        annualUpdateStatus: annualStatus,
        operationStatus: operationStatus,
        contributionMethods: contributionMethods,
        changeTypes: changeTypes
      }
    });
  } catch (error) {
    console.error('获取基础数据失败:', error);
    res.status(500).json({ success: false, message: '获取基础数据失败' });
  }
});
```

## 🧪 测试验证

### API测试清单
- [ ] GET /api/persons 返回正确的人员数据
- [ ] GET /api/shareholdings 返回正确的股东关系数据
- [ ] GET /api/employments 返回正确的任职记录数据
- [ ] GET /api/companies 返回包含关联数据的公司信息
- [ ] GET /api/base-data 返回所有基础数据
- [ ] POST /api/persons 创建人员功能正常
- [ ] POST /api/shareholdings 创建股东关系功能正常
- [ ] PUT /api/persons/:id 更新人员功能正常
- [ ] DELETE /api/persons/:id 删除人员功能正常

### 前端功能测试清单
- [ ] 股东信息页面数据加载正常
- [ ] 股东信息页面新增功能正常
- [ ] 股东信息页面编辑功能正常
- [ ] 股东信息页面删除功能正常
- [ ] 任职档案页面数据加载正常
- [ ] 任职档案页面CRUD功能正常
- [ ] 公司信息页面下拉选项正常
- [ ] 所有表单验证功能正常

## 📝 注意事项

### 1. 数据类型变更
- 投资金额从字符串改为数字类型
- 持股比例从字符串改为数字类型
- 日期格式保持一致

### 2. 字段名称变更
- `shareholderId` → `personId`
- `companyName` 保持不变，但来源改为关联查询

### 3. 新增字段处理
- `isProxy` - 是否代持
- `actualShareholderId` - 实际股东ID
- `actualShareholderName` - 实际股东姓名
- `isActive` - 记录是否有效

### 4. 兼容性考虑
- 保持原有API路径，通过重定向或别名支持
- 逐步迁移，避免一次性全部更改
- 提供数据格式转换函数

## 🔄 迁移时间表

### 第一周：后端API适配
- 创建新的API接口
- 保持旧接口兼容性
- 进行API测试

### 第二周：前端页面适配
- 更新前端API调用
- 测试页面功能
- 修复发现的问题

### 第三周：全面测试和优化
- 端到端功能测试
- 性能优化
- 文档更新

### 第四周：上线和监控
- 生产环境部署
- 监控系统运行
- 处理用户反馈
