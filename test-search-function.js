import axios from 'axios';

async function testSearchFunction() {
  try {
    console.log('🧪 测试搜索功能...');
    
    // 测试1：搜索"深圳"
    console.log('\n📊 测试1：搜索关键字"深圳"');
    const response1 = await axios.get('http://localhost:8080/api/tasks', {
      params: {
        keyword: '深圳',
        page: 1,
        limit: 100
      }
    });

    if (response1.data.success) {
      const tasks1 = response1.data.data.list || [];
      console.log(`✅ 找到 ${tasks1.length} 个包含"深圳"的任务:`);
      tasks1.forEach((task, index) => {
        console.log(`  ${index + 1}. ID: ${task.id}, 公司: ${task.companyName}, 类型: ${task.taskType}, 状态: ${task.taskStatus}`);
      });
    }

    // 测试2：搜索"测试"
    console.log('\n📊 测试2：搜索关键字"测试"');
    const response2 = await axios.get('http://localhost:8080/api/tasks', {
      params: {
        keyword: '测试',
        page: 1,
        limit: 100
      }
    });

    if (response2.data.success) {
      const tasks2 = response2.data.data.list || [];
      console.log(`✅ 找到 ${tasks2.length} 个包含"测试"的任务:`);
      tasks2.forEach((task, index) => {
        console.log(`  ${index + 1}. ID: ${task.id}, 公司: ${task.companyName}, 类型: ${task.taskType}, 状态: ${task.taskStatus}`);
      });
    }

    // 测试3：搜索"不存在的公司"
    console.log('\n📊 测试3：搜索关键字"不存在的公司"');
    const response3 = await axios.get('http://localhost:8080/api/tasks', {
      params: {
        keyword: '不存在的公司',
        page: 1,
        limit: 100
      }
    });

    if (response3.data.success) {
      const tasks3 = response3.data.data.list || [];
      console.log(`✅ 找到 ${tasks3.length} 个包含"不存在的公司"的任务:`);
      tasks3.forEach((task, index) => {
        console.log(`  ${index + 1}. ID: ${task.id}, 公司: ${task.companyName}, 类型: ${task.taskType}, 状态: ${task.taskStatus}`);
      });
    }

    // 测试4：组合搜索（状态+关键字）
    console.log('\n📊 测试4：组合搜索（状态=已完成 + 关键字="以为"）');
    const response4 = await axios.get('http://localhost:8080/api/tasks', {
      params: {
        status: '已完成',
        keyword: '以为',
        page: 1,
        limit: 100
      }
    });

    if (response4.data.success) {
      const tasks4 = response4.data.data.list || [];
      console.log(`✅ 找到 ${tasks4.length} 个状态为"已完成"且包含"以为"的任务:`);
      tasks4.forEach((task, index) => {
        console.log(`  ${index + 1}. ID: ${task.id}, 公司: ${task.companyName}, 类型: ${task.taskType}, 状态: ${task.taskStatus}`);
      });
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

// 运行测试
testSearchFunction();
