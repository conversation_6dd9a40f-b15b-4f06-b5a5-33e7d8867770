# 股权管理系统 - 业务逻辑确认清单

## 📋 概述
本文档用于记录各个页面需要进一步确认的业务逻辑和内容，确保系统功能符合实际业务需求。

---

## 🗄️ 数据库配置页面
**路径**: 系统设置 → 数据库配置  
**状态**: ✅ 已修复图标兼容性问题

### 待确认事项
- [ ] 数据库连接参数的默认值是否符合实际环境
- [ ] 高级设置中的连接池大小、超时时间等参数是否合理
- [ ] 是否需要支持其他数据库类型（PostgreSQL、SQLite等）
- [ ] 配置保存后是否需要重启服务或热重载

---

## 📊 系统设置
### 用户管理
**路径**: 系统设置 → 用户管理  
**状态**: 待测试

### 待确认事项
- [ ] 用户角色权限体系设计
- [ ] 密码复杂度要求
- [ ] 用户状态管理（启用/禁用）
- [ ] 登录日志记录需求

---

## 📈 待办任务提醒
### 待确认事项
- [x] 最上方统计数据，任务状态应该保留哪几个？各个状态的判断逻辑是什么？
      - 一共四个状态：未开始，进行中，已完成，已核实
      - 待核实：最后主管审核审批
      - 当新增任务时，任务状态默认为未开始
      - 执行岗点击更新，选择进行中，任务状态更新为“进行中”
      - 进行中的任务上传完文件后，执行岗点击更新状态，选择“已完成”，任务状态更新为“已完成”
      - 已完成的任务才可以点击“核实”按钮，管理岗点击“已核实”确认后，任务状态更新为“已核实”
      - 已逾期，不作为一个状态：（状态为“未开始”或“进行中”）且系统当前日期＞任务截止日期，任务状态和截止日期都标为红色
      - 状态为“已完成”的，状态和截止日期标为绿色
      - “核实”菜单更新为“核实任务”
      - 点击菜单“核实”列出所有已完成的任务项
      - 下一周期任务创建应当为核实任务的所属年度的下一年度，而不是当前系统时间的下一年度
- [x] 最上方统计数据中“未完成”的计算逻辑是什么？
- [x] 不同状态的任务，可执行的编辑操作是什么？
      - 已完成状态可以点击更新改变上传文件，可点击核实
      - 未开始状态可以点击更新，不可以点击核实
      - 进行中状态可以点击更新，不可以点击核实
- [x] 待办任务提醒-排查页面中的自动补全任务的截止日期和开始日期什么规则
      - 不管年审：不会创建任何年审年报任务和地址维护任务
      - 固定周期：开始日期为当年1月1日，结束日期为当年6月30日
      - 滚动周期：开始日期为设立日期对应今年的日期 - 3个月，结束日期为设立日期对应今年的日期 + 2个月
      - 地址维护：
          - 假设签3年合同，每年需要提醒合同到期日
          - 重复周期（年）修改为每年提醒，下拉列表选择是或否
          - 任务的开始日期与结束日期跟合同保持一致
          - 如果每年提醒为是，下一年度的1月1日自动创建一条一样的任务
          - 如果每年提醒为否，则不会自动创建下一年度的任务，补全任务里也不需要排查和补全
- [x] 待办任务提醒-排查页面中的年份是什么规则，是N-1
- [x] 待办任务提醒-核实页面：自动创建的下一年度任务应该和本任务一样，所以任务类型不用选，主体也不用选，这里画的主体可以多选的逻辑是什么？
      - 核实任务页面展现已完成任务清单，每个任务旁边有核实按钮
      - 用户点击核实，弹窗出现该任务的详情，包括文件，文件可点击打开，弹窗中有“核实”和“取消”按钮，用户点击“核实”按钮，任务状态变为“已核实”，更新数据库信息，用户点击“取消”按钮，返回核实任务页面
- [x] 查询按钮工作不正常
- [ ] 不管年审的公司是否需要维护地址？

---

## 📈 公司信息变更确认
### 待确认事项
- [x] 点击确认后，弹窗里内容是什么？———按照现有的就OK
- [x] 在确认弹窗里，是否需要包含取消，确认，退回三个按钮，如果退回退回到哪里？
- [x] 退回后，任务状态变成什么？
      - 变更提交后，状态变为“待确认”，管理者用户点击确认按钮弹窗显示该变更详情，点击“确定”按钮后，状态变为“已确认”，点击“退回”按钮后，状态变为“有异常”
      - 在执行者界面，只能看到编辑按钮，不能看到确认按钮，在管理者用户界面，只能看到确认按钮，不能看到编辑按钮
- [x] 变更基础信息页，为什么有确认？如果确认是下一个步骤，那页面和提交变更页面应该不同，不应该是一个页面
- [x] 点击任何一条记录，弹窗出现变更的详细信息，弹窗中变更内容逐条换行显示
- [x] 变更基础信息的代理机构好像是硬编码数据，不是从数据库导入

---

## 📈 档案更新规范
### 待确认事项
- [x] 新增档案更新规范页面，选择不同的变更类型时，变更字段是否应该不一样？
      - 是的。✅为允许用户修改内容，不✅为用户不能修改内容
      - 不同变更类型，变更字段的内容应该不同，和新增公司页面保持一致
- [x] 编辑档案更新规范页面，展示内容与新增档案规范页面保持一致

---

## 📈 公司信息-公司信息
### 待确认事项
- [x] 地区类型增加一个“离岸”
- [x] 没找到——变更详情：点击数字，进入公司详情定位到变更历史
      - 点击“变更历史”旁边的数字，进入该公司的详情页面并且定位到变更历史
- [x] 小铃铛上的数字代表什么？
      - 小铃铛代表有多少个待办任务，作为提醒
- [x] 每个公司卡片右上角的灰色内容是什么状态？——可以先不管
- [ ] 没找到——注销：选择公司，点击注销，进入功能
- [ ] 点击删除按钮，删除的是变更信息，还是公司信息，还是该公司的所有信息？
- [ ] 没找到——待办：该公司的未完成的待办数量，点击后跳转到待办任务提醒，并筛选该公司的待办
      - 待办指的小铃铛？

---

## 📈 公司信息-公司详情
### 待确认事项
- [x] 是否要添加一个按钮“添加财务信息”——不需要
- [x] 财务信息和股本出资信息应该是作为一个模块，需要整合

---

## 📈 公司信息-新增公司
### 待确认事项
- [X] 新增公司页面：是否要添加一个按钮“添加财务信息”——不需要
- [x] 新增公司页面：提交后在数据库中添加操作人用户信息，不需要显示
- [x] 地区是下拉列表，选择所有地区供用户单选
- [X] 股东信息中，如果用户未选择，代持字段默认值为否
- [x] 添加股东，登记股东和实际股东都是下拉列表
- [x] 添加对外投资信息，主体名称为下拉列表，选项是所有的已经建立公司档案的企业，增加是否登记股东权益下拉列表选择是或者否
- [x] 非代持为实线，代持为虚线
- [x] 股权关系图：法务版只显示登记股东，不显示代持关系
- [x] 股权关系图：投资者版既显示登记股东，同时显示代持关系
- [x] 股权关系图：对外投资信息-是否登记投资权益如果为是的，法务版显示，如果为否，法务版不显示，投资者版显示
- [ ] 录入公司名字时需要检查该名字是否为持股公司，如果是持股公司，在保存新增公司记录时，删除其作为股东公司的信息
- [ ]持股公司转建档案公司怎么转的问题

---

## 📈 业务板块
### 待确认事项
- [x] 查询按钮无法正常工作

---

## 📈 股东信息
### 待确认事项
- [x] 只能出现未建立公司档案的持股公司，已经在公司档案中存在的企业，不能出现在此列表中（在公司信息页面出现的企业，不能出现在股东信息页面的列表中）
- [x] 未建立公司档案的企业不需要在股权关系图上继续往上穿透股权关系

---

## 📈 任职档案
### 待确认事项
- [x] 是否需要保留历史的任职信息记录
- [x] 新增人员页面是否可以去除

---

## 📈 业务板块
### 已完成功能
- [x] 公司数量点击功能：点击数字弹出该业务板块下的公司列表
- [x] 公司详情跳转：点击公司名称跳转到公司详情页面并关闭弹窗
- [x] 弹窗返回按钮：点击返回按钮关闭弹窗

### 已解决问题
#### 问题1：公司列表弹窗无数据显示
**问题描述**: 点击业务板块的公司数量数字时，弹窗打开但没有显示任何公司信息

**问题原因**: 后端Express.js路由顺序冲突
- 通用路由 `/api/companies/:id` 定义在前，会拦截所有 `/api/companies/xxx` 的请求
- 具体路由 `/api/companies/by-segment` 定义在后，永远不会被匹配到
- 导致前端请求 `/api/companies/by-segment?segment=电子商务` 时，被错误解析为 `/api/companies/:id`，其中 `id` 参数为 `"by-segment"`

**解决方案**:
1. 调整后端路由定义顺序，将具体路由放在通用路由之前：
   ```javascript
   // 具体路由 - 必须放在 :id 路由之前
   app.get('/api/companies/by-segment', async (req, res) => { ... });

   // 通用路由 - 必须放在具体路由之后
   app.get('/api/companies/:id', async (req, res) => { ... });
   ```
2. 删除重复的路由定义
3. 重启服务器使路由修改生效

**测试结果**: ✅ 功能正常
- 点击公司数量正确显示公司列表
- 公司信息完整显示（中文名、英文名、注册资本、成立日期、地区）
- 点击公司名称正确跳转到公司详情页面

**技术要点**: Express.js路由匹配是按定义顺序进行的，更具体的路由必须放在更通用的路由之前

#### 问题2：公司详情页面显示空白
**问题描述**: 从业务板块页面点击公司名称跳转到公司详情页面时，页面显示空白，没有任何公司信息

**问题原因**: 公司详情页面API接口缺失
- 前端页面 `CompanyDetailPage.tsx` 调用 `getCompanyById(id)` 获取公司详情
- 后端缺少对应的 `/api/companies/:id` 路由实现
- 导致API调用失败，页面无法获取数据显示空白

**解决方案**:
1. 在后端 `server/index.js` 中添加公司详情API接口：
   ```javascript
   // 根据ID获取单个公司详情
   app.get('/api/companies/:id', async (req, res) => {
     try {
       const { id } = req.params;
       const [rows] = await pool.query(
         `SELECT
           id,
           company_name_cn as chineseName,
           company_name_en as englishName,
           registered_capital as registeredCapital,
           establish_date as establishmentDate,
           business_segment as businessScope,
           region,
           agency as agentOrganization,
           annual_update as annualReport,
           registered_address as registrationAddress,
           operation_status as operationStatus
         FROM companies
         WHERE id = ?`,
         [id]
       );

       if (rows.length === 0) {
         return res.status(404).json({ success: false, message: '公司不存在' });
       }

       res.json(rows[0]);
     } catch (error) {
       console.error('获取公司详情失败:', error);
       res.status(500).json({ success: false, message: '获取公司详情失败' });
     }
   });
   ```
2. 确保数据库字段映射正确，前端期望的字段名与后端返回的字段名一致
3. 重启服务器使新接口生效

**测试结果**: ✅ 功能正常
- 公司详情页面正确显示公司基本信息
- 所有字段数据正确映射和显示
- 页面布局和样式正常

**技术要点**: 前后端API接口设计需要保持一致，确保字段名映射正确

### 待确认事项
- [ ] 新增业务板块页面是否可以去除

---

## 📈 股权管理
### 待确认事项
- [ ] 股权结构展示方式
- [ ] 股权变更流程和审批机制
- [ ] 历史记录保存策略
- [ ] 报表生成需求

---

## 📝 基础数据管理
### 待确认事项
- [ ] 数据字典维护范围
- [ ] 数据导入导出格式
- [ ] 数据备份策略
- [ ] 数据同步机制

---

## 🔔 任务提醒
### 待确认事项
- [ ] 提醒触发条件
- [ ] 通知方式（邮件、短信、系统内通知）
- [ ] 提醒频率设置
- [ ] 逾期任务处理流程

---

## 📋 变更信息管理
### 待确认事项
- [ ] 变更类型分类标准
- [ ] 变更审批流程
- [ ] 变更历史追溯要求
- [ ] 变更影响分析

---

## 👥 股东信息管理
### 已解决问题
#### 问题1：股东信息页面API错误和数据不完整
**问题描述**: 股东信息页面显示"获取投资记录失败: Request failed with status code 404"错误，且只显示个人股东，缺少企业股东信息

**问题原因**:
1. 数据库设计中有两个不同的表存储不同类型的股东信息：
   - `shareholdings` 表：存储个人股东持有公司股份的信息
   - `investments` 表：存储企业对企业的投资信息
2. 原始API只查询了 `shareholdings` 表，遗漏了企业股东
3. API字段映射错误：`investments` 表中字段为 `investee_company_name`，不是 `investee_name`

**解决方案**:
1. 修改 `/api/shareholders` API，同时从两个表获取数据：
   ```javascript
   // 1. 从shareholdings表获取个人股东
   const [individualShareholders] = await pool.query(`...FROM persons p INNER JOIN shareholdings s...`);

   // 2. 从investments表获取企业股东
   const [companyShareholders] = await pool.query(`...FROM companies c INNER JOIN investments i...`);

   // 3. 合并数据
   const allShareholders = [...individualShareholders, ...companyShareholders];
   ```

2. 修改 `/api/shareholders/:id/investments` API，支持两种股东类型：
   ```javascript
   // 判断ID类型
   const [personCheck] = await pool.query('SELECT id FROM persons WHERE id = ?', [id]);
   const [companyCheck] = await pool.query('SELECT id FROM companies WHERE id = ?', [id]);

   if (personCheck.length > 0) {
     // 个人股东：查询shareholdings表
   } else if (companyCheck.length > 0) {
     // 企业股东：查询investments表
   }
   ```

3. 修正字段映射：使用正确的字段名 `investee_company_name`

**测试结果**: ✅ 功能正常
- 股东列表正确显示个人股东（9个）和企业股东（12个）
- 个人股东投资记录正常显示（如张大威持有深圳科技有限公司21.19%股份）
- 企业股东投资记录正常显示（如北京投资有限公司的对外投资记录）
- 页面不再显示404错误

**技术要点**:
- 理解数据库表的不同用途：`shareholdings`用于个人持股，`investments`用于企业投资
- API设计需要考虑不同数据源的整合
- 字段映射必须与实际数据库结构一致

### 待确认事项
- [ ] 股东信息字段完整性
- [ ] 股东权益计算规则
- [ ] 股东变更通知机制
- [ ] 股东报告生成需求

---

## 📊 财务信息
### 待确认事项
- [ ] 财务数据录入流程
- [ ] 财务报表模板
- [ ] 数据审核机制
- [ ] 财务数据安全要求

---

## 📁 档案管理
### 待确认事项
- [ ] 文件上传格式限制
- [ ] 文件存储策略
- [ ] 文件访问权限控制
- [ ] 文件版本管理

---

## 🔧 技术相关
### 已解决问题
- ✅ `@ant-design/icons` 兼容性问题已解决（使用emoji图标替代）
- ✅ Express.js路由顺序冲突问题已解决（调整路由定义顺序）

### 技术解决方案记录
#### Express.js路由顺序最佳实践
**问题**: 路由匹配冲突导致API调用失败
**解决方案**:
1. 将具体路由（如 `/api/companies/by-segment`）放在通用路由（如 `/api/companies/:id`）之前
2. Express.js按定义顺序匹配路由，第一个匹配的路由会被执行
3. 删除重复的路由定义避免混淆

**代码示例**:
```javascript
// ✅ 正确顺序
app.get('/api/companies/by-segment', handler1);  // 具体路由在前
app.get('/api/companies/:id', handler2);         // 通用路由在后

// ❌ 错误顺序
app.get('/api/companies/:id', handler2);         // 通用路由在前会拦截所有请求
app.get('/api/companies/by-segment', handler1);  // 具体路由永远不会被匹配
```

#### API接口设计最佳实践
**问题**: 前后端字段映射不一致导致数据显示异常
**解决方案**:
1. 统一前后端字段命名规范（建议使用camelCase）
2. 在SQL查询中使用别名确保返回字段名与前端期望一致
3. 添加适当的错误处理和状态码返回
4. 记录详细的API调用日志便于调试

**代码示例**:
```javascript
// ✅ 正确的字段映射
const [rows] = await pool.query(
  `SELECT
    id,
    company_name_cn as chineseName,    // 数据库字段 -> 前端期望字段
    company_name_en as englishName,
    registered_capital as registeredCapital
  FROM companies
  WHERE id = ?`,
  [id]
);

// ❌ 错误的字段映射
const [rows] = await pool.query(
  'SELECT * FROM companies WHERE id = ?',  // 返回数据库原始字段名
  [id]
);
```

### 待确认事项
- [ ] 系统性能要求
- [ ] 数据安全加密需求
- [ ] 系统备份恢复策略
- [ ] 多用户并发处理能力

---

## 📅 更新日志
- **2025-01-17**: 完成公司信息变更确认页面权限控制功能及问题修复
  - 实现基于用户角色ID的完整权限控制系统
  - 系统管理员和管理员：可确认待确认记录，查看所有记录
  - 操作员：可编辑有异常记录，撤销待确认记录，查看已确认记录
  - 查看员：只能查看所有记录，无编辑权限
  - 动态按钮显示和弹窗权限控制
  - 完整的API集成和测试数据创建
  - 修复JSON数据显示问题：格式化显示原值和新值，不再显示原始JSON字符串
  - 修复系统管理员/管理员点击有异常记录时的弹窗显示问题
  - 修复操作员编辑弹窗中的JSON数据显示问题：改进格式化逻辑，使用多行文本框显示
  - 修复右上角用户信息显示异常问题：添加调试信息、异常检测和缓存清除功能
  - 优化股权图-主体公司页面布局：实现多股东垂直平均分布、缩小股权比例显示框、优化比例框位置避免重叠
  - 修复股权图垂直分布和连接线问题：精确垂直平均分布、修复股权比例位置错误、完整显示代持关系（实际股东+虚线）
- **2025-01-02**: 完成菜单结构简化和页面清理
  - 数据库状态修复：将所有"待核实"状态的任务改为"已完成"状态（8个任务）
  - 菜单简化：删除待办任务提醒、任职档案、业务板块、股东信息模块的多余二级菜单
  - 页面清理：删除AnnualReport.tsx、AddressMaintenance.tsx、CustomTask.tsx、EditTask.tsx、UpdateProgress.tsx、AddPerson.tsx、ShareholderAdd.tsx、ShareholderRegister.tsx等页面文件
  - 路由更新：清理对应的路由配置和import语句
- **2025-06-23**: 解决公司详情页面显示空白问题（API接口缺失）
- **2025-06-23**: 解决业务板块页面公司列表弹窗无数据显示问题（Express.js路由顺序冲突）
- **2025-06-22**: 创建文档，记录数据库配置页面修复情况
- **2025-06-22**: 添加各模块业务逻辑确认清单

---

## 📝 备注
请在测试各个页面时，将发现的业务逻辑问题和需要确认的内容添加到对应模块下。
使用 `- [ ]` 表示待确认项目，完成后改为 `- [x]`。
