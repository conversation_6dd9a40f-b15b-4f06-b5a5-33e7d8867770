import mysql from 'mysql2/promise';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 数据库配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management',
  socketPath: '/tmp/mysql.sock'
};

async function backupDatabase() {
  let connection;

  try {
    console.log('🔄 开始备份数据库...');

    // 连接数据库
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 测试连接
    await connection.execute('SELECT 1');
    console.log('✅ 数据库连接测试成功');
    
    // 获取所有表名
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`📋 发现 ${tables.length} 个表`);
    
    // 创建备份文件
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const backupFileName = `backup_${timestamp}.sql`;
    const backupPath = path.join(__dirname, backupFileName);
    
    let backupContent = `-- 数据库备份文件\n-- 创建时间: ${new Date().toLocaleString()}\n-- 数据库: ${dbConfig.database}\n\n`;
    backupContent += `CREATE DATABASE IF NOT EXISTS \`${dbConfig.database}\`;\n`;
    backupContent += `USE \`${dbConfig.database}\`;\n\n`;
    
    // 备份每个表的结构和数据
    for (const tableRow of tables) {
      const tableName = Object.values(tableRow)[0];
      console.log(`📦 备份表: ${tableName}`);
      
      // 获取表结构
      const [createTable] = await connection.execute(`SHOW CREATE TABLE \`${tableName}\``);
      backupContent += `-- 表结构: ${tableName}\n`;
      backupContent += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
      backupContent += createTable[0]['Create Table'] + ';\n\n';
      
      // 获取表数据
      const [rows] = await connection.execute(`SELECT * FROM \`${tableName}\``);
      if (rows.length > 0) {
        backupContent += `-- 表数据: ${tableName}\n`;
        
        // 获取列名
        const [columns] = await connection.execute(`SHOW COLUMNS FROM \`${tableName}\``);
        const columnNames = columns.map(col => `\`${col.Field}\``).join(', ');
        
        backupContent += `INSERT INTO \`${tableName}\` (${columnNames}) VALUES\n`;
        
        const values = rows.map(row => {
          const rowValues = Object.values(row).map(value => {
            if (value === null) return 'NULL';
            if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`;
            if (value instanceof Date) return `'${value.toISOString().slice(0, 19).replace('T', ' ')}'`;
            return value;
          });
          return `(${rowValues.join(', ')})`;
        });
        
        backupContent += values.join(',\n') + ';\n\n';
      }
    }
    
    // 写入备份文件
    fs.writeFileSync(backupPath, backupContent);
    console.log(`✅ 备份完成: ${backupFileName}`);
    console.log(`📁 备份文件路径: ${backupPath}`);
    
    // 验证备份文件
    const stats = fs.statSync(backupPath);
    console.log(`📊 备份文件大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
    
    return backupPath;
    
  } catch (error) {
    console.error('❌ 备份失败:', error);
    console.error('错误详情:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行备份
if (import.meta.url === `file://${process.argv[1]}`) {
  backupDatabase()
    .then(backupPath => {
      console.log('🎉 数据库备份成功完成!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 备份过程中发生错误:', error);
      process.exit(1);
    });
}

export { backupDatabase };
