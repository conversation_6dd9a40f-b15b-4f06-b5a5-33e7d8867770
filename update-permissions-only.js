import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function updatePermissions() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 1. 清空现有权限和角色权限关联
    console.log('🗑️ 清空现有权限数据...');
    await connection.execute('DELETE FROM role_permissions');
    await connection.execute('DELETE FROM permissions');
    
    // 2. 插入新的权限数据
    console.log('📝 插入新的权限数据...');
    const permissions = [
      // 系统设置权限
      [1, 'system', 'user_add', '新增用户'],
      [2, 'system', 'user_management', '用户管理'],
      [3, 'system', 'database_config', '数据库配置'],

      // 任务管理权限
      [4, 'task', 'view', '查看任务'],
      [5, 'task', 'add', '新增任务'],
      [6, 'task', 'edit', '编辑任务'],
      [7, 'task', 'delete', '删除任务'],
      [8, 'task', 'verify', '核实任务'],
      [9, 'task', 'investigation', '任务排查'],

      // 公司信息权限
      [10, 'company', 'view', '查看公司信息'],
      [11, 'company', 'add', '新增公司'],
      [12, 'company', 'edit', '编辑公司信息'],
      [13, 'company', 'delete', '删除公司'],
      [14, 'company', 'change_confirm', '公司信息变更确认'],
      [15, 'company', 'finance', '财务信息管理'],
      [16, 'company', 'equity_chart', '股权图查看'],

      // 任职档案权限
      [17, 'employment', 'view', '查看任职档案'],
      [18, 'employment', 'add', '新增任职记录'],
      [19, 'employment', 'edit', '编辑任职记录'],
      [20, 'employment', 'delete', '删除任职记录'],

      // 业务板块权限
      [21, 'business', 'view', '查看业务板块'],
      [22, 'business', 'edit', '编辑业务板块'],
      [23, 'business', 'equity_chart', '业务板块股权图'],

      // 股东信息权限
      [24, 'shareholder', 'view', '查看股东信息'],
      [25, 'shareholder', 'add', '新增股东'],
      [26, 'shareholder', 'edit', '编辑股东信息'],
      [27, 'shareholder', 'delete', '删除股东'],

      // 基础数据权限
      [28, 'basic_data', 'view', '查看基础数据'],
      [29, 'basic_data', 'edit', '编辑基础数据'],

      // 档案更新规范权限
      [30, 'archive', 'view', '查看档案规范'],
      [31, 'archive', 'add', '新增档案规范'],
      [32, 'archive', 'edit', '编辑档案规范'],
      [33, 'archive', 'delete', '删除档案规范']
    ];

    for (const [id, module, action, description] of permissions) {
      await connection.execute(
        'INSERT INTO permissions (id, module, action, description, created_at) VALUES (?, ?, ?, ?, NOW())',
        [id, module, action, description]
      );
    }

    // 3. 设置角色权限关联
    console.log('🔗 设置角色权限关联...');
    
    // 系统管理员权限：系统设置操作权限 + 其他所有查看权限
    const systemAdminPerms = [1, 2, 3, 4, 10, 16, 17, 21, 23, 24, 28, 30];
    for (const permId of systemAdminPerms) {
      await connection.execute(
        'INSERT INTO role_permissions (role_id, permission_id, created_at) VALUES (?, ?, NOW())',
        [1, permId]
      );
    }

    // 管理员权限：除系统设置外的所有操作权限
    const managerPerms = [4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33];
    for (const permId of managerPerms) {
      await connection.execute(
        'INSERT INTO role_permissions (role_id, permission_id, created_at) VALUES (?, ?, NOW())',
        [2, permId]
      );
    }

    // 操作员权限：初步与管理员一致
    for (const permId of managerPerms) {
      await connection.execute(
        'INSERT INTO role_permissions (role_id, permission_id, created_at) VALUES (?, ?, NOW())',
        [3, permId]
      );
    }

    // 查看员权限：除系统设置外的所有查看权限
    const viewerPerms = [4, 10, 16, 17, 21, 23, 24, 28, 30];
    for (const permId of viewerPerms) {
      await connection.execute(
        'INSERT INTO role_permissions (role_id, permission_id, created_at) VALUES (?, ?, NOW())',
        [4, permId]
      );
    }

    // 4. 删除临时角色
    console.log('🗑️ 删除临时角色...');
    await connection.execute('DELETE FROM user_roles WHERE id = 5');

    // 验证更新结果
    console.log('\n🔍 验证更新结果...');
    
    // 检查权限数量
    const [permCount] = await connection.execute('SELECT COUNT(*) as count FROM permissions');
    console.log(`📊 权限总数: ${permCount[0].count}`);

    // 检查角色权限关联数量
    const [rolePermCount] = await connection.execute('SELECT COUNT(*) as count FROM role_permissions');
    console.log(`🔗 角色权限关联数: ${rolePermCount[0].count}`);

    // 检查各角色的权限数量
    const [rolePermDetails] = await connection.execute(`
      SELECT ur.name as role_name, COUNT(rp.permission_id) as perm_count 
      FROM user_roles ur 
      LEFT JOIN role_permissions rp ON ur.id = rp.role_id 
      GROUP BY ur.id, ur.name 
      ORDER BY ur.id
    `);
    console.log('🎯 各角色权限数量:');
    rolePermDetails.forEach(rp => {
      console.log(`  ${rp.role_name}: ${rp.perm_count} 个权限`);
    });

    console.log('\n🎉 权限系统更新完成！');

  } catch (error) {
    console.error('❌ 更新失败:', error);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行更新
updatePermissions().catch(console.error);
