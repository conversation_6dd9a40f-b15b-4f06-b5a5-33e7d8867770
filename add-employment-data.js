import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function addEmploymentData() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 任职记录数据 - 基于现有的公司和人员
    const employmentData = [
      // 深圳科技有限公司 (ID: 43)
      { person_id: 53, company_id: 43, position: '法定代表人', start_date: '2020-01-15', end_date: null, is_active: 1 }, // 张三
      { person_id: 53, company_id: 43, position: '董事长', start_date: '2020-01-15', end_date: null, is_active: 1 }, // 张三
      { person_id: 54, company_id: 43, position: '总经理', start_date: '2020-02-01', end_date: null, is_active: 1 }, // 李四
      { person_id: 55, company_id: 43, position: '财务负责人', start_date: '2020-03-01', end_date: null, is_active: 1 }, // 王五
      
      // 北京投资控股有限公司 (ID: 44)
      { person_id: 56, company_id: 44, position: '法定代表人', start_date: '2019-06-01', end_date: null, is_active: 1 }, // 赵六
      { person_id: 56, company_id: 44, position: '执行董事', start_date: '2019-06-01', end_date: null, is_active: 1 }, // 赵六
      { person_id: 57, company_id: 44, position: '总经理', start_date: '2019-07-01', end_date: null, is_active: 1 }, // 钱七
      { person_id: 58, company_id: 44, position: '监事', start_date: '2019-06-01', end_date: null, is_active: 1 }, // 孙八
      
      // 上海金融服务有限公司 (ID: 45)
      { person_id: 59, company_id: 45, position: '法定代表人', start_date: '2021-03-15', end_date: null, is_active: 1 }, // 周九
      { person_id: 59, company_id: 45, position: '董事长', start_date: '2021-03-15', end_date: null, is_active: 1 }, // 周九
      { person_id: 60, company_id: 45, position: '总经理', start_date: '2021-04-01', end_date: null, is_active: 1 }, // 吴十
      { person_id: 53, company_id: 45, position: '董事', start_date: '2021-03-15', end_date: null, is_active: 1 }, // 张三
      
      // 广州制造有限公司 (ID: 46)
      { person_id: 54, company_id: 46, position: '法定代表人', start_date: '2018-09-01', end_date: null, is_active: 1 }, // 李四
      { person_id: 54, company_id: 46, position: '执行董事', start_date: '2018-09-01', end_date: null, is_active: 1 }, // 李四
      { person_id: 55, company_id: 46, position: '经理', start_date: '2018-10-01', end_date: null, is_active: 1 }, // 王五
      { person_id: 56, company_id: 46, position: '监事', start_date: '2018-09-01', end_date: null, is_active: 1 }, // 赵六
      
      // 杭州互联网科技有限公司 (ID: 47)
      { person_id: 57, company_id: 47, position: '法定代表人', start_date: '2022-01-10', end_date: null, is_active: 1 }, // 钱七
      { person_id: 57, company_id: 47, position: '董事长', start_date: '2022-01-10', end_date: null, is_active: 1 }, // 钱七
      { person_id: 58, company_id: 47, position: '副总经理', start_date: '2022-02-01', end_date: null, is_active: 1 }, // 孙八
      { person_id: 59, company_id: 47, position: '财务负责人', start_date: '2022-02-15', end_date: null, is_active: 1 }, // 周九
      
      // 成都新能源有限公司 (ID: 48)
      { person_id: 60, company_id: 48, position: '法定代表人', start_date: '2023-05-01', end_date: null, is_active: 1 }, // 吴十
      { person_id: 60, company_id: 48, position: '执行董事', start_date: '2023-05-01', end_date: null, is_active: 1 }, // 吴十
      { person_id: 53, company_id: 48, position: '总经理', start_date: '2023-06-01', end_date: null, is_active: 1 }, // 张三
      
      // 西安软件开发有限公司 (ID: 49)
      { person_id: 54, company_id: 49, position: '法定代表人', start_date: '2021-11-01', end_date: null, is_active: 1 }, // 李四
      { person_id: 54, company_id: 49, position: '董事长', start_date: '2021-11-01', end_date: null, is_active: 1 }, // 李四
      { person_id: 55, company_id: 49, position: '总经理', start_date: '2021-12-01', end_date: null, is_active: 1 }, // 王五
      { person_id: 56, company_id: 49, position: '监事', start_date: '2021-11-01', end_date: null, is_active: 1 }, // 赵六
      
      // 一些历史任职记录（已离职）
      { person_id: 57, company_id: 43, position: '副总经理', start_date: '2020-02-01', end_date: '2021-12-31', is_active: 0 }, // 钱七曾在深圳科技任职
      { person_id: 58, company_id: 45, position: '财务负责人', start_date: '2021-03-15', end_date: '2022-06-30', is_active: 0 }, // 孙八曾在上海金融任职
    ];

    console.log('\n📝 开始添加任职记录...');
    
    let successCount = 0;
    for (const employment of employmentData) {
      try {
        const [result] = await connection.query(`
          INSERT INTO employments (
            person_id, company_id, position, start_date, end_date, is_active, created_by, updated_by
          ) VALUES (?, ?, ?, ?, ?, ?, 1, 1)
        `, [
          employment.person_id,
          employment.company_id,
          employment.position,
          employment.start_date,
          employment.end_date,
          employment.is_active
        ]);
        
        console.log(`✅ 添加任职记录成功 - ID: ${result.insertId}`);
        successCount++;
      } catch (error) {
        console.error(`❌ 添加任职记录失败:`, error.message);
      }
    }

    console.log(`\n📊 任职记录添加完成，成功添加 ${successCount} 条记录`);

    // 验证添加结果
    const [employments] = await connection.query(`
      SELECT e.id, p.name as person_name, c.company_name_cn, e.position, 
             e.start_date, e.end_date, e.is_active
      FROM employments e
      LEFT JOIN persons p ON e.person_id = p.id
      LEFT JOIN companies c ON e.company_id = c.id
      ORDER BY c.company_name_cn, e.position, p.name
    `);
    
    console.log('\n📋 当前任职记录:');
    let currentCompany = '';
    employments.forEach(emp => {
      if (emp.company_name_cn !== currentCompany) {
        currentCompany = emp.company_name_cn;
        console.log(`\n🏢 ${currentCompany}:`);
      }
      const status = emp.is_active ? '在职' : '离职';
      const endDate = emp.end_date ? ` - ${emp.end_date}` : '';
      console.log(`  ${emp.person_name} | ${emp.position} | ${emp.start_date}${endDate} | ${status}`);
    });

  } catch (error) {
    console.error('❌ 添加失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行添加
addEmploymentData();
