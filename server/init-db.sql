-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS stake_management;

-- 使用数据库
USE stake_management;

-- 创建公司表
CREATE TABLE IF NOT EXISTS companies (
  id INT AUTO_INCREMENT PRIMARY KEY,
  company_name_cn VARCHAR(255) NOT NULL COMMENT '公司中文名',
  company_name_en VARCHAR(255) NOT NULL COMMENT '公司英文名',
  registered_capital VARCHAR(50) COMMENT '注册资本（万元）',
  establish_date DATE COMMENT '设立日期',
  business_segment VARCHAR(100) COMMENT '业务板块',
  region VARCHAR(100) COMMENT '地区',
  agency VARCHAR(100) COMMENT '代理机构',
  annual_update VARCHAR(50) COMMENT '年审更新',
  registered_address TEXT COMMENT '注册地址',
  operation_status VARCHAR(50) COMMENT '存续状况',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- 添加唯一索引确保公司中英文名组合唯一
  UNIQUE INDEX idx_company_name (company_name_cn, company_name_en)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建高管信息表
CREATE TABLE IF NOT EXISTS executives (
  id INT AUTO_INCREMENT PRIMARY KEY,
  company_id INT NOT NULL COMMENT '关联的公司ID',
  position VARCHAR(100) COMMENT '职位',
  person VARCHAR(100) COMMENT '任职人员',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- 外键约束
  FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建股东信息表
CREATE TABLE IF NOT EXISTS shareholders (
  id INT AUTO_INCREMENT PRIMARY KEY,
  company_id INT NOT NULL COMMENT '关联的公司ID',
  name VARCHAR(100) COMMENT '股东名称',
  investment_amount VARCHAR(50) COMMENT '认缴出资额（万元）',
  percentage DECIMAL(5,2) COMMENT '持股比例（%）',
  start_date DATE COMMENT '出资日期',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- 外键约束
  FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建对外投资信息表
CREATE TABLE IF NOT EXISTS investments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  company_id INT NOT NULL COMMENT '关联的公司ID',
  company_name VARCHAR(255) COMMENT '投资对象',
  investment_amount VARCHAR(50) COMMENT '投资金额（万元）',
  percentage DECIMAL(5,2) COMMENT '持股比例（%）',
  start_date DATE COMMENT '投资日期',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  -- 外键约束
  FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建业务板块表
CREATE TABLE IF NOT EXISTS business_segments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE COMMENT '业务板块名称',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建地区表
CREATE TABLE IF NOT EXISTS regions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE COMMENT '地区名称',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建代理机构表
CREATE TABLE IF NOT EXISTS agencies (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL UNIQUE COMMENT '代理机构名称',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 插入一些示例数据

-- 业务板块示例数据
INSERT INTO business_segments (name) VALUES
('电子商务'),
('制造业'),
('金融服务'),
('教育培训'),
('医疗健康');

-- 地区示例数据
INSERT INTO regions (name) VALUES
('华南'),
('华东'),
('华北'),
('西南'),
('东北');

-- 代理机构示例数据
INSERT INTO agencies (name) VALUES
('代理机构A'),
('代理机构B'),
('代理机构C');