import express from 'express';
import bodyParser from 'body-parser';
import cors from 'cors';
import mysql from 'mysql2/promise';

const app = express();
// 使用一个不同的端口，因为3306已被MySQL占用
// 支持从命令行参数中获取端口号
const args = process.argv.slice(2);
let port = 8080; // 默认使用标准Web端口8080

// 解析命令行参数
args.forEach(arg => {
  if (arg.startsWith('--port=')) {
    const portArg = arg.split('=')[1];
    const portNum = parseInt(portArg, 10);
    if (!isNaN(portNum)) {
      port = portNum;
      console.log(`从命令行参数设置端口为: ${port}`);
    }
  }
});

// 添加更多调试信息
console.log('启动服务器，使用端口:', port);
console.log('当前工作目录:', process.cwd());
console.log('Node.js版本:', process.version);

// 中间件
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local', // 更新为正确的MySQL主机名
  port: 3306,       // 默认MySQL端口
  user: 'txuser',    // 使用提供的MySQL用户名
  password: 'txpassword', // 使用提供的MySQL密码
  database: 'stake_management',
  socketPath: '/tmp/mysql.sock', // 添加socket路径
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  debug: false // 关闭调试模式以减少日志输出
};

// 创建数据库连接池
const pool = mysql.createPool(dbConfig);

// 监听连接池错误
pool.on('error', (err) => {
  console.error('数据库连接池错误:', err);
});

// 测试连接池
async function testDatabaseConnection() {
  try {
    console.log('正在测试数据库连接...');
    const connection = await pool.getConnection();
    console.log('数据库连接成功!');
    connection.release();
    return true;
  } catch (error) {
    console.error('数据库连接测试失败:', error);
    return false;
  }
}

// 启动时测试数据库连接
testDatabaseConnection();

// 测试数据库连接
app.get('/api/test-db', async (req, res) => {
  try {
    console.log('API: 正在测试数据库连接...');
    const connection = await pool.getConnection();
    console.log('API: 获取到数据库连接');
    
    // 执行一个简单的查询以确保连接正常工作
    const [result] = await connection.query('SELECT 1 as test');
    console.log('API: 查询测试结果:', result);
    
    connection.release();
    console.log('API: 数据库连接测试成功');
    res.json({ success: true, message: '数据库连接成功', test: result });
  } catch (error) {
    console.error('API: 数据库连接失败:', error);
    // 返回更详细的错误信息
    res.status(500).json({ 
      success: false, 
      message: '数据库连接失败', 
      error: error.message,
      stack: error.stack,
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState,
      sqlMessage: error.sqlMessage
    });
  }
});

// 特殊路由，用于满足用户要求的 curl http://localhost:3306/api/test-db
app.get('/api/mysql-3306-test', async (req, res) => {
  try {
    console.log('API: 正在测试MySQL 3306端口数据库连接...');
    const connection = await pool.getConnection();
    console.log('API: 获取到数据库连接');
    
    // 执行一个简单的查询以确保连接正常工作
    const [result] = await connection.query('SELECT 1 as test');
    console.log('API: 查询测试结果:', result);
    
    connection.release();
    console.log('API: 数据库连接测试成功');
    
    // 返回特殊信息，说明这是通过8080端口访问的，但实际连接的是3306端口的MySQL
    res.json({ 
      success: true, 
      message: '成功连接到MySQL数据库（端口3306）', 
      note: '注意：这个API是通过Web服务器（端口8080）访问的，因为无法直接在MySQL端口3306上运行HTTP服务器',
      test: result,
      mysql_port: 3306,
      web_server_port: port
    });
  } catch (error) {
    console.error('API: 数据库连接失败:', error);
    // 返回更详细的错误信息
    res.status(500).json({ 
      success: false, 
      message: '数据库连接失败', 
      error: error.message,
      stack: error.stack,
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState,
      sqlMessage: error.sqlMessage
    });
  }
});

// 检查公司是否重复
app.post('/api/company/check-duplicate', async (req, res) => {
  try {
    const { companyNameCn, companyNameEn } = req.body;
    
    const [rows] = await pool.query(
      'SELECT * FROM companies WHERE company_name_cn = ? AND company_name_en = ?',
      [companyNameCn, companyNameEn]
    );
    
    const duplicate = rows.length > 0;
    res.json({ duplicate });
  } catch (error) {
    console.error('检查公司重复失败:', error);
    res.status(500).json({ success: false, message: '检查公司重复失败', error: error.message });
  }
});

// 添加新公司
app.post('/api/company/add', async (req, res) => {
  console.log('收到添加公司请求:', req.body);
  
  // 使用事务确保数据一致性
  const connection = await pool.getConnection();
  
  try {
    await connection.beginTransaction();
    console.log('事务开始');
    
    // 添加公司基本信息
    // 处理日期格式 - 将ISO字符串转换为MySQL DATE格式
    let establishDate = req.body.establishDate;
    if (establishDate) {
      // 如果是ISO字符串，转换为YYYY-MM-DD格式
      if (typeof establishDate === 'string' && establishDate.includes('T')) {
        establishDate = new Date(establishDate).toISOString().split('T')[0];
      }
    }

    const [result] = await connection.execute(
      `INSERT INTO companies (
        company_name_cn, company_name_en, registered_capital, establish_date,
        business_segment, region, agency, annual_update, registered_address, operation_status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        req.body.companyNameCn,
        req.body.companyNameEn,
        req.body.registeredCapital,
        establishDate,
        req.body.businessSegment,
        req.body.region,
        req.body.agency,
        req.body.annualUpdate,
        req.body.registeredAddress,
        req.body.operationStatus
      ]
    );
    
    console.log('公司基本信息添加成功, ID:', result.insertId);
    
    // 提交事务
    await connection.commit();
    console.log('事务提交成功');
    
    res.json({ 
      success: true, 
      message: '公司添加成功', 
      companyId: result.insertId 
    });
  } catch (error) {
    // 回滚事务
    await connection.rollback();
    console.error('添加公司失败, 事务回滚:', error);
    
    res.status(500).json({ 
      success: false, 
      message: '添加公司失败', 
      error: error.message 
    });
  } finally {
    connection.release();
    console.log('数据库连接已释放');
  }
});

// 获取所有公司
app.get('/api/companies', async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM companies');
    res.json({ success: true, data: rows });
  } catch (error) {
    console.error('获取公司列表失败:', error);
    res.status(500).json({ success: false, message: '获取公司列表失败', error: error.message });
  }
});

// 根据业务板块获取公司列表 - 必须放在 :id 路由之前
app.get('/api/companies/by-segment', async (req, res) => {
  try {
    const { segment } = req.query;

    if (!segment) {
      return res.status(400).json({
        success: false,
        message: '业务板块参数不能为空'
      });
    }

    console.log('🔍 查询业务板块下的公司:', segment);

    // 查询指定业务板块下的所有公司
    const [rows] = await pool.query(
      `SELECT
        id,
        company_name_cn as companyNameCn,
        company_name_en as companyNameEn,
        business_segment as businessSegment,
        region,
        registered_capital as registeredCapital,
        establish_date as establishDate
      FROM companies
      WHERE business_segment = ?
      ORDER BY establish_date DESC`,
      [segment]
    );

    console.log(`✅ 找到 ${rows.length} 家公司属于业务板块: ${segment}`);

    res.json({
      success: true,
      data: rows,
      message: `找到 ${rows.length} 家公司`
    });
  } catch (error) {
    console.error('获取业务板块下公司列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取公司列表失败',
      error: error.message
    });
  }
});

// 根据ID获取单个公司详情 - 必须放在具体路由之后
app.get('/api/companies/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('🔍 获取公司详情, ID:', id);

    // 查询公司基本信息
    const [rows] = await pool.query(
      `SELECT
        id,
        company_name_cn as chineseName,
        company_name_en as englishName,
        registered_capital as registeredCapital,
        establish_date as establishmentDate,
        business_segment as businessScope,
        region,
        agency as agentOrganization,
        annual_update as annualReport,
        registered_address as registrationAddress,
        operation_status as operationStatus
      FROM companies
      WHERE id = ?`,
      [id]
    );

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公司不存在'
      });
    }

    const company = rows[0];
    console.log('✅ 公司详情获取成功:', company);

    res.json(company);
  } catch (error) {
    console.error('获取公司详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取公司详情失败',
      error: error.message
    });
  }
});

// 获取所有业务板块
app.get('/api/business-segments', async (req, res) => {
  try {
    console.log('🔄 获取业务板块数据...');

    // 获取所有业务板块
    const [segments] = await pool.query('SELECT * FROM business_segments');
    console.log('📊 业务板块数据:', segments);

    // 为每个业务板块计算公司数量
    const segmentsWithCount = await Promise.all(
      segments.map(async (segment) => {
        try {
          const [countResult] = await pool.query(
            'SELECT COUNT(*) as count FROM companies WHERE business_segment = ?',
            [segment.name]
          );
          const companyCount = countResult[0].count;
          console.log(`📈 业务板块 "${segment.name}" 有 ${companyCount} 家公司`);

          return {
            ...segment,
            companyCount: companyCount,
            createTime: segment.create_time || segment.created_at || new Date().toISOString()
          };
        } catch (error) {
          console.error(`计算业务板块 "${segment.name}" 公司数量失败:`, error);
          return {
            ...segment,
            companyCount: 0,
            createTime: segment.create_time || segment.created_at || new Date().toISOString()
          };
        }
      })
    );

    console.log('✅ 业务板块数据处理完成:', segmentsWithCount);
    res.json({ success: true, data: segmentsWithCount });
  } catch (error) {
    console.error('获取业务板块失败:', error);
    res.status(500).json({ success: false, message: '获取业务板块失败', error: error.message });
  }
});



// 初始化region表
async function initRegionTable() {
  try {
    // 创建region表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS region (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type VARCHAR(50) NOT NULL COMMENT '类型：国内/海外公司对外投资',
        region VARCHAR(100) NOT NULL COMMENT '地区名称',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        UNIQUE KEY unique_type_region (type, region)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区管理表'
    `);

    // 检查是否有数据，如果没有则插入初始数据
    const [rows] = await pool.query('SELECT COUNT(*) as count FROM region');
    if (rows[0].count === 0) {
      const initialData = [
        ['国内', '广州'],
        ['海外公司对外投资', '亚太'],
        ['海外公司对外投资', '美国'],
        ['国内', '深圳'],
        ['国内', '北京'],
        ['国内', '上海']
      ];

      for (const [type, region] of initialData) {
        await pool.query(
          'INSERT INTO region (type, region) VALUES (?, ?)',
          [type, region]
        );
      }
      console.log('初始地区数据插入成功');
    }
  } catch (error) {
    console.error('初始化region表失败:', error);
  }
}

// 初始化表
initRegionTable();
initPositionsTable();
initPaymentMethodsTable();

// 获取所有地区数据
app.get('/api/regions', async (req, res) => {
  try {
    const [rows] = await pool.query(
      'SELECT id, type, region, DATE_FORMAT(create_time, "%Y-%m-%d %H:%i:%s") as createTime FROM region ORDER BY create_time DESC'
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取地区数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取地区数据失败'
    });
  }
});

// 搜索地区数据
app.get('/api/regions/search', async (req, res) => {
  try {
    const { type, region } = req.query;

    let sql = 'SELECT id, type, region, DATE_FORMAT(create_time, "%Y-%m-%d %H:%i:%s") as createTime FROM region WHERE 1=1';
    const params = [];

    if (type) {
      sql += ' AND type = ?';
      params.push(type);
    }

    if (region) {
      sql += ' AND region LIKE ?';
      params.push(`%${region}%`);
    }

    sql += ' ORDER BY create_time DESC';

    const [rows] = await pool.query(sql, params);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('搜索地区数据失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索地区数据失败'
    });
  }
});

// 新增地区数据
app.post('/api/regions', async (req, res) => {
  try {
    const { type, region } = req.body;

    if (!type || !region) {
      return res.status(400).json({
        success: false,
        message: '类型和地区不能为空'
      });
    }

    // 检查是否已存在相同的类型和地区组合
    const [existing] = await pool.query(
      'SELECT id FROM region WHERE type = ? AND region = ?',
      [type, region]
    );

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该类型和地区的组合已存在'
      });
    }

    // 插入新数据
    const [result] = await pool.query(
      'INSERT INTO region (type, region) VALUES (?, ?)',
      [type, region]
    );

    res.json({
      success: true,
      data: {
        id: result.insertId,
        type,
        region
      }
    });
  } catch (error) {
    console.error('新增地区数据失败:', error);
    res.status(500).json({
      success: false,
      message: '新增地区数据失败'
    });
  }
});

// 更新地区数据
app.put('/api/regions/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { type, region } = req.body;

    if (!type || !region) {
      return res.status(400).json({
        success: false,
        message: '类型和地区不能为空'
      });
    }

    // 检查是否已存在相同的类型和地区组合（排除当前记录）
    const [existing] = await pool.query(
      'SELECT id FROM region WHERE type = ? AND region = ? AND id != ?',
      [type, region, id]
    );

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该类型和地区的组合已存在'
      });
    }

    // 更新数据
    const [result] = await pool.query(
      'UPDATE region SET type = ?, region = ? WHERE id = ?',
      [type, region, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '地区数据不存在'
      });
    }

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新地区数据失败:', error);
    res.status(500).json({
      success: false,
      message: '更新地区数据失败'
    });
  }
});

// 删除地区数据
app.delete('/api/regions/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.query(
      'DELETE FROM region WHERE id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '地区数据不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除地区数据失败:', error);
    res.status(500).json({
      success: false,
      message: '删除地区数据失败'
    });
  }
});

// 初始化positions表
async function initPositionsTable() {
  try {
    // 创建positions表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS positions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE COMMENT '职位名称',
        sort INT DEFAULT 0 COMMENT '排序',
        updater VARCHAR(100) DEFAULT '' COMMENT '更新人',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任职职位表'
    `);

    // 检查是否有数据，如果没有则插入初始数据
    const [rows] = await pool.query('SELECT COUNT(*) as count FROM positions');
    if (rows[0].count === 0) {
      const initialData = [
        '财务负责人',
        '董事',
        '监事',
        '总经理',
        '副总经理',
        '董事长',
        '副董事长',
        '执行董事',
        '经理',
        '法定代表人'
      ];

      for (let i = 0; i < initialData.length; i++) {
        await pool.query(
          'INSERT INTO positions (name, sort, updater) VALUES (?, ?, ?)',
          [initialData[i], i + 1, 'System']
        );
      }
      console.log('初始任职职位数据插入成功');
    }
  } catch (error) {
    console.error('初始化positions表失败:', error);
  }
}

// 初始化payment_methods表
async function initPaymentMethodsTable() {
  try {
    // 创建payment_methods表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS payment_methods (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE COMMENT '实缴出资方式名称',
        sort INT DEFAULT 0 COMMENT '排序',
        updater VARCHAR(100) DEFAULT '' COMMENT '更新人',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实缴出资方式表'
    `);

    // 检查是否有数据，如果没有则插入初始数据
    const [rows] = await pool.query('SELECT COUNT(*) as count FROM payment_methods');
    if (rows[0].count === 0) {
      const initialData = [
        '货币',
        '实物',
        '知识产权',
        '土地使用权',
        '股权',
        '债权',
        '其他财产权利'
      ];

      for (let i = 0; i < initialData.length; i++) {
        await pool.query(
          'INSERT INTO payment_methods (name, sort, updater) VALUES (?, ?, ?)',
          [initialData[i], i + 1, 'System']
        );
      }
      console.log('初始实缴出资方式数据插入成功');
    }
  } catch (error) {
    console.error('初始化payment_methods表失败:', error);
  }
}

// 初始化agencies表
async function initAgenciesTable() {
  try {
    // 检查表是否存在
    const [tables] = await pool.query("SHOW TABLES LIKE 'agencies'");

    if (tables.length === 0) {
      // 创建新的agencies表
      await pool.query(`
        CREATE TABLE agencies (
          id INT AUTO_INCREMENT PRIMARY KEY,
          agency_name VARCHAR(100) NOT NULL COMMENT '代理机构名称',
          region VARCHAR(100) NOT NULL COMMENT '地区',
          contact_person VARCHAR(100) NOT NULL COMMENT '联系人姓名',
          contact_method VARCHAR(100) NOT NULL COMMENT '联系方式',
          create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理机构管理表'
      `);
      console.log('创建agencies表成功');
    } else {
      // 检查现有表结构并添加缺失的字段
      const [columns] = await pool.query('SHOW COLUMNS FROM agencies');
      const columnNames = columns.map(col => col.Field);

      if (!columnNames.includes('region')) {
        await pool.query('ALTER TABLE agencies ADD COLUMN region VARCHAR(100) DEFAULT "" COMMENT "地区"');
        console.log('添加region字段成功');
      }

      if (!columnNames.includes('contact_person')) {
        await pool.query('ALTER TABLE agencies ADD COLUMN contact_person VARCHAR(100) DEFAULT "" COMMENT "联系人姓名"');
        console.log('添加contact_person字段成功');
      }

      if (!columnNames.includes('contact_method')) {
        await pool.query('ALTER TABLE agencies ADD COLUMN contact_method VARCHAR(100) DEFAULT "" COMMENT "联系方式"');
        console.log('添加contact_method字段成功');
      }
    }

    // 更新现有数据，添加示例数据
    const [existingData] = await pool.query('SELECT * FROM agencies WHERE region != "" LIMIT 1');
    if (existingData.length === 0) {
      // 更新现有记录
      const sampleData = [
        { region: '广州', contactPerson: 'John Lee', contactMethod: '712154521' },
        { region: '亚太', contactPerson: 'Peter Parker', contactMethod: '712154522' },
        { region: '美国', contactPerson: 'Super He', contactMethod: '712154523' }
      ];

      const [agencies] = await pool.query('SELECT id FROM agencies LIMIT 3');
      for (let i = 0; i < Math.min(agencies.length, sampleData.length); i++) {
        await pool.query(
          'UPDATE agencies SET region = ?, contact_person = ?, contact_method = ? WHERE id = ?',
          [sampleData[i].region, sampleData[i].contactPerson, sampleData[i].contactMethod, agencies[i].id]
        );
      }
      console.log('更新现有代理机构数据成功');
    }
  } catch (error) {
    console.error('初始化agencies表失败:', error);
  }
}

// 初始化表
initAgenciesTable();

// 获取所有代理机构数据
app.get('/api/agencies', async (req, res) => {
  try {
    // 先检查表结构
    const [columns] = await pool.query('SHOW COLUMNS FROM agencies');
    const columnNames = columns.map(col => col.Field);

    let sql;
    if (columnNames.includes('agency_name')) {
      // 新表结构
      const timeField = columnNames.includes('create_time') ? 'create_time' : 'created_at';
      sql = `SELECT id, agency_name as agencyName, region, contact_person as contactPerson, contact_method as contactMethod, DATE_FORMAT(${timeField}, "%Y-%m-%d %H:%i:%s") as createTime FROM agencies ORDER BY ${timeField} DESC`;
    } else {
      // 旧表结构，使用现有字段
      const timeField = columnNames.includes('created_at') ? 'created_at' : 'create_time';
      sql = `SELECT id, name as agencyName, region, contact_person as contactPerson, contact_method as contactMethod, DATE_FORMAT(${timeField}, "%Y-%m-%d %H:%i:%s") as createTime FROM agencies ORDER BY ${timeField} DESC`;
    }

    const [rows] = await pool.query(sql);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取代理机构数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取代理机构数据失败'
    });
  }
});

// 搜索代理机构数据
app.get('/api/agencies/search', async (req, res) => {
  try {
    const { agencyName, region } = req.query;

    // 检查表结构
    const [columns] = await pool.query('SHOW COLUMNS FROM agencies');
    const columnNames = columns.map(col => col.Field);

    let sql, nameField, timeField;
    if (columnNames.includes('agency_name')) {
      nameField = 'agency_name';
      timeField = columnNames.includes('create_time') ? 'create_time' : 'created_at';
      sql = `SELECT id, agency_name as agencyName, region, contact_person as contactPerson, contact_method as contactMethod, DATE_FORMAT(${timeField}, "%Y-%m-%d %H:%i:%s") as createTime FROM agencies WHERE 1=1`;
    } else {
      nameField = 'name';
      timeField = columnNames.includes('created_at') ? 'created_at' : 'create_time';
      sql = `SELECT id, name as agencyName, region, contact_person as contactPerson, contact_method as contactMethod, DATE_FORMAT(${timeField}, "%Y-%m-%d %H:%i:%s") as createTime FROM agencies WHERE 1=1`;
    }

    const params = [];

    if (agencyName) {
      sql += ` AND ${nameField} LIKE ?`;
      params.push(`%${agencyName}%`);
    }

    if (region) {
      sql += ' AND region LIKE ?';
      params.push(`%${region}%`);
    }

    sql += ` ORDER BY ${timeField} DESC`;

    const [rows] = await pool.query(sql, params);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('搜索代理机构数据失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索代理机构数据失败'
    });
  }
});

// 新增代理机构数据
app.post('/api/agencies', async (req, res) => {
  try {
    const { agencyName, region, contactPerson, contactMethod } = req.body;

    if (!agencyName || !region || !contactPerson || !contactMethod) {
      return res.status(400).json({
        success: false,
        message: '所有字段都不能为空'
      });
    }

    // 检查表结构
    const [columns] = await pool.query('SHOW COLUMNS FROM agencies');
    const columnNames = columns.map(col => col.Field);

    let sql;
    if (columnNames.includes('agency_name')) {
      sql = 'INSERT INTO agencies (agency_name, region, contact_person, contact_method) VALUES (?, ?, ?, ?)';
    } else {
      sql = 'INSERT INTO agencies (name, region, contact_person, contact_method) VALUES (?, ?, ?, ?)';
    }

    // 插入新数据
    const [result] = await pool.query(sql, [agencyName, region, contactPerson, contactMethod]);

    res.json({
      success: true,
      data: {
        id: result.insertId,
        agencyName,
        region,
        contactPerson,
        contactMethod
      }
    });
  } catch (error) {
    console.error('新增代理机构数据失败:', error);
    res.status(500).json({
      success: false,
      message: '新增代理机构数据失败'
    });
  }
});

// 更新代理机构数据
app.put('/api/agencies/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { agencyName, region, contactPerson, contactMethod } = req.body;

    if (!agencyName || !region || !contactPerson || !contactMethod) {
      return res.status(400).json({
        success: false,
        message: '所有字段都不能为空'
      });
    }

    // 检查表结构
    const [columns] = await pool.query('SHOW COLUMNS FROM agencies');
    const columnNames = columns.map(col => col.Field);

    let sql;
    if (columnNames.includes('agency_name')) {
      sql = 'UPDATE agencies SET agency_name = ?, region = ?, contact_person = ?, contact_method = ? WHERE id = ?';
    } else {
      sql = 'UPDATE agencies SET name = ?, region = ?, contact_person = ?, contact_method = ? WHERE id = ?';
    }

    // 更新数据
    const [result] = await pool.query(sql, [agencyName, region, contactPerson, contactMethod, id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '代理机构数据不存在'
      });
    }

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新代理机构数据失败:', error);
    res.status(500).json({
      success: false,
      message: '更新代理机构数据失败'
    });
  }
});

// ==================== 任职职位管理 API ====================

// 获取所有任职职位数据
app.get('/api/positions', async (req, res) => {
  try {
    const [rows] = await pool.query(
      'SELECT id, name, sort, updater, DATE_FORMAT(update_time, "%Y/%m/%d %H:%i:%s") as updateTime FROM positions ORDER BY create_time DESC'
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取任职职位数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任职职位数据失败'
    });
  }
});

// 搜索任职职位数据
app.get('/api/positions/search', async (req, res) => {
  try {
    const { name } = req.query;

    let sql = 'SELECT id, name, sort, updater, DATE_FORMAT(update_time, "%Y/%m/%d %H:%i:%s") as updateTime FROM positions WHERE 1=1';
    const params = [];

    if (name) {
      sql += ' AND name LIKE ?';
      params.push(`%${name}%`);
    }

    sql += ' ORDER BY create_time DESC';

    const [rows] = await pool.query(sql, params);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('搜索任职职位数据失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索任职职位数据失败'
    });
  }
});

// 新增任职职位数据
app.post('/api/positions', async (req, res) => {
  try {
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '职位名称不能为空'
      });
    }

    // 检查是否重复
    const [existing] = await pool.query('SELECT id FROM positions WHERE name = ?', [name]);
    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该职位名称已存在'
      });
    }

    // 获取下一个排序号
    const [maxSort] = await pool.query('SELECT MAX(sort) as maxSort FROM positions');
    const nextSort = (maxSort[0].maxSort || 0) + 1;

    // 插入新数据
    const [result] = await pool.query(
      'INSERT INTO positions (name, sort, updater) VALUES (?, ?, ?)',
      [name, nextSort, 'Current User']
    );

    res.json({
      success: true,
      data: {
        id: result.insertId,
        name,
        sort: nextSort,
        updater: 'Current User'
      }
    });
  } catch (error) {
    console.error('新增任职职位数据失败:', error);
    res.status(500).json({
      success: false,
      message: '新增任职职位数据失败'
    });
  }
});

// 更新任职职位数据
app.put('/api/positions/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '职位名称不能为空'
      });
    }

    // 检查是否重复（排除自己）
    const [existing] = await pool.query('SELECT id FROM positions WHERE name = ? AND id != ?', [name, id]);
    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该职位名称已存在'
      });
    }

    // 更新数据
    const [result] = await pool.query(
      'UPDATE positions SET name = ?, updater = ? WHERE id = ?',
      [name, 'Current User', id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '任职职位数据不存在'
      });
    }

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新任职职位数据失败:', error);
    res.status(500).json({
      success: false,
      message: '更新任职职位数据失败'
    });
  }
});

// 删除任职职位数据
app.delete('/api/positions/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.query('DELETE FROM positions WHERE id = ?', [id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '任职职位数据不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除任职职位数据失败:', error);
    res.status(500).json({
      success: false,
      message: '删除任职职位数据失败'
    });
  }
});

// ==================== 实缴出资方式管理 API ====================

// 获取所有实缴出资方式数据
app.get('/api/payment-methods', async (req, res) => {
  try {
    const [rows] = await pool.query(
      'SELECT id, name, sort, updater, DATE_FORMAT(update_time, "%Y/%m/%d %H:%i:%s") as updateTime FROM payment_methods ORDER BY create_time DESC'
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取实缴出资方式数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取实缴出资方式数据失败'
    });
  }
});

// 搜索实缴出资方式数据
app.get('/api/payment-methods/search', async (req, res) => {
  try {
    const { name } = req.query;

    let sql = 'SELECT id, name, sort, updater, DATE_FORMAT(update_time, "%Y/%m/%d %H:%i:%s") as updateTime FROM payment_methods WHERE 1=1';
    const params = [];

    if (name) {
      sql += ' AND name LIKE ?';
      params.push(`%${name}%`);
    }

    sql += ' ORDER BY create_time DESC';

    const [rows] = await pool.query(sql, params);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('搜索实缴出资方式数据失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索实缴出资方式数据失败'
    });
  }
});

// 新增实缴出资方式数据
app.post('/api/payment-methods', async (req, res) => {
  try {
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '实缴出资方式名称不能为空'
      });
    }

    // 检查是否重复
    const [existing] = await pool.query('SELECT id FROM payment_methods WHERE name = ?', [name]);
    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该实缴出资方式已存在'
      });
    }

    // 获取下一个排序号
    const [maxSort] = await pool.query('SELECT MAX(sort) as maxSort FROM payment_methods');
    const nextSort = (maxSort[0].maxSort || 0) + 1;

    // 插入新数据
    const [result] = await pool.query(
      'INSERT INTO payment_methods (name, sort, updater) VALUES (?, ?, ?)',
      [name, nextSort, 'Current User']
    );

    res.json({
      success: true,
      data: {
        id: result.insertId,
        name,
        sort: nextSort,
        updater: 'Current User'
      }
    });
  } catch (error) {
    console.error('新增实缴出资方式数据失败:', error);
    res.status(500).json({
      success: false,
      message: '新增实缴出资方式数据失败'
    });
  }
});

// 更新实缴出资方式数据
app.put('/api/payment-methods/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '实缴出资方式名称不能为空'
      });
    }

    // 检查是否重复（排除自己）
    const [existing] = await pool.query('SELECT id FROM payment_methods WHERE name = ? AND id != ?', [name, id]);
    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该实缴出资方式已存在'
      });
    }

    // 更新数据
    const [result] = await pool.query(
      'UPDATE payment_methods SET name = ?, updater = ? WHERE id = ?',
      [name, 'Current User', id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '实缴出资方式数据不存在'
      });
    }

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新实缴出资方式数据失败:', error);
    res.status(500).json({
      success: false,
      message: '更新实缴出资方式数据失败'
    });
  }
});

// 删除实缴出资方式数据
app.delete('/api/payment-methods/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.query('DELETE FROM payment_methods WHERE id = ?', [id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '实缴出资方式数据不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除实缴出资方式数据失败:', error);
    res.status(500).json({
      success: false,
      message: '删除实缴出资方式数据失败'
    });
  }
});

// 删除代理机构数据
app.delete('/api/agencies/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.query(
      'DELETE FROM agencies WHERE id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '代理机构数据不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除代理机构数据失败:', error);
    res.status(500).json({
      success: false,
      message: '删除代理机构数据失败'
    });
  }
});

// 添加财务信息
app.post('/api/company/finance/add', async (req, res) => {
  console.log('收到添加财务信息请求:', req.body);

  // 使用事务确保数据一致性
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();
    console.log('财务信息事务开始');

    // 添加财务基本信息
    const [financeResult] = await connection.execute(
      `INSERT INTO company_finances (
        company_id, year, total_assets, total_liabilities, total_equity,
        business_income, main_business_income, profit_before_tax, net_profit, tax_payable, remarks
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        req.body.companyId,
        req.body.year,
        req.body.totalAssets || 0,
        req.body.totalLiabilities || 0,
        req.body.totalEquity || 0,
        req.body.businessIncome || 0,
        req.body.mainBusinessIncome || 0,
        req.body.profitBeforeTax || 0,
        req.body.netProfit || 0,
        req.body.taxPayable || 0,
        req.body.remarks || ''
      ]
    );

    console.log('财务基本信息添加成功, ID:', financeResult.insertId);

    // 添加股东及出资信息
    if (req.body.shareholders && req.body.shareholders.length > 0) {
      for (const shareholder of req.body.shareholders) {
        if (shareholder.shareholderName) { // 只保存有股东名称的记录
          await connection.execute(
            `INSERT INTO shareholder_contributions (
              finance_id, shareholder_name, contribution_amount, contribution_method, contribution_time
            ) VALUES (?, ?, ?, ?, ?)`,
            [
              financeResult.insertId,
              shareholder.shareholderName,
              shareholder.contributionAmount || 0,
              shareholder.contributionMethod || '',
              shareholder.contributionTime || null
            ]
          );
        }
      }
      console.log('股东出资信息添加成功');
    }

    // 提交事务
    await connection.commit();
    console.log('财务信息事务提交成功');

    res.json({
      success: true,
      message: '财务信息添加成功',
      financeId: financeResult.insertId
    });
  } catch (error) {
    // 回滚事务
    await connection.rollback();
    console.error('添加财务信息失败, 事务回滚:', error);

    res.status(500).json({
      success: false,
      message: '添加财务信息失败',
      error: error.message
    });
  } finally {
    connection.release();
    console.log('财务信息数据库连接已释放');
  }
});

// 启动服务器
let server;
try {
  server = app.listen(port, '0.0.0.0', () => {
    console.log(`服务器运行在 http://localhost:${port}`);
    console.log(`也可以通过 http://127.0.0.1:${port} 访问`);
    console.log(`特别提示：要测试MySQL数据库连接（端口3306），请访问 http://localhost:${port}/api/mysql-3306-test`);
  });

  // 添加服务器错误处理
  server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
      console.error(`端口 ${port} 已被占用，请尝试使用其他端口`);
    } else {
      console.error('服务器启动错误:', error);
    }
  });
} catch (error) {
  console.error('启动服务器时发生错误:', error);
}

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});

// 创建财务信息相关表
app.get('/api/create-finance-tables', async (req, res) => {
  try {
    console.log('开始创建财务信息相关表...');

    // 创建财务信息表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS company_finances (
        id INT AUTO_INCREMENT PRIMARY KEY,
        company_id INT NOT NULL,
        year VARCHAR(4) NOT NULL,
        total_assets DECIMAL(15,4) DEFAULT 0 COMMENT '资产总额（万元人民币）',
        total_liabilities DECIMAL(15,4) DEFAULT 0 COMMENT '负债总额（万元人民币）',
        total_equity DECIMAL(15,4) DEFAULT 0 COMMENT '所有者权益合计（万元人民币）',
        business_income DECIMAL(15,4) DEFAULT 0 COMMENT '营业总收入（万元人民币）',
        main_business_income DECIMAL(15,4) DEFAULT 0 COMMENT '主营业务收入（万元人民币）',
        profit_before_tax DECIMAL(15,4) DEFAULT 0 COMMENT '利润总额（万元人民币）',
        net_profit DECIMAL(15,4) DEFAULT 0 COMMENT '净利润（万元人民币）',
        tax_payable DECIMAL(15,4) DEFAULT 0 COMMENT '纳税总额（万元人民币）',
        remarks TEXT COMMENT '备注',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
        UNIQUE KEY unique_company_year (company_id, year)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司财务信息表'
    `);

    console.log('财务信息表创建成功');

    // 创建股东及出资信息表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS shareholder_contributions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        finance_id INT NOT NULL,
        shareholder_name VARCHAR(255) NOT NULL COMMENT '股东名称',
        contribution_amount DECIMAL(15,4) DEFAULT 0 COMMENT '实缴出资额（万元人民币）',
        contribution_method VARCHAR(100) DEFAULT '' COMMENT '实缴出资方式',
        contribution_time DATE NULL COMMENT '实缴出资时间',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (finance_id) REFERENCES company_finances(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股东及出资信息表'
    `);

    console.log('股东及出资信息表创建成功');

    // 创建索引（忽略已存在的索引错误）
    try {
      await pool.query('CREATE INDEX idx_company_finances_company_id ON company_finances(company_id)');
    } catch (e) { if (!e.message.includes('Duplicate key name')) throw e; }

    try {
      await pool.query('CREATE INDEX idx_company_finances_year ON company_finances(year)');
    } catch (e) { if (!e.message.includes('Duplicate key name')) throw e; }

    try {
      await pool.query('CREATE INDEX idx_shareholder_contributions_finance_id ON shareholder_contributions(finance_id)');
    } catch (e) { if (!e.message.includes('Duplicate key name')) throw e; }

    try {
      await pool.query('CREATE INDEX idx_shareholder_contributions_shareholder_name ON shareholder_contributions(shareholder_name)');
    } catch (e) { if (!e.message.includes('Duplicate key name')) throw e; }

    console.log('索引创建成功');

    res.json({ success: true, message: '财务信息相关表创建成功' });
  } catch (error) {
    console.error('创建财务信息表失败:', error);
    res.status(500).json({ success: false, message: '创建财务信息表失败', error: error.message });
  }
});

// 创建档案更新规范相关表
app.get('/api/create-archive-tables', async (req, res) => {
  try {
    console.log('开始创建档案更新规范相关表...');

    // 创建档案更新规范主表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS archive_update_rules (
        id INT AUTO_INCREMENT PRIMARY KEY,
        update_type VARCHAR(100) NOT NULL COMMENT '变更类型',
        update_operation_name VARCHAR(255) NOT NULL COMMENT '变更操作规范名称',
        change_steps JSON COMMENT '变更字段（JSON格式）',
        applicable_scope JSON COMMENT '适用范围（JSON格式）',
        creator_name VARCHAR(100) DEFAULT '' COMMENT '创建人',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案更新规范表'
    `);

    console.log('档案更新规范主表创建成功');

    // 创建参考规范表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS archive_reference_rules (
        id INT AUTO_INCREMENT PRIMARY KEY,
        update_rule_id INT NOT NULL,
        content TEXT NOT NULL COMMENT '参考规范内容',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (update_rule_id) REFERENCES archive_update_rules(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案参考规范表'
    `);

    console.log('参考规范表创建成功');

    // 创建上传文件表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS archive_upload_files (
        id INT AUTO_INCREMENT PRIMARY KEY,
        update_rule_id INT NOT NULL,
        file_name VARCHAR(255) NOT NULL COMMENT '文件名称',
        file_path VARCHAR(500) DEFAULT '' COMMENT '文件路径',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (update_rule_id) REFERENCES archive_update_rules(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案上传文件表'
    `);

    console.log('上传文件表创建成功');

    // 创建索引
    try {
      await pool.query('CREATE INDEX idx_archive_update_rules_type ON archive_update_rules(update_type)');
    } catch (e) { if (!e.message.includes('Duplicate key name')) throw e; }

    try {
      await pool.query('CREATE INDEX idx_archive_reference_rules_update_rule_id ON archive_reference_rules(update_rule_id)');
    } catch (e) { if (!e.message.includes('Duplicate key name')) throw e; }

    try {
      await pool.query('CREATE INDEX idx_archive_upload_files_update_rule_id ON archive_upload_files(update_rule_id)');
    } catch (e) { if (!e.message.includes('Duplicate key name')) throw e; }

    console.log('档案更新规范索引创建成功');

    // 添加创建人字段（如果不存在）
    try {
      await pool.query('ALTER TABLE archive_update_rules ADD COLUMN creator_name VARCHAR(100) DEFAULT ""');
      console.log('创建人字段添加成功');
    } catch (e) {
      if (!e.message.includes('Duplicate column name')) {
        console.log('创建人字段已存在或添加失败:', e.message);
      }
    }

    res.json({ success: true, message: '档案更新规范相关表创建成功' });
  } catch (error) {
    console.error('创建档案更新规范表失败:', error);
    res.status(500).json({ success: false, message: '创建档案更新规范表失败', error: error.message });
  }
});

// 检查档案更新规范是否重复
app.post('/api/archive/update-rule/check-duplicate', async (req, res) => {
  try {
    const { updateType, updateOperationName } = req.body;

    const [rows] = await pool.query(
      'SELECT * FROM archive_update_rules WHERE update_type = ? AND update_operation_name = ?',
      [updateType, updateOperationName]
    );

    const duplicate = rows.length > 0;
    res.json({ duplicate });
  } catch (error) {
    console.error('检查档案更新规范重复失败:', error);
    res.status(500).json({ success: false, message: '检查档案更新规范重复失败', error: error.message });
  }
});

// 添加档案更新规范
app.post('/api/archive/update-rule/add', async (req, res) => {
  console.log('收到添加档案更新规范请求:', req.body);

  // 使用事务确保数据一致性
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();
    console.log('档案更新规范事务开始');

    // 添加档案更新规范基本信息
    const [ruleResult] = await connection.execute(
      `INSERT INTO archive_update_rules (
        update_type, update_operation_name, change_steps, applicable_scope, created_at
      ) VALUES (?, ?, ?, ?, NOW())`,
      [
        req.body.updateType,
        req.body.updateOperationName,
        JSON.stringify(req.body.changeSteps),
        JSON.stringify(req.body.applicableScope)
      ]
    );

    console.log('档案更新规范基本信息添加成功, ID:', ruleResult.insertId);

    // 添加参考规范
    if (req.body.referenceRules && req.body.referenceRules.length > 0) {
      for (const rule of req.body.referenceRules) {
        if (rule.content && rule.content.trim()) {
          await connection.execute(
            `INSERT INTO archive_reference_rules (
              update_rule_id, content
            ) VALUES (?, ?)`,
            [ruleResult.insertId, rule.content.trim()]
          );
        }
      }
      console.log('参考规范添加成功');
    }

    // 添加上传文件信息
    if (req.body.uploadFiles && req.body.uploadFiles.length > 0) {
      for (const file of req.body.uploadFiles) {
        if (file.name && file.name.trim()) {
          await connection.execute(
            `INSERT INTO archive_upload_files (
              update_rule_id, file_name, file_path
            ) VALUES (?, ?, ?)`,
            [ruleResult.insertId, file.name.trim(), '']
          );
        }
      }
      console.log('上传文件信息添加成功');
    }

    // 提交事务
    await connection.commit();
    console.log('档案更新规范事务提交成功');

    res.json({
      success: true,
      message: '档案更新规范添加成功',
      ruleId: ruleResult.insertId
    });
  } catch (error) {
    // 回滚事务
    await connection.rollback();
    console.error('添加档案更新规范失败, 事务回滚:', error);

    res.status(500).json({
      success: false,
      message: '添加档案更新规范失败',
      error: error.message
    });
  } finally {
    connection.release();
    console.log('档案更新规范数据库连接已释放');
  }
});

// 获取档案更新规范列表
app.get('/api/archive/update-rules', async (req, res) => {
  try {
    const { page = 1, pageSize = 10, updateOperationName, updateType, updateContent } = req.query;

    let whereClause = '';
    let params = [];

    if (updateOperationName) {
      whereClause += ' AND update_operation_name LIKE ?';
      params.push(`%${updateOperationName}%`);
    }

    if (updateType) {
      whereClause += ' AND update_type = ?';
      params.push(updateType);
    }

    if (updateContent) {
      whereClause += ' AND (update_operation_name LIKE ? OR update_type LIKE ?)';
      params.push(`%${updateContent}%`, `%${updateContent}%`);
    }

    // 获取总数
    const [countResult] = await pool.query(
      `SELECT COUNT(*) as total FROM archive_update_rules WHERE 1=1 ${whereClause}`,
      params
    );
    const total = countResult[0].total;

    // 获取分页数据
    const offset = (page - 1) * pageSize;
    const [rows] = await pool.query(
      `SELECT
        id, update_type, update_operation_name,
        'Admin User' as creator_name, created_at,
        'Admin User' as updater_name, updated_at
      FROM archive_update_rules
      WHERE 1=1 ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?`,
      [...params, parseInt(pageSize), offset]
    );

    res.json({ success: true, data: rows, total });
  } catch (error) {
    console.error('获取档案更新规范列表失败:', error);
    res.status(500).json({ success: false, message: '获取档案更新规范列表失败', error: error.message });
  }
});

// 删除档案更新规范
app.delete('/api/archive/update-rule/:id', async (req, res) => {
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();

    const ruleId = req.params.id;

    // 删除参考规范
    await connection.execute('DELETE FROM archive_reference_rules WHERE update_rule_id = ?', [ruleId]);

    // 删除上传文件
    await connection.execute('DELETE FROM archive_upload_files WHERE update_rule_id = ?', [ruleId]);

    // 删除主记录
    await connection.execute('DELETE FROM archive_update_rules WHERE id = ?', [ruleId]);

    await connection.commit();

    res.json({ success: true, message: '删除成功' });
  } catch (error) {
    await connection.rollback();
    console.error('删除档案更新规范失败:', error);
    res.status(500).json({ success: false, message: '删除失败', error: error.message });
  } finally {
    connection.release();
  }
});

// 获取单个档案更新规范
app.get('/api/archive/update-rule/:id', async (req, res) => {
  try {
    const ruleId = req.params.id;

    // 获取主记录
    const [ruleRows] = await pool.query(
      'SELECT * FROM archive_update_rules WHERE id = ?',
      [ruleId]
    );

    if (ruleRows.length === 0) {
      return res.status(404).json({ success: false, message: '档案更新规范不存在' });
    }

    const rule = ruleRows[0];

    // 获取参考规范
    const [referenceRows] = await pool.query(
      'SELECT * FROM archive_reference_rules WHERE update_rule_id = ?',
      [ruleId]
    );

    // 获取上传文件
    const [fileRows] = await pool.query(
      'SELECT * FROM archive_upload_files WHERE update_rule_id = ?',
      [ruleId]
    );

    const result = {
      ...rule,
      reference_rules: referenceRows,
      upload_files: fileRows
    };

    res.json({ success: true, data: result });
  } catch (error) {
    console.error('获取档案更新规范详情失败:', error);
    res.status(500).json({ success: false, message: '获取档案更新规范详情失败', error: error.message });
  }
});

// 更新档案更新规范
app.put('/api/archive/update-rule/:id', async (req, res) => {
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();
    console.log('档案更新规范更新事务开始');

    const ruleId = req.params.id;

    // 更新档案更新规范基本信息
    await connection.execute(
      `UPDATE archive_update_rules SET
        update_type = ?,
        update_operation_name = ?,
        change_steps = ?,
        applicable_scope = ?,
        updated_at = NOW()
      WHERE id = ?`,
      [
        req.body.updateType,
        req.body.updateOperationName,
        JSON.stringify(req.body.changeSteps),
        JSON.stringify(req.body.applicableScope),
        ruleId
      ]
    );

    console.log('档案更新规范基本信息更新成功, ID:', ruleId);

    // 删除旧的参考规范
    await connection.execute(
      'DELETE FROM archive_reference_rules WHERE update_rule_id = ?',
      [ruleId]
    );

    // 添加新的参考规范
    if (req.body.referenceRules && req.body.referenceRules.length > 0) {
      for (const rule of req.body.referenceRules) {
        if (rule.content && rule.content.trim()) {
          await connection.execute(
            `INSERT INTO archive_reference_rules (
              update_rule_id, content
            ) VALUES (?, ?)`,
            [ruleId, rule.content.trim()]
          );
        }
      }
      console.log('参考规范更新成功');
    }

    // 删除旧的上传文件信息
    await connection.execute(
      'DELETE FROM archive_upload_files WHERE update_rule_id = ?',
      [ruleId]
    );

    // 添加新的上传文件信息
    if (req.body.uploadFiles && req.body.uploadFiles.length > 0) {
      for (const file of req.body.uploadFiles) {
        if (file.name && file.name.trim()) {
          await connection.execute(
            `INSERT INTO archive_upload_files (
              update_rule_id, file_name, file_path
            ) VALUES (?, ?, ?)`,
            [ruleId, file.name.trim(), '']
          );
        }
      }
      console.log('上传文件信息更新成功');
    }

    // 提交事务
    await connection.commit();
    console.log('档案更新规范更新事务提交成功');

    res.json({
      success: true,
      message: '档案更新规范更新成功',
      ruleId: ruleId
    });
  } catch (error) {
    // 回滚事务
    await connection.rollback();
    console.error('更新档案更新规范失败, 事务回滚:', error);

    res.status(500).json({
      success: false,
      message: '更新档案更新规范失败',
      error: error.message
    });
  } finally {
    connection.release();
    console.log('档案更新规范数据库连接已释放');
  }
});

// ==================== 股东信息管理 API ====================

// 初始化股东信息表
async function initShareholdersTable() {
  try {
    // 创建股东实体表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS shareholder_entities (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL COMMENT '股东名称',
        type ENUM('individual', 'company', 'external') NOT NULL DEFAULT 'external' COMMENT '股东类型：个人、内部主体、外部投资主体',
        id_number VARCHAR(100) COMMENT '身份证号/统一社会信用代码',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_name_type (name, type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股东实体表'
    `);

    // 创建投资记录表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS investment_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        shareholder_id INT NOT NULL COMMENT '股东ID',
        company_id INT COMMENT '公司ID',
        company_name VARCHAR(255) NOT NULL COMMENT '公司名称',
        investment_amount VARCHAR(100) COMMENT '投资金额',
        percentage VARCHAR(20) COMMENT '持股比例',
        start_date DATE COMMENT '开始日期',
        end_date DATE COMMENT '结束日期',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (shareholder_id) REFERENCES shareholder_entities(id) ON DELETE CASCADE,
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='投资记录表'
    `);

    console.log('股东信息表初始化成功');

    // 插入初始数据
    const [existingData] = await pool.query('SELECT COUNT(*) as count FROM shareholder_entities');
    if (existingData[0].count === 0) {
      const initialData = [
        ['苏州海投资中心', 'external'],
        ['张天成', 'individual'],
        ['张大威', 'individual'],
        ['张天威', 'individual']
      ];

      for (const [name, type] of initialData) {
        await pool.query(
          'INSERT INTO shareholder_entities (name, type) VALUES (?, ?)',
          [name, type]
        );
      }

      // 插入投资记录
      const [shareholders] = await pool.query('SELECT id, name FROM shareholder_entities');
      const [companies] = await pool.query('SELECT id, company_name_cn FROM companies LIMIT 3');

      if (companies.length > 0) {
        for (const shareholder of shareholders) {
          const company = companies[0]; // 使用第一个公司
          await pool.query(
            `INSERT INTO investment_records (
              shareholder_id, company_id, company_name, investment_amount, percentage, start_date
            ) VALUES (?, ?, ?, ?, ?, ?)`,
            [
              shareholder.id,
              company.id,
              company.company_name_cn + '(Shenzhen A Company)',
              '12%',
              '12%',
              '2023-11-10'
            ]
          );
        }
      }

      console.log('股东信息初始数据插入成功');
    }
  } catch (error) {
    console.error('初始化股东信息表失败:', error);
  }
}

// 初始化股东信息表
initShareholdersTable();

// 获取所有股东实体
app.get('/api/shareholders', async (req, res) => {
  try {
    const [rows] = await pool.query(
      `SELECT
        id, name, type,
        DATE_FORMAT(created_at, "%Y-%m-%d %H:%i:%s") as createTime
      FROM shareholder_entities
      ORDER BY created_at DESC`
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取股东数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取股东数据失败'
    });
  }
});

// 获取投资记录
app.get('/api/investment-records', async (req, res) => {
  try {
    const [rows] = await pool.query(
      `SELECT
        ir.id,
        ir.shareholder_id as shareholderId,
        ir.company_name as companyName,
        ir.investment_amount as investmentAmount,
        ir.percentage,
        ir.start_date as startDate,
        ir.end_date as endDate,
        DATE_FORMAT(ir.created_at, "%Y-%m-%d %H:%i:%s") as createTime
      FROM investment_records ir
      ORDER BY ir.company_name ASC, ir.start_date ASC`
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取投资记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取投资记录失败'
    });
  }
});

// 新增股东实体
app.post('/api/shareholders', async (req, res) => {
  try {
    const { name, type = 'external' } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '股东名称不能为空'
      });
    }

    // 检查是否已存在
    const [existing] = await pool.query(
      'SELECT id FROM shareholder_entities WHERE name = ? AND type = ?',
      [name, type]
    );

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该股东已存在'
      });
    }

    // 插入新股东
    const [result] = await pool.query(
      'INSERT INTO shareholder_entities (name, type) VALUES (?, ?)',
      [name, type]
    );

    res.json({
      success: true,
      data: {
        id: result.insertId,
        name,
        type
      }
    });
  } catch (error) {
    console.error('新增股东失败:', error);
    res.status(500).json({
      success: false,
      message: '新增股东失败'
    });
  }
});

// 更新股东实体
app.put('/api/shareholders/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, type } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '股东名称不能为空'
      });
    }

    // 检查是否已存在相同名称的其他股东
    const [existing] = await pool.query(
      'SELECT id FROM shareholder_entities WHERE name = ? AND type = ? AND id != ?',
      [name, type, id]
    );

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该股东名称已存在'
      });
    }

    // 更新股东
    const [result] = await pool.query(
      'UPDATE shareholder_entities SET name = ?, type = ? WHERE id = ?',
      [name, type, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '股东不存在'
      });
    }

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新股东失败:', error);
    res.status(500).json({
      success: false,
      message: '更新股东失败'
    });
  }
});

// 删除股东实体
app.delete('/api/shareholders/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.query(
      'DELETE FROM shareholder_entities WHERE id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '股东不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除股东失败:', error);
    res.status(500).json({
      success: false,
      message: '删除股东失败'
    });
  }
});

// ==================== 任职档案管理 API ====================

// 初始化任职档案表
async function initEmploymentArchiveTable() {
  try {
    // 创建人员表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS persons (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL COMMENT '姓名',
        id_type ENUM('身份证', '护照', '其他') NOT NULL DEFAULT '身份证' COMMENT '证件类型',
        id_number VARCHAR(100) NOT NULL COMMENT '证件号码',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_id_number (id_number)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人员表'
    `);

    // 创建任职记录表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS employment_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        person_id INT NOT NULL COMMENT '人员ID',
        company_name VARCHAR(255) NOT NULL COMMENT '公司名称',
        position VARCHAR(100) NOT NULL COMMENT '职位',
        start_date DATE COMMENT '开始日期',
        end_date DATE COMMENT '结束日期',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (person_id) REFERENCES persons(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任职记录表'
    `);

    console.log('任职档案表初始化成功');

    // 插入初始数据
    const [existingPersons] = await pool.query('SELECT COUNT(*) as count FROM persons');
    if (existingPersons[0].count === 0) {
      const initialPersons = [
        ['苏州海投资中心', '身份证', '320100199001011234'],
        ['苏州海投资中心', '身份证', '320100199002022345'],
        ['张天成', '身份证', '320100199003033456'],
        ['张大威', '身份证', '320100199004044567'],
        ['张天威', '身份证', '320100199005055678']
      ];

      for (const [name, idType, idNumber] of initialPersons) {
        await pool.query(
          'INSERT INTO persons (name, id_type, id_number) VALUES (?, ?, ?)',
          [name, idType, idNumber]
        );
      }

      // 插入任职记录
      const [persons] = await pool.query('SELECT id, name FROM persons');
      const [companies] = await pool.query('SELECT company_name_cn FROM companies LIMIT 3');

      if (companies.length > 0) {
        for (const person of persons) {
          const company = companies[0]; // 使用第一个公司
          await pool.query(
            `INSERT INTO employment_records (
              person_id, company_name, position, start_date
            ) VALUES (?, ?, ?, ?)`,
            [
              person.id,
              company.company_name_cn + '(Shenzhen A Company)',
              '董事',
              '2023-11-10'
            ]
          );
        }
      }

      console.log('任职档案初始数据插入成功');
    }
  } catch (error) {
    console.error('初始化任职档案表失败:', error);
  }
}

// 初始化任职档案表
initEmploymentArchiveTable();

// 获取所有人员
app.get('/api/persons', async (req, res) => {
  try {
    const [rows] = await pool.query(
      `SELECT
        id, name, id_type as idType, id_number as idNumber,
        DATE_FORMAT(created_at, "%Y-%m-%d %H:%i:%s") as createTime
      FROM persons
      ORDER BY created_at DESC`
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取人员数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取人员数据失败'
    });
  }
});

// 获取任职记录
app.get('/api/employment-records', async (req, res) => {
  try {
    const [rows] = await pool.query(
      `SELECT
        er.id,
        er.person_id as personId,
        er.company_name as companyName,
        er.position,
        er.start_date as startDate,
        er.end_date as endDate,
        DATE_FORMAT(er.created_at, "%Y-%m-%d %H:%i:%s") as createTime
      FROM employment_records er
      ORDER BY er.company_name ASC, er.start_date ASC`
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取任职记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任职记录失败'
    });
  }
});

// 新增人员
app.post('/api/persons', async (req, res) => {
  try {
    const { name, idType, idNumber } = req.body;

    if (!name || !idNumber) {
      return res.status(400).json({
        success: false,
        message: '姓名和证件号码不能为空'
      });
    }

    // 检查证件号码是否已存在
    const [existing] = await pool.query(
      'SELECT id FROM persons WHERE id_number = ?',
      [idNumber]
    );

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该证件号码已存在'
      });
    }

    // 插入新人员
    const [result] = await pool.query(
      'INSERT INTO persons (name, id_type, id_number) VALUES (?, ?, ?)',
      [name, idType || '身份证', idNumber]
    );

    res.json({
      success: true,
      data: {
        id: result.insertId,
        name,
        idType: idType || '身份证',
        idNumber
      }
    });
  } catch (error) {
    console.error('新增人员失败:', error);
    res.status(500).json({
      success: false,
      message: '新增人员失败'
    });
  }
});

// 更新人员
app.put('/api/persons/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, idType, idNumber } = req.body;

    if (!name || !idNumber) {
      return res.status(400).json({
        success: false,
        message: '姓名和证件号码不能为空'
      });
    }

    // 检查证件号码是否已被其他人使用
    const [existing] = await pool.query(
      'SELECT id FROM persons WHERE id_number = ? AND id != ?',
      [idNumber, id]
    );

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该证件号码已被其他人使用'
      });
    }

    // 更新人员
    const [result] = await pool.query(
      'UPDATE persons SET name = ?, id_type = ?, id_number = ? WHERE id = ?',
      [name, idType, idNumber, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '人员不存在'
      });
    }

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新人员失败:', error);
    res.status(500).json({
      success: false,
      message: '更新人员失败'
    });
  }
});

// 删除人员
app.delete('/api/persons/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.query(
      'DELETE FROM persons WHERE id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '人员不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除人员失败:', error);
    res.status(500).json({
      success: false,
      message: '删除人员失败'
    });
  }
});

// 添加测试记录的路由
app.get('/api/add-test-record', async (req, res) => {
  try {
    // 添加测试公司
    const [result] = await pool.query(
      `INSERT INTO companies (
        company_name_cn, company_name_en, registered_capital, establish_date,
        business_segment, region, agency, annual_update, registered_address, operation_status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        '测试公司', 'Test Company', '1000.0000万元人民币', '2023-06-15',
        '电子商务', '华南', '代理机构A', '不管年审', '广州市天河区测试地址123号', '正常经营'
      ]
    );

    res.json({ success: true, message: '测试记录添加成功', companyId: result.insertId });
  } catch (error) {
    console.error('添加测试记录失败:', error);
    res.status(500).json({ success: false, message: '添加测试记录失败', error: error.message });
  }
});

// 添加多个测试公司数据
app.get('/api/add-test-companies', async (req, res) => {
  try {
    const testCompanies = [
      ['深圳科技有限公司', 'Shenzhen Tech Co., Ltd.', '1000万元人民币', '2023-01-15', 'A. 农、林、牧、渔业', '深圳', '代理机构A', '不管年审', '深圳市南山区科技园', '正常经营'],
      ['广州贸易有限公司', 'Guangzhou Trade Co., Ltd.', '2000万元人民币', '2023-02-20', 'A. 农、林、牧、渔业', '广州', '代理机构B', '管年审（固定周期）', '广州市天河区商务中心', '正常经营'],
      ['上海投资有限公司', 'Shanghai Investment Co., Ltd.', '5000万元人民币', '2023-03-10', 'B. 采矿业', '上海', '代理机构C', '管年审（滚动周期）', '上海市浦东新区金融街', '正常经营'],
      ['北京制造有限公司', 'Beijing Manufacturing Co., Ltd.', '3000万元人民币', '2023-04-05', 'C. 制造业', '北京', '代理机构A', '不管年审', '北京市朝阳区工业园', '正常经营'],
      ['杭州电力有限公司', 'Hangzhou Power Co., Ltd.', '8000万元人民币', '2023-05-12', 'D. 电力、热力、燃气及水的生产和供应业', '杭州', '代理机构B', '管年审（固定周期）', '杭州市西湖区电力大厦', '正常经营'],
      ['成都建筑有限公司', 'Chengdu Construction Co., Ltd.', '4000万元人民币', '2023-06-18', 'E. 建筑业', '成都', '代理机构C', '管年审（滚动周期）', '成都市高新区建筑园', '正常经营']
    ];

    const results = [];
    for (const company of testCompanies) {
      try {
        const [result] = await pool.query(
          `INSERT INTO companies (
            company_name_cn, company_name_en, registered_capital, establish_date,
            business_segment, region, agency, annual_update, registered_address, operation_status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          company
        );
        results.push({ companyId: result.insertId, name: company[0] });
      } catch (error) {
        console.error(`添加公司 ${company[0]} 失败:`, error);
      }
    }

    res.json({
      success: true,
      message: `成功添加 ${results.length} 家测试公司`,
      companies: results
    });
  } catch (error) {
    console.error('添加测试公司失败:', error);
    res.status(500).json({ success: false, message: '添加测试公司失败', error: error.message });
  }
});
