import express from 'express';
import bodyParser from 'body-parser';
import cors from 'cors';
import mysql from 'mysql2/promise';

const app = express();
// Windows用户专用配置 - 使用一个不同的端口，因为3306已被MySQL占用
// 支持从命令行参数中获取端口号
const args = process.argv.slice(2);
let port = 8080; // 默认使用标准Web端口8080

// 解析命令行参数
args.forEach(arg => {
  if (arg.startsWith('--port=')) {
    const portArg = arg.split('=')[1];
    const portNum = parseInt(portArg, 10);
    if (!isNaN(portNum)) {
      port = portNum;
      console.log(`从命令行参数设置端口为: ${port}`);
    }
  }
});

// 添加更多调试信息
console.log('启动服务器，使用端口:', port);
console.log('当前工作目录:', process.cwd());
console.log('Node.js版本:', process.version);

// 中间件 - 配置CORS允许前端访问
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://127.0.0.1:5173', 'http://127.0.0.1:5174'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// ============================================================================
// Windows用户数据库连接配置
// ============================================================================
const dbConfig = {
  host: 'localhost',        // Windows本地主机
  port: 3306,              // 默认MySQL端口
  user: 'root',            // MySQL用户名（请根据实际情况修改）
  password: 'Yiwill@2025', // MySQL密码（请根据实际情况修改）
  database: 'stake_management_v2',
  // 注意：Windows不需要socketPath配置
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  debug: false // 关闭调试模式以减少日志输出
};

// 如果您创建了专用数据库用户，请使用以下配置：
// const dbConfig = {
//   host: 'localhost',
//   port: 3306,
//   user: 'stake_user',
//   password: 'stake_password_2025',
//   database: 'stake_management_v2',
//   waitForConnections: true,
//   connectionLimit: 10,
//   queueLimit: 0,
//   debug: false
// };

// 创建数据库连接池
const pool = mysql.createPool(dbConfig);

// 监听连接池错误
pool.on('error', (err) => {
  console.error('数据库连接池错误:', err);
});

// 测试数据库连接
async function testDatabaseConnection() {
  try {
    console.log('正在测试数据库连接...');
    const connection = await pool.getConnection();
    console.log('数据库连接成功!');
    connection.release();
    return true;
  } catch (error) {
    console.error('数据库连接失败:', error.message);
    console.error('请检查以下配置:');
    console.error('1. MySQL服务是否正在运行');
    console.error('2. 数据库用户名和密码是否正确');
    console.error('3. 数据库名称是否存在');
    console.error('4. 网络连接是否正常');
    return false;
  }
}

// 启动时测试数据库连接
testDatabaseConnection();

// ============================================================================
// API路由定义
// ============================================================================

// 健康检查接口
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    message: '股权管理系统后端服务正常运行',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  });
});

// 数据库连接测试接口
app.get('/api/mysql-3306-test', async (req, res) => {
  try {
    const connection = await pool.getConnection();
    const [rows] = await connection.execute('SELECT 1 as test, NOW() as current_time, VERSION() as mysql_version');
    connection.release();
    
    res.json({ 
      success: true, 
      message: '数据库连接测试成功',
      data: rows[0],
      config: {
        host: dbConfig.host,
        port: dbConfig.port,
        database: dbConfig.database,
        user: dbConfig.user
      }
    });
  } catch (error) {
    console.error('API: 数据库连接失败:', error);
    res.status(500).json({ 
      success: false, 
      message: '数据库连接失败', 
      error: error.message,
      config: {
        host: dbConfig.host,
        port: dbConfig.port,
        database: dbConfig.database,
        user: dbConfig.user
      }
    });
  }
});

// 获取数据库表信息
app.get('/api/database/tables', async (req, res) => {
  try {
    const [tables] = await pool.query('SHOW TABLES');
    res.json({
      success: true,
      message: '获取数据库表信息成功',
      tables: tables
    });
  } catch (error) {
    console.error('获取数据库表信息失败:', error);
    res.status(500).json({
      success: false,
      message: '获取数据库表信息失败',
      error: error.message
    });
  }
});

// 注意：这里只包含了基础的API接口
// 完整的业务API接口请参考原始的 server/index.js 文件

// ============================================================================
// 服务器启动
// ============================================================================

// 启动服务器
app.listen(port, () => {
  console.log(`服务器运行在 http://localhost:${port}`);
  console.log(`也可以通过 http://127.0.0.1:${port} 访问`);
  console.log(`特别提示：要测试MySQL数据库连接（端口3306），请访问 http://localhost:${port}/api/mysql-3306-test`);
}).on('error', (err) => {
  if (err.code === 'EADDRINUSE') {
    console.error(`端口 ${port} 已被占用，请尝试使用其他端口`);
    console.error(`您可以使用以下命令指定其他端口:`);
    console.error(`node server/index.js --port=8081`);
  } else {
    console.error('服务器启动失败:', err);
  }
});

// 优雅关闭
process.on('SIGINT', async () => {
  console.log('\n正在关闭服务器...');
  try {
    await pool.end();
    console.log('数据库连接池已关闭');
  } catch (error) {
    console.error('关闭数据库连接池时出错:', error);
  }
  process.exit(0);
});

export default app;
