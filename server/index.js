import express from 'express';
import bodyParser from 'body-parser';
import cors from 'cors';
import mysql from 'mysql2/promise';

const app = express();
// 使用一个不同的端口，因为3306已被MySQL占用
// 支持从命令行参数中获取端口号
const args = process.argv.slice(2);
let port = 8080; // 默认使用标准Web端口8080

// 解析命令行参数
args.forEach(arg => {
  if (arg.startsWith('--port=')) {
    const portArg = arg.split('=')[1];
    const portNum = parseInt(portArg, 10);
    if (!isNaN(portNum)) {
      port = portNum;
      console.log(`从命令行参数设置端口为: ${port}`);
    }
  }
});

// 添加更多调试信息
console.log('启动服务器，使用端口:', port);
console.log('当前工作目录:', process.cwd());
console.log('Node.js版本:', process.version);

// 中间件 - 配置CORS允许前端访问
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://127.0.0.1:5173', 'http://127.0.0.1:5174'],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization']
}));
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local', // 更新为正确的MySQL主机名
  port: 3306,       // 默认MySQL端口
  user: 'txuser',    // 使用提供的MySQL用户名
  password: 'txpassword', // 使用提供的MySQL密码
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock', // 添加socket路径
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  debug: false // 关闭调试模式以减少日志输出
};

// 创建数据库连接池
const pool = mysql.createPool(dbConfig);

// 监听连接池错误
pool.on('error', (err) => {
  console.error('数据库连接池错误:', err);
});

// 测试连接池
async function testDatabaseConnection() {
  try {
    console.log('正在测试数据库连接...');
    const connection = await pool.getConnection();
    console.log('数据库连接成功!');
    connection.release();
    return true;
  } catch (error) {
    console.error('数据库连接测试失败:', error);
    return false;
  }
}

// 启动时测试数据库连接
testDatabaseConnection();

// 测试数据库连接
app.get('/api/test-db', async (req, res) => {
  try {
    console.log('API: 正在测试数据库连接...');
    const connection = await pool.getConnection();
    console.log('API: 获取到数据库连接');
    
    // 执行一个简单的查询以确保连接正常工作
    const [result] = await connection.query('SELECT 1 as test');
    console.log('API: 查询测试结果:', result);
    
    connection.release();
    console.log('API: 数据库连接测试成功');
    res.json({ success: true, message: '数据库连接成功', test: result });
  } catch (error) {
    console.error('API: 数据库连接失败:', error);
    // 返回更详细的错误信息
    res.status(500).json({ 
      success: false, 
      message: '数据库连接失败', 
      error: error.message,
      stack: error.stack,
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState,
      sqlMessage: error.sqlMessage
    });
  }
});

// 特殊路由，用于满足用户要求的 curl http://localhost:3306/api/test-db
app.get('/api/mysql-3306-test', async (req, res) => {
  try {
    console.log('API: 正在测试MySQL 3306端口数据库连接...');
    const connection = await pool.getConnection();
    console.log('API: 获取到数据库连接');
    
    // 执行一个简单的查询以确保连接正常工作
    const [result] = await connection.query('SELECT 1 as test');
    console.log('API: 查询测试结果:', result);
    
    connection.release();
    console.log('API: 数据库连接测试成功');
    
    // 返回特殊信息，说明这是通过8080端口访问的，但实际连接的是3306端口的MySQL
    res.json({ 
      success: true, 
      message: '成功连接到MySQL数据库（端口3306）', 
      note: '注意：这个API是通过Web服务器（端口8080）访问的，因为无法直接在MySQL端口3306上运行HTTP服务器',
      test: result,
      mysql_port: 3306,
      web_server_port: port
    });
  } catch (error) {
    console.error('API: 数据库连接失败:', error);
    // 返回更详细的错误信息
    res.status(500).json({ 
      success: false, 
      message: '数据库连接失败', 
      error: error.message,
      stack: error.stack,
      code: error.code,
      errno: error.errno,
      sqlState: error.sqlState,
      sqlMessage: error.sqlMessage
    });
  }
});

// 临时API：查看companies表结构
app.get('/api/debug/companies-structure', async (req, res) => {
  try {
    const [structure] = await pool.query('DESCRIBE companies');
    res.json({
      success: true,
      structure: structure
    });
  } catch (error) {
    console.error('查看表结构失败:', error);
    res.status(500).json({
      success: false,
      message: '查看表结构失败',
      error: error.message
    });
  }
});

// 临时API：查看investments表结构
app.get('/api/debug/investments-structure', async (req, res) => {
  try {
    const [structure] = await pool.query('DESCRIBE investments');
    res.json({
      success: true,
      structure: structure
    });
  } catch (error) {
    console.error('查看investments表结构失败:', error);
    res.status(500).json({
      success: false,
      message: '查看investments表结构失败',
      error: error.message
    });
  }
});

// 临时API：查看investments表数据
app.get('/api/debug/investments-data', async (req, res) => {
  try {
    const [data] = await pool.query('SELECT * FROM investments LIMIT 10');
    res.json({
      success: true,
      data: data
    });
  } catch (error) {
    console.error('查看investments表数据失败:', error);
    res.status(500).json({
      success: false,
      message: '查看investments表数据失败',
      error: error.message
    });
  }
});

// 检查公司是否重复
app.post('/api/company/check-duplicate', async (req, res) => {
  try {
    const { companyNameCn, companyNameEn } = req.body;
    
    const [rows] = await pool.query(
      'SELECT * FROM companies WHERE company_name_cn = ? AND company_name_en = ?',
      [companyNameCn, companyNameEn]
    );
    
    const duplicate = rows.length > 0;
    res.json({ duplicate });
  } catch (error) {
    console.error('检查公司重复失败:', error);
    res.status(500).json({ success: false, message: '检查公司重复失败', error: error.message });
  }
});

// 添加新公司
app.post('/api/company/add', async (req, res) => {
  console.log('收到添加公司请求:', req.body);
  
  // 使用事务确保数据一致性
  const connection = await pool.getConnection();
  
  try {
    await connection.beginTransaction();
    console.log('事务开始');
    
    // 添加公司基本信息
    // 处理日期格式 - 将ISO字符串转换为MySQL DATE格式
    let establishDate = req.body.establishDate;
    if (establishDate) {
      // 如果是ISO字符串，转换为YYYY-MM-DD格式
      if (typeof establishDate === 'string' && establishDate.includes('T')) {
        establishDate = new Date(establishDate).toISOString().split('T')[0];
      }
    }

    // 先查找业务板块ID
    let businessSegmentId = null;
    if (req.body.businessSegment) {
      const [segmentResult] = await connection.query(
        'SELECT id FROM business_segments WHERE name = ?',
        [req.body.businessSegment]
      );
      if (segmentResult.length > 0) {
        businessSegmentId = segmentResult[0].id;
      }
    }

    const [result] = await connection.execute(
      `INSERT INTO companies (
        company_name_cn, company_name_en, registered_capital, establish_date,
        business_segment_id, region, agency, annual_update, registered_address, operation_status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        req.body.companyNameCn,
        req.body.companyNameEn,
        req.body.registeredCapital,
        establishDate,
        businessSegmentId,
        req.body.region,
        req.body.agency,
        req.body.annualUpdate,
        req.body.registeredAddress,
        req.body.operationStatus
      ]
    );
    
    console.log('公司基本信息添加成功, ID:', result.insertId);
    
    // 提交事务
    await connection.commit();
    console.log('事务提交成功');
    
    res.json({ 
      success: true, 
      message: '公司添加成功', 
      companyId: result.insertId 
    });
  } catch (error) {
    // 回滚事务
    await connection.rollback();
    console.error('添加公司失败, 事务回滚:', error);
    
    res.status(500).json({ 
      success: false, 
      message: '添加公司失败', 
      error: error.message 
    });
  } finally {
    connection.release();
    console.log('数据库连接已释放');
  }
});

// 获取所有公司
app.get('/api/companies', async (req, res) => {
  try {
    const [rows] = await pool.query('SELECT * FROM companies');
    res.json({ success: true, data: rows });
  } catch (error) {
    console.error('获取公司列表失败:', error);
    res.status(500).json({ success: false, message: '获取公司列表失败', error: error.message });
  }
});

// 根据业务板块获取公司列表 - 必须放在 :id 路由之前
app.get('/api/companies/by-segment', async (req, res) => {
  try {
    const { segment } = req.query;

    if (!segment) {
      return res.status(400).json({
        success: false,
        message: '业务板块参数不能为空'
      });
    }

    console.log('🔍 查询业务板块下的公司:', segment);

    // 查询指定业务板块下的所有公司
    const [rows] = await pool.query(
      `SELECT
        c.id,
        c.company_name_cn as companyNameCn,
        c.company_name_en as companyNameEn,
        bs.name as businessSegment,
        r.name as region,
        c.registered_capital as registeredCapital,
        c.establish_date as establishDate
      FROM companies c
      LEFT JOIN business_segments bs ON c.business_segment_id = bs.id
      LEFT JOIN regions r ON c.region_id = r.id
      WHERE bs.name = ?
      ORDER BY c.establish_date DESC`,
      [segment]
    );

    console.log(`✅ 找到 ${rows.length} 家公司属于业务板块: ${segment}`);

    res.json({
      success: true,
      data: rows,
      message: `找到 ${rows.length} 家公司`
    });
  } catch (error) {
    console.error('获取业务板块下公司列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取公司列表失败',
      error: error.message
    });
  }
});

// 更新公司信息
app.put('/api/companies/:id', async (req, res) => {
  try {
    const companyId = req.params.id;
    const {
      chineseName,
      englishName,
      registeredCapital,
      establishmentDate,
      businessScopeId,
      regionId,
      agencyId,
      registrationAddress,
      operationStatusId
    } = req.body;

    // 验证必填字段
    if (!chineseName || !englishName || !registeredCapital || !establishmentDate) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 更新公司信息
    const [result] = await pool.query(
      `UPDATE companies
       SET company_name_cn = ?, company_name_en = ?, registered_capital = ?,
           establish_date = ?, business_segment_id = ?, region_id = ?,
           agency_id = ?, registered_address = ?, operation_status_id = ?
       WHERE id = ?`,
      [
        chineseName,
        englishName,
        registeredCapital,
        establishmentDate,
        businessScopeId,
        regionId,
        agencyId || null,
        registrationAddress,
        operationStatusId || null,
        companyId
      ]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '公司不存在'
      });
    }

    console.log(`✅ 公司信息更新成功: ID ${companyId}, 名称 ${chineseName}`);

    res.json({
      success: true,
      message: '公司信息更新成功'
    });

  } catch (error) {
    console.error('更新公司信息失败:', error);
    res.status(500).json({
      success: false,
      message: '更新公司信息失败'
    });
  }
});

// 根据ID获取单个公司详情 - 必须放在具体路由之后
app.get('/api/companies/:id', async (req, res) => {
  try {
    const { id } = req.params;

    console.log('🔍 获取公司详情, ID:', id);

    // 查询公司基本信息 - 使用正确的字段名
    const [rows] = await pool.query(
      `SELECT
        c.id,
        c.company_name_cn as chineseName,
        c.company_name_en as englishName,
        c.registered_capital as registeredCapital,
        c.establish_date as establishmentDate,
        c.business_segment_id as businessScopeId,
        c.region_id as regionId,
        c.agency_id as agencyId,
        c.annual_update_status_id as annualReportId,
        c.registered_address as registrationAddress,
        c.operation_status_id as operationStatusId,
        bs.name as businessScope
      FROM companies c
      LEFT JOIN business_segments bs ON c.business_segment_id = bs.id
      WHERE c.id = ?`,
      [id]
    );

    if (rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公司不存在'
      });
    }

    const company = rows[0];
    console.log('✅ 公司详情获取成功:', company);

    res.json({
      success: true,
      data: company
    });
  } catch (error) {
    console.error('获取公司详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取公司详情失败',
      error: error.message
    });
  }
});

// 获取所有业务板块
app.get('/api/business-segments', async (req, res) => {
  try {
    console.log('🔄 获取业务板块数据...');
    const { name } = req.query;

    // 构建查询SQL
    let sql = 'SELECT * FROM business_segments';
    let params = [];

    if (name && name.trim() !== '') {
      sql += ' WHERE name LIKE ?';
      params.push(`%${name}%`);
      console.log('🔍 按名称查询:', name);
    }

    // 获取业务板块
    const [segments] = await pool.query(sql, params);
    console.log('📊 业务板块数据:', segments);

    // 为每个业务板块计算公司数量
    const segmentsWithCount = await Promise.all(
      segments.map(async (segment) => {
        try {
          const [countResult] = await pool.query(
            'SELECT COUNT(*) as count FROM companies WHERE business_segment_id = ?',
            [segment.id]
          );
          const companyCount = countResult[0].count;
          console.log(`📈 业务板块 "${segment.name}" 有 ${companyCount} 家公司`);

          return {
            ...segment,
            companyCount: companyCount,
            createTime: segment.create_time || segment.created_at || new Date().toISOString()
          };
        } catch (error) {
          console.error(`计算业务板块 "${segment.name}" 公司数量失败:`, error);
          return {
            ...segment,
            companyCount: 0,
            createTime: segment.create_time || segment.created_at || new Date().toISOString()
          };
        }
      })
    );

    console.log('✅ 业务板块数据处理完成:', segmentsWithCount);
    res.json({ success: true, data: segmentsWithCount });
  } catch (error) {
    console.error('获取业务板块失败:', error);
    res.status(500).json({ success: false, message: '获取业务板块失败', error: error.message });
  }
});



// 初始化region表
async function initRegionTable() {
  try {
    // 创建region表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS region (
        id INT AUTO_INCREMENT PRIMARY KEY,
        type VARCHAR(50) NOT NULL COMMENT '类型：国内/海外公司对外投资',
        region VARCHAR(100) NOT NULL COMMENT '地区名称',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        UNIQUE KEY unique_type_region (type, region)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='地区管理表'
    `);

    // 检查是否有数据，如果没有则插入初始数据
    const [rows] = await pool.query('SELECT COUNT(*) as count FROM region');
    if (rows[0].count === 0) {
      const initialData = [
        ['国内', '广州'],
        ['海外公司对外投资', '亚太'],
        ['海外公司对外投资', '美国'],
        ['国内', '深圳'],
        ['国内', '北京'],
        ['国内', '上海']
      ];

      for (const [type, region] of initialData) {
        await pool.query(
          'INSERT INTO region (type, region) VALUES (?, ?)',
          [type, region]
        );
      }
      console.log('初始地区数据插入成功');
    }
  } catch (error) {
    console.error('初始化region表失败:', error);
  }
}

// 初始化表
initRegionTable();
initPositionsTable();
initPaymentMethodsTable();

// 获取所有地区数据
app.get('/api/regions', async (req, res) => {
  try {
    const [rows] = await pool.query(
      'SELECT id, type, region, DATE_FORMAT(create_time, "%Y-%m-%d %H:%i:%s") as createTime FROM region ORDER BY create_time DESC'
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取地区数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取地区数据失败'
    });
  }
});

// 搜索地区数据
app.get('/api/regions/search', async (req, res) => {
  try {
    const { type, region } = req.query;

    let sql = 'SELECT id, type, region, DATE_FORMAT(create_time, "%Y-%m-%d %H:%i:%s") as createTime FROM region WHERE 1=1';
    const params = [];

    if (type) {
      sql += ' AND type = ?';
      params.push(type);
    }

    if (region) {
      sql += ' AND region LIKE ?';
      params.push(`%${region}%`);
    }

    sql += ' ORDER BY create_time DESC';

    const [rows] = await pool.query(sql, params);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('搜索地区数据失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索地区数据失败'
    });
  }
});

// 地区数据验证函数
function validateRegionData(type, region) {
  // 定义地区分类
  const domesticRegions = [
    // 直辖市
    '北京', '上海', '天津', '重庆',
    // 省份
    '河北', '山西', '辽宁', '吉林', '黑龙江', '江苏', '浙江', '安徽', '福建', '江西',
    '山东', '河南', '湖北', '湖南', '广东', '海南', '四川', '贵州', '云南', '陕西',
    '甘肃', '青海', '台湾',
    // 自治区
    '内蒙古', '广西', '西藏', '宁夏', '新疆',
    // 特别行政区
    '香港', '澳门',
    // 主要城市
    '广州', '深圳', '东莞', '佛山', '中山', '珠海', '惠州', '江门', '肇庆', '汕头', '湛江',
    '成都', '绵阳', '德阳', '南充', '宜宾', '自贡', '乐山', '泸州', '达州', '内江',
    '杭州', '宁波', '温州', '嘉兴', '湖州', '绍兴', '金华', '衢州', '舟山', '台州', '丽水',
    '南京', '无锡', '徐州', '常州', '苏州', '南通', '连云港', '淮安', '盐城', '扬州', '镇江', '泰州', '宿迁',
    '武汉', '黄石', '十堰', '宜昌', '襄阳', '鄂州', '荆门', '孝感', '荆州', '黄冈', '咸宁', '随州',
    '长沙', '株洲', '湘潭', '衡阳', '邵阳', '岳阳', '常德', '张家界', '益阳', '郴州', '永州', '怀化', '娄底',
    '西安', '铜川', '宝鸡', '咸阳', '渭南', '延安', '汉中', '榆林', '安康', '商洛',
    '济南', '青岛', '淄博', '枣庄', '东营', '烟台', '潍坊', '济宁', '泰安', '威海', '日照', '临沂', '德州', '聊城', '滨州', '菏泽',
    '郑州', '开封', '洛阳', '平顶山', '安阳', '鹤壁', '新乡', '焦作', '濮阳', '许昌', '漯河', '三门峡', '南阳', '商丘', '信阳', '周口', '驻马店',
    '石家庄', '唐山', '秦皇岛', '邯郸', '邢台', '保定', '张家口', '承德', '沧州', '廊坊', '衡水',
    '太原', '大同', '阳泉', '长治', '晋城', '朔州', '晋中', '运城', '忻州', '临汾', '吕梁',
    '沈阳', '大连', '鞍山', '抚顺', '本溪', '丹东', '锦州', '营口', '阜新', '辽阳', '盘锦', '铁岭', '朝阳', '葫芦岛',
    '长春', '吉林', '四平', '辽源', '通化', '白山', '松原', '白城',
    '哈尔滨', '齐齐哈尔', '鸡西', '鹤岗', '双鸭山', '大庆', '伊春', '佳木斯', '七台河', '牡丹江', '黑河', '绥化',
    '合肥', '芜湖', '蚌埠', '淮南', '马鞍山', '淮北', '铜陵', '安庆', '黄山', '滁州', '阜阳', '宿州', '六安', '亳州', '池州', '宣城',
    '福州', '厦门', '莆田', '三明', '泉州', '漳州', '南平', '龙岩', '宁德',
    '南昌', '景德镇', '萍乡', '九江', '新余', '鹰潭', '赣州', '吉安', '宜春', '抚州', '上饶',
    '昆明', '曲靖', '玉溪', '保山', '昭通', '丽江', '普洱', '临沧',
    '贵阳', '六盘水', '遵义', '安顺', '毕节', '铜仁',
    '兰州', '嘉峪关', '金昌', '白银', '天水', '武威', '张掖', '平凉', '酒泉', '庆阳', '定西', '陇南',
    '西宁', '海东',
    '银川', '石嘴山', '吴忠', '固原', '中卫',
    '乌鲁木齐', '克拉玛依', '吐鲁番', '哈密',
    '拉萨', '日喀则', '昌都', '林芝', '山南', '那曲',
    '呼和浩特', '包头', '乌海', '赤峰', '通辽', '鄂尔多斯', '呼伦贝尔', '巴彦淖尔', '乌兰察布',
    '南宁', '柳州', '桂林', '梧州', '北海', '防城港', '钦州', '贵港', '玉林', '百色', '贺州', '河池', '来宾', '崇左',
    '海口', '三亚', '三沙', '儋州',
    // 地区概念
    '华南', '华东', '华北', '华中', '西南', '西北', '东北'
  ];

  const overseasRegions = [
    '美国', '英国', '日本', '韩国', '新加坡', '澳大利亚', '加拿大', '德国', '法国',
    '意大利', '西班牙', '荷兰', '瑞士', '瑞典', '挪威', '丹麦', '芬兰', '比利时',
    '奥地利', '爱尔兰', '葡萄牙', '希腊', '波兰', '捷克', '匈牙利', '俄罗斯',
    '印度', '泰国', '马来西亚', '印尼', '菲律宾', '越南', '缅甸', '柬埔寨',
    '巴西', '阿根廷', '墨西哥', '智利', '秘鲁', '哥伦比亚', '委内瑞拉',
    '南非', '埃及', '摩洛哥', '尼日利亚', '肯尼亚', '亚太', '欧洲', '北美', '南美'
  ];

  const offshoreRegions = [
    '开曼群岛', '英属维尔京群岛', '百慕大', '塞舌尔', '萨摩亚', '马绍尔群岛',
    '巴哈马', '巴巴多斯', '安圭拉', '特克斯和凯科斯群岛', '库克群岛', '纽埃',
    '瓦努阿图', '毛里求斯', '直布罗陀', '马恩岛', '泽西岛', '根西岛'
  ];

  if (type === '国内') {
    // 国内类型：地区必须是国内城市或省份
    return domesticRegions.some(domesticRegion =>
      domesticRegion.includes(region) || region.includes(domesticRegion)
    );
  } else if (type === '离岸') {
    // 离岸类型：地区必须是离岸金融中心
    return offshoreRegions.some(offshoreRegion =>
      offshoreRegion.includes(region) || region.includes(offshoreRegion)
    );
  } else if (type === '海外') {
    // 海外类型：地区必须是非中国的国家或城市
    return overseasRegions.some(overseasRegion =>
      overseasRegion.includes(region) || region.includes(overseasRegion)
    );
  }

  return false;
}

// 新增地区数据
app.post('/api/regions', async (req, res) => {
  try {
    const { type, region } = req.body;

    if (!type || !region) {
      return res.status(400).json({
        success: false,
        message: '地区类型和地区名称不能为空'
      });
    }

    // 验证地区类型是否有效
    if (!['国内', '离岸', '海外'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: '地区类型必须是：国内、离岸、海外'
      });
    }

    // 数据验证
    const isValid = validateRegionData(type, region);
    if (!isValid) {
      let errorMessage;
      if (type === '国内') {
        errorMessage = '地区类型选择为"国内"时，地区必须是中国的城市或省份';
      } else if (type === '离岸') {
        errorMessage = '地区类型选择为"离岸"时，地区必须是离岸金融中心';
      } else if (type === '海外') {
        errorMessage = '地区类型选择为"海外"时，地区必须是非中国的国家或城市';
      }

      return res.status(400).json({
        success: false,
        message: errorMessage
      });
    }

    // 检查是否已存在相同的类型和地区组合
    const [existing] = await pool.query(
      'SELECT id FROM region WHERE type = ? AND region = ?',
      [type, region]
    );

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该地区类型和地区的组合已存在'
      });
    }

    // 插入新数据
    const [result] = await pool.query(
      'INSERT INTO region (type, region) VALUES (?, ?)',
      [type, region]
    );

    res.json({
      success: true,
      data: {
        id: result.insertId,
        type,
        region
      }
    });
  } catch (error) {
    console.error('新增地区数据失败:', error);
    res.status(500).json({
      success: false,
      message: '新增地区数据失败'
    });
  }
});

// 更新地区数据
app.put('/api/regions/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { type, region } = req.body;

    if (!type || !region) {
      return res.status(400).json({
        success: false,
        message: '地区类型和地区名称不能为空'
      });
    }

    // 验证地区类型是否有效
    if (!['国内', '离岸', '海外'].includes(type)) {
      return res.status(400).json({
        success: false,
        message: '地区类型必须是：国内、离岸、海外'
      });
    }

    // 数据验证
    const isValid = validateRegionData(type, region);
    if (!isValid) {
      let errorMessage;
      if (type === '国内') {
        errorMessage = '地区类型选择为"国内"时，地区必须是中国的城市或省份';
      } else if (type === '离岸') {
        errorMessage = '地区类型选择为"离岸"时，地区必须是离岸金融中心';
      } else if (type === '海外') {
        errorMessage = '地区类型选择为"海外"时，地区必须是非中国的国家或城市';
      }

      return res.status(400).json({
        success: false,
        message: errorMessage
      });
    }

    // 检查是否已存在相同的类型和地区组合（排除当前记录）
    const [existing] = await pool.query(
      'SELECT id FROM region WHERE type = ? AND region = ? AND id != ?',
      [type, region, id]
    );

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该地区类型和地区的组合已存在'
      });
    }

    // 更新数据
    const [result] = await pool.query(
      'UPDATE region SET type = ?, region = ? WHERE id = ?',
      [type, region, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '地区数据不存在'
      });
    }

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新地区数据失败:', error);
    res.status(500).json({
      success: false,
      message: '更新地区数据失败'
    });
  }
});

// 删除地区数据
app.delete('/api/regions/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.query(
      'DELETE FROM region WHERE id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '地区数据不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除地区数据失败:', error);
    res.status(500).json({
      success: false,
      message: '删除地区数据失败'
    });
  }
});

// 初始化positions表
async function initPositionsTable() {
  try {
    // 创建positions表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS positions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE COMMENT '职位名称',
        sort INT DEFAULT 0 COMMENT '排序',
        updater VARCHAR(100) DEFAULT '' COMMENT '更新人',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任职职位表'
    `);

    // 检查是否有数据，如果没有则插入初始数据
    const [rows] = await pool.query('SELECT COUNT(*) as count FROM positions');
    if (rows[0].count === 0) {
      const initialData = [
        '财务负责人',
        '董事',
        '监事',
        '总经理',
        '副总经理',
        '董事长',
        '副董事长',
        '执行董事',
        '经理',
        '法定代表人'
      ];

      for (let i = 0; i < initialData.length; i++) {
        await pool.query(
          'INSERT INTO positions (name, sort, updater) VALUES (?, ?, ?)',
          [initialData[i], i + 1, 'System']
        );
      }
      console.log('初始任职职位数据插入成功');
    }
  } catch (error) {
    console.error('初始化positions表失败:', error);
  }
}

// 初始化payment_methods表
async function initPaymentMethodsTable() {
  try {
    // 创建payment_methods表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS payment_methods (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(100) NOT NULL UNIQUE COMMENT '实缴出资方式名称',
        sort INT DEFAULT 0 COMMENT '排序',
        updater VARCHAR(100) DEFAULT '' COMMENT '更新人',
        update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
        create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间'
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实缴出资方式表'
    `);

    // 检查是否有数据，如果没有则插入初始数据
    const [rows] = await pool.query('SELECT COUNT(*) as count FROM payment_methods');
    if (rows[0].count === 0) {
      const initialData = [
        '货币',
        '实物',
        '知识产权',
        '土地使用权',
        '股权',
        '债权',
        '其他财产权利'
      ];

      for (let i = 0; i < initialData.length; i++) {
        await pool.query(
          'INSERT INTO payment_methods (name, sort, updater) VALUES (?, ?, ?)',
          [initialData[i], i + 1, 'System']
        );
      }
      console.log('初始实缴出资方式数据插入成功');
    }
  } catch (error) {
    console.error('初始化payment_methods表失败:', error);
  }
}

// 初始化agencies表
async function initAgenciesTable() {
  try {
    // 检查表是否存在
    const [tables] = await pool.query("SHOW TABLES LIKE 'agencies'");

    if (tables.length === 0) {
      // 创建新的agencies表
      await pool.query(`
        CREATE TABLE agencies (
          id INT AUTO_INCREMENT PRIMARY KEY,
          agency_name VARCHAR(100) NOT NULL COMMENT '代理机构名称',
          region VARCHAR(100) NOT NULL COMMENT '地区',
          contact_person VARCHAR(100) NOT NULL COMMENT '联系人姓名',
          contact_method VARCHAR(100) NOT NULL COMMENT '联系方式',
          create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
          update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='代理机构管理表'
      `);
      console.log('创建agencies表成功');
    } else {
      // 检查现有表结构并添加缺失的字段
      const [columns] = await pool.query('SHOW COLUMNS FROM agencies');
      const columnNames = columns.map(col => col.Field);

      if (!columnNames.includes('region')) {
        await pool.query('ALTER TABLE agencies ADD COLUMN region VARCHAR(100) DEFAULT "" COMMENT "地区"');
        console.log('添加region字段成功');
      }

      if (!columnNames.includes('contact_person')) {
        await pool.query('ALTER TABLE agencies ADD COLUMN contact_person VARCHAR(100) DEFAULT "" COMMENT "联系人姓名"');
        console.log('添加contact_person字段成功');
      }

      if (!columnNames.includes('contact_method')) {
        await pool.query('ALTER TABLE agencies ADD COLUMN contact_method VARCHAR(100) DEFAULT "" COMMENT "联系方式"');
        console.log('添加contact_method字段成功');
      }
    }

    // 更新现有数据，添加示例数据
    const [existingData] = await pool.query('SELECT * FROM agencies WHERE region != "" LIMIT 1');
    if (existingData.length === 0) {
      // 更新现有记录
      const sampleData = [
        { region: '广州', contactPerson: 'John Lee', contactMethod: '712154521' },
        { region: '亚太', contactPerson: 'Peter Parker', contactMethod: '712154522' },
        { region: '美国', contactPerson: 'Super He', contactMethod: '712154523' }
      ];

      const [agencies] = await pool.query('SELECT id FROM agencies LIMIT 3');
      for (let i = 0; i < Math.min(agencies.length, sampleData.length); i++) {
        await pool.query(
          'UPDATE agencies SET region = ?, contact_person = ?, contact_method = ? WHERE id = ?',
          [sampleData[i].region, sampleData[i].contactPerson, sampleData[i].contactMethod, agencies[i].id]
        );
      }
      console.log('更新现有代理机构数据成功');
    }
  } catch (error) {
    console.error('初始化agencies表失败:', error);
  }
}

// 初始化表
initAgenciesTable();

// 获取所有代理机构数据
app.get('/api/agencies', async (req, res) => {
  try {
    // 先检查表结构
    const [columns] = await pool.query('SHOW COLUMNS FROM agencies');
    const columnNames = columns.map(col => col.Field);

    let sql;
    if (columnNames.includes('agency_name')) {
      // 新表结构
      const timeField = columnNames.includes('create_time') ? 'create_time' : 'created_at';
      sql = `SELECT id, agency_name as agencyName, region, contact_person as contactPerson, contact_method as contactMethod, DATE_FORMAT(${timeField}, "%Y-%m-%d %H:%i:%s") as createTime FROM agencies ORDER BY ${timeField} DESC`;
    } else {
      // 旧表结构，使用现有字段
      const timeField = columnNames.includes('created_at') ? 'created_at' : 'create_time';
      sql = `SELECT id, name as agencyName, region, contact_person as contactPerson, contact_method as contactMethod, DATE_FORMAT(${timeField}, "%Y-%m-%d %H:%i:%s") as createTime FROM agencies ORDER BY ${timeField} DESC`;
    }

    const [rows] = await pool.query(sql);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取代理机构数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取代理机构数据失败'
    });
  }
});

// 搜索代理机构数据
app.get('/api/agencies/search', async (req, res) => {
  try {
    const { agencyName, region } = req.query;

    // 检查表结构
    const [columns] = await pool.query('SHOW COLUMNS FROM agencies');
    const columnNames = columns.map(col => col.Field);

    let sql, nameField, timeField;
    if (columnNames.includes('agency_name')) {
      nameField = 'agency_name';
      timeField = columnNames.includes('create_time') ? 'create_time' : 'created_at';
      sql = `SELECT id, agency_name as agencyName, region, contact_person as contactPerson, contact_method as contactMethod, DATE_FORMAT(${timeField}, "%Y-%m-%d %H:%i:%s") as createTime FROM agencies WHERE 1=1`;
    } else {
      nameField = 'name';
      timeField = columnNames.includes('created_at') ? 'created_at' : 'create_time';
      sql = `SELECT id, name as agencyName, region, contact_person as contactPerson, contact_method as contactMethod, DATE_FORMAT(${timeField}, "%Y-%m-%d %H:%i:%s") as createTime FROM agencies WHERE 1=1`;
    }

    const params = [];

    if (agencyName) {
      sql += ` AND ${nameField} LIKE ?`;
      params.push(`%${agencyName}%`);
    }

    if (region) {
      sql += ' AND region LIKE ?';
      params.push(`%${region}%`);
    }

    sql += ` ORDER BY ${timeField} DESC`;

    const [rows] = await pool.query(sql, params);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('搜索代理机构数据失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索代理机构数据失败'
    });
  }
});

// 新增代理机构数据
app.post('/api/agencies', async (req, res) => {
  try {
    const { agencyName, region, contactPerson, contactMethod } = req.body;

    if (!agencyName || !region || !contactPerson || !contactMethod) {
      return res.status(400).json({
        success: false,
        message: '所有字段都不能为空'
      });
    }

    // 检查表结构
    const [columns] = await pool.query('SHOW COLUMNS FROM agencies');
    const columnNames = columns.map(col => col.Field);

    let sql;
    if (columnNames.includes('agency_name')) {
      sql = 'INSERT INTO agencies (agency_name, region, contact_person, contact_method) VALUES (?, ?, ?, ?)';
    } else {
      sql = 'INSERT INTO agencies (name, region, contact_person, contact_method) VALUES (?, ?, ?, ?)';
    }

    // 插入新数据
    const [result] = await pool.query(sql, [agencyName, region, contactPerson, contactMethod]);

    res.json({
      success: true,
      data: {
        id: result.insertId,
        agencyName,
        region,
        contactPerson,
        contactMethod
      }
    });
  } catch (error) {
    console.error('新增代理机构数据失败:', error);
    res.status(500).json({
      success: false,
      message: '新增代理机构数据失败'
    });
  }
});

// 更新代理机构数据
app.put('/api/agencies/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { agencyName, region, contactPerson, contactMethod } = req.body;

    if (!agencyName || !region || !contactPerson || !contactMethod) {
      return res.status(400).json({
        success: false,
        message: '所有字段都不能为空'
      });
    }

    // 检查表结构
    const [columns] = await pool.query('SHOW COLUMNS FROM agencies');
    const columnNames = columns.map(col => col.Field);

    let sql;
    if (columnNames.includes('agency_name')) {
      sql = 'UPDATE agencies SET agency_name = ?, region = ?, contact_person = ?, contact_method = ? WHERE id = ?';
    } else {
      sql = 'UPDATE agencies SET name = ?, region = ?, contact_person = ?, contact_method = ? WHERE id = ?';
    }

    // 更新数据
    const [result] = await pool.query(sql, [agencyName, region, contactPerson, contactMethod, id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '代理机构数据不存在'
      });
    }

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新代理机构数据失败:', error);
    res.status(500).json({
      success: false,
      message: '更新代理机构数据失败'
    });
  }
});

// ==================== 年审状态管理 API ====================

// 获取年审状态列表
app.get('/api/annual-update-status', async (req, res) => {
  try {
    const [rows] = await pool.query(`
      SELECT
        id,
        name,
        description,
        is_active,
        created_at as createTime
      FROM annual_update_status
      WHERE is_active = 1
      ORDER BY id
    `);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取年审状态列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取年审状态列表失败'
    });
  }
});

// ==================== 存续状态管理 API ====================

// 获取存续状态列表
app.get('/api/operation-status', async (req, res) => {
  try {
    const [rows] = await pool.query(`
      SELECT
        id,
        name,
        description,
        is_active,
        created_at as createTime
      FROM operation_status
      WHERE is_active = 1
      ORDER BY id
    `);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取存续状态列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取存续状态列表失败'
    });
  }
});

// ==================== 任职职位管理 API ====================

// 获取所有任职职位数据
app.get('/api/positions', async (req, res) => {
  try {
    const [rows] = await pool.query(
      'SELECT id, name, sort, updater, DATE_FORMAT(update_time, "%Y/%m/%d %H:%i:%s") as updateTime FROM positions ORDER BY create_time DESC'
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取任职职位数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任职职位数据失败'
    });
  }
});

// 搜索任职职位数据
app.get('/api/positions/search', async (req, res) => {
  try {
    const { name } = req.query;

    let sql = 'SELECT id, name, sort, updater, DATE_FORMAT(update_time, "%Y/%m/%d %H:%i:%s") as updateTime FROM positions WHERE 1=1';
    const params = [];

    if (name) {
      sql += ' AND name LIKE ?';
      params.push(`%${name}%`);
    }

    sql += ' ORDER BY create_time DESC';

    const [rows] = await pool.query(sql, params);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('搜索任职职位数据失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索任职职位数据失败'
    });
  }
});

// 新增任职职位数据
app.post('/api/positions', async (req, res) => {
  try {
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '职位名称不能为空'
      });
    }

    // 检查是否重复
    const [existing] = await pool.query('SELECT id FROM positions WHERE name = ?', [name]);
    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该职位名称已存在'
      });
    }

    // 获取下一个排序号
    const [maxSort] = await pool.query('SELECT MAX(sort) as maxSort FROM positions');
    const nextSort = (maxSort[0].maxSort || 0) + 1;

    // 插入新数据
    const [result] = await pool.query(
      'INSERT INTO positions (name, sort, updater) VALUES (?, ?, ?)',
      [name, nextSort, 'Current User']
    );

    res.json({
      success: true,
      data: {
        id: result.insertId,
        name,
        sort: nextSort,
        updater: 'Current User'
      }
    });
  } catch (error) {
    console.error('新增任职职位数据失败:', error);
    res.status(500).json({
      success: false,
      message: '新增任职职位数据失败'
    });
  }
});

// 更新任职职位数据
app.put('/api/positions/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '职位名称不能为空'
      });
    }

    // 检查是否重复（排除自己）
    const [existing] = await pool.query('SELECT id FROM positions WHERE name = ? AND id != ?', [name, id]);
    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该职位名称已存在'
      });
    }

    // 更新数据
    const [result] = await pool.query(
      'UPDATE positions SET name = ?, updater = ? WHERE id = ?',
      [name, 'Current User', id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '任职职位数据不存在'
      });
    }

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新任职职位数据失败:', error);
    res.status(500).json({
      success: false,
      message: '更新任职职位数据失败'
    });
  }
});

// 删除任职职位数据
app.delete('/api/positions/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.query('DELETE FROM positions WHERE id = ?', [id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '任职职位数据不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除任职职位数据失败:', error);
    res.status(500).json({
      success: false,
      message: '删除任职职位数据失败'
    });
  }
});

// ==================== 实缴出资方式管理 API ====================

// 获取所有实缴出资方式数据
app.get('/api/payment-methods', async (req, res) => {
  try {
    const [rows] = await pool.query(
      'SELECT id, name, sort, updater, DATE_FORMAT(update_time, "%Y/%m/%d %H:%i:%s") as updateTime FROM payment_methods ORDER BY create_time DESC'
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取实缴出资方式数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取实缴出资方式数据失败'
    });
  }
});

// 搜索实缴出资方式数据
app.get('/api/payment-methods/search', async (req, res) => {
  try {
    const { name } = req.query;

    let sql = 'SELECT id, name, sort, updater, DATE_FORMAT(update_time, "%Y/%m/%d %H:%i:%s") as updateTime FROM payment_methods WHERE 1=1';
    const params = [];

    if (name) {
      sql += ' AND name LIKE ?';
      params.push(`%${name}%`);
    }

    sql += ' ORDER BY create_time DESC';

    const [rows] = await pool.query(sql, params);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('搜索实缴出资方式数据失败:', error);
    res.status(500).json({
      success: false,
      message: '搜索实缴出资方式数据失败'
    });
  }
});

// 新增实缴出资方式数据
app.post('/api/payment-methods', async (req, res) => {
  try {
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '实缴出资方式名称不能为空'
      });
    }

    // 检查是否重复
    const [existing] = await pool.query('SELECT id FROM payment_methods WHERE name = ?', [name]);
    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该实缴出资方式已存在'
      });
    }

    // 获取下一个排序号
    const [maxSort] = await pool.query('SELECT MAX(sort) as maxSort FROM payment_methods');
    const nextSort = (maxSort[0].maxSort || 0) + 1;

    // 插入新数据
    const [result] = await pool.query(
      'INSERT INTO payment_methods (name, sort, updater) VALUES (?, ?, ?)',
      [name, nextSort, 'Current User']
    );

    res.json({
      success: true,
      data: {
        id: result.insertId,
        name,
        sort: nextSort,
        updater: 'Current User'
      }
    });
  } catch (error) {
    console.error('新增实缴出资方式数据失败:', error);
    res.status(500).json({
      success: false,
      message: '新增实缴出资方式数据失败'
    });
  }
});

// 更新实缴出资方式数据
app.put('/api/payment-methods/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({
        success: false,
        message: '实缴出资方式名称不能为空'
      });
    }

    // 检查是否重复（排除自己）
    const [existing] = await pool.query('SELECT id FROM payment_methods WHERE name = ? AND id != ?', [name, id]);
    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该实缴出资方式已存在'
      });
    }

    // 更新数据
    const [result] = await pool.query(
      'UPDATE payment_methods SET name = ?, updater = ? WHERE id = ?',
      [name, 'Current User', id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '实缴出资方式数据不存在'
      });
    }

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新实缴出资方式数据失败:', error);
    res.status(500).json({
      success: false,
      message: '更新实缴出资方式数据失败'
    });
  }
});

// 删除实缴出资方式数据
app.delete('/api/payment-methods/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.query('DELETE FROM payment_methods WHERE id = ?', [id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '实缴出资方式数据不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除实缴出资方式数据失败:', error);
    res.status(500).json({
      success: false,
      message: '删除实缴出资方式数据失败'
    });
  }
});

// 删除代理机构数据
app.delete('/api/agencies/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.query(
      'DELETE FROM agencies WHERE id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '代理机构数据不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除代理机构数据失败:', error);
    res.status(500).json({
      success: false,
      message: '删除代理机构数据失败'
    });
  }
});

// 添加财务信息
app.post('/api/company/finance/add', async (req, res) => {
  console.log('收到添加财务信息请求:', req.body);

  // 使用事务确保数据一致性
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();
    console.log('财务信息事务开始');

    // 添加财务基本信息
    const [financeResult] = await connection.execute(
      `INSERT INTO company_finances (
        company_id, year, total_assets, total_liabilities, total_equity,
        business_income, main_business_income, profit_before_tax, net_profit, tax_payable, remarks
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        req.body.companyId,
        req.body.year,
        req.body.totalAssets || 0,
        req.body.totalLiabilities || 0,
        req.body.totalEquity || 0,
        req.body.businessIncome || 0,
        req.body.mainBusinessIncome || 0,
        req.body.profitBeforeTax || 0,
        req.body.netProfit || 0,
        req.body.taxPayable || 0,
        req.body.remarks || ''
      ]
    );

    console.log('财务基本信息添加成功, ID:', financeResult.insertId);

    // 添加股东及出资信息
    if (req.body.shareholders && req.body.shareholders.length > 0) {
      for (const shareholder of req.body.shareholders) {
        if (shareholder.shareholderName) { // 只保存有股东名称的记录
          await connection.execute(
            `INSERT INTO shareholder_contributions (
              finance_id, shareholder_name, contribution_amount, contribution_method, contribution_time
            ) VALUES (?, ?, ?, ?, ?)`,
            [
              financeResult.insertId,
              shareholder.shareholderName,
              shareholder.contributionAmount || 0,
              shareholder.contributionMethod || '',
              shareholder.contributionTime || null
            ]
          );
        }
      }
      console.log('股东出资信息添加成功');
    }

    // 提交事务
    await connection.commit();
    console.log('财务信息事务提交成功');

    res.json({
      success: true,
      message: '财务信息添加成功',
      financeId: financeResult.insertId
    });
  } catch (error) {
    // 回滚事务
    await connection.rollback();
    console.error('添加财务信息失败, 事务回滚:', error);

    res.status(500).json({
      success: false,
      message: '添加财务信息失败',
      error: error.message
    });
  } finally {
    connection.release();
    console.log('财务信息数据库连接已释放');
  }
});

// 初始化数据库表
async function initializeTables() {
  try {
    await initCompanyChangeLogsTable();
    console.log('✅ 所有数据库表初始化完成');
  } catch (error) {
    console.error('❌ 数据库表初始化失败:', error);
  }
}

// 启动服务器
async function startServer() {
  let server;
  try {
    // 初始化数据库表
    await initializeTables();

    server = app.listen(port, '0.0.0.0', () => {
      console.log(`服务器运行在 http://localhost:${port}`);
      console.log(`也可以通过 http://127.0.0.1:${port} 访问`);
      console.log(`特别提示：要测试MySQL数据库连接（端口3306），请访问 http://localhost:${port}/api/mysql-3306-test`);
    });

  // 添加服务器错误处理
  server.on('error', (error) => {
    if (error.code === 'EADDRINUSE') {
      console.error(`端口 ${port} 已被占用，请尝试使用其他端口`);
    } else {
      console.error('服务器启动错误:', error);
    }
  });
  } catch (error) {
    console.error('启动服务器时发生错误:', error);
  }
}

// 启动服务器
startServer();

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('未处理的Promise拒绝:', reason);
});

// 创建财务信息相关表
app.get('/api/create-finance-tables', async (req, res) => {
  try {
    console.log('开始创建财务信息相关表...');

    // 创建财务信息表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS company_finances (
        id INT AUTO_INCREMENT PRIMARY KEY,
        company_id INT NOT NULL,
        year VARCHAR(4) NOT NULL,
        total_assets DECIMAL(15,4) DEFAULT 0 COMMENT '资产总额（万元人民币）',
        total_liabilities DECIMAL(15,4) DEFAULT 0 COMMENT '负债总额（万元人民币）',
        total_equity DECIMAL(15,4) DEFAULT 0 COMMENT '所有者权益合计（万元人民币）',
        business_income DECIMAL(15,4) DEFAULT 0 COMMENT '营业总收入（万元人民币）',
        main_business_income DECIMAL(15,4) DEFAULT 0 COMMENT '主营业务收入（万元人民币）',
        profit_before_tax DECIMAL(15,4) DEFAULT 0 COMMENT '利润总额（万元人民币）',
        net_profit DECIMAL(15,4) DEFAULT 0 COMMENT '净利润（万元人民币）',
        tax_payable DECIMAL(15,4) DEFAULT 0 COMMENT '纳税总额（万元人民币）',
        remarks TEXT COMMENT '备注',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
        UNIQUE KEY unique_company_year (company_id, year)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司财务信息表'
    `);

    console.log('财务信息表创建成功');

    // 创建股东及出资信息表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS shareholder_contributions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        finance_id INT NOT NULL,
        shareholder_name VARCHAR(255) NOT NULL COMMENT '股东名称',
        contribution_amount DECIMAL(15,4) DEFAULT 0 COMMENT '实缴出资额（万元人民币）',
        contribution_method VARCHAR(100) DEFAULT '' COMMENT '实缴出资方式',
        contribution_time DATE NULL COMMENT '实缴出资时间',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (finance_id) REFERENCES company_finances(id) ON DELETE CASCADE
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股东及出资信息表'
    `);

    console.log('股东及出资信息表创建成功');

    // 创建索引（忽略已存在的索引错误）
    try {
      await pool.query('CREATE INDEX idx_company_finances_company_id ON company_finances(company_id)');
    } catch (e) { if (!e.message.includes('Duplicate key name')) throw e; }

    try {
      await pool.query('CREATE INDEX idx_company_finances_year ON company_finances(year)');
    } catch (e) { if (!e.message.includes('Duplicate key name')) throw e; }

    try {
      await pool.query('CREATE INDEX idx_shareholder_contributions_finance_id ON shareholder_contributions(finance_id)');
    } catch (e) { if (!e.message.includes('Duplicate key name')) throw e; }

    try {
      await pool.query('CREATE INDEX idx_shareholder_contributions_shareholder_name ON shareholder_contributions(shareholder_name)');
    } catch (e) { if (!e.message.includes('Duplicate key name')) throw e; }

    console.log('索引创建成功');

    res.json({ success: true, message: '财务信息相关表创建成功' });
  } catch (error) {
    console.error('创建财务信息表失败:', error);
    res.status(500).json({ success: false, message: '创建财务信息表失败', error: error.message });
  }
});

// 检查档案更新规范表结构
app.get('/api/check-archive-tables', async (req, res) => {
  try {
    console.log('检查档案更新规范表结构...');

    // 检查所有archive相关的表
    const [allTables] = await pool.query("SHOW TABLES LIKE 'archive%'");
    console.log('所有archive相关表:', allTables);

    // 检查表是否存在
    const [tables] = await pool.query("SHOW TABLES LIKE 'archive_update_rules'");
    console.log('表存在检查结果:', tables);

    if (tables.length > 0) {
      // 检查表结构
      const [columns] = await pool.query("DESCRIBE archive_update_rules");
      console.log('表结构:', columns);

      res.json({
        success: true,
        message: '表结构检查完成',
        tableExists: true,
        allArchiveTables: allTables,
        columns: columns
      });
    } else {
      res.json({
        success: true,
        message: '表不存在',
        tableExists: false,
        allArchiveTables: allTables,
        columns: []
      });
    }
  } catch (error) {
    console.error('检查表结构失败:', error);
    res.status(500).json({
      success: false,
      message: '检查表结构失败',
      error: error.message
    });
  }
});

// 修复档案更新规范表结构（添加缺少的字段）
app.get('/api/fix-archive-tables', async (req, res) => {
  try {
    console.log('开始修复档案更新规范表结构...');

    // 添加缺少的字段
    try {
      await pool.query('ALTER TABLE archive_update_rules ADD COLUMN update_type VARCHAR(100) NOT NULL DEFAULT ""');
      console.log('update_type字段添加成功');
    } catch (e) {
      if (e.message.includes('Duplicate column name')) {
        console.log('update_type字段已存在');
      } else {
        console.log('update_type字段添加失败:', e.message);
      }
    }

    try {
      await pool.query('ALTER TABLE archive_update_rules ADD COLUMN update_operation_name VARCHAR(255) NOT NULL DEFAULT ""');
      console.log('update_operation_name字段添加成功');
    } catch (e) {
      if (e.message.includes('Duplicate column name')) {
        console.log('update_operation_name字段已存在');
      } else {
        console.log('update_operation_name字段添加失败:', e.message);
      }
    }

    try {
      await pool.query('ALTER TABLE archive_update_rules ADD COLUMN change_steps JSON');
      console.log('change_steps字段添加成功');
    } catch (e) {
      if (e.message.includes('Duplicate column name')) {
        console.log('change_steps字段已存在');
      } else {
        console.log('change_steps字段添加失败:', e.message);
      }
    }

    try {
      await pool.query('ALTER TABLE archive_update_rules ADD COLUMN applicable_scope JSON');
      console.log('applicable_scope字段添加成功');
    } catch (e) {
      if (e.message.includes('Duplicate column name')) {
        console.log('applicable_scope字段已存在');
      } else {
        console.log('applicable_scope字段添加失败:', e.message);
      }
    }

    // 创建参考规范表（如果不存在）
    try {
      await pool.query(`
        CREATE TABLE IF NOT EXISTS archive_reference_rules (
          id INT AUTO_INCREMENT PRIMARY KEY,
          update_rule_id INT NOT NULL,
          content TEXT NOT NULL COMMENT '参考规范内容',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX idx_update_rule_id (update_rule_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案参考规范表'
      `);
      console.log('参考规范表创建成功');
    } catch (e) {
      console.log('参考规范表创建失败:', e.message);
    }

    // 创建上传文件表（如果不存在）
    try {
      await pool.query(`
        CREATE TABLE IF NOT EXISTS archive_upload_files (
          id INT AUTO_INCREMENT PRIMARY KEY,
          update_rule_id INT NOT NULL,
          file_name VARCHAR(255) NOT NULL COMMENT '文件名称',
          file_path VARCHAR(500) DEFAULT '' COMMENT '文件路径',
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          INDEX idx_update_rule_id (update_rule_id)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案上传文件表'
      `);
      console.log('上传文件表创建成功');
    } catch (e) {
      console.log('上传文件表创建失败:', e.message);
    }

    res.json({ success: true, message: '档案更新规范表结构修复成功' });
  } catch (error) {
    console.error('修复档案更新规范表失败:', error);
    res.status(500).json({ success: false, message: '修复档案更新规范表失败', error: error.message });
  }
});

// 创建档案更新规范相关表（保留旧版本兼容）
app.get('/api/create-archive-tables', async (req, res) => {
  // 重定向到重新创建API
  try {
    const response = await fetch('http://localhost:8080/api/recreate-archive-tables');
    const result = await response.json();
    res.json(result);
  } catch (error) {
    res.status(500).json({ success: false, message: '创建表失败', error: error.message });
  }
});

// 检查档案更新规范是否重复
app.post('/api/archive/update-rule/check-duplicate', async (req, res) => {
  try {
    const { updateType, updateOperationName } = req.body;

    const [rows] = await pool.query(
      'SELECT * FROM archive_update_rules WHERE update_type = ? AND update_operation_name = ?',
      [updateType, updateOperationName]
    );

    const duplicate = rows.length > 0;
    res.json({ duplicate });
  } catch (error) {
    console.error('检查档案更新规范重复失败:', error);
    res.status(500).json({ success: false, message: '检查档案更新规范重复失败', error: error.message });
  }
});

// 添加档案更新规范
app.post('/api/archive/update-rule/add', async (req, res) => {
  console.log('收到添加档案更新规范请求:', req.body);

  // 使用事务确保数据一致性
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();
    console.log('档案更新规范事务开始');

    // 添加档案更新规范基本信息
    const [ruleResult] = await connection.execute(
      `INSERT INTO archive_update_rules (
        update_type, update_operation_name, change_steps, applicable_scope, created_at
      ) VALUES (?, ?, ?, ?, NOW())`,
      [
        req.body.updateType,
        req.body.updateOperationName,
        JSON.stringify(req.body.changeSteps),
        JSON.stringify(req.body.applicableScope)
      ]
    );

    console.log('档案更新规范基本信息添加成功, ID:', ruleResult.insertId);

    // 添加参考规范
    if (req.body.referenceRules && req.body.referenceRules.length > 0) {
      for (const rule of req.body.referenceRules) {
        if (rule.content && rule.content.trim()) {
          await connection.execute(
            `INSERT INTO archive_reference_rules (
              update_rule_id, content
            ) VALUES (?, ?)`,
            [ruleResult.insertId, rule.content.trim()]
          );
        }
      }
      console.log('参考规范添加成功');
    }

    // 添加上传文件信息
    if (req.body.uploadFiles && req.body.uploadFiles.length > 0) {
      for (const file of req.body.uploadFiles) {
        if (file.name && file.name.trim()) {
          await connection.execute(
            `INSERT INTO archive_upload_files (
              update_rule_id, file_name, file_path
            ) VALUES (?, ?, ?)`,
            [ruleResult.insertId, file.name.trim(), '']
          );
        }
      }
      console.log('上传文件信息添加成功');
    }

    // 提交事务
    await connection.commit();
    console.log('档案更新规范事务提交成功');

    res.json({
      success: true,
      message: '档案更新规范添加成功',
      ruleId: ruleResult.insertId
    });
  } catch (error) {
    // 回滚事务
    await connection.rollback();
    console.error('添加档案更新规范失败, 事务回滚:', error);

    res.status(500).json({
      success: false,
      message: '添加档案更新规范失败',
      error: error.message
    });
  } finally {
    connection.release();
    console.log('档案更新规范数据库连接已释放');
  }
});

// 获取档案更新规范列表
app.get('/api/archive/update-rules', async (req, res) => {
  try {
    const { page = 1, pageSize = 10, updateOperationName, updateType, updateContent } = req.query;

    let whereClause = '';
    let params = [];

    if (updateOperationName) {
      whereClause += ' AND update_operation_name LIKE ?';
      params.push(`%${updateOperationName}%`);
    }

    if (updateType) {
      whereClause += ' AND update_type = ?';
      params.push(updateType);
    }

    if (updateContent) {
      whereClause += ' AND (update_operation_name LIKE ? OR update_type LIKE ?)';
      params.push(`%${updateContent}%`, `%${updateContent}%`);
    }

    // 获取总数
    const [countResult] = await pool.query(
      `SELECT COUNT(*) as total FROM archive_update_rules WHERE 1=1 ${whereClause}`,
      params
    );
    const total = countResult[0].total;

    // 获取分页数据
    const offset = (page - 1) * pageSize;
    const [rows] = await pool.query(
      `SELECT
        id, update_type, update_operation_name,
        'Admin User' as creator_name, created_at,
        'Admin User' as updater_name, updated_at
      FROM archive_update_rules
      WHERE 1=1 ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?`,
      [...params, parseInt(pageSize), offset]
    );

    res.json({ success: true, data: rows, total });
  } catch (error) {
    console.error('获取档案更新规范列表失败:', error);
    res.status(500).json({ success: false, message: '获取档案更新规范列表失败', error: error.message });
  }
});

// 删除档案更新规范
app.delete('/api/archive/update-rule/:id', async (req, res) => {
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();

    const ruleId = req.params.id;

    // 删除参考规范
    await connection.execute('DELETE FROM archive_reference_rules WHERE update_rule_id = ?', [ruleId]);

    // 删除上传文件
    await connection.execute('DELETE FROM archive_upload_files WHERE update_rule_id = ?', [ruleId]);

    // 删除主记录
    await connection.execute('DELETE FROM archive_update_rules WHERE id = ?', [ruleId]);

    await connection.commit();

    res.json({ success: true, message: '删除成功' });
  } catch (error) {
    await connection.rollback();
    console.error('删除档案更新规范失败:', error);
    res.status(500).json({ success: false, message: '删除失败', error: error.message });
  } finally {
    connection.release();
  }
});

// 获取单个档案更新规范
app.get('/api/archive/update-rule/:id', async (req, res) => {
  try {
    const ruleId = req.params.id;

    // 获取主记录
    const [ruleRows] = await pool.query(
      'SELECT * FROM archive_update_rules WHERE id = ?',
      [ruleId]
    );

    if (ruleRows.length === 0) {
      return res.status(404).json({ success: false, message: '档案更新规范不存在' });
    }

    const rule = ruleRows[0];

    // 获取参考规范
    const [referenceRows] = await pool.query(
      'SELECT * FROM archive_reference_rules WHERE update_rule_id = ?',
      [ruleId]
    );

    // 获取上传文件
    const [fileRows] = await pool.query(
      'SELECT * FROM archive_upload_files WHERE update_rule_id = ?',
      [ruleId]
    );

    const result = {
      ...rule,
      reference_rules: referenceRows,
      upload_files: fileRows
    };

    res.json({ success: true, data: result });
  } catch (error) {
    console.error('获取档案更新规范详情失败:', error);
    res.status(500).json({ success: false, message: '获取档案更新规范详情失败', error: error.message });
  }
});

// 更新档案更新规范
app.put('/api/archive/update-rule/:id', async (req, res) => {
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();
    console.log('档案更新规范更新事务开始');

    const ruleId = req.params.id;

    // 更新档案更新规范基本信息
    await connection.execute(
      `UPDATE archive_update_rules SET
        update_type = ?,
        update_operation_name = ?,
        change_steps = ?,
        applicable_scope = ?,
        updated_at = NOW()
      WHERE id = ?`,
      [
        req.body.updateType,
        req.body.updateOperationName,
        JSON.stringify(req.body.changeSteps),
        JSON.stringify(req.body.applicableScope),
        ruleId
      ]
    );

    console.log('档案更新规范基本信息更新成功, ID:', ruleId);

    // 删除旧的参考规范
    await connection.execute(
      'DELETE FROM archive_reference_rules WHERE update_rule_id = ?',
      [ruleId]
    );

    // 添加新的参考规范
    if (req.body.referenceRules && req.body.referenceRules.length > 0) {
      for (const rule of req.body.referenceRules) {
        if (rule.content && rule.content.trim()) {
          await connection.execute(
            `INSERT INTO archive_reference_rules (
              update_rule_id, content
            ) VALUES (?, ?)`,
            [ruleId, rule.content.trim()]
          );
        }
      }
      console.log('参考规范更新成功');
    }

    // 删除旧的上传文件信息
    await connection.execute(
      'DELETE FROM archive_upload_files WHERE update_rule_id = ?',
      [ruleId]
    );

    // 添加新的上传文件信息
    if (req.body.uploadFiles && req.body.uploadFiles.length > 0) {
      for (const file of req.body.uploadFiles) {
        if (file.name && file.name.trim()) {
          await connection.execute(
            `INSERT INTO archive_upload_files (
              update_rule_id, file_name, file_path
            ) VALUES (?, ?, ?)`,
            [ruleId, file.name.trim(), '']
          );
        }
      }
      console.log('上传文件信息更新成功');
    }

    // 提交事务
    await connection.commit();
    console.log('档案更新规范更新事务提交成功');

    res.json({
      success: true,
      message: '档案更新规范更新成功',
      ruleId: ruleId
    });
  } catch (error) {
    // 回滚事务
    await connection.rollback();
    console.error('更新档案更新规范失败, 事务回滚:', error);

    res.status(500).json({
      success: false,
      message: '更新档案更新规范失败',
      error: error.message
    });
  } finally {
    connection.release();
    console.log('档案更新规范数据库连接已释放');
  }
});

// ==================== 股东信息管理 API ====================

// 初始化股东信息表
async function initShareholdersTable() {
  try {
    // 创建股东实体表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS shareholder_entities (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(255) NOT NULL COMMENT '股东名称',
        type ENUM('individual', 'company', 'external') NOT NULL DEFAULT 'external' COMMENT '股东类型：个人、内部主体、外部投资主体',
        id_number VARCHAR(100) COMMENT '身份证号/统一社会信用代码',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        UNIQUE KEY unique_name_type (name, type)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股东实体表'
    `);

    // 创建投资记录表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS investment_records (
        id INT AUTO_INCREMENT PRIMARY KEY,
        shareholder_id INT NOT NULL COMMENT '股东ID',
        company_id INT COMMENT '公司ID',
        company_name VARCHAR(255) NOT NULL COMMENT '公司名称',
        investment_amount VARCHAR(100) COMMENT '投资金额',
        percentage VARCHAR(20) COMMENT '持股比例',
        start_date DATE COMMENT '开始日期',
        end_date DATE COMMENT '结束日期',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIGN KEY (shareholder_id) REFERENCES shareholder_entities(id) ON DELETE CASCADE,
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='投资记录表'
    `);

    console.log('股东信息表初始化成功');

    // 插入初始数据
    const [existingData] = await pool.query('SELECT COUNT(*) as count FROM shareholder_entities');
    if (existingData[0].count === 0) {
      const initialData = [
        ['苏州海投资中心', 'external'],
        ['张天成', 'individual'],
        ['张大威', 'individual'],
        ['张天威', 'individual']
      ];

      for (const [name, type] of initialData) {
        await pool.query(
          'INSERT INTO shareholder_entities (name, type) VALUES (?, ?)',
          [name, type]
        );
      }

      // 插入投资记录
      const [shareholders] = await pool.query('SELECT id, name FROM shareholder_entities');
      const [companies] = await pool.query('SELECT id, company_name_cn FROM companies LIMIT 3');

      if (companies.length > 0) {
        for (const shareholder of shareholders) {
          const company = companies[0]; // 使用第一个公司
          await pool.query(
            `INSERT INTO investment_records (
              shareholder_id, company_id, company_name, investment_amount, percentage, start_date
            ) VALUES (?, ?, ?, ?, ?, ?)`,
            [
              shareholder.id,
              company.id,
              company.company_name_cn + '(Shenzhen A Company)',
              '12%',
              '12%',
              '2023-11-10'
            ]
          );
        }
      }

      console.log('股东信息初始数据插入成功');
    }
  } catch (error) {
    console.error('初始化股东信息表失败:', error);
  }
}

// 初始化股东信息表
initShareholdersTable();

// 获取有投资记录的股东信息（包括个人股东和企业股东）
app.get('/api/shareholders', async (req, res) => {
  try {
    const { excludeCompanies } = req.query;
    console.log('🔄 获取股东数据，excludeCompanies:', excludeCompanies);

    // 1. 从shareholdings表获取个人股东信息
    const [individualShareholders] = await pool.query(`
      SELECT DISTINCT
        p.id,
        p.name,
        p.id_type as idType,
        p.id_number as idNumber,
        p.phone,
        p.email,
        DATE_FORMAT(p.created_at, "%Y-%m-%d %H:%i:%s") as createTime,
        'individual' as type,
        COUNT(s.id) as investmentCount,
        SUM(CAST(s.investment_amount AS DECIMAL(15,2))) as totalInvestment
      FROM persons p
      INNER JOIN shareholdings s ON p.id = s.person_id
      WHERE s.is_active = true
      GROUP BY p.id, p.name, p.id_type, p.id_number, p.phone, p.email, p.created_at
    `);

    // 2. 从investments表获取企业股东信息（投资方公司）
    let companyShareholders = [];
    if (excludeCompanies === 'true') {
      // 排除已在公司信息页面出现的企业（即作为被投资方的公司）
      const [result] = await pool.query(`
        SELECT DISTINCT
          c.id,
          c.company_name_cn as name,
          'company' as idType,
          c.company_code as idNumber,
          NULL as phone,
          NULL as email,
          DATE_FORMAT(c.created_at, "%Y-%m-%d %H:%i:%s") as createTime,
          'company' as type,
          COUNT(ir.id) as investmentCount,
          SUM(CAST(REPLACE(ir.investment_amount, '万元', '') AS DECIMAL(15,2))) as totalInvestment
        FROM companies c
        INNER JOIN investment_records ir ON c.id = ir.shareholder_id
        WHERE c.id NOT IN (
          SELECT DISTINCT company_id
          FROM investment_records
          WHERE company_id IS NOT NULL
        )
        GROUP BY c.id, c.company_name_cn, c.company_code, c.created_at
      `);
      companyShareholders = result;
      console.log('🔍 排除公司信息页面企业后，企业股东数量:', companyShareholders.length);
    } else {
      // 获取所有企业股东
      const [result] = await pool.query(`
        SELECT DISTINCT
          c.id,
          c.company_name_cn as name,
          'company' as idType,
          c.company_code as idNumber,
          NULL as phone,
          NULL as email,
          DATE_FORMAT(c.created_at, "%Y-%m-%d %H:%i:%s") as createTime,
          'company' as type,
          COUNT(ir.id) as investmentCount,
          SUM(CAST(REPLACE(ir.investment_amount, '万元', '') AS DECIMAL(15,2))) as totalInvestment
        FROM companies c
        INNER JOIN investment_records ir ON c.id = ir.shareholder_id
        GROUP BY c.id, c.company_name_cn, c.company_code, c.created_at
      `);
      companyShareholders = result;
    }

    // 3. 合并两种类型的股东数据
    const allShareholders = [
      ...individualShareholders,
      ...companyShareholders
    ].sort((a, b) => a.name.localeCompare(b.name));

    res.json({
      success: true,
      data: allShareholders
    });
  } catch (error) {
    console.error('获取股东数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取股东数据失败'
    });
  }
});



// 获取股东关系（替代原来的投资记录接口）
app.get('/api/investment-records', async (req, res) => {
  try {
    const [rows] = await pool.query(
      `SELECT
        s.id,
        s.person_id as shareholderId,
        p.name as shareholderName,
        s.company_id as companyId,
        c.company_name_cn as companyName,
        s.investment_amount as investmentAmount,
        s.percentage,
        s.is_proxy as isProxy,
        s.actual_shareholder_id as actualShareholderId,
        ap.name as actualShareholderName,
        s.start_date as startDate,
        s.end_date as endDate,
        s.is_active as isActive,
        DATE_FORMAT(s.created_at, "%Y-%m-%d %H:%i:%s") as createTime
      FROM shareholdings s
      LEFT JOIN persons p ON s.person_id = p.id
      LEFT JOIN companies c ON s.company_id = c.id
      LEFT JOIN persons ap ON s.actual_shareholder_id = ap.id
      WHERE s.is_active = true
      ORDER BY c.company_name_cn ASC, s.start_date ASC`
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取股东关系失败:', error);
    res.status(500).json({
      success: false,
      message: '获取股东关系失败'
    });
  }
});

// 新增获取股东关系API
app.get('/api/shareholdings', async (req, res) => {
  try {
    const [rows] = await pool.query(
      `SELECT
        s.id,
        s.person_id as personId,
        p.name as personName,
        s.company_id as companyId,
        c.company_name_cn as companyName,
        s.investment_amount as investmentAmount,
        s.percentage,
        s.is_proxy as isProxy,
        s.actual_shareholder_id as actualShareholderId,
        ap.name as actualShareholderName,
        s.start_date as startDate,
        s.end_date as endDate,
        s.is_active as isActive,
        DATE_FORMAT(s.created_at, "%Y-%m-%d %H:%i:%s") as createTime
      FROM shareholdings s
      LEFT JOIN persons p ON s.person_id = p.id
      LEFT JOIN companies c ON s.company_id = c.id
      LEFT JOIN persons ap ON s.actual_shareholder_id = ap.id
      WHERE s.is_active = true
      ORDER BY c.company_name_cn ASC, s.start_date ASC`
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取股东关系失败:', error);
    res.status(500).json({
      success: false,
      message: '获取股东关系失败'
    });
  }
});

// 获取特定股东的投资记录（支持个人股东和企业股东）
app.get('/api/shareholders/:id/investments', async (req, res) => {
  try {
    const { id } = req.params;

    // 首先判断这个ID是个人还是公司
    const [personCheck] = await pool.query('SELECT id FROM persons WHERE id = ?', [id]);
    const [companyCheck] = await pool.query('SELECT id FROM companies WHERE id = ?', [id]);

    let rows = [];

    if (personCheck.length > 0) {
      // 个人股东：从shareholdings表获取投资记录
      const [personalInvestments] = await pool.query(
        `SELECT
          s.id,
          s.person_id as shareholderId,
          p.name as shareholderName,
          s.company_id as companyId,
          c.company_name_cn as companyName,
          s.investment_amount as investmentAmount,
          s.percentage,
          s.is_proxy as isProxy,
          s.actual_shareholder_id as actualShareholderId,
          ap.name as actualShareholderName,
          s.start_date as startDate,
          s.end_date as endDate,
          s.is_active as isActive,
          DATE_FORMAT(s.created_at, "%Y-%m-%d %H:%i:%s") as createTime,
          'individual' as shareholderType
        FROM shareholdings s
        LEFT JOIN persons p ON s.person_id = p.id
        LEFT JOIN companies c ON s.company_id = c.id
        LEFT JOIN persons ap ON s.actual_shareholder_id = ap.id
        WHERE s.person_id = ? AND s.is_active = true
        ORDER BY c.company_name_cn ASC, s.start_date ASC`,
        [id]
      );
      rows = personalInvestments;
    } else if (companyCheck.length > 0) {
      // 企业股东：从investments表获取投资记录
      const [companyInvestments] = await pool.query(
        `SELECT
          i.id,
          i.investor_company_id as shareholderId,
          ic.company_name_cn as shareholderName,
          i.investee_company_id as companyId,
          i.investee_company_name as companyName,
          i.investment_amount as investmentAmount,
          i.percentage,
          false as isProxy,
          null as actualShareholderId,
          null as actualShareholderName,
          i.start_date as startDate,
          i.end_date as endDate,
          i.is_active as isActive,
          DATE_FORMAT(i.created_at, "%Y-%m-%d %H:%i:%s") as createTime,
          'company' as shareholderType
        FROM investments i
        LEFT JOIN companies ic ON i.investor_company_id = ic.id
        WHERE i.investor_company_id = ? AND i.is_active = true
        ORDER BY i.investee_company_name ASC, i.start_date ASC`,
        [id]
      );
      rows = companyInvestments;
    }

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取股东投资记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取股东投资记录失败'
    });
  }
});

// 获取投资记录（支持查询参数）
app.get('/api/investments', async (req, res) => {
  try {
    const { shareholderId } = req.query;

    let query = `
      SELECT
        s.id,
        s.person_id as shareholderId,
        p.name as shareholderName,
        s.company_id as companyId,
        c.company_name_cn as companyName,
        s.investment_amount as investmentAmount,
        s.percentage,
        s.is_proxy as isProxy,
        s.actual_shareholder_id as actualShareholderId,
        ap.name as actualShareholderName,
        s.start_date as startDate,
        s.end_date as endDate,
        s.is_active as isActive,
        DATE_FORMAT(s.created_at, "%Y-%m-%d %H:%i:%s") as createTime
      FROM shareholdings s
      LEFT JOIN persons p ON s.person_id = p.id
      LEFT JOIN companies c ON s.company_id = c.id
      LEFT JOIN persons ap ON s.actual_shareholder_id = ap.id
      WHERE s.is_active = true`;

    const params = [];

    if (shareholderId) {
      query += ' AND s.person_id = ?';
      params.push(shareholderId);
    }

    query += ' ORDER BY c.company_name_cn ASC, s.start_date ASC';

    const [rows] = await pool.query(query, params);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取投资记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取投资记录失败'
    });
  }
});

// ==================== 股权图数据 API ====================

// 获取股权图数据
app.get('/api/equity-chart/:companyId', async (req, res) => {
  try {
    const { companyId } = req.params;

    console.log('🔍 获取股权图数据, 公司ID:', companyId);

    // 获取中心公司信息
    const [centerCompany] = await pool.query(
      `SELECT id, company_name_cn as name FROM companies WHERE id = ?`,
      [companyId]
    );

    if (centerCompany.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公司不存在'
      });
    }

    // 获取直接股东关系（不递归）
    const getDirectShareholders = async (companyId) => {
      console.log(`🔍 获取公司 ${companyId} 的直接股东`);

      // 获取个人股东
      const [personalShareholders] = await pool.query(`
        SELECT
          s.id,
          s.person_id as shareholderId,
          p.name as shareholderName,
          'person' as shareholderType,
          s.company_id as companyId,
          c.company_name_cn as companyName,
          s.percentage,
          s.is_proxy as isProxy,
          s.actual_shareholder_id as actualShareholderId,
          ap.name as actualShareholderName
        FROM shareholdings s
        LEFT JOIN persons p ON s.person_id = p.id
        LEFT JOIN companies c ON s.company_id = c.id
        LEFT JOIN persons ap ON s.actual_shareholder_id = ap.id
        WHERE s.company_id = ? AND s.is_active = true AND s.end_date IS NULL
      `, [companyId]);

      console.log(`📊 找到 ${personalShareholders.length} 个个人股东`);

      // 获取企业股东（通过investments表）
      const [companyShareholders] = await pool.query(`
        SELECT
          i.id,
          i.investor_company_id as shareholderId,
          ic.company_name_cn as shareholderName,
          'company' as shareholderType,
          i.investee_company_id as companyId,
          ec.company_name_cn as companyName,
          i.percentage,
          false as isProxy,
          null as actualShareholderId,
          null as actualShareholderName
        FROM investments i
        LEFT JOIN companies ic ON i.investor_company_id = ic.id
        LEFT JOIN companies ec ON i.investee_company_id = ec.id
        WHERE i.investee_company_id = ? AND i.is_active = true AND i.end_date IS NULL
      `, [companyId]);

      console.log(`🏢 找到 ${companyShareholders.length} 个企业股东`);

      return [...personalShareholders, ...companyShareholders];
    };

    // 递归获取向上的完整股权链
    const getCompleteUpstreamChain = async (companyId, visited = new Set(), level = 0) => {
      if (visited.has(companyId)) return [];
      visited.add(companyId);

      console.log(`🔍 获取公司 ${companyId} 第${level}层的股东`);

      // 获取直接股东
      const directShareholders = await getDirectShareholders(companyId);
      let allShareholders = directShareholders.map(s => ({ ...s, level }));

      console.log(`📊 第${level}层找到 ${directShareholders.length} 个直接股东`);

      // 处理代持关系 - 展开实际股东的股权链
      for (const shareholder of directShareholders) {
        if (shareholder.isProxy && shareholder.actualShareholderId) {
          console.log(`🔍 发现代持关系: ${shareholder.shareholderName} 代持 ${shareholder.actualShareholderName} 的股份`);

          // 查找实际股东持有的其他公司股份
          const [actualShareholderHoldings] = await pool.query(`
            SELECT
              s.id,
              s.person_id as shareholderId,
              p.name as shareholderName,
              'person' as shareholderType,
              s.company_id as companyId,
              c.company_name_cn as companyName,
              s.percentage,
              s.is_proxy as isProxy,
              s.actual_shareholder_id as actualShareholderId,
              ap.name as actualShareholderName
            FROM shareholdings s
            LEFT JOIN persons p ON s.person_id = p.id
            LEFT JOIN companies c ON s.company_id = c.id
            LEFT JOIN persons ap ON s.actual_shareholder_id = ap.id
            WHERE s.person_id = ? AND s.is_active = true AND s.end_date IS NULL AND s.company_id != ?
          `, [shareholder.actualShareholderId, companyId]);

          // 将实际股东的其他持股关系添加到结果中
          const actualHoldings = actualShareholderHoldings.map(h => ({ ...h, level: level + 1 }));
          allShareholders = allShareholders.concat(actualHoldings);

          // 递归查找实际股东持股公司的上级股东
          for (const holding of actualShareholderHoldings) {
            if (holding.companyId && !visited.has(holding.companyId)) {
              const upstreamOfHolding = await getCompleteUpstreamChain(holding.companyId, visited, level + 2);
              allShareholders = allShareholders.concat(upstreamOfHolding);
            }
          }
        }
      }

      // 对所有企业股东进行递归展开
      for (const shareholder of directShareholders) {
        if (shareholder.shareholderType === 'company') {
          console.log(`🏢 递归展开企业股东: ${shareholder.shareholderName} (ID: ${shareholder.shareholderId})`);
          const upstreamShareholders = await getCompleteUpstreamChain(
            shareholder.shareholderId,
            visited,
            level + 1
          );
          allShareholders = allShareholders.concat(upstreamShareholders);
        }
      }

      return allShareholders;
    };

    // 递归获取向下的完整投资链
    const getCompleteDownstreamChain = async (companyId, visited = new Set(), level = 0) => {
      if (visited.has(companyId)) return [];
      visited.add(companyId);

      console.log(`🔍 获取公司 ${companyId} 第${level}层的投资`);

      const [investments] = await pool.query(`
        SELECT
          i.id,
          i.investor_company_id as investorId,
          ic.company_name_cn as investorName,
          i.investee_company_id as investeeId,
          i.investee_company_name as investeeName,
          i.percentage
        FROM investments i
        LEFT JOIN companies ic ON i.investor_company_id = ic.id
        WHERE i.investor_company_id = ? AND i.is_active = true AND i.end_date IS NULL
      `, [companyId]);

      let allInvestments = investments.map(inv => ({ ...inv, level }));

      console.log(`📊 第${level}层找到 ${investments.length} 个投资关系`);

      // 递归获取每个被投资公司的下级投资
      for (const investment of investments) {
        if (investment.investeeId) {
          console.log(`🏢 递归展开被投资公司: ${investment.investeeName} (ID: ${investment.investeeId})`);
          const downstreamInvestments = await getCompleteDownstreamChain(
            investment.investeeId,
            visited,
            level + 1
          );
          allInvestments = allInvestments.concat(downstreamInvestments);
        }
      }

      return allInvestments;
    };

    // 获取所有相关公司列表（用于法务版筛选）
    const getAllRelatedCompanies = async (companyId) => {
      const upstreamShareholders = await getCompleteUpstreamChain(companyId);
      const downstreamInvestments = await getCompleteDownstreamChain(companyId);

      const companies = new Set();
      companies.add(companyId);

      // 添加上游企业股东
      upstreamShareholders.forEach(s => {
        if (s.shareholderType === 'company') {
          companies.add(s.shareholderId);
        }
        companies.add(s.companyId);
      });

      // 添加下游投资企业
      downstreamInvestments.forEach(i => {
        companies.add(i.investorId);
        if (i.investeeId) {
          companies.add(i.investeeId);
        }
      });

      // 获取公司名称
      const companyIds = Array.from(companies);
      if (companyIds.length === 0) return [];

      const [companyList] = await pool.query(`
        SELECT id, company_name_cn as name
        FROM companies
        WHERE id IN (${companyIds.map(() => '?').join(',')})
      `, companyIds);

      return companyList;
    };

    // 获取完整的股权链数据
    console.log(`🚀 开始获取公司 ${companyId} 的完整股权链`);
    const upstreamShareholders = await getCompleteUpstreamChain(parseInt(companyId));
    const downstreamInvestments = await getCompleteDownstreamChain(parseInt(companyId));
    const relatedCompanies = await getAllRelatedCompanies(parseInt(companyId));

    console.log(`📊 完整股权链统计:`);
    console.log(`   - 上游股东: ${upstreamShareholders.length} 个`);
    console.log(`   - 下游投资: ${downstreamInvestments.length} 个`);
    console.log(`   - 相关公司: ${relatedCompanies.length} 个`);

    res.json({
      success: true,
      data: {
        centerCompany: centerCompany[0],
        upstreamShareholders,
        downstreamInvestments,
        relatedCompanies
      }
    });

  } catch (error) {
    console.error('获取股权图数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取股权图数据失败',
      error: error.message
    });
  }
});

// 获取多个公司的股权图数据（法务版）
app.post('/api/equity-chart/multiple', async (req, res) => {
  try {
    const { companyIds } = req.body;

    if (!companyIds || !Array.isArray(companyIds) || companyIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '公司ID列表不能为空'
      });
    }

    console.log('🔍 获取多个公司股权图数据, 公司IDs:', companyIds);

    // 获取所有中心公司信息
    const [centerCompanies] = await pool.query(
      `SELECT id, company_name_cn as name FROM companies WHERE id IN (${companyIds.map(() => '?').join(',')})`,
      companyIds
    );

    if (centerCompanies.length === 0) {
      return res.status(404).json({
        success: false,
        message: '公司不存在'
      });
    }

    // 递归获取向上的股东关系
    const getUpstreamShareholders = async (companyId, visited = new Set()) => {
      if (visited.has(companyId)) return [];
      visited.add(companyId);

      // 获取个人股东
      const [personalShareholders] = await pool.query(`
        SELECT
          s.id,
          s.person_id as shareholderId,
          p.name as shareholderName,
          'person' as shareholderType,
          s.company_id as companyId,
          c.company_name_cn as companyName,
          s.percentage,
          s.is_proxy as isProxy,
          s.actual_shareholder_id as actualShareholderId,
          ap.name as actualShareholderName
        FROM shareholdings s
        LEFT JOIN persons p ON s.person_id = p.id
        LEFT JOIN companies c ON s.company_id = c.id
        LEFT JOIN persons ap ON s.actual_shareholder_id = ap.id
        WHERE s.company_id = ? AND s.is_active = true AND s.end_date IS NULL
      `, [companyId]);

      // 获取企业股东（通过investments表）
      const [companyShareholders] = await pool.query(`
        SELECT
          i.id,
          i.investor_company_id as shareholderId,
          ic.company_name_cn as shareholderName,
          'company' as shareholderType,
          i.investee_company_id as companyId,
          ec.company_name_cn as companyName,
          i.percentage,
          false as isProxy,
          null as actualShareholderId,
          null as actualShareholderName
        FROM investments i
        LEFT JOIN companies ic ON i.investor_company_id = ic.id
        LEFT JOIN companies ec ON i.investee_company_id = ec.id
        WHERE i.investee_company_id = ? AND i.is_active = true AND i.end_date IS NULL
      `, [companyId]);

      let allShareholders = [...personalShareholders, ...companyShareholders];

      // 对于代持关系，继续向上查找实际股东的股权关系
      for (const shareholder of personalShareholders) {
        if (shareholder.isProxy && shareholder.actualShareholderId) {
          console.log(`🔍 发现代持关系: ${shareholder.shareholderName} 代持 ${shareholder.actualShareholderName} 的股份`);

          // 查找实际股东持有的其他公司股份
          const [actualShareholderHoldings] = await pool.query(`
            SELECT
              s.id,
              s.person_id as shareholderId,
              p.name as shareholderName,
              'person' as shareholderType,
              s.company_id as companyId,
              c.company_name_cn as companyName,
              s.percentage,
              s.is_proxy as isProxy,
              s.actual_shareholder_id as actualShareholderId,
              ap.name as actualShareholderName
            FROM shareholdings s
            LEFT JOIN persons p ON s.person_id = p.id
            LEFT JOIN companies c ON s.company_id = c.id
            LEFT JOIN persons ap ON s.actual_shareholder_id = ap.id
            WHERE s.person_id = ? AND s.is_active = true AND s.end_date IS NULL AND s.company_id != ?
          `, [shareholder.actualShareholderId, companyId]);

          console.log(`📊 实际股东 ${shareholder.actualShareholderName} 还持有 ${actualShareholderHoldings.length} 个其他公司的股份`);

          // 将实际股东的其他持股关系添加到结果中
          allShareholders = allShareholders.concat(actualShareholderHoldings);

          // 递归查找实际股东持股公司的上级股东
          for (const holding of actualShareholderHoldings) {
            if (!visited.has(holding.companyId)) {
              const upstreamOfHolding = await getUpstreamShareholders(holding.companyId, visited);
              allShareholders = allShareholders.concat(upstreamOfHolding);
            }
          }
        }
      }

      // 递归获取企业股东的上级股东
      for (const shareholder of companyShareholders) {
        const upstreamShareholders = await getUpstreamShareholders(shareholder.shareholderId, visited);
        allShareholders = allShareholders.concat(upstreamShareholders);
      }

      return allShareholders;
    };

    // 递归获取向下的投资关系
    const getDownstreamInvestments = async (companyId, visited = new Set()) => {
      if (visited.has(companyId)) return [];
      visited.add(companyId);

      const [investments] = await pool.query(`
        SELECT
          i.id,
          i.investor_company_id as investorId,
          ic.company_name_cn as investorName,
          i.investee_company_id as investeeId,
          i.investee_company_name as investeeName,
          i.percentage
        FROM investments i
        LEFT JOIN companies ic ON i.investor_company_id = ic.id
        WHERE i.investor_company_id = ? AND i.is_active = true AND i.end_date IS NULL
      `, [companyId]);

      let allInvestments = [...investments];

      // 递归获取下级公司的投资
      for (const investment of investments) {
        if (investment.investeeId) {
          const downstreamInvestments = await getDownstreamInvestments(investment.investeeId, visited);
          allInvestments = allInvestments.concat(downstreamInvestments);
        }
      }

      return allInvestments;
    };

    // 合并所有公司的股权数据
    let allUpstreamShareholders = [];
    let allDownstreamInvestments = [];
    const allRelatedCompanies = new Set();

    for (const companyId of companyIds) {
      const upstreamShareholders = await getUpstreamShareholders(parseInt(companyId));
      const downstreamInvestments = await getDownstreamInvestments(parseInt(companyId));

      allUpstreamShareholders = allUpstreamShareholders.concat(upstreamShareholders);
      allDownstreamInvestments = allDownstreamInvestments.concat(downstreamInvestments);

      // 添加相关公司
      allRelatedCompanies.add(companyId);
      upstreamShareholders.forEach(s => {
        if (s.shareholderType === 'company') {
          allRelatedCompanies.add(s.shareholderId);
        }
        allRelatedCompanies.add(s.companyId);
      });
      downstreamInvestments.forEach(i => {
        allRelatedCompanies.add(i.investorId);
        if (i.investeeId) {
          allRelatedCompanies.add(i.investeeId);
        }
      });
    }

    // 去重处理
    const uniqueUpstreamShareholders = allUpstreamShareholders.filter((item, index, self) =>
      index === self.findIndex(t => t.id === item.id)
    );

    const uniqueDownstreamInvestments = allDownstreamInvestments.filter((item, index, self) =>
      index === self.findIndex(t => t.id === item.id)
    );

    // 获取相关公司名称
    const companyIds_array = Array.from(allRelatedCompanies);
    const [relatedCompanies] = await pool.query(`
      SELECT id, company_name_cn as name
      FROM companies
      WHERE id IN (${companyIds_array.map(() => '?').join(',')})
    `, companyIds_array);

    res.json({
      success: true,
      data: {
        centerCompanies,
        upstreamShareholders: uniqueUpstreamShareholders,
        downstreamInvestments: uniqueDownstreamInvestments,
        relatedCompanies
      }
    });

  } catch (error) {
    console.error('获取多个公司股权图数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取多个公司股权图数据失败',
      error: error.message
    });
  }
});

// 数据清理API - 删除重复的股权记录
app.post('/api/equity-chart/cleanup-duplicates', async (req, res) => {
  try {
    console.log('🧹 开始清理重复的股权数据...');

    // 清理shareholdings表中的重复记录
    const [shareholdingDuplicates] = await pool.query(`
      SELECT person_id, company_id, COUNT(*) as count
      FROM shareholdings
      WHERE is_active = true AND end_date IS NULL
      GROUP BY person_id, company_id
      HAVING COUNT(*) > 1
    `);

    let shareholdingCleaned = 0;
    for (const duplicate of shareholdingDuplicates) {
      // 保留最新的记录，删除其他的
      const [records] = await pool.query(`
        SELECT id FROM shareholdings
        WHERE person_id = ? AND company_id = ? AND is_active = true AND end_date IS NULL
        ORDER BY created_at DESC
      `, [duplicate.person_id, duplicate.company_id]);

      if (records.length > 1) {
        const keepId = records[0].id;
        const deleteIds = records.slice(1).map(r => r.id);

        await pool.query(`
          UPDATE shareholdings
          SET is_active = false, updated_at = NOW()
          WHERE id IN (${deleteIds.map(() => '?').join(',')})
        `, deleteIds);

        shareholdingCleaned += deleteIds.length;
        console.log(`✅ 清理股东关系重复记录: person_id=${duplicate.person_id}, company_id=${duplicate.company_id}, 删除${deleteIds.length}条`);
      }
    }

    // 清理investments表中的重复记录
    const [investmentDuplicates] = await pool.query(`
      SELECT investor_company_id, investee_company_id, COUNT(*) as count
      FROM investments
      WHERE is_active = true AND end_date IS NULL AND investee_company_id IS NOT NULL
      GROUP BY investor_company_id, investee_company_id
      HAVING COUNT(*) > 1
    `);

    let investmentCleaned = 0;
    for (const duplicate of investmentDuplicates) {
      // 保留最新的记录，删除其他的
      const [records] = await pool.query(`
        SELECT id FROM investments
        WHERE investor_company_id = ? AND investee_company_id = ? AND is_active = true AND end_date IS NULL
        ORDER BY created_at DESC
      `, [duplicate.investor_company_id, duplicate.investee_company_id]);

      if (records.length > 1) {
        const keepId = records[0].id;
        const deleteIds = records.slice(1).map(r => r.id);

        await pool.query(`
          UPDATE investments
          SET is_active = false, updated_at = NOW()
          WHERE id IN (${deleteIds.map(() => '?').join(',')})
        `, deleteIds);

        investmentCleaned += deleteIds.length;
        console.log(`✅ 清理投资关系重复记录: investor=${duplicate.investor_company_id}, investee=${duplicate.investee_company_id}, 删除${deleteIds.length}条`);
      }
    }

    res.json({
      success: true,
      message: '数据清理完成',
      data: {
        shareholdingCleaned,
        investmentCleaned,
        totalCleaned: shareholdingCleaned + investmentCleaned
      }
    });

  } catch (error) {
    console.error('数据清理失败:', error);
    res.status(500).json({
      success: false,
      message: '数据清理失败',
      error: error.message
    });
  }
});

// 新增人员（替代原来的新增股东接口）
app.post('/api/shareholders', async (req, res) => {
  try {
    const { name, idType = '身份证', idNumber } = req.body;

    if (!name || !idNumber) {
      return res.status(400).json({
        success: false,
        message: '姓名和证件号码不能为空'
      });
    }

    // 检查证件号码是否已存在
    const [existing] = await pool.query(
      'SELECT id FROM persons WHERE id_number = ?',
      [idNumber]
    );

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该证件号码已存在'
      });
    }

    // 插入新人员
    const [result] = await pool.query(
      'INSERT INTO persons (name, id_type, id_number, created_by) VALUES (?, ?, ?, 1)',
      [name, idType, idNumber]
    );

    res.json({
      success: true,
      data: {
        id: result.insertId,
        name,
        idType,
        idNumber
      }
    });
  } catch (error) {
    console.error('新增人员失败:', error);
    res.status(500).json({
      success: false,
      message: '新增人员失败'
    });
  }
});

// 新增人员API
app.post('/api/persons', async (req, res) => {
  try {
    const { name, idType = '身份证', idNumber, phone, email } = req.body;

    if (!name || !idNumber) {
      return res.status(400).json({
        success: false,
        message: '姓名和证件号码不能为空'
      });
    }

    // 检查证件号码是否已存在
    const [existing] = await pool.query(
      'SELECT id FROM persons WHERE id_number = ?',
      [idNumber]
    );

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该证件号码已存在'
      });
    }

    // 插入新人员
    const [result] = await pool.query(
      'INSERT INTO persons (name, id_type, id_number, phone, email, created_by) VALUES (?, ?, ?, ?, ?, 1)',
      [name, idType, idNumber, phone, email]
    );

    res.json({
      success: true,
      data: {
        id: result.insertId,
        name,
        idType,
        idNumber,
        phone,
        email
      }
    });
  } catch (error) {
    console.error('新增人员失败:', error);
    res.status(500).json({
      success: false,
      message: '新增人员失败'
    });
  }
});

// 更新人员（替代原来的更新股东接口）
app.put('/api/shareholders/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, idType, idNumber, phone, email } = req.body;

    if (!name || !idNumber) {
      return res.status(400).json({
        success: false,
        message: '姓名和证件号码不能为空'
      });
    }

    // 检查证件号码是否已被其他人使用
    const [existing] = await pool.query(
      'SELECT id FROM persons WHERE id_number = ? AND id != ?',
      [idNumber, id]
    );

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该证件号码已被其他人使用'
      });
    }

    // 更新人员
    const [result] = await pool.query(
      'UPDATE persons SET name = ?, id_type = ?, id_number = ?, phone = ?, email = ?, updated_by = 1 WHERE id = ?',
      [name, idType, idNumber, phone, email, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '人员不存在'
      });
    }

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新人员失败:', error);
    res.status(500).json({
      success: false,
      message: '更新人员失败'
    });
  }
});

// 更新人员API
app.put('/api/persons/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { name, idType, idNumber, phone, email } = req.body;

    if (!name || !idNumber) {
      return res.status(400).json({
        success: false,
        message: '姓名和证件号码不能为空'
      });
    }

    // 检查证件号码是否已被其他人使用
    const [existing] = await pool.query(
      'SELECT id FROM persons WHERE id_number = ? AND id != ?',
      [idNumber, id]
    );

    if (existing.length > 0) {
      return res.status(400).json({
        success: false,
        message: '该证件号码已被其他人使用'
      });
    }

    // 更新人员
    const [result] = await pool.query(
      'UPDATE persons SET name = ?, id_type = ?, id_number = ?, phone = ?, email = ?, updated_by = 1 WHERE id = ?',
      [name, idType, idNumber, phone, email, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '人员不存在'
      });
    }

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新人员失败:', error);
    res.status(500).json({
      success: false,
      message: '更新人员失败'
    });
  }
});

// 删除人员（替代原来的删除股东接口）
app.delete('/api/shareholders/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.query(
      'DELETE FROM persons WHERE id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '人员不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除人员失败:', error);
    res.status(500).json({
      success: false,
      message: '删除人员失败'
    });
  }
});

// 删除人员API
app.delete('/api/persons/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.query(
      'DELETE FROM persons WHERE id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '人员不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除人员失败:', error);
    res.status(500).json({
      success: false,
      message: '删除人员失败'
    });
  }
});

// ==================== 任职档案管理 API ====================

// 获取任职记录
app.get('/api/employments', async (req, res) => {
  try {
    const [rows] = await pool.query(
      `SELECT
        e.id,
        e.person_id as personId,
        p.name as personName,
        e.company_id as companyId,
        c.company_name_cn as companyName,
        e.position,
        e.start_date as startDate,
        e.end_date as endDate,
        e.is_active as isActive,
        DATE_FORMAT(e.created_at, "%Y-%m-%d %H:%i:%s") as createTime
      FROM employments e
      LEFT JOIN persons p ON e.person_id = p.id
      LEFT JOIN companies c ON e.company_id = c.id
      WHERE e.is_active = true
      ORDER BY c.company_name_cn ASC, e.start_date ASC`
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取任职记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任职记录失败'
    });
  }
});

// 获取任职档案 - 只显示个人（非公司实体）
app.get('/api/employment-records', async (req, res) => {
  try {
    const { includeHistory } = req.query;
    console.log('🔄 获取任职记录，includeHistory:', includeHistory);

    // 构建WHERE条件
    let whereCondition = 'p.name IS NOT NULL';
    if (includeHistory !== 'true') {
      whereCondition += ' AND e.is_active = true';
    }

    const [rows] = await pool.query(`
      SELECT
        e.id,
        e.person_id as personId,
        p.name as personName,
        p.id_type as idType,
        p.id_number as idNumber,
        CONCAT(
          REPEAT('*', GREATEST(0, LENGTH(p.id_number) - 4)),
          RIGHT(p.id_number, 4)
        ) as maskedIdNumber,
        e.company_id as companyId,
        c.company_name_cn as companyName,
        e.position,
        DATE_FORMAT(e.start_date, "%Y-%m-%d") as startDate,
        DATE_FORMAT(e.end_date, "%Y-%m-%d") as endDate,
        e.is_active as isActive,
        DATE_FORMAT(e.created_at, "%Y-%m-%d %H:%i:%s") as createTime,
        CASE
          WHEN e.is_active = true THEN '在职'
          ELSE '离职'
        END as status
      FROM employments e
      LEFT JOIN persons p ON e.person_id = p.id
      LEFT JOIN companies c ON e.company_id = c.id
      WHERE ${whereCondition}
      ORDER BY p.name ASC, e.is_active DESC, e.start_date DESC
    `);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取任职记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任职记录失败'
    });
  }
});

// 获取个人列表（用于任职档案页面）- 只返回有任职记录的个人，去重
app.get('/api/persons', async (req, res) => {
  try {
    const [rows] = await pool.query(`
      SELECT
        p.id,
        p.name,
        p.id_type as idType,
        p.id_number as idNumber,
        CONCAT(
          REPEAT('*', GREATEST(0, LENGTH(p.id_number) - 4)),
          RIGHT(p.id_number, 4)
        ) as maskedIdNumber,
        p.phone,
        p.email,
        DATE_FORMAT(p.created_at, "%Y-%m-%d %H:%i:%s") as createTime,
        COUNT(e.id) as employmentCount
      FROM persons p
      INNER JOIN employments e ON p.id = e.person_id
      WHERE e.is_active = true
        AND p.name IS NOT NULL
      GROUP BY p.id, p.name, p.id_type, p.id_number, p.phone, p.email, p.created_at
      HAVING COUNT(e.id) > 0
      ORDER BY p.name ASC
    `);

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取个人列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取个人列表失败'
    });
  }
});

// 获取所有人员列表（用于新增公司等场景）- 返回所有人员，不限制是否有任职记录
app.get('/api/persons/all', async (req, res) => {
  try {
    const [rows] = await pool.query(`
      SELECT DISTINCT p.name
      FROM persons p
      WHERE p.name IS NOT NULL
        AND p.name != ''
      ORDER BY p.name ASC
    `);

    // 转换为前端期望的格式
    const formattedData = rows.map(row => ({
      name: row.name
    }));

    res.json({
      success: true,
      data: formattedData
    });
  } catch (error) {
    console.error('获取所有人员数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取所有人员数据失败'
    });
  }
});

// 查看persons表数据（用于调试）
app.get('/api/debug/persons', async (req, res) => {
  try {
    const [rows] = await pool.query(`
      SELECT
        id,
        name,
        id_type,
        id_number,
        created_at
      FROM persons
      ORDER BY created_at DESC
    `);

    res.json({
      success: true,
      data: rows,
      count: rows.length
    });
  } catch (error) {
    console.error('获取persons表数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取persons表数据失败'
    });
  }
});

// 清理persons表中的公司数据
app.post('/api/debug/clean-persons', async (req, res) => {
  try {
    console.log('开始清理persons表中的公司数据...');

    // 查找包含公司名称的记录
    const [companyRecords] = await pool.query(`
      SELECT id, name FROM persons
      WHERE name LIKE '%有限公司%'
         OR name LIKE '%股份有限公司%'
         OR name LIKE '%集团%'
         OR name LIKE '%中心%'
         OR name LIKE '%投资%'
         OR name LIKE '%苏州海%'
         OR LENGTH(name) > 10
    `);

    console.log('找到需要删除的公司记录:', companyRecords.length, '条');
    companyRecords.forEach(record => {
      console.log('- ID:', record.id, '名称:', record.name);
    });

    if (companyRecords.length > 0) {
      // 删除这些记录
      const companyIds = companyRecords.map(r => r.id);
      const placeholders = companyIds.map(() => '?').join(',');

      const [result] = await pool.query(
        `DELETE FROM persons WHERE id IN (${placeholders})`,
        companyIds
      );

      console.log('删除结果:', result.affectedRows, '条记录被删除');

      res.json({
        success: true,
        message: `成功删除 ${result.affectedRows} 条公司记录`,
        deletedRecords: companyRecords,
        affectedRows: result.affectedRows
      });
    } else {
      res.json({
        success: true,
        message: '没有找到需要删除的公司记录',
        deletedRecords: [],
        affectedRows: 0
      });
    }
  } catch (error) {
    console.error('清理persons表失败:', error);
    res.status(500).json({
      success: false,
      message: '清理persons表失败',
      error: error.message
    });
  }
});

// 新增任职记录
app.post('/api/employments', async (req, res) => {
  try {
    const { personId, companyId, position, startDate, endDate } = req.body;

    if (!personId || !companyId || !position) {
      return res.status(400).json({
        success: false,
        message: '人员、公司和职位不能为空'
      });
    }

    // 插入新任职记录
    const [result] = await pool.query(
      'INSERT INTO employments (person_id, company_id, position, start_date, end_date, created_by) VALUES (?, ?, ?, ?, ?, 1)',
      [personId, companyId, position, startDate, endDate]
    );

    res.json({
      success: true,
      data: {
        id: result.insertId,
        personId,
        companyId,
        position,
        startDate,
        endDate
      }
    });
  } catch (error) {
    console.error('新增任职记录失败:', error);
    res.status(500).json({
      success: false,
      message: '新增任职记录失败'
    });
  }
});

// 更新任职记录
app.put('/api/employments/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const { personId, companyId, position, startDate, endDate } = req.body;

    if (!personId || !companyId || !position) {
      return res.status(400).json({
        success: false,
        message: '人员、公司和职位不能为空'
      });
    }

    // 更新任职记录
    const [result] = await pool.query(
      'UPDATE employments SET person_id = ?, company_id = ?, position = ?, start_date = ?, end_date = ?, updated_by = 1 WHERE id = ?',
      [personId, companyId, position, startDate, endDate, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '任职记录不存在'
      });
    }

    res.json({
      success: true,
      message: '更新成功'
    });
  } catch (error) {
    console.error('更新任职记录失败:', error);
    res.status(500).json({
      success: false,
      message: '更新任职记录失败'
    });
  }
});

// 删除任职记录
app.delete('/api/employments/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.query(
      'DELETE FROM employments WHERE id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '任职记录不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除任职记录失败:', error);
    res.status(500).json({
      success: false,
      message: '删除任职记录失败'
    });
  }
});

// ==================== 待办任务 API ====================

// 获取待办任务统计数据
app.get('/api/tasks/statistics', async (req, res) => {
  try {
    console.log('开始获取任务统计数据...');

    // 获取按任务类型和状态的详细统计
    const [detailedStats] = await pool.query(`
      SELECT
        task_type,
        task_status,
        COUNT(*) as count
      FROM pending_tasks
      GROUP BY task_type, task_status
    `);

    console.log('详细统计数据:', detailedStats);

    // 初始化统计数据结构
    const typeStats = {
      '全部': 0,
      '年审年报': 0,
      '地址维护': 0,
      '自定义任务': 0
    };

    const statusStats = {
      '已逾期': 0,
      '未开始': 0,
      '进行中': 0,
      '待核实': 0,
      '已完成': 0
    };

    // 按任务类型分组的状态统计
    const typeStatusStats = {
      '年审年报': {
        '已逾期': 0,
        '未开始': 0,
        '进行中': 0,
        '待核实': 0,
        '已完成': 0
      },
      '地址维护': {
        '已逾期': 0,
        '未开始': 0,
        '进行中': 0,
        '待核实': 0,
        '已完成': 0
      },
      '自定义任务': {
        '已逾期': 0,
        '未开始': 0,
        '进行中': 0,
        '待核实': 0,
        '已完成': 0
      }
    };

    // 处理详细统计数据
    detailedStats.forEach(item => {
      const taskType = item.task_type;
      const taskStatus = item.task_status;
      const count = item.count;

      // 更新任务类型总数
      if (typeStats[taskType] !== undefined) {
        typeStats[taskType] += count;
      }
      typeStats['全部'] += count;

      // 更新任务状态总数
      if (statusStats[taskStatus] !== undefined) {
        statusStats[taskStatus] += count;
      }

      // 更新按类型分组的状态统计
      if (typeStatusStats[taskType] && typeStatusStats[taskType][taskStatus] !== undefined) {
        typeStatusStats[taskType][taskStatus] += count;
      }
    });

    console.log('处理后的统计数据:');
    console.log('typeStats:', typeStats);
    console.log('statusStats:', statusStats);
    console.log('typeStatusStats:', typeStatusStats);

    res.json({
      success: true,
      data: {
        typeStats,
        statusStats,
        typeStatusStats
      }
    });
  } catch (error) {
    console.error('获取任务统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任务统计数据失败'
    });
  }
});

// 获取待办任务列表
app.get('/api/tasks', async (req, res) => {
  try {
    const { status, type, year, keyword, page = 1, limit = 10 } = req.query;
    console.log('获取任务列表参数:', { status, type, year, keyword, page, limit });

    let whereConditions = [];
    let params = [];

    if (status && status !== '全部') {
      whereConditions.push('task_status = ?');
      params.push(status);
    }

    if (type && type !== '全部') {
      whereConditions.push('task_type = ?');
      params.push(type);
    }

    if (year) {
      whereConditions.push('year = ?');
      params.push(year);
    }

    if (keyword) {
      whereConditions.push('company_name LIKE ?');
      params.push(`%${keyword}%`);
    }

    console.log('WHERE条件:', whereConditions);
    console.log('参数:', params);

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    // 自定义排序：逾期任务优先，然后按状态排序，最后按截止日期排序
    const orderClause = `
      ORDER BY
        CASE
          WHEN (task_status IN ('未开始', '进行中') AND deadline < CURDATE()) THEN 0
          ELSE 1
        END,
        CASE task_status
          WHEN '未开始' THEN 1
          WHEN '进行中' THEN 2
          WHEN '已完成' THEN 3
          WHEN '已核实' THEN 4
        END,
        deadline ASC
    `;

    const offset = (page - 1) * limit;

    const [rows] = await pool.query(`
      SELECT
        id,
        task_status as taskStatus,
        task_type as taskType,
        year,
        DATE_FORMAT(start_date, "%Y-%m-%d") as startDate,
        DATE_FORMAT(deadline, "%Y-%m-%d") as deadline,
        company_id as companyId,
        company_name as companyName,
        business_segment as businessSegment,
        remarks,
        priority,
        progress,
        DATE_FORMAT(created_at, "%Y-%m-%d %H:%i:%s") as createTime
      FROM pending_tasks
      ${whereClause}
      ${orderClause}
      LIMIT ? OFFSET ?
    `, [...params, parseInt(limit), offset]);

    // 获取总数
    const [countResult] = await pool.query(`
      SELECT COUNT(*) as total FROM pending_tasks ${whereClause}
    `, params);

    res.json({
      success: true,
      data: {
        list: rows,
        total: countResult[0].total,
        page: parseInt(page),
        limit: parseInt(limit)
      }
    });
  } catch (error) {
    console.error('获取待办任务列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取待办任务列表失败'
    });
  }
});

// 删除待办任务
app.delete('/api/tasks/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const [result] = await pool.query(
      'DELETE FROM pending_tasks WHERE id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '任务不存在'
      });
    }

    res.json({
      success: true,
      message: '删除成功'
    });
  } catch (error) {
    console.error('删除任务失败:', error);
    res.status(500).json({
      success: false,
      message: '删除任务失败'
    });
  }
});

// ==================== 基础数据 API ====================

// 获取所有基础数据
app.get('/api/base-data', async (req, res) => {
  try {
    const [segments] = await pool.query('SELECT id, name FROM business_segments WHERE is_active = true ORDER BY name');
    const [regions] = await pool.query('SELECT id, type, name FROM regions WHERE is_active = true ORDER BY type, name');
    const [agencies] = await pool.query('SELECT id, name FROM agencies WHERE is_active = true ORDER BY name');
    const [annualStatus] = await pool.query('SELECT id, name FROM annual_update_status WHERE is_active = true ORDER BY name');
    const [operationStatus] = await pool.query('SELECT id, name FROM operation_status WHERE is_active = true ORDER BY name');
    const [contributionMethods] = await pool.query('SELECT id, name FROM contribution_methods WHERE is_active = true ORDER BY name');
    const [changeTypes] = await pool.query('SELECT id, name FROM change_types WHERE is_active = true ORDER BY name');

    res.json({
      success: true,
      data: {
        businessSegments: segments,
        regions: regions,
        agencies: agencies,
        annualUpdateStatus: annualStatus,
        operationStatus: operationStatus,
        contributionMethods: contributionMethods,
        changeTypes: changeTypes
      }
    });
  } catch (error) {
    console.error('获取基础数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取基础数据失败'
    });
  }
});

// 获取公司列表（用于下拉选择）
app.get('/api/companies-list', async (req, res) => {
  try {
    const [rows] = await pool.query(
      'SELECT id, company_name_cn as name FROM companies ORDER BY company_name_cn'
    );

    res.json({
      success: true,
      data: rows
    });
  } catch (error) {
    console.error('获取公司列表失败:', error);
    // 如果字段不存在，尝试不过滤状态
    try {
      const [rows] = await pool.query(
        'SELECT id, company_name_cn as name FROM companies ORDER BY company_name_cn'
      );
      res.json({
        success: true,
        data: rows
      });
    } catch (fallbackError) {
      console.error('获取公司列表失败（备用查询）:', fallbackError);
      res.status(500).json({
        success: false,
        message: '获取公司列表失败'
      });
    }
  }
});

// 批量创建年审年报任务API
app.post('/api/task/create-annual-tasks', async (req, res) => {
  try {
    const { taskType, year, remarks } = req.body;
    console.log(`开始批量创建年审年报任务，年度: ${year}`);

    if (!taskType || !year) {
      return res.status(400).json({
        success: false,
        message: '任务类型和年度不能为空'
      });
    }

    const connection = await pool.getConnection();
    let createdCount = 0;

    try {
      await connection.beginTransaction();

      // 获取所有公司（简化查询，避免字段不匹配问题）
      const [companies] = await connection.query(`
        SELECT c.id, c.company_name_cn,
               COALESCE(bs.name, '未分类') as business_segment,
               COALESCE(aus.name, '不管年审') as annual_update,
               c.establish_date
        FROM companies c
        LEFT JOIN business_segments bs ON c.business_segment_id = bs.id
        LEFT JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      `);

      console.log(`找到 ${companies.length} 个非注销公司`);

      for (const company of companies) {
        // 检查该年度该公司是否已有年审年报任务
        const [existing] = await connection.query(`
          SELECT id FROM pending_tasks
          WHERE task_type = '年审年报' AND year = ? AND company_id = ?
        `, [year, company.id]);

        if (existing.length > 0) {
          console.log(`公司 ${company.company_name_cn} 已存在${year}年度年审年报任务，跳过`);
          continue;
        }

        // 根据年审要求计算开始和结束日期
        let startDate, deadline;

        if (company.annual_update === '不管年审') {
          // 不管年审的公司不创建任务
          continue;
        } else if (company.annual_update === '管年审（固定周期）') {
          // 固定周期：开始日期当年1月1日，结束日期当年6月30日
          startDate = `${year}-01-01`;
          deadline = `${year}-06-30`;
        } else if (company.annual_update === '管年审（滚动周期）') {
          // 滚动周期：根据设立日期计算
          if (company.establish_date) {
            const establishDate = new Date(company.establish_date);
            const currentYear = parseInt(year);

            // 开始日期：设立日期对应今年的日期 - 3个月
            const startMonth = establishDate.getMonth() + 1 - 3;
            const startDay = establishDate.getDate();

            if (startMonth <= 0) {
              startDate = `${currentYear - 1}-${12 + startMonth}-${startDay.toString().padStart(2, '0')}`;
            } else {
              startDate = `${currentYear}-${startMonth.toString().padStart(2, '0')}-${startDay.toString().padStart(2, '0')}`;
            }

            // 结束日期：设立日期对应今年的日期 + 2个月
            const endMonth = establishDate.getMonth() + 1 + 2;
            const endDay = establishDate.getDate();

            if (endMonth > 12) {
              deadline = `${currentYear + 1}-${(endMonth - 12).toString().padStart(2, '0')}-${endDay.toString().padStart(2, '0')}`;
            } else {
              deadline = `${currentYear}-${endMonth.toString().padStart(2, '0')}-${endDay.toString().padStart(2, '0')}`;
            }
          } else {
            // 如果没有设立日期，使用默认值
            startDate = `${year}-01-01`;
            deadline = `${year}-12-31`;
          }
        } else {
          // 默认情况
          startDate = `${year}-01-01`;
          deadline = `${year}-12-31`;
        }

        // 创建任务
        await connection.query(`
          INSERT INTO pending_tasks (
            task_status, task_type, year, start_date, deadline,
            company_id, company_name, business_segment,
            remarks, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          '未开始', taskType, year, startDate, deadline,
          company.id, company.company_name_cn, company.business_segment || '未分类',
          remarks || `系统批量创建的${year}年度年审年报任务`, 1
        ]);
        createdCount++;
      }

      await connection.commit();
      console.log(`成功创建 ${createdCount} 个年审年报任务`);

      res.json({
        success: true,
        message: `成功创建 ${createdCount} 个年审年报任务`,
        data: { createdCount }
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('批量创建年审年报任务失败:', error);
    res.status(500).json({
      success: false,
      message: '批量创建年审年报任务失败',
      error: error.message
    });
  }
});

// 批量创建地址维护任务API
app.post('/api/task/create-address-tasks', async (req, res) => {
  try {
    const { taskType, year, companyIds, startDate, deadline, repeatCycle, remarks } = req.body;
    console.log(`开始批量创建地址维护任务，年度: ${year}`);

    if (!taskType || !year || !companyIds || !Array.isArray(companyIds) || companyIds.length === 0) {
      return res.status(400).json({
        success: false,
        message: '任务类型、年度和主体公司不能为空'
      });
    }

    if (!startDate || !deadline) {
      return res.status(400).json({
        success: false,
        message: '开始日期和结束日期不能为空'
      });
    }

    const connection = await pool.getConnection();
    let createdCount = 0;

    try {
      await connection.beginTransaction();

      for (const companyId of companyIds) {
        // 获取公司信息
        const [companyResult] = await connection.query(`
          SELECT c.id, c.company_name_cn,
                 COALESCE(bs.name, '未分类') as business_segment
          FROM companies c
          LEFT JOIN business_segments bs ON c.business_segment_id = bs.id
          WHERE c.id = ?
        `, [companyId]);

        if (companyResult.length === 0) {
          console.log(`公司ID ${companyId} 不存在，跳过`);
          continue;
        }

        const company = companyResult[0];

        // 检查该年度该公司是否已有地址维护任务
        const [existing] = await connection.query(`
          SELECT id FROM pending_tasks
          WHERE task_type = '地址维护' AND year = ? AND company_id = ?
        `, [year, companyId]);

        if (existing.length > 0) {
          console.log(`公司 ${company.company_name_cn} 已存在${year}年度地址维护任务，跳过`);
          continue;
        }

        // 创建任务
        await connection.query(`
          INSERT INTO pending_tasks (
            task_status, task_type, year, start_date, deadline,
            company_id, company_name, business_segment,
            remarks, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          '未开始', taskType, year, startDate, deadline,
          company.id, company.company_name_cn, company.business_segment || '未分类',
          remarks || `系统批量创建的${year}年度地址维护任务`, 1
        ]);
        createdCount++;
      }

      await connection.commit();
      console.log(`成功创建 ${createdCount} 个地址维护任务`);

      res.json({
        success: true,
        message: `成功创建 ${createdCount} 个地址维护任务`,
        data: { createdCount, repeatCycle }
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('批量创建地址维护任务失败:', error);
    res.status(500).json({
      success: false,
      message: '批量创建地址维护任务失败',
      error: error.message
    });
  }
});

// 创建自定义任务API
app.post('/api/task/create-custom-task', async (req, res) => {
  try {
    const { taskName, taskDescription, year, companyId, startDate, deadline, remarks } = req.body;
    console.log(`开始创建自定义任务: ${taskName}`);

    if (!taskName || !year || !companyId || !deadline) {
      return res.status(400).json({
        success: false,
        message: '任务名称、年度、主体公司和结束日期不能为空'
      });
    }

    // 获取公司信息
    const [companyResult] = await pool.query(`
      SELECT c.id, c.company_name_cn,
             COALESCE(bs.name, '未分类') as business_segment
      FROM companies c
      LEFT JOIN business_segments bs ON c.business_segment_id = bs.id
      WHERE c.id = ?
    `, [companyId]);

    if (companyResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: '指定的公司不存在'
      });
    }

    const company = companyResult[0];

    // 创建任务
    const [result] = await pool.query(`
      INSERT INTO pending_tasks (
        task_status, task_type, year, start_date, deadline,
        company_id, company_name, business_segment,
        remarks, created_by
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `, [
      '未开始', '自定义任务', year, startDate, deadline,
      company.id, company.company_name_cn, company.business_segment || '未分类',
      `${taskName}${taskDescription ? ' - ' + taskDescription : ''}${remarks ? ' | ' + remarks : ''}`, 1
    ]);

    console.log(`成功创建自定义任务，ID: ${result.insertId}`);

    res.json({
      success: true,
      message: '成功创建自定义任务',
      data: {
        taskId: result.insertId,
        taskName,
        companyName: company.company_name_cn
      }
    });

  } catch (error) {
    console.error('创建自定义任务失败:', error);
    res.status(500).json({
      success: false,
      message: '创建自定义任务失败',
      error: error.message
    });
  }
});

// 添加测试记录的路由
app.get('/api/add-test-record', async (req, res) => {
  try {
    // 添加测试公司
    const [result] = await pool.query(
      `INSERT INTO companies (
        company_name_cn, company_name_en, registered_capital, establish_date,
        business_segment, region, agency, annual_update, registered_address, operation_status
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        '测试公司', 'Test Company', '1000.0000万元人民币', '2023-06-15',
        '电子商务', '华南', '代理机构A', '不管年审', '广州市天河区测试地址123号', '正常经营'
      ]
    );

    res.json({ success: true, message: '测试记录添加成功', companyId: result.insertId });
  } catch (error) {
    console.error('添加测试记录失败:', error);
    res.status(500).json({ success: false, message: '添加测试记录失败', error: error.message });
  }
});

// 添加多个测试公司数据
app.get('/api/add-test-companies', async (req, res) => {
  try {
    const testCompanies = [
      ['深圳科技有限公司', 'Shenzhen Tech Co., Ltd.', '1000万元人民币', '2023-01-15', 'A. 农、林、牧、渔业', '深圳', '代理机构A', '不管年审', '深圳市南山区科技园', '正常经营'],
      ['广州贸易有限公司', 'Guangzhou Trade Co., Ltd.', '2000万元人民币', '2023-02-20', 'A. 农、林、牧、渔业', '广州', '代理机构B', '管年审（固定周期）', '广州市天河区商务中心', '正常经营'],
      ['上海投资有限公司', 'Shanghai Investment Co., Ltd.', '5000万元人民币', '2023-03-10', 'B. 采矿业', '上海', '代理机构C', '管年审（滚动周期）', '上海市浦东新区金融街', '正常经营'],
      ['北京制造有限公司', 'Beijing Manufacturing Co., Ltd.', '3000万元人民币', '2023-04-05', 'C. 制造业', '北京', '代理机构A', '不管年审', '北京市朝阳区工业园', '正常经营'],
      ['杭州电力有限公司', 'Hangzhou Power Co., Ltd.', '8000万元人民币', '2023-05-12', 'D. 电力、热力、燃气及水的生产和供应业', '杭州', '代理机构B', '管年审（固定周期）', '杭州市西湖区电力大厦', '正常经营'],
      ['成都建筑有限公司', 'Chengdu Construction Co., Ltd.', '4000万元人民币', '2023-06-18', 'E. 建筑业', '成都', '代理机构C', '管年审（滚动周期）', '成都市高新区建筑园', '正常经营']
    ];

    const results = [];
    for (const company of testCompanies) {
      try {
        const [result] = await pool.query(
          `INSERT INTO companies (
            company_name_cn, company_name_en, registered_capital, establish_date,
            business_segment, region, agency, annual_update, registered_address, operation_status
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          company
        );
        results.push({ companyId: result.insertId, name: company[0] });
      } catch (error) {
        console.error(`添加公司 ${company[0]} 失败:`, error);
      }
    }

    res.json({
      success: true,
      message: `成功添加 ${results.length} 家测试公司`,
      companies: results
    });
  } catch (error) {
    console.error('添加测试公司失败:', error);
    res.status(500).json({ success: false, message: '添加测试公司失败', error: error.message });
  }
});

// ============================================================================
// 公司变更记录相关API
// ============================================================================

// 获取变更记录列表
app.get('/api/company-change-logs', async (req, res) => {
  try {
    const [rows] = await pool.query(`
      SELECT
        cl.id,
        cl.company_id,
        c.company_name_cn as companyName,
        cl.change_type as changeType,
        CASE
          WHEN cl.change_type = 'basic' THEN '基础信息变更'
          WHEN cl.change_type = 'executive' THEN '高管信息变更'
          WHEN cl.change_type = 'shareholder' THEN '股东信息变更'
          WHEN cl.change_type = 'investment' THEN '对外投资变更'
          ELSE cl.change_type
        END as changeTypeText,
        cl.change_content as content,
        cl.old_value as oldValue,
        cl.new_value as newValue,
        DATE_FORMAT(cl.change_date, '%Y-%m-%d') as changeDate,
        cl.status,
        CASE
          WHEN cl.status = 'pending' THEN '待确认'
          WHEN cl.status = 'confirmed' THEN '已确认'
          WHEN cl.status = 'rejected' THEN '有异常'
          ELSE cl.status
        END as statusText,
        cl.operator,
        DATE_FORMAT(cl.operate_date, '%Y-%m-%d %H:%i:%s') as operateDate,
        DATE_FORMAT(cl.confirmed_at, '%Y-%m-%d %H:%i:%s') as confirmedAt
      FROM company_change_logs cl
      LEFT JOIN companies c ON cl.company_id = c.id
      ORDER BY cl.operate_date DESC
    `);

    const formattedRows = rows.map(row => ({
      ...row,
      details: {
        oldValue: row.oldValue,
        newValue: row.newValue
      }
    }));

    res.json({
      success: true,
      data: formattedRows
    });
  } catch (error) {
    console.error('获取变更记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取变更记录失败',
      error: error.message
    });
  }
});

// 获取变更记录统计
app.get('/api/company-change-logs/statistics', async (req, res) => {
  try {
    const [stats] = await pool.query(`
      SELECT
        change_type,
        COUNT(*) as count
      FROM company_change_logs
      WHERE status IN ('pending', 'rejected')
      GROUP BY change_type
    `);

    const statistics = {
      basicInfo: 0,
      executiveInfo: 0,
      shareholderInfo: 0,
      investmentInfo: 0
    };

    stats.forEach(stat => {
      switch (stat.change_type) {
        case 'basic':
          statistics.basicInfo = stat.count;
          break;
        case 'executive':
          statistics.executiveInfo = stat.count;
          break;
        case 'shareholder':
          statistics.shareholderInfo = stat.count;
          break;
        case 'investment':
          statistics.investmentInfo = stat.count;
          break;
      }
    });

    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    console.error('获取统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取统计数据失败',
      error: error.message
    });
  }
});

// 确认变更记录
app.post('/api/company-change-logs/confirm/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 更新变更记录状态为已确认
    const [result] = await pool.query(`
      UPDATE company_change_logs
      SET status = 'confirmed', confirmed_at = NOW()
      WHERE id = ?
    `, [id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '变更记录不存在'
      });
    }

    // TODO: 这里应该根据变更内容实际更新公司信息到companies表
    // 由于涉及复杂的业务逻辑，暂时只更新状态

    res.json({
      success: true,
      message: '确认成功'
    });
  } catch (error) {
    console.error('确认变更失败:', error);
    res.status(500).json({
      success: false,
      message: '确认变更失败',
      error: error.message
    });
  }
});

// 退回变更记录
app.post('/api/company-change-logs/reject/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 更新变更记录状态为有异常
    const [result] = await pool.query(`
      UPDATE company_change_logs
      SET status = 'rejected'
      WHERE id = ?
    `, [id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '变更记录不存在'
      });
    }

    res.json({
      success: true,
      message: '退回成功'
    });
  } catch (error) {
    console.error('退回变更失败:', error);
    res.status(500).json({
      success: false,
      message: '退回变更失败',
      error: error.message
    });
  }
});

// 删除变更记录
app.delete('/api/company-change-logs/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 删除变更记录
    const [result] = await pool.query(
      'DELETE FROM company_change_logs WHERE id = ?',
      [id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '变更记录不存在'
      });
    }

    res.json({
      success: true,
      message: '变更记录已删除'
    });

  } catch (error) {
    console.error('删除变更记录失败:', error);
    res.status(500).json({
      success: false,
      message: '删除变更记录失败',
      error: error.message
    });
  }
});

// 更新变更记录
app.put('/api/company-change-logs/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      change_content,
      old_value,
      new_value,
      change_date,
      operator,
      status
    } = req.body;

    // 更新变更记录
    const [result] = await pool.query(`
      UPDATE company_change_logs
      SET change_content = ?, old_value = ?, new_value = ?,
          change_date = ?, operator = ?, status = ?, updated_at = NOW()
      WHERE id = ?
    `, [change_content, old_value, new_value, change_date, operator, status, id]);

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '变更记录不存在'
      });
    }

    res.json({
      success: true,
      message: '变更记录已更新'
    });

  } catch (error) {
    console.error('更新变更记录失败:', error);
    res.status(500).json({
      success: false,
      message: '更新变更记录失败',
      error: error.message
    });
  }
});

// 提交变更记录
app.post('/api/company-change-logs/submit', async (req, res) => {
  try {
    const {
      company_id,
      change_type,
      change_content,
      old_value,
      new_value,
      change_date,
      operator
    } = req.body;

    // 插入变更记录
    const [result] = await pool.query(`
      INSERT INTO company_change_logs (
        company_id, change_type, change_content, old_value, new_value,
        change_date, status, operator, operate_date
      ) VALUES (?, ?, ?, ?, ?, ?, 'pending', ?, NOW())
    `, [
      company_id,
      change_type,
      change_content,
      old_value,
      new_value,
      change_date,
      operator
    ]);

    res.json({
      success: true,
      message: '变更记录提交成功',
      data: { id: result.insertId }
    });
  } catch (error) {
    console.error('提交变更记录失败:', error);
    res.status(500).json({
      success: false,
      message: '提交变更记录失败',
      error: error.message
    });
  }
});

// ============================================================================
// 任务排查相关API
// ============================================================================

// 任务排查API
app.post('/api/task/investigation', async (req, res) => {
  try {
    const { year } = req.body;
    console.log(`开始任务排查，年度: ${year}`);

    // 1. 获取存续公司数量
    const [companiesResult] = await pool.query(`
      SELECT COUNT(*) as total FROM companies
    `);
    const totalCompanies = companiesResult[0].total;

    // 2. 根据年审状态获取公司分类
    // 获取各种年审状态的公司数量
    const [fixedAnnualResult] = await pool.query(`
      SELECT COUNT(*) as count FROM companies c
      JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      WHERE aus.name = '管年审（固定周期）'
    `);
    const fixedAnnualCompanies = fixedAnnualResult[0].count;

    const [rollingAnnualResult] = await pool.query(`
      SELECT COUNT(*) as count FROM companies c
      JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      WHERE aus.name = '管年审（滚动周期）'
    `);
    const rollingAnnualCompanies = rollingAnnualResult[0].count;

    const [noAnnualResult] = await pool.query(`
      SELECT COUNT(*) as count FROM companies c
      JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      WHERE aus.name = '不管年审'
    `);
    const noAnnualCompanies = noAnnualResult[0].count;

    // 年审年报理论数 = 管年审的公司数（固定周期 + 滚动周期）
    const annualReportTheory = fixedAnnualCompanies + rollingAnnualCompanies;

    // 年审年报实际数（当前年度的年审年报任务数，只统计管年审的公司）
    const [annualReportActualResult] = await pool.query(`
      SELECT COUNT(*) as count FROM pending_tasks pt
      JOIN companies c ON pt.company_id = c.id
      JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      WHERE pt.task_type = '年审年报' AND pt.year = ? AND aus.name IN ('管年审（固定周期）', '管年审（滚动周期）')
    `, [year]);
    const annualReportActual = annualReportActualResult[0].count;

    // 年审年报缺失数 = 理论数 - 实际数
    const annualReportMissing = Math.max(0, annualReportTheory - annualReportActual);

    // 3. 获取地址维护相关数据
    // 地址维护理论数 = 所有管年审的公司（固定周期 + 滚动周期）
    const addressMaintenanceTheory = fixedAnnualCompanies + rollingAnnualCompanies;

    // 地址维护实际数（当前年度的地址维护任务数，只统计管年审的公司）
    const [addressMaintenanceActualResult] = await pool.query(`
      SELECT COUNT(*) as count FROM pending_tasks pt
      JOIN companies c ON pt.company_id = c.id
      JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      WHERE pt.task_type = '地址维护' AND pt.year = ? AND aus.name IN ('管年审（固定周期）', '管年审（滚动周期）')
    `, [year]);
    const addressMaintenanceActual = addressMaintenanceActualResult[0].count;

    // 地址维护缺失数 = 理论数 - 实际数
    const addressMaintenanceMissing = Math.max(0, addressMaintenanceTheory - addressMaintenanceActual);

    // 4. 获取缺失任务的公司列表
    // 年审年报缺失的公司（只包含管年审的公司）
    const [missingAnnualCompanies] = await pool.query(`
      SELECT c.company_name_cn as companyName, '年审年报' as taskType
      FROM companies c
      JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      WHERE aus.name IN ('管年审（固定周期）', '管年审（滚动周期）')
        AND c.id NOT IN (
          SELECT DISTINCT company_id FROM pending_tasks
          WHERE task_type = '年审年报' AND year = ? AND company_id IS NOT NULL
        )
      ORDER BY c.company_name_cn
    `, [year]);

    // 地址维护缺失的公司（只包含管年审的公司）
    const [missingAddressCompanies] = await pool.query(`
      SELECT c.company_name_cn as companyName, '地址维护' as taskType
      FROM companies c
      JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      WHERE aus.name IN ('管年审（固定周期）', '管年审（滚动周期）')
        AND c.id NOT IN (
          SELECT DISTINCT company_id FROM pending_tasks
          WHERE task_type = '地址维护' AND year = ? AND company_id IS NOT NULL
        )
      ORDER BY c.company_name_cn
    `, [year]);

    // 合并缺失任务列表并按公司分组
    const taskMap = new Map();

    // 处理年审年报缺失任务
    missingAnnualCompanies.forEach(company => {
      if (!taskMap.has(company.companyName)) {
        taskMap.set(company.companyName, {
          companyName: company.companyName,
          missingAnnualReport: true,
          missingAddressMaintenance: false
        });
      } else {
        taskMap.get(company.companyName).missingAnnualReport = true;
      }
    });

    // 处理地址维护缺失任务
    missingAddressCompanies.forEach(company => {
      if (!taskMap.has(company.companyName)) {
        taskMap.set(company.companyName, {
          companyName: company.companyName,
          missingAnnualReport: false,
          missingAddressMaintenance: true
        });
      } else {
        taskMap.get(company.companyName).missingAddressMaintenance = true;
      }
    });

    // 转换为数组并排序
    const missingTasks = Array.from(taskMap.values()).sort((a, b) =>
      a.companyName.localeCompare(b.companyName, 'zh-CN')
    );

    // 5. 构建返回数据
    const result = {
      year,
      totalCompanies,
      // 不管年审公司数（确保不为负数）
      noAnnualCompanies,
      // 管年审（固定周期）公司数
      fixedAnnualCompanies,
      // 管年审（滚动周期）公司数
      rollingAnnualCompanies,
      // 年审年报统计
      annualReport: {
        theory: annualReportTheory,
        actual: annualReportActual,
        missing: annualReportMissing
      },
      // 地址维护统计
      addressMaintenance: {
        theory: addressMaintenanceTheory,
        actual: addressMaintenanceActual,
        missing: addressMaintenanceMissing
      },
      // 缺失任务列表
      missingTasks
    };

    console.log('任务排查结果:', result);
    res.json({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('任务排查失败:', error);
    res.status(500).json({
      success: false,
      message: '任务排查失败',
      error: error.message
    });
  }
});

// 补全缺失任务API
app.post('/api/task/complete-missing', async (req, res) => {
  try {
    const { year } = req.body;
    console.log(`开始补全缺失任务，年度: ${year}`);

    const connection = await pool.getConnection();
    let createdCount = 0;

    try {
      await connection.beginTransaction();

      // 1. 补全年审年报任务（只为管年审的公司创建）
      const [missingAnnualCompanies] = await connection.query(`
        SELECT c.id, c.company_name_cn, c.establish_date, aus.name as annual_status,
               bs.name as business_segment
        FROM companies c
        JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
        LEFT JOIN business_segments bs ON c.business_segment_id = bs.id
        WHERE aus.name IN ('管年审（固定周期）', '管年审（滚动周期）')
          AND c.id NOT IN (
            SELECT DISTINCT company_id FROM pending_tasks
            WHERE task_type = '年审年报' AND year = ? AND company_id IS NOT NULL
          )
      `, [year]);

      console.log(`找到 ${missingAnnualCompanies.length} 个公司缺失年审年报任务`);
      for (const company of missingAnnualCompanies) {
        let startDate, deadline;

        if (company.annual_status === '管年审（固定周期）') {
          // 固定周期：开始日期为1月1日，结束日期为6月30日
          startDate = `${year}-01-01`;
          deadline = `${year}-06-30`;
        } else if (company.annual_status === '管年审（滚动周期）') {
          // 滚动周期：基于成立日期计算
          const establishDate = new Date(company.establish_date);
          const establishMonth = establishDate.getMonth() + 1; // 月份从0开始
          const establishDay = establishDate.getDate();

          // 开始日期：成立日期对应的年度日期 - 3个月
          const startMonth = establishMonth - 3;
          if (startMonth <= 0) {
            startDate = `${year - 1}-${12 + startMonth}-${establishDay}`;
          } else {
            startDate = `${year}-${startMonth.toString().padStart(2, '0')}-${establishDay.toString().padStart(2, '0')}`;
          }

          // 结束日期：成立日期对应的年度日期 + 2个月
          const endMonth = establishMonth + 2;
          if (endMonth > 12) {
            deadline = `${year + 1}-${(endMonth - 12).toString().padStart(2, '0')}-${establishDay.toString().padStart(2, '0')}`;
          } else {
            deadline = `${year}-${endMonth.toString().padStart(2, '0')}-${establishDay.toString().padStart(2, '0')}`;
          }
        }

        await connection.query(`
          INSERT INTO pending_tasks (
            task_status, task_type, year, start_date, deadline,
            company_id, company_name, business_segment,
            remarks, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          '未开始', '年审年报', year, startDate, deadline,
          company.id, company.company_name_cn, company.business_segment || '未分类',
          `系统自动创建的${year}年度年审年报任务（${company.annual_status}）`, 1
        ]);
        createdCount++;
      }

      // 2. 补全地址维护任务（只为管年审的公司创建）
      const [missingAddressCompanies] = await connection.query(`
        SELECT c.id, c.company_name_cn, c.establish_date, aus.name as annual_status,
               bs.name as business_segment
        FROM companies c
        JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
        LEFT JOIN business_segments bs ON c.business_segment_id = bs.id
        WHERE aus.name IN ('管年审（固定周期）', '管年审（滚动周期）')
          AND c.id NOT IN (
            SELECT DISTINCT company_id FROM pending_tasks
            WHERE task_type = '地址维护' AND year = ? AND company_id IS NOT NULL
          )
      `, [year]);

      console.log(`找到 ${missingAddressCompanies.length} 个公司缺失地址维护任务`);
      for (const company of missingAddressCompanies) {
        let startDate, deadline;

        if (company.annual_status === '管年审（固定周期）') {
          // 固定周期：开始日期为1月1日，结束日期为6月30日
          startDate = `${year}-01-01`;
          deadline = `${year}-06-30`;
        } else if (company.annual_status === '管年审（滚动周期）') {
          // 滚动周期：基于成立日期计算
          const establishDate = new Date(company.establish_date);
          const establishMonth = establishDate.getMonth() + 1;
          const establishDay = establishDate.getDate();

          // 开始日期：成立日期对应的年度日期 - 3个月
          const startMonth = establishMonth - 3;
          if (startMonth <= 0) {
            startDate = `${year - 1}-${12 + startMonth}-${establishDay}`;
          } else {
            startDate = `${year}-${startMonth.toString().padStart(2, '0')}-${establishDay.toString().padStart(2, '0')}`;
          }

          // 结束日期：成立日期对应的年度日期 + 2个月
          const endMonth = establishMonth + 2;
          if (endMonth > 12) {
            deadline = `${year + 1}-${(endMonth - 12).toString().padStart(2, '0')}-${establishDay.toString().padStart(2, '0')}`;
          } else {
            deadline = `${year}-${endMonth.toString().padStart(2, '0')}-${establishDay.toString().padStart(2, '0')}`;
          }
        }

        await connection.query(`
          INSERT INTO pending_tasks (
            task_status, task_type, year, start_date, deadline,
            company_id, company_name, business_segment,
            remarks, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          '未开始', '地址维护', year, startDate, deadline,
          company.id, company.company_name_cn, company.business_segment || '未分类',
          `系统自动创建的${year}年度地址维护任务（${company.annual_status}）`, 1
        ]);
        createdCount++;
      }

      await connection.commit();
      console.log(`成功补全 ${createdCount} 个缺失任务`);

      res.json({
        success: true,
        message: `成功补全 ${createdCount} 个缺失任务`,
        data: { createdCount }
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('补全缺失任务失败:', error);
    res.status(500).json({
      success: false,
      message: '补全缺失任务失败',
      error: error.message
    });
  }
});

// 清理测试数据API
app.post('/api/cleanup-test-data', async (req, res) => {
  try {
    const { years } = req.body;
    console.log(`开始清理测试数据，年度: ${years.join(', ')}`);

    const connection = await pool.getConnection();
    let deletedCount = 0;

    try {
      await connection.beginTransaction();

      for (const year of years) {
        const [result] = await connection.query(`
          DELETE FROM pending_tasks WHERE year = ?
        `, [year]);
        deletedCount += result.affectedRows;
        console.log(`删除 ${year} 年的 ${result.affectedRows} 条任务记录`);
      }

      await connection.commit();
      console.log(`成功清理 ${deletedCount} 条测试数据`);

      res.json({
        success: true,
        message: `成功清理 ${deletedCount} 条测试数据`,
        data: { deletedCount }
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('清理测试数据失败:', error);
    res.status(500).json({
      success: false,
      message: '清理测试数据失败',
      error: error.message
    });
  }
});

// 获取年份列表API
app.get('/api/task/years', async (req, res) => {
  try {
    console.log('获取任务年份列表');

    // 从数据库中获取所有不重复的年份
    const [yearsResult] = await pool.query(`
      SELECT DISTINCT year FROM pending_tasks
      WHERE year IS NOT NULL
      ORDER BY year DESC
    `);

    const years = yearsResult.map(row => row.year);
    console.log('找到的年份列表:', years);

    res.json({
      success: true,
      data: years
    });

  } catch (error) {
    console.error('获取年份列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取年份列表失败',
      error: error.message
    });
  }
});

// 获取单个任务详情API
app.get('/api/tasks/:id', async (req, res) => {
  try {
    const { id } = req.params;
    console.log(`获取任务详情，ID: ${id}`);

    const [taskResult] = await pool.query(`
      SELECT * FROM pending_tasks WHERE id = ?
    `, [id]);

    if (taskResult.length === 0) {
      return res.status(404).json({
        success: false,
        message: '任务不存在'
      });
    }

    const task = taskResult[0];
    console.log('找到任务:', task);

    res.json({
      success: true,
      data: task
    });

  } catch (error) {
    console.error('获取任务详情失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任务详情失败',
      error: error.message
    });
  }
});

// 获取任务类型列表API
app.get('/api/task/types', async (req, res) => {
  try {
    console.log('获取任务类型列表');

    // 从数据库中获取所有不重复的任务类型
    const [typesResult] = await pool.query(`
      SELECT DISTINCT task_type FROM pending_tasks
      WHERE task_type IS NOT NULL
      ORDER BY task_type
    `);

    const types = typesResult.map(row => row.task_type);
    console.log('找到的任务类型列表:', types);

    res.json({
      success: true,
      data: types
    });

  } catch (error) {
    console.error('获取任务类型列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取任务类型列表失败',
      error: error.message
    });
  }
});

// 核实任务API
app.post('/api/task/verify', async (req, res) => {
  try {
    const {
      taskId,
      isVerified,
      shouldCreateNext,
      nextTaskData
    } = req.body;

    console.log(`开始核实任务，任务ID: ${taskId}, 是否核实: ${isVerified}, 是否创建下一年度任务: ${shouldCreateNext}`);

    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      // 1. 更新当前任务状态为已核实
      await connection.query(`
        UPDATE pending_tasks
        SET task_status = '已核实'
        WHERE id = ?
      `, [taskId]);

      let createdTaskId = null;

      // 2. 如果选择创建下一年度任务，则创建新任务
      if (shouldCreateNext && nextTaskData) {
        // 先获取当前任务的完整信息
        const [currentTask] = await connection.query(`
          SELECT company_id, company_name, business_segment, year FROM pending_tasks WHERE id = ?
        `, [taskId]);

        if (currentTask.length > 0) {
          // 下一周期任务的年度应该是当前任务年度+1
          const currentYear = parseInt(currentTask[0].year);
          const nextYear = currentYear + 1;

          const [result] = await connection.query(`
            INSERT INTO pending_tasks (
              task_status, task_type, year, start_date, deadline,
              company_id, company_name, business_segment,
              remarks, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            '未开始',
            nextTaskData.taskType,
            nextYear.toString(), // 使用当前任务年度+1
            nextTaskData.startDate,
            nextTaskData.deadline,
            currentTask[0].company_id,
            currentTask[0].company_name,
            currentTask[0].business_segment,
            `${nextTaskData.remarks}${nextTaskData.repeatCycle ? `，重复周期：${nextTaskData.repeatCycle}年` : ''}`,
            1 // 创建者ID
          ]);
          createdTaskId = result.insertId;
        }
      }

      await connection.commit();
      console.log(`任务核实成功，任务ID: ${taskId}${createdTaskId ? `，创建了新任务ID: ${createdTaskId}` : ''}`);

      res.json({
        success: true,
        message: `任务核实成功${createdTaskId ? '，并创建了下一年度任务' : ''}`,
        data: {
          verifiedTaskId: taskId,
          createdTaskId
        }
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('核实任务失败:', error);
    res.status(500).json({
      success: false,
      message: '核实任务失败',
      error: error.message
    });
  }
});

// 编辑任务API
app.put('/api/tasks/:id', async (req, res) => {
  try {
    const taskId = req.params.id;
    const { startDate, deadline, repeatCycle, remarks } = req.body;

    console.log(`编辑任务 ID: ${taskId}`);

    if (!taskId) {
      return res.status(400).json({
        success: false,
        message: '任务ID不能为空'
      });
    }

    // 检查任务是否存在
    const [existingTask] = await pool.query(
      'SELECT id FROM pending_tasks WHERE id = ?',
      [taskId]
    );

    if (existingTask.length === 0) {
      return res.status(404).json({
        success: false,
        message: '任务不存在'
      });
    }

    // 更新任务
    await pool.query(`
      UPDATE pending_tasks
      SET start_date = ?, deadline = ?, remarks = ?, updated_at = NOW()
      WHERE id = ?
    `, [startDate, deadline, remarks, taskId]);

    console.log(`任务 ${taskId} 编辑成功`);

    res.json({
      success: true,
      message: '任务编辑成功'
    });

  } catch (error) {
    console.error('编辑任务失败:', error);
    res.status(500).json({
      success: false,
      message: '编辑任务失败',
      error: error.message
    });
  }
});

// 更新任务进度API
app.put('/api/tasks/:id/update-progress', async (req, res) => {
  try {
    const taskId = req.params.id;
    const { taskStatus, uploadedFiles, createNextCycle, nextCycleData } = req.body;

    console.log(`更新任务进度 ID: ${taskId}, 状态: ${taskStatus}`);

    if (!taskId || !taskStatus) {
      return res.status(400).json({
        success: false,
        message: '任务ID和状态不能为空'
      });
    }

    const connection = await pool.getConnection();

    try {
      await connection.beginTransaction();

      // 检查任务是否存在
      const [existingTask] = await connection.query(
        'SELECT * FROM pending_tasks WHERE id = ?',
        [taskId]
      );

      if (existingTask.length === 0) {
        return res.status(404).json({
          success: false,
          message: '任务不存在'
        });
      }

      const task = existingTask[0];

      // 更新任务状态
      await connection.query(`
        UPDATE pending_tasks
        SET task_status = ?, updated_at = NOW()
        WHERE id = ?
      `, [taskStatus, taskId]);

      // 如果需要创建下一周期任务
      if (createNextCycle && nextCycleData && taskStatus === '已完成') {
        // 获取公司信息
        const [companyResult] = await connection.query(`
          SELECT c.id, c.company_name_cn,
                 COALESCE(bs.name, '未分类') as business_segment
          FROM companies c
          LEFT JOIN business_segments bs ON c.business_segment_id = bs.id
          WHERE c.id = ?
        `, [nextCycleData.companyId]);

        if (companyResult.length > 0) {
          const company = companyResult[0];

          // 创建下一周期任务
          await connection.query(`
            INSERT INTO pending_tasks (
              task_status, task_type, year, start_date, deadline,
              company_id, company_name, business_segment,
              remarks, created_by
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            '未开始', nextCycleData.taskType, nextCycleData.year,
            nextCycleData.startDate, nextCycleData.deadline,
            company.id, company.company_name_cn, company.business_segment,
            nextCycleData.remarks || `系统自动创建的${nextCycleData.year}年度${nextCycleData.taskType}任务`, 1
          ]);

          console.log(`创建下一周期任务成功: ${nextCycleData.taskType} - ${nextCycleData.year}`);
        }
      }

      await connection.commit();
      console.log(`任务 ${taskId} 进度更新成功`);

      res.json({
        success: true,
        message: '任务进度更新成功'
      });

    } catch (error) {
      await connection.rollback();
      throw error;
    } finally {
      connection.release();
    }

  } catch (error) {
    console.error('更新任务进度失败:', error);
    res.status(500).json({
      success: false,
      message: '更新任务进度失败',
      error: error.message
    });
  }
});

// ==================== 公司信息变更确认 API ====================

// 初始化公司变更记录表
async function initCompanyChangeLogsTable() {
  try {
    // 先创建users表（如果不存在）
    await pool.query(`
      CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        username VARCHAR(100) NOT NULL UNIQUE COMMENT '用户名',
        email VARCHAR(100) COMMENT '邮箱',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表'
    `);

    // 插入默认用户（如果不存在）
    await pool.query(`
      INSERT IGNORE INTO users (id, username) VALUES (1, 'admin')
    `);

    // 删除旧表并重新创建
    await pool.query(`DROP TABLE IF EXISTS company_change_logs`);

    // 创建公司变更记录表
    await pool.query(`
      CREATE TABLE company_change_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        company_id INT NOT NULL COMMENT '公司ID',
        change_type ENUM('basic', 'executive', 'shareholder', 'investment') NOT NULL COMMENT '变更类型',
        change_content TEXT NOT NULL COMMENT '变更内容详情',
        old_value TEXT COMMENT '变更前的值',
        new_value TEXT COMMENT '变更后的值',
        change_date DATE NOT NULL COMMENT '变更日期',
        status ENUM('pending', 'confirmed', 'rejected') DEFAULT 'pending' COMMENT '确认状态',
        operator VARCHAR(100) COMMENT '操作人',
        operate_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
        confirmed_by INT COMMENT '确认人ID',
        confirmed_at TIMESTAMP NULL COMMENT '确认时间',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

        INDEX idx_company_change (company_id),
        INDEX idx_change_type (change_type),
        INDEX idx_change_status (status),
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
        FOREIGN KEY (confirmed_by) REFERENCES users(id)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司信息变更记录表'
    `);

    // 检查是否已有测试数据
    const [existingRecords] = await pool.query('SELECT COUNT(*) as count FROM company_change_logs');
    if (existingRecords[0].count === 0) {
      console.log('🔄 开始插入公司变更测试数据...');
      await insertTestChangeRecords();
    }

    console.log('✅ 公司变更记录表初始化成功');
  } catch (error) {
    console.error('❌ 公司变更记录表初始化失败:', error);
  }
}

// 插入测试变更记录 - 基于真实数据库数据
async function insertTestChangeRecords() {
  try {
    // 先清空现有的测试数据
    await pool.query('DELETE FROM company_change_logs');
    console.log('🗑️ 已清空现有变更记录');

    // 先获取实际存在的公司ID
    const [companies] = await pool.query('SELECT id, company_name_cn FROM companies LIMIT 10');
    console.log('📋 可用的公司列表:', companies.map(c => `${c.id}: ${c.company_name_cn}`));

    if (companies.length === 0) {
      console.log('⚠️ 没有找到公司数据，跳过插入变更记录');
      return;
    }

    const testRecords = [
      // 基础信息变更 - 基于真实公司数据
      {
        company_id: companies[0]?.id || 1, // 使用第一个公司
        change_type: 'basic',
        change_content: `公司中文名：【${companies[0]?.company_name_cn}】→【${companies[0]?.company_name_cn}发展有限公司】`,
        old_value: companies[0]?.company_name_cn,
        new_value: `${companies[0]?.company_name_cn}发展有限公司`,
        change_date: '2024-12-01',
        status: 'pending',
        operator: '张丽丽'
      },
      {
        company_id: companies[1]?.id || companies[0]?.id || 1, // 使用第二个公司
        change_type: 'basic',
        change_content: '注册资本：【2000万元】→【3000万元】',
        old_value: '2000.0000',
        new_value: '3000.0000',
        change_date: '2024-12-02',
        status: 'pending',
        operator: '王小明'
      },
      {
        company_id: companies[2]?.id || companies[0]?.id || 1, // 使用第三个公司
        change_type: 'basic',
        change_content: '注册地址：【上海市浦东新区金融街】→【上海市浦东新区科技园区88号】',
        old_value: '上海市浦东新区金融街',
        new_value: '上海市浦东新区科技园区88号',
        change_date: '2024-12-03',
        status: 'confirmed',
        operator: '李华'
      },

      // 高管信息变更 - 基于真实任职数据
      {
        company_id: companies[3]?.id || companies[0]?.id || 1, // 使用第四个公司
        change_type: 'executive',
        change_content: '职位变更：【张天威 - 经理】→【张天威 - 总经理】',
        old_value: '张天威 - 经理',
        new_value: '张天威 - 总经理',
        change_date: '2024-12-04',
        status: 'pending',
        operator: '赵敏'
      },
      {
        company_id: companies[4]?.id || companies[0]?.id || 1, // 使用第五个公司
        change_type: 'executive',
        change_content: '职位变更：【张大威 - 监事】→【张大威 - 董事长】',
        old_value: '张大威 - 监事',
        new_value: '张大威 - 董事长',
        change_date: '2024-12-05',
        status: 'rejected',
        operator: '陈小红'
      },
      {
        company_id: companies[5]?.id || companies[0]?.id || 1, // 使用第六个公司
        change_type: 'executive',
        change_content: '删除高管：【张伟 - 总经理】',
        old_value: '张伟 - 总经理',
        new_value: '',
        change_date: '2024-12-06',
        status: 'confirmed',
        operator: '孙丽'
      },

      // 股东信息变更 - 基于真实股东数据
      {
        company_id: companies[6]?.id || companies[0]?.id || 1, // 使用第七个公司
        change_type: 'shareholder',
        change_content: '股东持股比例变更：【张天成 32.07%】→【张天成 40%】',
        old_value: '张天成 - 32.07%',
        new_value: '张天成 - 40%',
        change_date: '2024-12-07',
        status: 'pending',
        operator: '周杰'
      },
      {
        company_id: companies[1]?.id || companies[0]?.id || 1, // 重复使用第二个公司
        change_type: 'shareholder',
        change_content: '股东持股比例变更：【张天威 31.43%】→【张天威 25%】',
        old_value: '张天威 - 31.43%',
        new_value: '张天威 - 25%',
        change_date: '2024-12-08',
        status: 'pending',
        operator: '吴刚'
      },
      {
        company_id: companies[7]?.id || companies[0]?.id || 1, // 使用第八个公司
        change_type: 'shareholder',
        change_content: '删除股东：【陈静 17.52%】',
        old_value: '陈静 - 17.52%',
        new_value: '',
        change_date: '2024-12-09',
        status: 'confirmed',
        operator: '郑小花'
      },

      // 对外投资变更 - 基于真实投资数据
      {
        company_id: companies[3]?.id || companies[0]?.id || 1, // 重复使用第四个公司
        change_type: 'investment',
        change_content: '投资金额变更：【被投资公司8 - 3139.13万元 - 19.12%】→【被投资公司8 - 4000万元 - 19.12%】',
        old_value: '被投资公司8 - 3139.13万元 - 19.12%',
        new_value: '被投资公司8 - 4000万元 - 19.12%',
        change_date: '2024-12-10',
        status: 'pending',
        operator: '马云飞'
      },
      {
        company_id: companies[0]?.id || 1, // 重复使用第一个公司
        change_type: 'investment',
        change_content: '投资持股比例变更：【被投资公司1 - 2231.52万元 - 42.30%】→【被投资公司1 - 2231.52万元 - 50%】',
        old_value: '被投资公司1 - 2231.52万元 - 42.30%',
        new_value: '被投资公司1 - 2231.52万元 - 50%',
        change_date: '2024-12-11',
        status: 'confirmed',
        operator: '刘德华'
      }
    ];

    for (const record of testRecords) {
      await pool.query(`
        INSERT INTO company_change_logs
        (company_id, change_type, change_content, old_value, new_value, change_date, status, operator)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `, [
        record.company_id,
        record.change_type,
        record.change_content,
        record.old_value,
        record.new_value,
        record.change_date,
        record.status,
        record.operator
      ]);
    }

    console.log(`✅ 成功插入 ${testRecords.length} 条公司变更测试记录`);
  } catch (error) {
    console.error('❌ 插入测试变更记录失败:', error);
  }
}

// 获取变更确认统计数据
app.get('/api/company-change-confirmation/statistics', async (req, res) => {
  try {
    // 从数据库获取真实统计数据
    const [basicInfo] = await pool.query(
      'SELECT COUNT(*) as count FROM company_change_logs WHERE change_type = "basic" AND status = "pending"'
    );
    const [executiveInfo] = await pool.query(
      'SELECT COUNT(*) as count FROM company_change_logs WHERE change_type = "executive" AND status = "pending"'
    );
    const [shareholderInfo] = await pool.query(
      'SELECT COUNT(*) as count FROM company_change_logs WHERE change_type = "shareholder" AND status = "pending"'
    );
    const [investmentInfo] = await pool.query(
      'SELECT COUNT(*) as count FROM company_change_logs WHERE change_type = "investment" AND status = "pending"'
    );

    const statistics = {
      basicInfo: basicInfo[0].count,
      executiveInfo: executiveInfo[0].count,
      shareholderInfo: shareholderInfo[0].count,
      investmentInfo: investmentInfo[0].count
    };

    res.json({
      success: true,
      data: statistics
    });
  } catch (error) {
    console.error('获取变更确认统计数据失败:', error);
    res.status(500).json({
      success: false,
      message: '获取变更确认统计数据失败'
    });
  }
});

// 获取变更记录列表
app.get('/api/company-change-confirmation/records', async (req, res) => {
  try {
    // 从数据库获取真实变更记录
    const [records] = await pool.query(`
      SELECT
        ccl.id,
        ccl.company_id,
        ccl.change_type,
        ccl.change_content,
        ccl.old_value,
        ccl.new_value,
        ccl.change_date,
        ccl.status,
        ccl.operator,
        ccl.operate_date,
        c.company_name_cn,
        c.company_name_en,
        CASE
          WHEN ccl.change_type = 'basic' THEN '基础信息'
          WHEN ccl.change_type = 'executive' THEN '高管信息'
          WHEN ccl.change_type = 'shareholder' THEN '股东信息'
          WHEN ccl.change_type = 'investment' THEN '对外投资'
          ELSE '未知类型'
        END as changeTypeText,
        CASE
          WHEN ccl.status = 'pending' THEN '待确认'
          WHEN ccl.status = 'confirmed' THEN '已确认'
          WHEN ccl.status = 'rejected' THEN '已退回'
          ELSE '未知状态'
        END as statusText
      FROM company_change_logs ccl
      LEFT JOIN companies c ON ccl.company_id = c.id
      ORDER BY
        CASE ccl.status
          WHEN 'pending' THEN 1
          WHEN 'confirmed' THEN 2
          WHEN 'rejected' THEN 3
          ELSE 4
        END,
        ccl.operate_date DESC
    `);

    // 格式化数据
    const formattedRecords = records.map(record => ({
      id: record.id,
      companyId: record.company_id,
      companyName: record.company_name_cn,
      changeType: record.change_type,
      changeTypeText: record.changeTypeText,
      content: record.change_content,
      oldValue: record.old_value,
      newValue: record.new_value,
      changeDate: record.change_date ? new Date(record.change_date).toISOString().split('T')[0] : '',
      operator: record.operator,
      operateDate: record.operate_date ? new Date(record.operate_date).toLocaleString('zh-CN') : '',
      status: record.status,
      statusText: record.statusText
    }));

    res.json({
      success: true,
      data: formattedRecords
    });
  } catch (error) {
    console.error('获取变更记录失败:', error);
    res.status(500).json({
      success: false,
      message: '获取变更记录失败'
    });
  }
});

// 应用变更到数据库的函数
async function applyChangeToDatabase(changeRecord) {
  const { company_id, change_type, old_value, new_value, change_content } = changeRecord;

  try {
    switch (change_type) {
      case 'basic':
        await applyBasicInfoChange(company_id, old_value, new_value, change_content);
        break;
      case 'executive':
        await applyExecutiveChange(company_id, old_value, new_value);
        break;
      case 'shareholder':
        await applyShareholderChange(company_id, old_value, new_value);
        break;
      case 'investment':
        await applyInvestmentChange(company_id, old_value, new_value);
        break;
      default:
        console.log(`⚠️ 未知的变更类型: ${change_type}`);
    }
  } catch (error) {
    console.error(`❌ 应用变更失败 (${change_type}):`, error);
    throw error;
  }
}

// 应用基础信息变更
async function applyBasicInfoChange(companyId, oldValue, newValue, changeContent) {
  console.log(`📝 应用基础信息变更: 公司ID ${companyId}, ${oldValue} → ${newValue}`);
  console.log(`📋 变更内容: ${changeContent}`);

  // 根据变更内容判断要更新的字段
  if (changeContent && changeContent.includes('公司中文名')) {
    // 公司名称变更
    await pool.query(
      'UPDATE companies SET company_name_cn = ? WHERE id = ?',
      [newValue, companyId]
    );
    console.log(`🏢 公司名称变更: ${oldValue} → ${newValue}`);
  } else if (changeContent && changeContent.includes('注册资本')) {
    // 注册资本变更
    const amount = parseFloat(newValue.replace(/[万元]/g, '')) * 10000; // 转换为元
    await pool.query(
      'UPDATE companies SET registered_capital = ? WHERE id = ?',
      [amount, companyId]
    );
    console.log(`💰 注册资本变更: ${oldValue} → ${newValue}`);
  } else if (changeContent && changeContent.includes('注册地址')) {
    // 注册地址变更
    await pool.query(
      'UPDATE companies SET registration_address = ? WHERE id = ?',
      [newValue, companyId]
    );
    console.log(`📍 注册地址变更: ${oldValue} → ${newValue}`);
  } else if (changeContent && changeContent.includes('英文名称')) {
    // 英文名称变更
    await pool.query(
      'UPDATE companies SET company_name_en = ? WHERE id = ?',
      [newValue, companyId]
    );
    console.log(`🔤 英文名称变更: ${oldValue} → ${newValue}`);
  } else {
    console.log(`⚠️ 未识别的基础信息变更类型: ${changeContent}`);
  }
}

// 应用高管信息变更
async function applyExecutiveChange(companyId, oldValue, newValue) {
  console.log(`👔 应用高管信息变更: 公司ID ${companyId}, ${oldValue} → ${newValue}`);

  if (newValue === '') {
    // 删除高管 - 设置结束日期
    const [personName, position] = oldValue.split(' - ');
    // 通过person_id查找记录
    const [persons] = await pool.query('SELECT id FROM persons WHERE name = ?', [personName]);
    if (persons.length > 0) {
      const personId = persons[0].id;
      await pool.query(
        'UPDATE employments SET end_date = NOW() WHERE company_id = ? AND person_id = ? AND position = ? AND end_date IS NULL',
        [companyId, personId, position]
      );
      console.log(`🗑️ 删除高管: ${personName} - ${position}`);
    }
  } else if (oldValue === '') {
    // 新增高管 - 插入新记录
    const [personName, position] = newValue.split(' - ');
    // 首先获取person_id
    const [persons] = await pool.query('SELECT id FROM persons WHERE name = ?', [personName]);
    const personId = persons.length > 0 ? persons[0].id : null;

    await pool.query(
      'INSERT INTO employments (person_id, company_id, position, start_date, created_by) VALUES (?, ?, ?, NOW(), 1)',
      [personId, companyId, position]
    );
    console.log(`➕ 新增高管: ${personName} - ${position}`);
  } else {
    // 职位变更 - 更新现有记录
    const [oldPersonName, oldPosition] = oldValue.split(' - ');
    const [newPersonName, newPosition] = newValue.split(' - ');

    if (oldPersonName === newPersonName) {
      // 同一人职位变更
      const [persons] = await pool.query('SELECT id FROM persons WHERE name = ?', [oldPersonName]);
      if (persons.length > 0) {
        const personId = persons[0].id;
        await pool.query(
          'UPDATE employments SET position = ? WHERE company_id = ? AND person_id = ? AND position = ? AND end_date IS NULL',
          [newPosition, companyId, personId, oldPosition]
        );
        console.log(`🔄 职位变更: ${oldPersonName} ${oldPosition} → ${newPosition}`);
      }
    }
  }
}

// 应用股东信息变更
async function applyShareholderChange(companyId, oldValue, newValue) {
  console.log(`💰 应用股东信息变更: 公司ID ${companyId}, ${oldValue} → ${newValue}`);

  if (newValue === '') {
    // 删除股东
    const [shareholderName, percentage] = oldValue.split(' - ');
    // 通过股东名称查找person_id
    const [persons] = await pool.query('SELECT id FROM persons WHERE name = ?', [shareholderName]);
    if (persons.length > 0) {
      const personId = persons[0].id;
      await pool.query(
        'UPDATE shareholdings SET is_active = 0, end_date = NOW() WHERE company_id = ? AND person_id = ? AND is_active = 1',
        [companyId, personId]
      );
      console.log(`🗑️ 删除股东: ${shareholderName} - ${percentage}`);
    }
  } else if (oldValue === '') {
    // 新增股东
    const [shareholderName, percentageStr] = newValue.split(' - ');
    const percentage = parseFloat(percentageStr.replace('%', ''));

    // 查找或创建股东
    let [persons] = await pool.query('SELECT id FROM persons WHERE name = ?', [shareholderName]);
    let personId;

    if (persons.length === 0) {
      // 创建新的股东记录
      const [result] = await pool.query(
        'INSERT INTO persons (name, id_type, id_number) VALUES (?, ?, ?)',
        [shareholderName, '营业执照', 'AUTO_' + Date.now()]
      );
      personId = result.insertId;
    } else {
      personId = persons[0].id;
    }

    // 插入股东记录
    await pool.query(
      'INSERT INTO shareholdings (person_id, company_id, investment_amount, percentage, start_date, created_by) VALUES (?, ?, ?, ?, NOW(), 1)',
      [personId, companyId, 0, percentage]
    );
    console.log(`➕ 新增股东: ${shareholderName} - ${percentage}%`);
  } else {
    // 股东持股比例变更
    const [oldShareholderName, oldPercentageStr] = oldValue.split(' - ');
    const [newShareholderName, newPercentageStr] = newValue.split(' - ');

    if (oldShareholderName === newShareholderName) {
      // 同一股东持股比例变更
      const newPercentage = parseFloat(newPercentageStr.replace('%', ''));
      const [persons] = await pool.query('SELECT id FROM persons WHERE name = ?', [oldShareholderName]);

      if (persons.length > 0) {
        const personId = persons[0].id;
        await pool.query(
          'UPDATE shareholdings SET percentage = ? WHERE company_id = ? AND person_id = ? AND is_active = 1',
          [newPercentage, companyId, personId]
        );
        console.log(`🔄 股东持股比例变更: ${oldShareholderName} ${oldPercentageStr} → ${newPercentageStr}`);
      }
    }
  }
}

// 应用对外投资变更
async function applyInvestmentChange(companyId, oldValue, newValue) {
  console.log(`🏭 应用对外投资变更: 公司ID ${companyId}, ${oldValue} → ${newValue}`);

  if (newValue === '') {
    // 删除对外投资
    const [investeeName, amountStr, percentageStr] = oldValue.split(' - ');
    // 查找被投资公司ID
    const [companies] = await pool.query('SELECT id FROM companies WHERE company_name_cn = ?', [investeeName]);
    if (companies.length > 0) {
      const investeeId = companies[0].id;
      await pool.query(
        'UPDATE investments SET is_active = 0 WHERE investor_id = ? AND investee_id = ? AND is_active = 1',
        [companyId, investeeId]
      );
      console.log(`🗑️ 删除对外投资: ${investeeName} - ${amountStr} - ${percentageStr}`);
    }
  } else if (oldValue === '') {
    // 新增对外投资
    const [investeeName, amountStr, percentageStr] = newValue.split(' - ');
    const amount = parseFloat(amountStr.replace(/[万元]/g, '')) * 10000; // 转换为元
    const percentage = parseFloat(percentageStr.replace('%', ''));

    // 查找被投资公司ID
    let [companies] = await pool.query('SELECT id FROM companies WHERE company_name_cn = ?', [investeeName]);
    let investeeId;

    if (companies.length === 0) {
      // 如果被投资公司不存在，创建一个基本记录
      const [result] = await pool.query(
        'INSERT INTO companies (company_name_cn, registered_capital, establishment_date) VALUES (?, ?, NOW())',
        [investeeName, amount]
      );
      investeeId = result.insertId;
    } else {
      investeeId = companies[0].id;
    }

    // 插入投资记录
    await pool.query(
      'INSERT INTO investments (investor_id, investee_id, investment_amount, ownership_percentage, investment_date, created_by) VALUES (?, ?, ?, ?, NOW(), 1)',
      [companyId, investeeId, amount, percentage]
    );
    console.log(`➕ 新增对外投资: ${investeeName} - ${amountStr} - ${percentage}%`);
  } else {
    // 投资信息变更
    const [oldInvesteeName, oldAmountStr, oldPercentageStr] = oldValue.split(' - ');
    const [newInvesteeName, newAmountStr, newPercentageStr] = newValue.split(' - ');

    if (oldInvesteeName === newInvesteeName) {
      // 同一被投资公司的投资金额或持股比例变更
      const newAmount = parseFloat(newAmountStr.replace(/[万元]/g, '')) * 10000;
      const newPercentage = parseFloat(newPercentageStr.replace('%', ''));

      const [companies] = await pool.query('SELECT id FROM companies WHERE company_name_cn = ?', [oldInvesteeName]);
      if (companies.length > 0) {
        const investeeId = companies[0].id;
        await pool.query(
          'UPDATE investments SET investment_amount = ?, ownership_percentage = ? WHERE investor_id = ? AND investee_id = ? AND is_active = 1',
          [newAmount, newPercentage, companyId, investeeId]
        );
        console.log(`🔄 投资信息变更: ${oldInvesteeName} ${oldAmountStr}/${oldPercentageStr} → ${newAmountStr}/${newPercentageStr}`);
      }
    }
  }
}

// 提交变更记录
app.post('/api/company-change-confirmation/submit', async (req, res) => {
  try {
    const {
      company_id,
      change_type,
      change_content,
      old_value,
      new_value,
      change_date,
      operator
    } = req.body;

    // 验证必填字段
    if (!company_id || !change_type || !change_content || !old_value || !new_value) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 插入变更记录
    const [result] = await pool.query(
      `INSERT INTO company_change_logs
       (company_id, change_type, change_content, old_value, new_value, change_date, operator, status, operate_date)
       VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', NOW())`,
      [company_id, change_type, change_content, old_value, new_value, change_date, operator]
    );

    console.log(`📝 新增变更记录: 公司ID ${company_id}, 类型 ${change_type}`);

    res.json({
      success: true,
      message: '变更记录提交成功',
      data: {
        id: result.insertId
      }
    });

  } catch (error) {
    console.error('提交变更记录失败:', error);
    res.status(500).json({
      success: false,
      message: '提交变更记录失败'
    });
  }
});

// 确认变更
app.post('/api/company-change-confirmation/confirm/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 首先获取变更记录详情
    const [changeRecords] = await pool.query(
      'SELECT * FROM company_change_logs WHERE id = ?',
      [id]
    );

    if (changeRecords.length === 0) {
      return res.status(404).json({
        success: false,
        message: '变更记录不存在'
      });
    }

    const changeRecord = changeRecords[0];

    // 根据变更类型应用实际的数据库更新
    await applyChangeToDatabase(changeRecord);

    // 更新变更记录状态为已确认
    const [result] = await pool.query(
      'UPDATE company_change_logs SET status = "confirmed", confirmed_by = 1, confirmed_at = NOW() WHERE id = ?',
      [id]
    );

    console.log(`✅ 确认变更记录 ID: ${id}, 类型: ${changeRecord.change_type}`);

    res.json({
      success: true,
      message: '确认成功'
    });
  } catch (error) {
    console.error('确认变更失败:', error);
    res.status(500).json({
      success: false,
      message: '确认变更失败'
    });
  }
});

// ==================== 用户认证 API ====================

// 用户登录API
app.post('/api/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    console.log('🔐 收到登录请求:', { username });

    // 验证必填字段
    if (!username || !password) {
      return res.status(400).json({
        success: false,
        message: '用户名和密码不能为空'
      });
    }

    // 查询用户信息
    const [userResult] = await pool.query(`
      SELECT
        u.id,
        u.username,
        u.password_hash,
        u.account_status,
        COALESCE(p.name, u.username) as realName,
        COALESCE(p.email, '') as email,
        COALESCE(p.phone, '') as phone,
        '' as department,
        '' as position,
        u.role_id as roleId,
        ur.name as roleName
      FROM users u
      LEFT JOIN persons p ON u.person_id = p.id
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      WHERE u.username = ? AND u.account_status = 'active'
    `, [username]);

    if (userResult.length === 0) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    const user = userResult[0];

    // 验证密码（简单验证，生产环境应使用bcrypt）
    const expectedPassword = Buffer.from(password).toString('base64');
    if (user.password_hash !== expectedPassword) {
      return res.status(401).json({
        success: false,
        message: '用户名或密码错误'
      });
    }

    // 获取用户权限
    const [permissionsResult] = await pool.query(`
      SELECT
        p.id,
        p.module,
        p.action,
        p.description
      FROM permissions p
      JOIN role_permissions rp ON p.id = rp.permission_id
      WHERE rp.role_id = ?
    `, [user.roleId]);

    // 构建返回数据
    const userData = {
      id: user.id,
      username: user.username,
      realName: user.realName,
      email: user.email,
      phone: user.phone,
      department: user.department,
      position: user.position,
      roleId: user.roleId,
      roleName: user.roleName,
      status: user.account_status
    };

    // 更新最后登录时间
    await pool.query(
      'UPDATE users SET last_login = NOW() WHERE id = ?',
      [user.id]
    );

    console.log('✅ 登录成功:', {
      userId: user.id,
      username: user.username,
      roleName: user.roleName,
      permissionCount: permissionsResult.length
    });

    res.json({
      success: true,
      message: '登录成功',
      data: {
        user: userData,
        permissions: permissionsResult
      }
    });

  } catch (error) {
    console.error('❌ 登录失败:', error);
    res.status(500).json({
      success: false,
      message: '登录失败，请稍后重试',
      error: error.message
    });
  }
});

// 验证token API（可选，用于检查登录状态）
app.get('/api/auth/verify', async (req, res) => {
  try {
    // 这里可以实现JWT token验证
    // 目前简化处理，直接返回成功
    res.json({
      success: true,
      message: '认证有效'
    });
  } catch (error) {
    console.error('❌ 认证验证失败:', error);
    res.status(401).json({
      success: false,
      message: '认证失败'
    });
  }
});

// ==================== 用户管理 API ====================

// 获取用户列表
app.get('/api/users', async (req, res) => {
  try {
    const { page = 1, pageSize = 10, username, realName, department, role, status } = req.query;
    console.log('获取用户列表参数:', { page, pageSize, username, realName, department, role, status });

    let whereConditions = [];
    let params = [];

    // 构建查询条件
    if (username) {
      whereConditions.push('u.username LIKE ?');
      params.push(`%${username}%`);
    }
    if (realName) {
      whereConditions.push('p.name LIKE ?');
      params.push(`%${realName}%`);
    }
    if (role) {
      whereConditions.push('u.role_id = ?');
      params.push(role);
    }
    if (status) {
      whereConditions.push('u.account_status = ?');
      params.push(status);
    }

    const whereClause = whereConditions.length > 0 ? 'WHERE ' + whereConditions.join(' AND ') : '';

    // 获取总数
    const [countResult] = await pool.query(`
      SELECT COUNT(*) as total
      FROM users u
      LEFT JOIN persons p ON u.person_id = p.id
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      ${whereClause}
    `, params);

    const total = countResult[0].total;

    // 获取分页数据
    const offset = (page - 1) * pageSize;
    const [users] = await pool.query(`
      SELECT
        u.id,
        u.username,
        COALESCE(p.name, u.username) as realName,
        COALESCE(p.email, '') as email,
        COALESCE(p.phone, '') as phone,
        '' as department,
        '' as position,
        u.role_id as roleId,
        ur.name as roleName,
        u.account_status as status,
        u.last_login as lastLoginTime,
        u.created_at as createdAt,
        u.created_by as createdBy
      FROM users u
      LEFT JOIN persons p ON u.person_id = p.id
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      ${whereClause}
      ORDER BY u.created_at DESC
      LIMIT ? OFFSET ?
    `, [...params, parseInt(pageSize), offset]);

    res.json({
      success: true,
      data: users,
      total: total,
      page: parseInt(page),
      pageSize: parseInt(pageSize)
    });

  } catch (error) {
    console.error('获取用户列表失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户列表失败',
      error: error.message
    });
  }
});

// 新增用户
app.post('/api/users', async (req, res) => {
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();
    console.log('新增用户事务开始');

    const {
      username,
      password,
      realName,
      email,
      phone,
      department,
      position,
      roleId,
      status = 'active',
      createdBy
    } = req.body;

    console.log('收到新增用户请求:', { username, realName, email, phone, department, position, roleId, status });

    // 验证必填字段
    if (!username || !password || !realName || !email || !roleId) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 检查用户名是否已存在
    const [existingUser] = await connection.query(
      'SELECT id FROM users WHERE username = ?',
      [username]
    );

    if (existingUser.length > 0) {
      return res.status(409).json({
        success: false,
        message: '用户名已存在'
      });
    }

    // 1. 先创建或查找person记录
    let personId = null;

    // 检查是否已有同名人员
    const [existingPerson] = await connection.query(
      'SELECT id FROM persons WHERE name = ? AND email = ?',
      [realName, email]
    );

    if (existingPerson.length > 0) {
      personId = existingPerson[0].id;
      // 更新person信息
      await connection.query(
        `UPDATE persons SET
          phone = ?, email = ?, updated_at = NOW(), updated_by = ?
        WHERE id = ?`,
        [phone || '', email, 1, personId]
      );
    } else {
      // 创建新的person记录
      const [personResult] = await connection.query(
        `INSERT INTO persons (
          name, email, phone, id_type, id_number,
          created_at, updated_at, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, NOW(), NOW(), ?, ?)`,
        [realName, email, phone || '', '身份证', `temp_${Date.now()}`, 1, 1]
      );
      personId = personResult.insertId;
    }

    // 2. 创建用户记录（简单密码哈希，生产环境应使用bcrypt）
    const passwordHash = Buffer.from(password).toString('base64'); // 简单编码，生产环境需要proper hashing

    const [userResult] = await connection.query(
      `INSERT INTO users (
        username, password_hash, person_id, role_id, account_status,
        created_at, updated_at, created_by
      ) VALUES (?, ?, ?, ?, ?, NOW(), NOW(), ?)`,
      [username, passwordHash, personId, roleId, status, 1]
    );

    await connection.commit();
    console.log('新增用户事务提交成功');

    res.json({
      success: true,
      message: '用户创建成功',
      data: {
        id: userResult.insertId,
        username,
        personId
      }
    });

  } catch (error) {
    await connection.rollback();
    console.error('新增用户失败:', error);
    res.status(500).json({
      success: false,
      message: '新增用户失败',
      error: error.message
    });
  } finally {
    connection.release();
  }
});

// 更新用户信息
app.put('/api/users/:id', async (req, res) => {
  const connection = await pool.getConnection();

  try {
    await connection.beginTransaction();
    console.log('更新用户事务开始');

    const { id } = req.params;
    const {
      realName,
      email,
      phone,
      department,
      position,
      roleId,
      status
    } = req.body;

    console.log('收到更新用户请求:', { id, realName, email, phone, department, position, roleId, status });

    // 验证必填字段
    if (!realName || !email || !roleId) {
      return res.status(400).json({
        success: false,
        message: '缺少必填字段'
      });
    }

    // 检查用户是否存在
    const [existingUser] = await connection.query(
      'SELECT id, person_id FROM users WHERE id = ?',
      [id]
    );

    if (existingUser.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    const personId = existingUser[0].person_id;

    // 1. 更新person记录（如果存在）
    if (personId) {
      await connection.query(
        `UPDATE persons SET
          name = ?, email = ?, phone = ?, updated_at = NOW(), updated_by = ?
        WHERE id = ?`,
        [realName, email, phone || '', 1, personId]
      );
    } else {
      // 如果没有关联的person记录，创建一个
      const [personResult] = await connection.query(
        `INSERT INTO persons (
          name, email, phone, id_type, id_number,
          created_at, updated_at, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, NOW(), NOW(), ?, ?)`,
        [realName, email, phone || '', '身份证', `temp_${Date.now()}`, 1, 1]
      );

      // 更新用户记录关联新的person_id
      await connection.query(
        'UPDATE users SET person_id = ? WHERE id = ?',
        [personResult.insertId, id]
      );
    }

    // 2. 更新用户记录
    await connection.query(
      `UPDATE users SET
        role_id = ?, account_status = ?, updated_at = NOW()
      WHERE id = ?`,
      [roleId, status || 'active', id]
    );

    await connection.commit();
    console.log('更新用户事务提交成功');

    res.json({
      success: true,
      message: '用户更新成功'
    });

  } catch (error) {
    await connection.rollback();
    console.error('更新用户失败:', error);
    res.status(500).json({
      success: false,
      message: '更新用户失败',
      error: error.message
    });
  } finally {
    connection.release();
  }
});

// 更新用户状态
app.put('/api/users/:id/status', async (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status || !['active', 'inactive', 'locked'].includes(status)) {
      return res.status(400).json({
        success: false,
        message: '无效的状态值'
      });
    }

    const [result] = await pool.query(
      'UPDATE users SET account_status = ?, updated_at = NOW() WHERE id = ?',
      [status, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      message: '用户状态更新成功'
    });

  } catch (error) {
    console.error('更新用户状态失败:', error);
    res.status(500).json({
      success: false,
      message: '更新用户状态失败',
      error: error.message
    });
  }
});

// 删除用户
app.delete('/api/users/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // 检查用户是否存在
    const [userCheck] = await pool.query('SELECT id FROM users WHERE id = ?', [id]);
    if (userCheck.length === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    // 删除用户（注意：这里不删除关联的person记录，因为可能被其他地方引用）
    const [result] = await pool.query('DELETE FROM users WHERE id = ?', [id]);

    res.json({
      success: true,
      message: '用户删除成功'
    });

  } catch (error) {
    console.error('删除用户失败:', error);
    res.status(500).json({
      success: false,
      message: '删除用户失败',
      error: error.message
    });
  }
});

// 重置用户密码
app.post('/api/users/:id/reset-password', async (req, res) => {
  try {
    const { id } = req.params;

    // 生成临时密码
    const tempPassword = Math.random().toString(36).slice(-8);
    const passwordHash = Buffer.from(tempPassword).toString('base64');

    const [result] = await pool.query(
      'UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?',
      [passwordHash, id]
    );

    if (result.affectedRows === 0) {
      return res.status(404).json({
        success: false,
        message: '用户不存在'
      });
    }

    res.json({
      success: true,
      message: '密码重置成功',
      tempPassword: tempPassword // 实际生产环境中应该通过邮件发送
    });

  } catch (error) {
    console.error('重置密码失败:', error);
    res.status(500).json({
      success: false,
      message: '重置密码失败',
      error: error.message
    });
  }
});

// 获取用户角色列表
app.get('/api/user-roles', async (req, res) => {
  try {
    const [roles] = await pool.query(`
      SELECT
        id,
        name,
        description,
        is_active as isActive
      FROM user_roles
      WHERE is_active = 1
      ORDER BY id
    `);

    res.json({
      success: true,
      data: roles
    });

  } catch (error) {
    console.error('获取用户角色失败:', error);
    res.status(500).json({
      success: false,
      message: '获取用户角色失败',
      error: error.message
    });
  }
});

// 启动服务器
const PORT = process.env.PORT || 8080;
app.listen(PORT, () => {
  console.log(`服务器运行在 http://localhost:${PORT}`);
  console.log(`也可以通过 http://127.0.0.1:${PORT} 访问`);
  console.log(`特别提示：要测试MySQL数据库连接（端口3306），请访问 http://localhost:${PORT}/api/mysql-3306-test`);
});
