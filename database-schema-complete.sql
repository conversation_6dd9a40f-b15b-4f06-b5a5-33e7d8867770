-- ============================================================================
-- 股权管理系统完整数据库结构 SQL
-- 版本: v1.0
-- 创建日期: 2025-06-23
-- 说明: 基于优化方案的完整数据库结构，包含所有表和关系
-- ============================================================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS stake_management_v2 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE stake_management_v2;

-- 启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- ============================================================================
-- 1. 基础数据表 (Base Data Tables)
-- ============================================================================

-- 业务板块表
CREATE TABLE business_segments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) UNIQUE NOT NULL COMMENT '业务板块名称',
  description TEXT COMMENT '描述',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_segment_name (name),
  INDEX idx_segment_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='业务板块表';

-- 地区表
CREATE TABLE regions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  type ENUM('国内', '海外') NOT NULL COMMENT '地区类型',
  name VARCHAR(100) NOT NULL COMMENT '地区名称',
  parent_id INT COMMENT '父级地区ID',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_region_type (type),
  INDEX idx_region_name (name),
  INDEX idx_region_parent (parent_id),
  FOREIGN KEY (parent_id) REFERENCES regions(id) ON DELETE SET NULL,
  UNIQUE KEY unique_type_name (type, name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='地区表';

-- 代理机构表
CREATE TABLE agencies (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) UNIQUE NOT NULL COMMENT '代理机构名称',
  region_id INT COMMENT '所属地区ID',
  contact_person VARCHAR(100) COMMENT '联系人',
  contact_method VARCHAR(100) COMMENT '联系方式',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  
  INDEX idx_agency_name (name),
  INDEX idx_agency_region (region_id),
  FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='代理机构表';

-- 年审更新状态表
CREATE TABLE annual_update_status (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) UNIQUE NOT NULL COMMENT '状态名称',
  description TEXT COMMENT '描述',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_annual_status_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='年审更新状态表';

-- 公司存续状况表
CREATE TABLE operation_status (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) UNIQUE NOT NULL COMMENT '状况名称',
  description TEXT COMMENT '描述',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_operation_status_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司存续状况表';

-- 实缴出资方式表
CREATE TABLE contribution_methods (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) UNIQUE NOT NULL COMMENT '出资方式名称',
  description TEXT COMMENT '描述',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_contribution_method_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='实缴出资方式表';

-- 公司变更类型表
CREATE TABLE change_types (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) UNIQUE NOT NULL COMMENT '变更类型名称',
  description TEXT COMMENT '描述',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_change_type_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司变更类型表';

-- ============================================================================
-- 2. 用户权限管理表 (User Management Tables)
-- ============================================================================

-- 用户角色表
CREATE TABLE user_roles (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) UNIQUE NOT NULL COMMENT '角色名称',
  description TEXT COMMENT '角色描述',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  INDEX idx_role_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色表';

-- 权限表
CREATE TABLE permissions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  module VARCHAR(50) NOT NULL COMMENT '模块名称',
  action VARCHAR(50) NOT NULL COMMENT '操作类型',
  description VARCHAR(255) COMMENT '权限描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  UNIQUE KEY unique_module_action (module, action),
  INDEX idx_permission_module (module)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 角色权限关联表
CREATE TABLE role_permissions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  role_id INT NOT NULL,
  permission_id INT NOT NULL,
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  
  FOREIGN KEY (role_id) REFERENCES user_roles(id) ON DELETE CASCADE,
  FOREIGN KEY (permission_id) REFERENCES permissions(id) ON DELETE CASCADE,
  UNIQUE KEY unique_role_permission (role_id, permission_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- ============================================================================
-- 3. 核心实体表 (Core Entity Tables)
-- ============================================================================

-- 人员主表
CREATE TABLE persons (
  id INT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL COMMENT '姓名',
  id_type ENUM('身份证', '护照', '其他') DEFAULT '身份证' COMMENT '证件类型',
  id_number VARCHAR(100) UNIQUE NOT NULL COMMENT '证件号码',
  phone VARCHAR(20) COMMENT '联系电话',
  email VARCHAR(100) COMMENT '邮箱地址',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by INT COMMENT '创建用户ID',
  updated_by INT COMMENT '更新用户ID',
  
  INDEX idx_person_name (name),
  INDEX idx_person_id_number (id_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='人员主表';

-- 系统用户表
CREATE TABLE users (
  id INT AUTO_INCREMENT PRIMARY KEY,
  username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
  password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
  person_id INT COMMENT '关联人员ID',
  role_id INT NOT NULL COMMENT '角色ID',
  account_status ENUM('active', 'inactive', 'locked') DEFAULT 'active' COMMENT '账户状态',
  last_login TIMESTAMP NULL COMMENT '最后登录时间',
  failed_login_attempts INT DEFAULT 0 COMMENT '失败登录次数',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by INT COMMENT '创建用户ID',
  
  INDEX idx_user_username (username),
  INDEX idx_user_status (account_status),
  FOREIGN KEY (person_id) REFERENCES persons(id) ON DELETE SET NULL,
  FOREIGN KEY (role_id) REFERENCES user_roles(id),
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统用户表';

-- 为persons表添加外键约束（在users表创建后）
ALTER TABLE persons 
ADD FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
ADD FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL;

-- 公司主表
CREATE TABLE companies (
  id INT AUTO_INCREMENT PRIMARY KEY,
  company_name_cn VARCHAR(255) NOT NULL COMMENT '公司中文名',
  company_name_en VARCHAR(255) NOT NULL COMMENT '公司英文名',
  company_code VARCHAR(50) UNIQUE COMMENT '公司代码',
  registered_capital DECIMAL(15,4) COMMENT '注册资本（万元）',
  establish_date DATE COMMENT '设立日期',
  business_segment_id INT COMMENT '业务板块ID',
  region_id INT COMMENT '地区ID',
  agency_id INT COMMENT '代理机构ID',
  annual_update_status_id INT COMMENT '年审更新状态ID',
  operation_status_id INT COMMENT '存续状况ID',
  registered_address TEXT COMMENT '注册地址',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by INT COMMENT '创建用户ID',
  updated_by INT COMMENT '更新用户ID',
  
  UNIQUE INDEX idx_company_name (company_name_cn, company_name_en),
  INDEX idx_company_segment (business_segment_id),
  INDEX idx_company_region (region_id),
  INDEX idx_company_code (company_code),
  FOREIGN KEY (business_segment_id) REFERENCES business_segments(id) ON DELETE SET NULL,
  FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE SET NULL,
  FOREIGN KEY (agency_id) REFERENCES agencies(id) ON DELETE SET NULL,
  FOREIGN KEY (annual_update_status_id) REFERENCES annual_update_status(id) ON DELETE SET NULL,
  FOREIGN KEY (operation_status_id) REFERENCES operation_status(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司主表';

-- ============================================================================
-- 4. 关系表 (Relationship Tables)
-- ============================================================================

-- 股东关系表
CREATE TABLE shareholdings (
  id INT AUTO_INCREMENT PRIMARY KEY,
  person_id INT NOT NULL COMMENT '股东人员ID（名义股东）',
  company_id INT NOT NULL COMMENT '公司ID',
  investment_amount DECIMAL(15,4) COMMENT '投资金额（万元）',
  percentage DECIMAL(5,2) COMMENT '持股比例（%）',
  is_proxy BOOLEAN DEFAULT FALSE COMMENT '是否为代持',
  actual_shareholder_id INT COMMENT '实际股东ID（代持情况下）',
  start_date DATE COMMENT '开始日期',
  end_date DATE COMMENT '结束日期',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by INT COMMENT '创建用户ID',
  updated_by INT COMMENT '更新用户ID',

  INDEX idx_shareholding_person (person_id),
  INDEX idx_shareholding_company (company_id),
  INDEX idx_shareholding_actual (actual_shareholder_id),
  INDEX idx_shareholding_active (is_active),
  FOREIGN KEY (person_id) REFERENCES persons(id) ON DELETE CASCADE,
  FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
  FOREIGN KEY (actual_shareholder_id) REFERENCES persons(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股东关系表';

-- 任职关系表
CREATE TABLE employments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  person_id INT NOT NULL COMMENT '人员ID',
  company_id INT NOT NULL COMMENT '公司ID',
  position VARCHAR(100) NOT NULL COMMENT '职位',
  start_date DATE COMMENT '开始日期',
  end_date DATE COMMENT '结束日期',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by INT COMMENT '创建用户ID',
  updated_by INT COMMENT '更新用户ID',

  INDEX idx_employment_person (person_id),
  INDEX idx_employment_company (company_id),
  INDEX idx_employment_position (position),
  INDEX idx_employment_active (is_active),
  FOREIGN KEY (person_id) REFERENCES persons(id) ON DELETE CASCADE,
  FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任职关系表';

-- 对外投资关系表
CREATE TABLE investments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  investor_company_id INT NOT NULL COMMENT '投资方公司ID',
  investee_company_id INT COMMENT '被投资方公司ID（如果在系统内）',
  investee_company_name VARCHAR(255) NOT NULL COMMENT '被投资方公司名称',
  investment_amount DECIMAL(15,4) COMMENT '投资金额（万元）',
  percentage DECIMAL(5,2) COMMENT '持股比例（%）',
  start_date DATE COMMENT '投资日期',
  end_date DATE COMMENT '结束日期',
  is_active BOOLEAN DEFAULT TRUE COMMENT '是否有效',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by INT COMMENT '创建用户ID',
  updated_by INT COMMENT '更新用户ID',

  INDEX idx_investment_investor (investor_company_id),
  INDEX idx_investment_investee (investee_company_id),
  INDEX idx_investment_active (is_active),
  FOREIGN KEY (investor_company_id) REFERENCES companies(id) ON DELETE CASCADE,
  FOREIGN KEY (investee_company_id) REFERENCES companies(id) ON DELETE SET NULL,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='对外投资关系表';

-- ============================================================================
-- 5. 财务信息表 (Financial Tables)
-- ============================================================================

-- 财务信息表
CREATE TABLE company_finances (
  id INT AUTO_INCREMENT PRIMARY KEY,
  company_id INT NOT NULL COMMENT '公司ID',
  year VARCHAR(4) NOT NULL COMMENT '年份',
  total_assets DECIMAL(15,4) DEFAULT 0 COMMENT '资产总额（万元）',
  total_liabilities DECIMAL(15,4) DEFAULT 0 COMMENT '负债总额（万元）',
  total_equity DECIMAL(15,4) DEFAULT 0 COMMENT '所有者权益总额（万元）',
  business_income DECIMAL(15,4) DEFAULT 0 COMMENT '营业收入（万元）',
  main_business_income DECIMAL(15,4) DEFAULT 0 COMMENT '主营业务收入（万元）',
  profit_before_tax DECIMAL(15,4) DEFAULT 0 COMMENT '利润总额（万元）',
  net_profit DECIMAL(15,4) DEFAULT 0 COMMENT '净利润（万元）',
  tax_payable DECIMAL(15,4) DEFAULT 0 COMMENT '纳税总额（万元）',
  remarks TEXT COMMENT '备注',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by INT COMMENT '创建用户ID',
  updated_by INT COMMENT '更新用户ID',

  INDEX idx_finance_company (company_id),
  INDEX idx_finance_year (year),
  FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL,
  UNIQUE KEY unique_company_year (company_id, year)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='财务信息表';

-- 股东出资信息表
CREATE TABLE shareholder_contributions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  finance_id INT NOT NULL COMMENT '财务信息ID',
  person_id INT NOT NULL COMMENT '股东人员ID',
  contribution_amount DECIMAL(15,4) DEFAULT 0 COMMENT '实缴出资额（万元）',
  contribution_method_id INT COMMENT '实缴出资方式ID',
  contribution_time DATE COMMENT '实缴出资时间',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

  INDEX idx_contribution_finance (finance_id),
  INDEX idx_contribution_person (person_id),
  FOREIGN KEY (finance_id) REFERENCES company_finances(id) ON DELETE CASCADE,
  FOREIGN KEY (person_id) REFERENCES persons(id) ON DELETE CASCADE,
  FOREIGN KEY (contribution_method_id) REFERENCES contribution_methods(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股东出资信息表';

-- ============================================================================
-- 6. 档案管理表 (Archive Management Tables)
-- ============================================================================

-- 档案更新规范主表
CREATE TABLE archive_update_rules (
  id INT AUTO_INCREMENT PRIMARY KEY,
  change_type_id INT NOT NULL COMMENT '变更类型ID',
  operation_name VARCHAR(255) NOT NULL COMMENT '变更操作规范名称',
  allowed_fields JSON COMMENT '允许变更的字段（JSON数组）',
  description TEXT COMMENT '规范描述',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  created_by INT COMMENT '创建用户ID',
  updated_by INT COMMENT '更新用户ID',

  INDEX idx_archive_rule_type (change_type_id),
  INDEX idx_archive_rule_name (operation_name),
  FOREIGN KEY (change_type_id) REFERENCES change_types(id) ON DELETE CASCADE,
  FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
  FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案更新规范主表';

-- 档案规范适用业务板块关联表
CREATE TABLE archive_rule_segments (
  id INT AUTO_INCREMENT PRIMARY KEY,
  rule_id INT NOT NULL COMMENT '规范ID',
  segment_id INT NOT NULL COMMENT '业务板块ID',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  INDEX idx_rule_segment_rule (rule_id),
  INDEX idx_rule_segment_segment (segment_id),
  FOREIGN KEY (rule_id) REFERENCES archive_update_rules(id) ON DELETE CASCADE,
  FOREIGN KEY (segment_id) REFERENCES business_segments(id) ON DELETE CASCADE,
  UNIQUE KEY unique_rule_segment (rule_id, segment_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案规范适用业务板块关联表';

-- 档案规范适用地区关联表
CREATE TABLE archive_rule_regions (
  id INT AUTO_INCREMENT PRIMARY KEY,
  rule_id INT NOT NULL COMMENT '规范ID',
  region_type ENUM('国内', '海外') NOT NULL COMMENT '地区类型',
  region_id INT COMMENT '具体地区ID，NULL表示适用该类型所有地区',
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  INDEX idx_rule_region_rule (rule_id),
  INDEX idx_rule_region_type (region_type),
  INDEX idx_rule_region_region (region_id),
  FOREIGN KEY (rule_id) REFERENCES archive_update_rules(id) ON DELETE CASCADE,
  FOREIGN KEY (region_id) REFERENCES regions(id) ON DELETE CASCADE,
  UNIQUE KEY unique_rule_region (rule_id, region_type, region_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案规范适用地区关联表';

-- 档案规范参考文件表
CREATE TABLE archive_rule_files (
  id INT AUTO_INCREMENT PRIMARY KEY,
  rule_id INT NOT NULL COMMENT '规范ID',
  file_name VARCHAR(255) NOT NULL COMMENT '文件名称',
  original_name VARCHAR(255) NOT NULL COMMENT '原始文件名',
  file_path VARCHAR(500) COMMENT '文件路径',
  file_size BIGINT COMMENT '文件大小（字节）',
  file_type VARCHAR(50) COMMENT '文件类型',
  uploaded_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  uploaded_by INT COMMENT '上传用户ID',

  INDEX idx_rule_file_rule (rule_id),
  INDEX idx_rule_file_name (file_name),
  FOREIGN KEY (rule_id) REFERENCES archive_update_rules(id) ON DELETE CASCADE,
  FOREIGN KEY (uploaded_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案规范参考文件表';

-- ============================================================================
-- 7. 审计日志表 (Audit Log Tables)
-- ============================================================================

-- 公司信息变更记录表
CREATE TABLE company_change_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  company_id INT NOT NULL COMMENT '公司ID',
  change_type_id INT NOT NULL COMMENT '变更类型ID',
  field_name VARCHAR(100) COMMENT '变更字段名',
  old_value TEXT COMMENT '原值',
  new_value TEXT COMMENT '新值',
  change_reason TEXT COMMENT '变更原因',
  changed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  changed_by INT NOT NULL COMMENT '操作用户ID',

  INDEX idx_change_log_company (company_id),
  INDEX idx_change_log_type (change_type_id),
  INDEX idx_change_log_time (changed_at),
  INDEX idx_change_log_user (changed_by),
  FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
  FOREIGN KEY (change_type_id) REFERENCES change_types(id) ON DELETE CASCADE,
  FOREIGN KEY (changed_by) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司信息变更记录表';

-- 系统操作日志表
CREATE TABLE system_operation_logs (
  id INT AUTO_INCREMENT PRIMARY KEY,
  user_id INT COMMENT '操作用户ID',
  module VARCHAR(50) NOT NULL COMMENT '操作模块',
  action VARCHAR(50) NOT NULL COMMENT '操作类型',
  target_type VARCHAR(50) COMMENT '目标类型',
  target_id INT COMMENT '目标ID',
  description TEXT COMMENT '操作描述',
  ip_address VARCHAR(45) COMMENT '操作IP地址',
  user_agent TEXT COMMENT '用户代理',
  operated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,

  INDEX idx_operation_log_user (user_id),
  INDEX idx_operation_log_module (module),
  INDEX idx_operation_log_action (action),
  INDEX idx_operation_log_time (operated_at),
  INDEX idx_operation_log_target (target_type, target_id),
  FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统操作日志表';

-- ============================================================================
-- 8. 初始化基础数据 (Initial Data)
-- ============================================================================

-- 插入年审更新状态
INSERT INTO annual_update_status (name, description) VALUES
('管年审', '代理机构负责年审更新'),
('不管年审', '公司自行处理年审'),
('待确认', '年审责任方待确认');

-- 插入公司存续状况
INSERT INTO operation_status (name, description) VALUES
('正常经营', '公司正常运营中'),
('注销', '公司已注销'),
('吊销', '营业执照被吊销');

-- 插入实缴出资方式
INSERT INTO contribution_methods (name, description) VALUES
('货币', '现金出资'),
('实物', '实物资产出资'),
('知识产权', '专利、商标等知识产权出资'),
('土地使用权', '土地使用权出资'),
('股权', '其他公司股权出资'),
('债权', '债权转股权'),
('其他', '其他合法出资方式');

-- 插入公司变更类型
INSERT INTO change_types (name, description) VALUES
('基础信息变更', '公司名称、地址、注册资本等基础信息变更'),
('高管变更', '董事、监事、经理等高管人员变更'),
('股东变更', '股东及股权结构变更'),
('投资变更', '对外投资信息变更');

-- 插入用户角色
INSERT INTO user_roles (name, description) VALUES
('超级管理员', '拥有系统所有权限'),
('普通管理员', '拥有业务数据管理权限'),
('只读用户', '仅能查看数据，无编辑权限'),
('财务专员', '专门负责财务数据管理');

-- 插入权限数据
INSERT INTO permissions (module, action, description) VALUES
('company', 'view', '查看公司信息'),
('company', 'create', '新增公司'),
('company', 'edit', '编辑公司信息'),
('company', 'delete', '删除公司'),
('shareholder', 'view', '查看股东信息'),
('shareholder', 'create', '新增股东'),
('shareholder', 'edit', '编辑股东信息'),
('shareholder', 'delete', '删除股东'),
('employment', 'view', '查看任职档案'),
('employment', 'create', '新增任职记录'),
('employment', 'edit', '编辑任职记录'),
('employment', 'delete', '删除任职记录'),
('finance', 'view', '查看财务信息'),
('finance', 'create', '新增财务数据'),
('finance', 'edit', '编辑财务数据'),
('finance', 'delete', '删除财务数据'),
('archive', 'view', '查看档案规范'),
('archive', 'create', '新增档案规范'),
('archive', 'edit', '编辑档案规范'),
('archive', 'delete', '删除档案规范'),
('user', 'view', '查看用户管理'),
('user', 'create', '新增用户'),
('user', 'edit', '编辑用户信息'),
('user', 'delete', '删除用户'),
('system', 'config', '系统配置管理');

-- ============================================================================
-- 9. 创建视图 (Views) - 便于查询
-- ============================================================================

-- 股东信息视图
CREATE VIEW v_shareholdings AS
SELECT
  s.id,
  s.company_id,
  c.company_name_cn,
  s.person_id,
  p.name as shareholder_name,
  s.investment_amount,
  s.percentage,
  s.is_proxy,
  s.actual_shareholder_id,
  ap.name as actual_shareholder_name,
  s.start_date,
  s.end_date,
  s.is_active,
  s.created_at
FROM shareholdings s
LEFT JOIN companies c ON s.company_id = c.id
LEFT JOIN persons p ON s.person_id = p.id
LEFT JOIN persons ap ON s.actual_shareholder_id = ap.id;

-- 任职信息视图
CREATE VIEW v_employments AS
SELECT
  e.id,
  e.company_id,
  c.company_name_cn,
  e.person_id,
  p.name as person_name,
  e.position,
  e.start_date,
  e.end_date,
  e.is_active,
  e.created_at
FROM employments e
LEFT JOIN companies c ON e.company_id = c.id
LEFT JOIN persons p ON e.person_id = p.id;

-- 公司完整信息视图
CREATE VIEW v_companies_full AS
SELECT
  c.*,
  bs.name as business_segment_name,
  r.name as region_name,
  r.type as region_type,
  a.name as agency_name,
  aus.name as annual_update_status_name,
  os.name as operation_status_name
FROM companies c
LEFT JOIN business_segments bs ON c.business_segment_id = bs.id
LEFT JOIN regions r ON c.region_id = r.id
LEFT JOIN agencies a ON c.agency_id = a.id
LEFT JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
LEFT JOIN operation_status os ON c.operation_status_id = os.id;

-- ============================================================================
-- 完成数据库结构创建
-- ============================================================================
