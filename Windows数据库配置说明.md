# Windows数据库配置说明

## 📋 配置概述

在Windows系统上部署股权管理系统时，需要修改数据库连接配置以适应本地环境。

## 🔧 配置步骤

### 1. 找到配置文件
打开文件：`server/index.js`

### 2. 找到数据库配置部分
在文件中找到以下代码（大约在第40行）：

```javascript
const dbConfig = {
  host: 'SKiP-MBP.local', // 更新为正确的MySQL主机名
  port: 3306,       // 默认MySQL端口
  user: 'txuser',    // 使用提供的MySQL用户名
  password: 'txpassword', // 使用提供的MySQL密码
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock', // 添加socket路径
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  debug: false // 关闭调试模式以减少日志输出
};
```

### 3. 修改为Windows配置
将上述配置替换为：

```javascript
const dbConfig = {
  host: 'localhost',     // Windows本地主机
  port: 3306,           // 默认MySQL端口
  user: 'root',         // MySQL用户名
  password: 'Yiwill@2025', // MySQL密码
  database: 'stake_management_v2',
  // socketPath: '/tmp/mysql.sock', // Windows不需要此配置，注释掉
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  debug: false // 关闭调试模式以减少日志输出
};
```

## 🔐 安全配置（推荐）

如果您使用了专用数据库用户（通过数据库初始化脚本创建），请使用以下配置：

```javascript
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'stake_user',        // 专用用户名
  password: 'stake_password_2025', // 专用密码
  database: 'stake_management_v2',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  debug: false
};
```

## 📝 配置参数说明

| 参数 | 说明 | Windows默认值 |
|------|------|---------------|
| `host` | 数据库主机地址 | `localhost` |
| `port` | 数据库端口 | `3306` |
| `user` | 数据库用户名 | `root` 或 `stake_user` |
| `password` | 数据库密码 | 您设置的密码 |
| `database` | 数据库名称 | `stake_management_v2` |
| `socketPath` | Unix套接字路径 | Windows不需要，应注释掉 |

## 🔍 验证配置

修改配置后，可以通过以下方式验证：

1. 启动后端服务：
   ```
   npm run server
   ```

2. 查看控制台输出：
   - 如果显示"数据库连接成功!"，说明配置正确
   - 如果显示连接错误，请检查配置参数

3. 测试数据库连接：
   访问：http://localhost:8080/api/mysql-3306-test

## ⚠️ 常见问题

### 问题1：连接被拒绝
**原因**：MySQL服务未启动
**解决**：
1. 打开"服务"管理器
2. 找到MySQL服务并启动

### 问题2：用户名或密码错误
**原因**：配置的用户名或密码不正确
**解决**：
1. 确认MySQL安装时设置的密码
2. 或重新设置MySQL密码

### 问题3：数据库不存在
**原因**：未创建数据库或数据库名称错误
**解决**：
1. 运行数据库初始化脚本
2. 或手动创建数据库

## 🛠️ 手动创建数据库

如果自动初始化失败，可以手动创建：

```sql
-- 连接MySQL
mysql -u root -p

-- 创建数据库
CREATE DATABASE stake_management_v2 CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户（可选）
CREATE USER 'stake_user'@'localhost' IDENTIFIED BY 'stake_password_2025';
GRANT ALL PRIVILEGES ON stake_management_v2.* TO 'stake_user'@'localhost';
FLUSH PRIVILEGES;

-- 退出
EXIT;
```

## 📋 配置检查清单

完成配置后，请确认：

- [ ] `host` 设置为 `localhost`
- [ ] `port` 设置为 `3306`
- [ ] `user` 和 `password` 与MySQL设置一致
- [ ] `database` 设置为 `stake_management_v2`
- [ ] `socketPath` 已注释掉或删除
- [ ] MySQL服务正在运行
- [ ] 数据库已创建
- [ ] 后端服务可以正常启动

## 🔄 配置模板

### 基础配置模板（使用root用户）
```javascript
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'root',
  password: '您的MySQL密码',
  database: 'stake_management_v2',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  debug: false
};
```

### 安全配置模板（使用专用用户）
```javascript
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'stake_user',
  password: 'stake_password_2025',
  database: 'stake_management_v2',
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0,
  debug: false
};
```

---

**注意**：修改配置文件后，需要重启后端服务才能生效。
