-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (arm64)
--
-- Host: localhost    Database: stake_management_v2
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `archive_rule_regions`
--

DROP TABLE IF EXISTS `archive_rule_regions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `archive_rule_regions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `rule_id` int NOT NULL COMMENT '规范ID',
  `region_type` enum('国内','海外') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地区类型',
  `region_id` int DEFAULT NULL COMMENT '具体地区ID，NULL表示适用该类型所有地区',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_rule_region` (`rule_id`,`region_type`,`region_id`),
  KEY `idx_rule_region_rule` (`rule_id`),
  KEY `idx_rule_region_type` (`region_type`),
  KEY `idx_rule_region_region` (`region_id`),
  CONSTRAINT `archive_rule_regions_ibfk_1` FOREIGN KEY (`rule_id`) REFERENCES `archive_update_rules` (`id`) ON DELETE CASCADE,
  CONSTRAINT `archive_rule_regions_ibfk_2` FOREIGN KEY (`region_id`) REFERENCES `regions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=45 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案规范适用地区关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `archive_rule_regions`
--

LOCK TABLES `archive_rule_regions` WRITE;
/*!40000 ALTER TABLE `archive_rule_regions` DISABLE KEYS */;
INSERT INTO `archive_rule_regions` VALUES (1,1,'国内',1,'2025-06-24 08:24:00'),(2,1,'国内',2,'2025-06-24 08:24:00'),(3,2,'国内',2,'2025-06-24 08:24:00'),(4,2,'国内',3,'2025-06-24 08:24:00'),(5,3,'国内',3,'2025-06-24 08:24:00'),(6,3,'国内',4,'2025-06-24 08:24:00'),(7,4,'国内',4,'2025-06-24 08:24:00'),(8,5,'国内',5,'2025-06-24 08:24:00'),(9,6,'国内',6,'2025-06-24 08:24:00'),(10,6,'海外',7,'2025-06-24 08:24:00'),(11,6,'海外',8,'2025-06-24 08:24:00'),(12,7,'海外',7,'2025-06-24 08:24:00'),(13,7,'海外',8,'2025-06-24 08:24:00'),(14,7,'国内',9,'2025-06-24 08:24:00'),(15,8,'海外',8,'2025-06-24 08:24:00'),(16,9,'国内',9,'2025-06-24 08:24:00'),(17,9,'国内',10,'2025-06-24 08:24:00'),(18,10,'国内',10,'2025-06-24 08:24:00'),(19,10,'国内',11,'2025-06-24 08:24:00'),(20,11,'国内',11,'2025-06-24 08:24:00'),(21,12,'国内',12,'2025-06-24 08:24:00'),(23,14,'国内',2,'2025-06-24 08:24:00'),(24,14,'国内',3,'2025-06-24 08:24:00'),(25,14,'国内',4,'2025-06-24 08:24:00'),(26,15,'国内',3,'2025-06-24 08:24:00'),(27,15,'国内',4,'2025-06-24 08:24:00'),(28,16,'国内',4,'2025-06-24 08:24:00'),(29,16,'国内',5,'2025-06-24 08:24:00'),(30,16,'国内',6,'2025-06-24 08:24:00'),(31,17,'国内',5,'2025-06-24 08:24:00'),(32,18,'国内',6,'2025-06-24 08:24:00'),(33,19,'海外',7,'2025-06-24 08:24:00'),(34,19,'海外',8,'2025-06-24 08:24:00'),(35,20,'海外',8,'2025-06-24 08:24:00'),(36,21,'国内',9,'2025-06-24 08:24:00'),(37,21,'国内',10,'2025-06-24 08:24:00'),(38,21,'国内',11,'2025-06-24 08:24:00'),(39,22,'国内',10,'2025-06-24 08:24:00'),(40,22,'国内',11,'2025-06-24 08:24:00'),(41,22,'国内',12,'2025-06-24 08:24:00'),(42,23,'国内',11,'2025-06-24 08:24:00'),(43,23,'国内',12,'2025-06-24 08:24:00'),(44,24,'国内',12,'2025-06-24 08:24:00');
/*!40000 ALTER TABLE `archive_rule_regions` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 12:34:31
