-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (arm64)
--
-- Host: localhost    Database: stake_management_v2
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `archive_rule_segments`
--

DROP TABLE IF EXISTS `archive_rule_segments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `archive_rule_segments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `rule_id` int NOT NULL COMMENT '规范ID',
  `segment_id` int NOT NULL COMMENT '业务板块ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_rule_segment` (`rule_id`,`segment_id`),
  KEY `idx_rule_segment_rule` (`rule_id`),
  KEY `idx_rule_segment_segment` (`segment_id`),
  CONSTRAINT `archive_rule_segments_ibfk_1` FOREIGN KEY (`rule_id`) REFERENCES `archive_update_rules` (`id`) ON DELETE CASCADE,
  CONSTRAINT `archive_rule_segments_ibfk_2` FOREIGN KEY (`segment_id`) REFERENCES `business_segments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=110 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案规范适用业务板块关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `archive_rule_segments`
--

LOCK TABLES `archive_rule_segments` WRITE;
/*!40000 ALTER TABLE `archive_rule_segments` DISABLE KEYS */;
INSERT INTO `archive_rule_segments` VALUES (1,1,1,'2025-06-24 08:23:30'),(2,1,2,'2025-06-24 08:23:30'),(3,1,3,'2025-06-24 08:23:30'),(4,1,4,'2025-06-24 08:23:30'),(5,2,2,'2025-06-24 08:23:30'),(6,2,3,'2025-06-24 08:23:30'),(7,2,4,'2025-06-24 08:23:30'),(8,3,3,'2025-06-24 08:23:30'),(9,3,4,'2025-06-24 08:23:30'),(10,3,5,'2025-06-24 08:23:30'),(11,4,4,'2025-06-24 08:23:30'),(12,4,5,'2025-06-24 08:23:30'),(13,5,5,'2025-06-24 08:23:30'),(14,5,6,'2025-06-24 08:23:30'),(15,5,7,'2025-06-24 08:23:30'),(16,5,8,'2025-06-24 08:23:30'),(17,6,6,'2025-06-24 08:23:30'),(18,6,7,'2025-06-24 08:23:30'),(19,6,8,'2025-06-24 08:23:30'),(20,6,9,'2025-06-24 08:23:30'),(21,7,7,'2025-06-24 08:23:30'),(22,7,8,'2025-06-24 08:23:30'),(23,7,9,'2025-06-24 08:23:30'),(24,8,8,'2025-06-24 08:23:30'),(25,8,9,'2025-06-24 08:23:30'),(26,9,9,'2025-06-24 08:23:30'),(27,9,10,'2025-06-24 08:23:30'),(28,9,11,'2025-06-24 08:23:30'),(29,10,10,'2025-06-24 08:23:30'),(30,10,11,'2025-06-24 08:23:30'),(31,11,11,'2025-06-24 08:23:30'),(32,11,12,'2025-06-24 08:23:30'),(33,11,13,'2025-06-24 08:23:30'),(34,12,12,'2025-06-24 08:23:30'),(35,12,13,'2025-06-24 08:23:30'),(44,3,6,'2025-06-24 08:24:00'),(47,4,6,'2025-06-24 08:24:00'),(48,4,7,'2025-06-24 08:24:00'),(58,8,10,'2025-06-24 08:24:00'),(59,8,11,'2025-06-24 08:24:00'),(63,9,12,'2025-06-24 08:24:00'),(66,10,12,'2025-06-24 08:24:00'),(74,14,14,'2025-06-24 08:24:00'),(75,14,15,'2025-06-24 08:24:00'),(76,14,16,'2025-06-24 08:24:00'),(77,15,15,'2025-06-24 08:24:00'),(78,15,16,'2025-06-24 08:24:00'),(79,16,16,'2025-06-24 08:24:00'),(80,16,17,'2025-06-24 08:24:00'),(81,16,18,'2025-06-24 08:24:00'),(82,16,19,'2025-06-24 08:24:00'),(83,17,17,'2025-06-24 08:24:00'),(84,17,18,'2025-06-24 08:24:00'),(85,18,18,'2025-06-24 08:24:00'),(86,18,19,'2025-06-24 08:24:00'),(87,18,20,'2025-06-24 08:24:00'),(88,18,21,'2025-06-24 08:24:00'),(89,19,19,'2025-06-24 08:24:00'),(90,19,20,'2025-06-24 08:24:00'),(91,19,21,'2025-06-24 08:24:00'),(92,19,1,'2025-06-24 08:24:00'),(93,20,20,'2025-06-24 08:24:00'),(94,20,21,'2025-06-24 08:24:00'),(95,20,1,'2025-06-24 08:24:00'),(96,20,2,'2025-06-24 08:24:00'),(97,21,21,'2025-06-24 08:24:00'),(98,21,1,'2025-06-24 08:24:00'),(99,21,2,'2025-06-24 08:24:00'),(100,21,3,'2025-06-24 08:24:00'),(101,22,1,'2025-06-24 08:24:00'),(102,22,2,'2025-06-24 08:24:00'),(103,22,3,'2025-06-24 08:24:00'),(104,22,4,'2025-06-24 08:24:00'),(105,23,2,'2025-06-24 08:24:00'),(106,23,3,'2025-06-24 08:24:00'),(107,24,3,'2025-06-24 08:24:00'),(108,24,4,'2025-06-24 08:24:00'),(109,24,5,'2025-06-24 08:24:00');
/*!40000 ALTER TABLE `archive_rule_segments` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 12:34:32
