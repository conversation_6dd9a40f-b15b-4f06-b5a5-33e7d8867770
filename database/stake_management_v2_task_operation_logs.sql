-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (arm64)
--
-- Host: localhost    Database: stake_management_v2
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `task_operation_logs`
--

DROP TABLE IF EXISTS `task_operation_logs`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `task_operation_logs` (
  `id` int NOT NULL AUTO_INCREMENT,
  `task_id` int NOT NULL COMMENT '任务ID',
  `operation_type` enum('创建','更新','删除','核实','状态变更') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型',
  `old_status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '原状态',
  `new_status` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '新状态',
  `operation_content` text COLLATE utf8mb4_unicode_ci COMMENT '操作内容',
  `operation_reason` text COLLATE utf8mb4_unicode_ci COMMENT '操作原因',
  `operated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `operated_by` int DEFAULT NULL COMMENT '操作用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_task_log_task` (`task_id`),
  KEY `idx_task_log_type` (`operation_type`),
  KEY `idx_task_log_time` (`operated_at`),
  KEY `operated_by` (`operated_by`),
  CONSTRAINT `task_operation_logs_ibfk_1` FOREIGN KEY (`task_id`) REFERENCES `pending_tasks` (`id`) ON DELETE CASCADE,
  CONSTRAINT `task_operation_logs_ibfk_2` FOREIGN KEY (`operated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务操作日志表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `task_operation_logs`
--

LOCK TABLES `task_operation_logs` WRITE;
/*!40000 ALTER TABLE `task_operation_logs` DISABLE KEYS */;
/*!40000 ALTER TABLE `task_operation_logs` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 12:34:31
