-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (arm64)
--
-- Host: localhost    Database: stake_management_v2
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `role_permissions`
--

DROP TABLE IF EXISTS `role_permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `role_permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `role_id` int NOT NULL,
  `permission_id` int NOT NULL,
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_role_permission` (`role_id`,`permission_id`),
  KEY `permission_id` (`permission_id`),
  CONSTRAINT `role_permissions_ibfk_1` FOREIGN KEY (`role_id`) REFERENCES `user_roles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `role_permissions_ibfk_2` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=58 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `role_permissions`
--

LOCK TABLES `role_permissions` WRITE;
/*!40000 ALTER TABLE `role_permissions` DISABLE KEYS */;
INSERT INTO `role_permissions` VALUES (1,1,1,'2025-06-24 08:25:06'),(2,1,2,'2025-06-24 08:25:06'),(3,1,3,'2025-06-24 08:25:06'),(4,1,4,'2025-06-24 08:25:06'),(5,1,5,'2025-06-24 08:25:06'),(6,1,6,'2025-06-24 08:25:06'),(7,1,7,'2025-06-24 08:25:06'),(8,1,8,'2025-06-24 08:25:06'),(9,1,9,'2025-06-24 08:25:06'),(10,1,10,'2025-06-24 08:25:06'),(11,1,11,'2025-06-24 08:25:06'),(12,1,12,'2025-06-24 08:25:06'),(13,1,13,'2025-06-24 08:25:06'),(14,1,14,'2025-06-24 08:25:06'),(15,1,15,'2025-06-24 08:25:06'),(16,1,16,'2025-06-24 08:25:06'),(17,1,17,'2025-06-24 08:25:06'),(18,1,18,'2025-06-24 08:25:06'),(19,1,19,'2025-06-24 08:25:06'),(20,1,20,'2025-06-24 08:25:06'),(21,1,21,'2025-06-24 08:25:06'),(22,1,22,'2025-06-24 08:25:06'),(23,1,23,'2025-06-24 08:25:06'),(24,1,24,'2025-06-24 08:25:06'),(25,1,25,'2025-06-24 08:25:06'),(26,2,2,'2025-06-24 08:25:06'),(27,2,3,'2025-06-24 08:25:06'),(28,2,4,'2025-06-24 08:25:06'),(29,2,6,'2025-06-24 08:25:06'),(30,2,7,'2025-06-24 08:25:06'),(31,2,8,'2025-06-24 08:25:06'),(32,2,10,'2025-06-24 08:25:06'),(33,2,11,'2025-06-24 08:25:06'),(34,2,12,'2025-06-24 08:25:06'),(35,2,14,'2025-06-24 08:25:06'),(36,2,15,'2025-06-24 08:25:06'),(37,2,16,'2025-06-24 08:25:06'),(38,2,18,'2025-06-24 08:25:06'),(39,2,19,'2025-06-24 08:25:06'),(40,2,20,'2025-06-24 08:25:06'),(41,2,22,'2025-06-24 08:25:06'),(42,2,23,'2025-06-24 08:25:06'),(43,2,24,'2025-06-24 08:25:06'),(44,3,1,'2025-06-24 08:25:06'),(45,3,6,'2025-06-24 08:25:06'),(46,3,11,'2025-06-24 08:25:06'),(47,3,16,'2025-06-24 08:25:06'),(48,3,21,'2025-06-24 08:25:06'),(49,4,1,'2025-06-24 08:25:06'),(50,4,4,'2025-06-24 08:25:06'),(51,4,7,'2025-06-24 08:25:06'),(52,4,10,'2025-06-24 08:25:06'),(53,4,13,'2025-06-24 08:25:06'),(54,4,16,'2025-06-24 08:25:06'),(55,4,19,'2025-06-24 08:25:06'),(56,4,22,'2025-06-24 08:25:06'),(57,4,25,'2025-06-24 08:25:06');
/*!40000 ALTER TABLE `role_permissions` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 12:34:31
