-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (arm64)
--
-- Host: localhost    Database: stake_management_v2
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `users`
--

DROP TABLE IF EXISTS `users`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `users` (
  `id` int NOT NULL AUTO_INCREMENT,
  `username` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户名',
  `password_hash` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '密码哈希',
  `person_id` int DEFAULT NULL COMMENT '关联人员ID',
  `role_id` int NOT NULL COMMENT '角色ID',
  `account_status` enum('active','inactive','locked') COLLATE utf8mb4_unicode_ci DEFAULT 'active' COMMENT '账户状态',
  `last_login` timestamp NULL DEFAULT NULL COMMENT '最后登录时间',
  `failed_login_attempts` int DEFAULT '0' COMMENT '失败登录次数',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL COMMENT '创建用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  KEY `idx_user_username` (`username`),
  KEY `idx_user_status` (`account_status`),
  KEY `person_id` (`person_id`),
  KEY `role_id` (`role_id`),
  KEY `created_by` (`created_by`),
  CONSTRAINT `users_ibfk_1` FOREIGN KEY (`person_id`) REFERENCES `persons` (`id`) ON DELETE SET NULL,
  CONSTRAINT `users_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `user_roles` (`id`),
  CONSTRAINT `users_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统用户表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `users`
--

LOCK TABLES `users` WRITE;
/*!40000 ALTER TABLE `users` DISABLE KEYS */;
INSERT INTO `users` VALUES (1,'migration_user','temp_hash',NULL,1,'active',NULL,0,'2025-06-24 07:28:46','2025-06-24 07:28:46',NULL),(3,'admin','admin_hash',NULL,1,'active',NULL,0,'2025-06-24 08:21:50','2025-06-24 08:21:50',NULL),(4,'manager1','manager1_hash',NULL,2,'active',NULL,0,'2025-06-24 08:21:50','2025-06-24 08:21:50',NULL),(5,'manager2','manager2_hash',NULL,2,'active',NULL,0,'2025-06-24 08:21:50','2025-06-24 08:21:50',NULL),(6,'user1','user1_hash',NULL,3,'active',NULL,0,'2025-06-24 08:21:50','2025-06-24 08:21:50',NULL),(7,'user2','user2_hash',NULL,3,'active',NULL,0,'2025-06-24 08:21:50','2025-06-24 08:21:50',NULL),(8,'finance1','finance1_hash',NULL,4,'active',NULL,0,'2025-06-24 08:21:50','2025-06-24 08:21:50',NULL),(9,'finance2','finance2_hash',NULL,4,'active',NULL,0,'2025-06-24 08:21:50','2025-06-24 08:21:50',NULL),(10,'readonly1','readonly1_hash',NULL,3,'active',NULL,0,'2025-06-24 08:21:50','2025-06-24 08:21:50',NULL),(11,'readonly2','readonly2_hash',NULL,3,'active',NULL,0,'2025-06-24 08:21:50','2025-06-24 08:21:50',NULL),(12,'test_user','test_hash',NULL,3,'inactive',NULL,0,'2025-06-24 08:21:50','2025-06-24 08:21:50',NULL);
/*!40000 ALTER TABLE `users` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 12:34:32
