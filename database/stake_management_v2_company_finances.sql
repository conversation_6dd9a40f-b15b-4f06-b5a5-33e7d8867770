-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (arm64)
--
-- Host: localhost    Database: stake_management_v2
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `company_finances`
--

DROP TABLE IF EXISTS `company_finances`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `company_finances` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_id` int NOT NULL COMMENT '公司ID',
  `year` varchar(4) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '年份',
  `total_assets` decimal(15,4) DEFAULT '0.0000' COMMENT '资产总额（万元）',
  `total_liabilities` decimal(15,4) DEFAULT '0.0000' COMMENT '负债总额（万元）',
  `total_equity` decimal(15,4) DEFAULT '0.0000' COMMENT '所有者权益总额（万元）',
  `business_income` decimal(15,4) DEFAULT '0.0000' COMMENT '营业收入（万元）',
  `main_business_income` decimal(15,4) DEFAULT '0.0000' COMMENT '主营业务收入（万元）',
  `profit_before_tax` decimal(15,4) DEFAULT '0.0000' COMMENT '利润总额（万元）',
  `net_profit` decimal(15,4) DEFAULT '0.0000' COMMENT '净利润（万元）',
  `tax_payable` decimal(15,4) DEFAULT '0.0000' COMMENT '纳税总额（万元）',
  `remarks` text COLLATE utf8mb4_unicode_ci COMMENT '备注',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL COMMENT '创建用户ID',
  `updated_by` int DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_company_year` (`company_id`,`year`),
  KEY `idx_finance_company` (`company_id`),
  KEY `idx_finance_year` (`year`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `company_finances_ibfk_1` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `company_finances_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `company_finances_ibfk_3` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=122 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='财务信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `company_finances`
--

LOCK TABLES `company_finances` WRITE;
/*!40000 ALTER TABLE `company_finances` DISABLE KEYS */;
/*!40000 ALTER TABLE `company_finances` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 12:34:31
