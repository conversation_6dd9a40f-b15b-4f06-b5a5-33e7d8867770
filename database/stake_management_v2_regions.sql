-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (arm64)
--
-- Host: localhost    Database: stake_management_v2
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `regions`
--

DROP TABLE IF EXISTS `regions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `regions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `type` enum('国内','海外') COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地区类型',
  `name` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地区名称',
  `parent_id` int DEFAULT NULL COMMENT '父级地区ID',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_type_name` (`type`,`name`),
  KEY `idx_region_type` (`type`),
  KEY `idx_region_name` (`name`),
  KEY `idx_region_parent` (`parent_id`),
  CONSTRAINT `regions_ibfk_1` FOREIGN KEY (`parent_id`) REFERENCES `regions` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='地区表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `regions`
--

LOCK TABLES `regions` WRITE;
/*!40000 ALTER TABLE `regions` DISABLE KEYS */;
INSERT INTO `regions` VALUES (1,'国内','华南',NULL,1,'2025-06-17 10:41:32','2025-06-24 07:28:46'),(2,'国内','华东',NULL,1,'2025-06-17 10:41:32','2025-06-24 07:28:46'),(3,'国内','华北',NULL,1,'2025-06-17 10:41:32','2025-06-24 07:28:46'),(4,'国内','西南',NULL,1,'2025-06-17 10:41:32','2025-06-24 07:28:46'),(5,'国内','东北',NULL,1,'2025-06-17 10:41:32','2025-06-24 07:28:46'),(6,'国内','广州',NULL,1,'2025-06-22 08:45:44','2025-06-24 07:28:46'),(7,'海外','亚太',NULL,1,'2025-06-22 08:45:44','2025-06-24 07:28:46'),(8,'海外','美国',NULL,1,'2025-06-22 08:45:44','2025-06-24 07:28:46'),(9,'国内','深圳',NULL,1,'2025-06-22 08:45:44','2025-06-24 07:28:46'),(10,'国内','上海',NULL,1,'2025-06-22 08:45:44','2025-06-24 07:28:46'),(11,'国内','三亚',NULL,1,'2025-06-22 08:46:20','2025-06-24 07:28:46'),(12,'国内','北京',NULL,1,'2025-06-22 08:48:14','2025-06-24 07:28:46');
/*!40000 ALTER TABLE `regions` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 12:34:31
