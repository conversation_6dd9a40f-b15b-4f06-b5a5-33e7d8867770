-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (arm64)
--
-- Host: localhost    Database: stake_management_v2
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `shareholder_contributions`
--

DROP TABLE IF EXISTS `shareholder_contributions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shareholder_contributions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `finance_id` int NOT NULL COMMENT '财务信息ID',
  `person_id` int NOT NULL COMMENT '股东人员ID',
  `contribution_amount` decimal(15,4) DEFAULT '0.0000' COMMENT '实缴出资额（万元）',
  `contribution_method_id` int DEFAULT NULL COMMENT '实缴出资方式ID',
  `contribution_time` date DEFAULT NULL COMMENT '实缴出资时间',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_contribution_finance` (`finance_id`),
  KEY `idx_contribution_person` (`person_id`),
  KEY `contribution_method_id` (`contribution_method_id`),
  CONSTRAINT `shareholder_contributions_ibfk_1` FOREIGN KEY (`finance_id`) REFERENCES `company_finances` (`id`) ON DELETE CASCADE,
  CONSTRAINT `shareholder_contributions_ibfk_2` FOREIGN KEY (`person_id`) REFERENCES `persons` (`id`) ON DELETE CASCADE,
  CONSTRAINT `shareholder_contributions_ibfk_3` FOREIGN KEY (`contribution_method_id`) REFERENCES `contribution_methods` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=31 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股东出资信息表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shareholder_contributions`
--

LOCK TABLES `shareholder_contributions` WRITE;
/*!40000 ALTER TABLE `shareholder_contributions` DISABLE KEYS */;
/*!40000 ALTER TABLE `shareholder_contributions` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 12:34:32
