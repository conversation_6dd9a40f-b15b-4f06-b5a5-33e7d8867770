-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (arm64)
--
-- Host: localhost    Database: stake_management_v2
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `shareholdings`
--

DROP TABLE IF EXISTS `shareholdings`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `shareholdings` (
  `id` int NOT NULL AUTO_INCREMENT,
  `person_id` int NOT NULL COMMENT '股东人员ID（名义股东）',
  `company_id` int NOT NULL COMMENT '公司ID',
  `investment_amount` decimal(15,4) DEFAULT NULL COMMENT '投资金额（万元）',
  `percentage` decimal(5,2) DEFAULT NULL COMMENT '持股比例（%）',
  `is_proxy` tinyint(1) DEFAULT '0' COMMENT '是否为代持',
  `actual_shareholder_id` int DEFAULT NULL COMMENT '实际股东ID（代持情况下）',
  `start_date` date DEFAULT NULL COMMENT '开始日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否有效',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL COMMENT '创建用户ID',
  `updated_by` int DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_shareholding_person` (`person_id`),
  KEY `idx_shareholding_company` (`company_id`),
  KEY `idx_shareholding_actual` (`actual_shareholder_id`),
  KEY `idx_shareholding_active` (`is_active`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `shareholdings_ibfk_1` FOREIGN KEY (`person_id`) REFERENCES `persons` (`id`) ON DELETE CASCADE,
  CONSTRAINT `shareholdings_ibfk_2` FOREIGN KEY (`company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `shareholdings_ibfk_3` FOREIGN KEY (`actual_shareholder_id`) REFERENCES `persons` (`id`) ON DELETE SET NULL,
  CONSTRAINT `shareholdings_ibfk_4` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `shareholdings_ibfk_5` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=36 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='股东关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `shareholdings`
--

LOCK TABLES `shareholdings` WRITE;
/*!40000 ALTER TABLE `shareholdings` DISABLE KEYS */;
INSERT INTO `shareholdings` VALUES (30,53,43,3500.0000,35.00,0,NULL,'2023-01-01',NULL,1,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(31,54,43,2500.0000,25.00,1,55,'2023-01-01',NULL,1,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(32,56,44,3000.0000,60.00,0,NULL,'2023-01-01',NULL,1,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(33,57,44,1500.0000,30.00,0,NULL,'2023-01-01',NULL,1,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(34,58,45,1400.0000,70.00,0,NULL,'2023-01-01',NULL,1,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(35,59,45,600.0000,30.00,0,NULL,'2023-01-01',NULL,1,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL);
/*!40000 ALTER TABLE `shareholdings` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 12:34:31
