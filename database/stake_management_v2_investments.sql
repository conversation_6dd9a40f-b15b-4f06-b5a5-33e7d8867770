-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (arm64)
--
-- Host: localhost    Database: stake_management_v2
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `investments`
--

DROP TABLE IF EXISTS `investments`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `investments` (
  `id` int NOT NULL AUTO_INCREMENT,
  `investor_company_id` int NOT NULL COMMENT '投资方公司ID',
  `investee_company_id` int DEFAULT NULL COMMENT '被投资方公司ID（如果在系统内）',
  `investee_company_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '被投资方公司名称',
  `investment_amount` decimal(15,4) DEFAULT NULL COMMENT '投资金额（万元）',
  `percentage` decimal(5,2) DEFAULT NULL COMMENT '持股比例（%）',
  `start_date` date DEFAULT NULL COMMENT '投资日期',
  `end_date` date DEFAULT NULL COMMENT '结束日期',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否有效',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL COMMENT '创建用户ID',
  `updated_by` int DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_investment_investor` (`investor_company_id`),
  KEY `idx_investment_investee` (`investee_company_id`),
  KEY `idx_investment_active` (`is_active`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `investments_ibfk_1` FOREIGN KEY (`investor_company_id`) REFERENCES `companies` (`id`) ON DELETE CASCADE,
  CONSTRAINT `investments_ibfk_2` FOREIGN KEY (`investee_company_id`) REFERENCES `companies` (`id`) ON DELETE SET NULL,
  CONSTRAINT `investments_ibfk_3` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `investments_ibfk_4` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=41 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='对外投资关系表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `investments`
--

LOCK TABLES `investments` WRITE;
/*!40000 ALTER TABLE `investments` DISABLE KEYS */;
INSERT INTO `investments` VALUES (35,44,43,'深圳科技有限公司',4000.0000,40.00,'2023-01-01',NULL,1,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(36,45,44,'北京投资控股有限公司',500.0000,10.00,'2023-01-01',NULL,1,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(37,43,46,'广州制造有限公司',5100.0000,51.00,'2023-06-01',NULL,1,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(38,43,47,'杭州互联网科技有限公司',3000.0000,30.00,'2023-06-01',NULL,1,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(39,46,NULL,'深圳制造子公司',4000.0000,80.00,'2023-08-01',NULL,1,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(40,47,NULL,'杭州技术子公司',5000.0000,100.00,'2023-08-01',NULL,1,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL);
/*!40000 ALTER TABLE `investments` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 12:34:31
