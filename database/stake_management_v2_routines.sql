-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (arm64)
--
-- Host: localhost    Database: stake_management_v2
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Temporary view structure for view `v_shareholdings`
--

DROP TABLE IF EXISTS `v_shareholdings`;
/*!50001 DROP VIEW IF EXISTS `v_shareholdings`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_shareholdings` AS SELECT 
 1 AS `id`,
 1 AS `company_id`,
 1 AS `company_name_cn`,
 1 AS `person_id`,
 1 AS `shareholder_name`,
 1 AS `investment_amount`,
 1 AS `percentage`,
 1 AS `is_proxy`,
 1 AS `actual_shareholder_id`,
 1 AS `actual_shareholder_name`,
 1 AS `start_date`,
 1 AS `end_date`,
 1 AS `is_active`,
 1 AS `created_at`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `v_companies_full`
--

DROP TABLE IF EXISTS `v_companies_full`;
/*!50001 DROP VIEW IF EXISTS `v_companies_full`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_companies_full` AS SELECT 
 1 AS `id`,
 1 AS `company_name_cn`,
 1 AS `company_name_en`,
 1 AS `company_code`,
 1 AS `registered_capital`,
 1 AS `establish_date`,
 1 AS `business_segment_id`,
 1 AS `region_id`,
 1 AS `agency_id`,
 1 AS `annual_update_status_id`,
 1 AS `operation_status_id`,
 1 AS `registered_address`,
 1 AS `created_at`,
 1 AS `updated_at`,
 1 AS `created_by`,
 1 AS `updated_by`,
 1 AS `business_segment_name`,
 1 AS `region_name`,
 1 AS `region_type`,
 1 AS `agency_name`,
 1 AS `annual_update_status_name`,
 1 AS `operation_status_name`*/;
SET character_set_client = @saved_cs_client;

--
-- Temporary view structure for view `v_employments`
--

DROP TABLE IF EXISTS `v_employments`;
/*!50001 DROP VIEW IF EXISTS `v_employments`*/;
SET @saved_cs_client     = @@character_set_client;
/*!50503 SET character_set_client = utf8mb4 */;
/*!50001 CREATE VIEW `v_employments` AS SELECT 
 1 AS `id`,
 1 AS `company_id`,
 1 AS `company_name_cn`,
 1 AS `person_id`,
 1 AS `person_name`,
 1 AS `position`,
 1 AS `start_date`,
 1 AS `end_date`,
 1 AS `is_active`,
 1 AS `created_at`*/;
SET character_set_client = @saved_cs_client;

--
-- Final view structure for view `v_shareholdings`
--

/*!50001 DROP VIEW IF EXISTS `v_shareholdings`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`txuser`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_shareholdings` AS select `s`.`id` AS `id`,`s`.`company_id` AS `company_id`,`c`.`company_name_cn` AS `company_name_cn`,`s`.`person_id` AS `person_id`,`p`.`name` AS `shareholder_name`,`s`.`investment_amount` AS `investment_amount`,`s`.`percentage` AS `percentage`,`s`.`is_proxy` AS `is_proxy`,`s`.`actual_shareholder_id` AS `actual_shareholder_id`,`ap`.`name` AS `actual_shareholder_name`,`s`.`start_date` AS `start_date`,`s`.`end_date` AS `end_date`,`s`.`is_active` AS `is_active`,`s`.`created_at` AS `created_at` from (((`shareholdings` `s` left join `companies` `c` on((`s`.`company_id` = `c`.`id`))) left join `persons` `p` on((`s`.`person_id` = `p`.`id`))) left join `persons` `ap` on((`s`.`actual_shareholder_id` = `ap`.`id`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `v_companies_full`
--

/*!50001 DROP VIEW IF EXISTS `v_companies_full`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`txuser`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_companies_full` AS select `c`.`id` AS `id`,`c`.`company_name_cn` AS `company_name_cn`,`c`.`company_name_en` AS `company_name_en`,`c`.`company_code` AS `company_code`,`c`.`registered_capital` AS `registered_capital`,`c`.`establish_date` AS `establish_date`,`c`.`business_segment_id` AS `business_segment_id`,`c`.`region_id` AS `region_id`,`c`.`agency_id` AS `agency_id`,`c`.`annual_update_status_id` AS `annual_update_status_id`,`c`.`operation_status_id` AS `operation_status_id`,`c`.`registered_address` AS `registered_address`,`c`.`created_at` AS `created_at`,`c`.`updated_at` AS `updated_at`,`c`.`created_by` AS `created_by`,`c`.`updated_by` AS `updated_by`,`bs`.`name` AS `business_segment_name`,`r`.`name` AS `region_name`,`r`.`type` AS `region_type`,`a`.`name` AS `agency_name`,`aus`.`name` AS `annual_update_status_name`,`os`.`name` AS `operation_status_name` from (((((`companies` `c` left join `business_segments` `bs` on((`c`.`business_segment_id` = `bs`.`id`))) left join `regions` `r` on((`c`.`region_id` = `r`.`id`))) left join `agencies` `a` on((`c`.`agency_id` = `a`.`id`))) left join `annual_update_status` `aus` on((`c`.`annual_update_status_id` = `aus`.`id`))) left join `operation_status` `os` on((`c`.`operation_status_id` = `os`.`id`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;

--
-- Final view structure for view `v_employments`
--

/*!50001 DROP VIEW IF EXISTS `v_employments`*/;
/*!50001 SET @saved_cs_client          = @@character_set_client */;
/*!50001 SET @saved_cs_results         = @@character_set_results */;
/*!50001 SET @saved_col_connection     = @@collation_connection */;
/*!50001 SET character_set_client      = utf8mb4 */;
/*!50001 SET character_set_results     = utf8mb4 */;
/*!50001 SET collation_connection      = utf8mb4_unicode_ci */;
/*!50001 CREATE ALGORITHM=UNDEFINED */
/*!50013 DEFINER=`txuser`@`localhost` SQL SECURITY DEFINER */
/*!50001 VIEW `v_employments` AS select `e`.`id` AS `id`,`e`.`company_id` AS `company_id`,`c`.`company_name_cn` AS `company_name_cn`,`e`.`person_id` AS `person_id`,`p`.`name` AS `person_name`,`e`.`position` AS `position`,`e`.`start_date` AS `start_date`,`e`.`end_date` AS `end_date`,`e`.`is_active` AS `is_active`,`e`.`created_at` AS `created_at` from ((`employments` `e` left join `companies` `c` on((`e`.`company_id` = `c`.`id`))) left join `persons` `p` on((`e`.`person_id` = `p`.`id`))) */;
/*!50001 SET character_set_client      = @saved_cs_client */;
/*!50001 SET character_set_results     = @saved_cs_results */;
/*!50001 SET collation_connection      = @saved_col_connection */;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 12:34:32
