-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (arm64)
--
-- Host: localhost    Database: stake_management_v2
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `archive_update_rules`
--

DROP TABLE IF EXISTS `archive_update_rules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `archive_update_rules` (
  `id` int NOT NULL AUTO_INCREMENT,
  `change_type_id` int NOT NULL COMMENT '变更类型ID',
  `operation_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '变更操作规范名称',
  `allowed_fields` json DEFAULT NULL COMMENT '允许变更的字段（JSON数组）',
  `description` text COLLATE utf8mb4_unicode_ci COMMENT '规范描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL COMMENT '创建用户ID',
  `updated_by` int DEFAULT NULL COMMENT '更新用户ID',
  `creator_name` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT '',
  `update_type` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `update_operation_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '',
  `change_steps` json DEFAULT NULL,
  `applicable_scope` json DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_archive_rule_type` (`change_type_id`),
  KEY `idx_archive_rule_name` (`operation_name`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `archive_update_rules_ibfk_1` FOREIGN KEY (`change_type_id`) REFERENCES `change_types` (`id`) ON DELETE CASCADE,
  CONSTRAINT `archive_update_rules_ibfk_2` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `archive_update_rules_ibfk_3` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案更新规范主表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `archive_update_rules`
--

LOCK TABLES `archive_update_rules` WRITE;
/*!40000 ALTER TABLE `archive_update_rules` DISABLE KEYS */;
INSERT INTO `archive_update_rules` VALUES (1,1,'公司名称变更规范','[\"company_name_cn\", \"company_name_en\"]','公司名称变更的标准操作流程','2025-06-24 08:23:29','2025-06-24 08:23:29',1,NULL,'','','',NULL,NULL),(2,1,'注册资本变更规范','[\"registered_capital\"]','注册资本变更的标准操作流程','2025-06-24 08:23:29','2025-06-24 08:23:29',1,NULL,'','','',NULL,NULL),(3,1,'注册地址变更规范','[\"registered_address\"]','注册地址变更的标准操作流程','2025-06-24 08:23:29','2025-06-24 08:23:29',1,NULL,'','','',NULL,NULL),(4,2,'董事长变更规范','[\"position\", \"person_id\"]','董事长变更的标准操作流程','2025-06-24 08:23:29','2025-06-24 08:23:29',1,NULL,'','','',NULL,NULL),(5,2,'总经理变更规范','[\"position\", \"person_id\"]','总经理变更的标准操作流程','2025-06-24 08:23:29','2025-06-24 08:23:29',1,NULL,'','','',NULL,NULL),(6,2,'财务总监变更规范','[\"position\", \"person_id\"]','财务总监变更的标准操作流程','2025-06-24 08:23:29','2025-06-24 08:23:29',1,NULL,'','','',NULL,NULL),(7,3,'股东增加规范','[\"person_id\", \"investment_amount\", \"percentage\"]','新增股东的标准操作流程','2025-06-24 08:23:29','2025-06-24 08:23:29',1,NULL,'','','',NULL,NULL),(8,3,'股东减少规范','[\"person_id\", \"end_date\"]','股东退出的标准操作流程','2025-06-24 08:23:29','2025-06-24 08:23:29',1,NULL,'','','',NULL,NULL),(9,3,'股权转让规范','[\"person_id\", \"percentage\", \"investment_amount\"]','股权转让的标准操作流程','2025-06-24 08:23:29','2025-06-24 08:23:29',1,NULL,'','','',NULL,NULL),(10,4,'对外投资新增规范','[\"investee_company_name\", \"investment_amount\", \"percentage\"]','新增对外投资的标准操作流程','2025-06-24 08:23:29','2025-06-24 08:23:29',1,NULL,'','','',NULL,NULL),(11,4,'对外投资变更规范','[\"investment_amount\", \"percentage\"]','对外投资变更的标准操作流程','2025-06-24 08:23:29','2025-06-24 08:23:29',1,NULL,'','','',NULL,NULL),(12,4,'对外投资退出规范','[\"end_date\"]','对外投资退出的标准操作流程','2025-06-24 08:23:30','2025-06-24 08:23:30',1,NULL,'','','',NULL,NULL),(14,1,'注册资本变更规范','[\"registered_capital\"]','注册资本变更的标准操作流程','2025-06-24 08:24:00','2025-06-24 08:24:00',1,NULL,'','','',NULL,NULL),(15,1,'注册地址变更规范','[\"registered_address\"]','注册地址变更的标准操作流程','2025-06-24 08:24:00','2025-06-24 08:24:00',1,NULL,'','','',NULL,NULL),(16,2,'董事长变更规范','[\"position\", \"person_id\"]','董事长变更的标准操作流程','2025-06-24 08:24:00','2025-06-24 08:24:00',1,NULL,'','','',NULL,NULL),(17,2,'总经理变更规范','[\"position\", \"person_id\"]','总经理变更的标准操作流程','2025-06-24 08:24:00','2025-06-24 08:24:00',1,NULL,'','','',NULL,NULL),(18,2,'财务总监变更规范','[\"position\", \"person_id\"]','财务总监变更的标准操作流程','2025-06-24 08:24:00','2025-06-24 08:24:00',1,NULL,'','','',NULL,NULL),(19,3,'股东增加规范','[\"person_id\", \"investment_amount\", \"percentage\"]','新增股东的标准操作流程','2025-06-24 08:24:00','2025-06-24 08:24:00',1,NULL,'','','',NULL,NULL),(20,3,'股东减少规范','[\"person_id\", \"end_date\"]','股东退出的标准操作流程','2025-06-24 08:24:00','2025-06-24 08:24:00',1,NULL,'','','',NULL,NULL),(21,3,'股权转让规范','[\"person_id\", \"percentage\", \"investment_amount\"]','股权转让的标准操作流程','2025-06-24 08:24:00','2025-06-24 08:24:00',1,NULL,'','','',NULL,NULL),(22,4,'对外投资新增规范','[\"investee_company_name\", \"investment_amount\", \"percentage\"]','新增对外投资的标准操作流程','2025-06-24 08:24:00','2025-06-24 08:24:00',1,NULL,'','','',NULL,NULL),(23,4,'对外投资变更规范','[\"investment_amount\", \"percentage\"]','对外投资变更的标准操作流程','2025-06-24 08:24:00','2025-06-24 08:24:00',1,NULL,'','','',NULL,NULL),(24,4,'对外投资退出规范','[\"end_date\"]','对外投资退出的标准操作流程','2025-06-24 08:24:00','2025-06-24 08:24:00',1,NULL,'','','',NULL,NULL);
/*!40000 ALTER TABLE `archive_update_rules` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 12:34:32
