-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (arm64)
--
-- Host: localhost    Database: stake_management_v2
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `companies`
--

DROP TABLE IF EXISTS `companies`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `companies` (
  `id` int NOT NULL AUTO_INCREMENT,
  `company_name_cn` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公司中文名',
  `company_name_en` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '公司英文名',
  `company_code` varchar(50) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '公司代码',
  `registered_capital` decimal(15,4) DEFAULT NULL COMMENT '注册资本（万元）',
  `establish_date` date DEFAULT NULL COMMENT '设立日期',
  `business_segment_id` int DEFAULT NULL COMMENT '业务板块ID',
  `region_id` int DEFAULT NULL COMMENT '地区ID',
  `agency_id` int DEFAULT NULL COMMENT '代理机构ID',
  `annual_update_status_id` int DEFAULT NULL COMMENT '年审更新状态ID',
  `operation_status_id` int DEFAULT NULL COMMENT '存续状况ID',
  `registered_address` text COLLATE utf8mb4_unicode_ci COMMENT '注册地址',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  `created_by` int DEFAULT NULL COMMENT '创建用户ID',
  `updated_by` int DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_company_name` (`company_name_cn`,`company_name_en`),
  UNIQUE KEY `company_code` (`company_code`),
  KEY `idx_company_segment` (`business_segment_id`),
  KEY `idx_company_region` (`region_id`),
  KEY `idx_company_code` (`company_code`),
  KEY `agency_id` (`agency_id`),
  KEY `annual_update_status_id` (`annual_update_status_id`),
  KEY `operation_status_id` (`operation_status_id`),
  KEY `created_by` (`created_by`),
  KEY `updated_by` (`updated_by`),
  CONSTRAINT `companies_ibfk_1` FOREIGN KEY (`business_segment_id`) REFERENCES `business_segments` (`id`) ON DELETE SET NULL,
  CONSTRAINT `companies_ibfk_2` FOREIGN KEY (`region_id`) REFERENCES `regions` (`id`) ON DELETE SET NULL,
  CONSTRAINT `companies_ibfk_3` FOREIGN KEY (`agency_id`) REFERENCES `agencies` (`id`) ON DELETE SET NULL,
  CONSTRAINT `companies_ibfk_4` FOREIGN KEY (`annual_update_status_id`) REFERENCES `annual_update_status` (`id`) ON DELETE SET NULL,
  CONSTRAINT `companies_ibfk_5` FOREIGN KEY (`operation_status_id`) REFERENCES `operation_status` (`id`) ON DELETE SET NULL,
  CONSTRAINT `companies_ibfk_6` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  CONSTRAINT `companies_ibfk_7` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB AUTO_INCREMENT=50 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='公司主表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `companies`
--

LOCK TABLES `companies` WRITE;
/*!40000 ALTER TABLE `companies` DISABLE KEYS */;
INSERT INTO `companies` VALUES (43,'深圳科技有限公司','SZTECH001',NULL,1028.0000,'2020-01-01',1,1,1,NULL,1,NULL,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(44,'北京投资控股有限公司','BJINV001',NULL,5711.0000,'2020-01-01',1,1,1,NULL,1,NULL,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(45,'上海金融服务有限公司','SHFIN001',NULL,2141.0000,'2020-01-01',1,1,1,NULL,1,NULL,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(46,'广州制造有限公司','GZMFG001',NULL,3060.0000,'2020-01-01',1,1,1,NULL,1,NULL,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(47,'杭州互联网科技有限公司','HZINT001',NULL,1170.0000,'2020-01-01',1,1,1,NULL,1,NULL,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(48,'成都新能源有限公司','CDENE001',NULL,2934.0000,'2020-01-01',1,1,1,NULL,1,NULL,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL),(49,'西安软件开发有限公司','XASOFT001',NULL,5547.0000,'2020-01-01',1,1,1,NULL,1,NULL,'2025-06-30 09:50:47','2025-06-30 09:50:47',1,NULL);
/*!40000 ALTER TABLE `companies` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 12:34:32
