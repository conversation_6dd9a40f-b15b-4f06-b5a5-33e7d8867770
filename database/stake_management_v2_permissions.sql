-- MySQL dump 10.13  Distrib 8.0.42, for macos15 (arm64)
--
-- Host: localhost    Database: stake_management_v2
-- ------------------------------------------------------
-- Server version	9.3.0

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `permissions`
--

DROP TABLE IF EXISTS `permissions`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `permissions` (
  `id` int NOT NULL AUTO_INCREMENT,
  `module` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '模块名称',
  `action` varchar(50) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '操作类型',
  `description` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '权限描述',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_module_action` (`module`,`action`),
  KEY `idx_permission_module` (`module`)
) ENGINE=InnoDB AUTO_INCREMENT=26 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `permissions`
--

LOCK TABLES `permissions` WRITE;
/*!40000 ALTER TABLE `permissions` DISABLE KEYS */;
INSERT INTO `permissions` VALUES (1,'company','view','查看公司信息','2025-06-24 07:26:59'),(2,'company','create','新增公司','2025-06-24 07:26:59'),(3,'company','edit','编辑公司信息','2025-06-24 07:26:59'),(4,'company','delete','删除公司','2025-06-24 07:26:59'),(5,'shareholder','view','查看股东信息','2025-06-24 07:26:59'),(6,'shareholder','create','新增股东','2025-06-24 07:26:59'),(7,'shareholder','edit','编辑股东信息','2025-06-24 07:26:59'),(8,'shareholder','delete','删除股东','2025-06-24 07:26:59'),(9,'employment','view','查看任职档案','2025-06-24 07:26:59'),(10,'employment','create','新增任职记录','2025-06-24 07:26:59'),(11,'employment','edit','编辑任职记录','2025-06-24 07:26:59'),(12,'employment','delete','删除任职记录','2025-06-24 07:26:59'),(13,'finance','view','查看财务信息','2025-06-24 07:26:59'),(14,'finance','create','新增财务数据','2025-06-24 07:26:59'),(15,'finance','edit','编辑财务数据','2025-06-24 07:26:59'),(16,'finance','delete','删除财务数据','2025-06-24 07:26:59'),(17,'archive','view','查看档案规范','2025-06-24 07:26:59'),(18,'archive','create','新增档案规范','2025-06-24 07:26:59'),(19,'archive','edit','编辑档案规范','2025-06-24 07:26:59'),(20,'archive','delete','删除档案规范','2025-06-24 07:26:59'),(21,'user','view','查看用户管理','2025-06-24 07:26:59'),(22,'user','create','新增用户','2025-06-24 07:26:59'),(23,'user','edit','编辑用户信息','2025-06-24 07:26:59'),(24,'user','delete','删除用户','2025-06-24 07:26:59'),(25,'system','config','系统配置管理','2025-06-24 07:26:59');
/*!40000 ALTER TABLE `permissions` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2025-07-05 12:34:32
