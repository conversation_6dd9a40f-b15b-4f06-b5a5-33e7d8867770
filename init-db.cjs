const fs = require('fs');
const mysql = require('mysql2/promise');

async function initDb() {
  try {
    const sql = fs.readFileSync('./server/init-db.sql', 'utf8');
    
    // 首先创建一个没有指定数据库的连接
    let connection = await mysql.createConnection({
      host: 'localhost',
      user: 'txuser',
      password: 'txpassword'
    });
    
    console.log('连接到MySQL成功');
    
    // 创建数据库（如果不存在）
    await connection.execute('CREATE DATABASE IF NOT EXISTS stake_management;');
    console.log('确保数据库存在');
    
    // 关闭当前连接
    await connection.end();
    
    // 创建一个新的连接，指定数据库
    connection = await mysql.createConnection({
      host: 'localhost',
      user: 'txuser',
      password: 'txpassword',
      database: 'stake_management'
    });
    
    console.log('连接到stake_management数据库成功');
    
    // 分割SQL语句并执行，跳过CREATE DATABASE和USE语句
    const queries = sql.split(';').filter(query => {
      const trimmedQuery = query.trim();
      return trimmedQuery !== '' && 
             !trimmedQuery.toUpperCase().includes('CREATE DATABASE') && 
             !trimmedQuery.toUpperCase().includes('USE ');
    });
    
    for (const query of queries) {
      if (query.trim()) {
        try {
          await connection.execute(query + ';');
          console.log('执行SQL语句成功:', query.trim().substring(0, 50) + '...');
        } catch (err) {
          console.error('执行SQL语句失败:', err.message);
          console.error('问题SQL:', query);
        }
      }
    }
    
    console.log('数据库初始化完成');
    await connection.end();
  } catch (error) {
    console.error('初始化数据库失败:', error);
    process.exit(1);
  }
}

initDb();