const axios = require('axios');

async function testLogin() {
  try {
    console.log('🔐 测试登录功能...');
    
    // 测试用admin用户登录
    const loginResponse = await axios.post('http://localhost:8080/api/auth/login', {
      username: 'admin',
      password: '123456'
    });

    console.log('📊 登录响应:', JSON.stringify(loginResponse.data, null, 2));

    if (loginResponse.data.success) {
      const userData = loginResponse.data.data.user;
      console.log('\n✅ 登录成功，用户数据:');
      console.log('ID:', userData.id);
      console.log('Username:', userData.username);
      console.log('Real Name:', userData.realName);
      console.log('Email:', userData.email);
      console.log('Role ID:', userData.roleId);
      console.log('Role Name:', userData.roleName);
      console.log('Status:', userData.status);
    }

  } catch (error) {
    console.error('❌ 登录测试失败:', error.response?.data || error.message);
  }
}

testLogin();
