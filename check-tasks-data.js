import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function checkTasksData() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 检查任务表结构
    console.log('\n📋 检查任务表结构:');
    const [columns] = await connection.execute('DESCRIBE tasks');
    console.log('tasks表字段:');
    columns.forEach(col => {
      console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(可空)' : '(非空)'} ${col.Key ? `[${col.Key}]` : ''}`);
    });

    // 检查任务数据
    console.log('\n📊 检查任务数据统计:');
    const [taskStats] = await connection.execute(`
      SELECT 
        task_status,
        COUNT(*) as count
      FROM tasks 
      GROUP BY task_status
      ORDER BY count DESC
    `);
    
    console.log('任务状态统计:');
    taskStats.forEach(stat => {
      console.log(`  ${stat.task_status}: ${stat.count} 个任务`);
    });

    // 检查任务类型统计
    console.log('\n📋 检查任务类型统计:');
    const [typeStats] = await connection.execute(`
      SELECT 
        task_type,
        COUNT(*) as count
      FROM tasks 
      GROUP BY task_type
      ORDER BY count DESC
    `);
    
    console.log('任务类型统计:');
    typeStats.forEach(stat => {
      console.log(`  ${stat.task_type}: ${stat.count} 个任务`);
    });

    // 检查年度统计
    console.log('\n📅 检查年度统计:');
    const [yearStats] = await connection.execute(`
      SELECT 
        year,
        COUNT(*) as count
      FROM tasks 
      GROUP BY year
      ORDER BY year DESC
    `);
    
    console.log('年度统计:');
    yearStats.forEach(stat => {
      console.log(`  ${stat.year}年: ${stat.count} 个任务`);
    });

    // 检查已完成任务
    console.log('\n✅ 检查已完成任务:');
    const [completedTasks] = await connection.execute(`
      SELECT 
        id,
        task_type,
        year,
        company_name,
        task_status,
        created_at
      FROM tasks 
      WHERE task_status = '已完成'
      ORDER BY created_at DESC
      LIMIT 10
    `);
    
    console.log('最近10个已完成任务:');
    completedTasks.forEach(task => {
      console.log(`  ID:${task.id} ${task.task_type} ${task.year}年 ${task.company_name} (${task.created_at})`);
    });

    // 检查公司数据
    console.log('\n🏢 检查公司数据:');
    const [companyStats] = await connection.execute(`
      SELECT 
        annual_update_status,
        COUNT(*) as count
      FROM companies 
      GROUP BY annual_update_status
      ORDER BY count DESC
    `);
    
    console.log('公司年审状态统计:');
    companyStats.forEach(stat => {
      console.log(`  ${stat.annual_update_status}: ${stat.count} 个公司`);
    });

    // 检查地址维护提醒
    console.log('\n📍 检查地址维护提醒:');
    const [addressStats] = await connection.execute(`
      SELECT 
        address_maintenance_reminder,
        COUNT(*) as count
      FROM companies 
      GROUP BY address_maintenance_reminder
      ORDER BY count DESC
    `);
    
    console.log('地址维护提醒统计:');
    addressStats.forEach(stat => {
      console.log(`  ${stat.address_maintenance_reminder}: ${stat.count} 个公司`);
    });

    // 检查总公司数
    const [totalCompanies] = await connection.execute('SELECT COUNT(*) as total FROM companies');
    console.log(`\n📈 总公司数: ${totalCompanies[0].total}`);

    // 检查总任务数
    const [totalTasks] = await connection.execute('SELECT COUNT(*) as total FROM tasks');
    console.log(`📈 总任务数: ${totalTasks[0].total}`);

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行检查
checkTasksData().catch(console.error);
