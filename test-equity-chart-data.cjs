const axios = require('axios');

async function testEquityChartData() {
  try {
    console.log('🔍 测试股权图数据结构...');
    
    // 获取公司列表
    const companiesResponse = await axios.get('http://localhost:8080/api/companies');
    const companies = companiesResponse.data.data || [];
    
    console.log(`📊 找到 ${companies.length} 个公司`);
    
    if (companies.length > 0) {
      // 选择第一个公司测试
      const testCompany = companies[0];
      console.log(`🏢 测试公司: ${testCompany.company_name_cn} (ID: ${testCompany.id})`);
      
      // 获取股权图数据
      const equityResponse = await axios.get(`http://localhost:8080/api/equity-chart/${testCompany.id}`);
      const equityData = equityResponse.data.data;
      
      console.log('\n📈 股权图数据结构:');
      console.log('中心公司:', equityData.centerCompany);
      console.log(`上游股东数量: ${equityData.upstreamShareholders.length}`);
      console.log(`下游投资数量: ${equityData.downstreamInvestments.length}`);
      
      console.log('\n👥 上游股东详情:');
      equityData.upstreamShareholders.forEach((shareholder, index) => {
        console.log(`${index + 1}. ${shareholder.shareholderName} (${shareholder.shareholderType})`);
        console.log(`   - 持股公司ID: ${shareholder.companyId}`);
        console.log(`   - 持股比例: ${shareholder.percentage}%`);
        console.log(`   - 是否代持: ${shareholder.isProxy}`);
        if (shareholder.isProxy) {
          console.log(`   - 实际股东ID: ${shareholder.actualShareholderId}`);
          console.log(`   - 实际股东名称: ${shareholder.actualShareholderName}`);
        }
        console.log(`   - 层级: ${shareholder.level}`);
        console.log('');
      });
      
      console.log('\n💰 下游投资详情:');
      equityData.downstreamInvestments.forEach((investment, index) => {
        console.log(`${index + 1}. 投资方ID: ${investment.investorId} -> 被投资方: ${investment.investeeName}`);
        console.log(`   - 投资比例: ${investment.percentage}%`);
        console.log(`   - 层级: ${investment.level}`);
        console.log('');
      });
      
      // 分析代持关系
      const proxyRelations = equityData.upstreamShareholders.filter(s => s.isProxy);
      if (proxyRelations.length > 0) {
        console.log('\n🔗 代持关系分析:');
        proxyRelations.forEach((proxy, index) => {
          console.log(`${index + 1}. 代持人: ${proxy.shareholderName}`);
          console.log(`   - 实际股东: ${proxy.actualShareholderName}`);
          console.log(`   - 持股公司: ${proxy.companyId}`);
          console.log(`   - 持股比例: ${proxy.percentage}%`);
          console.log('');
        });
      } else {
        console.log('\n✅ 当前公司无代持关系');
      }
      
      // 分析垂直分布分组
      const groupsByTarget = new Map();
      equityData.upstreamShareholders.forEach(shareholder => {
        const targetId = shareholder.companyId;
        if (!groupsByTarget.has(targetId)) {
          groupsByTarget.set(targetId, []);
        }
        groupsByTarget.get(targetId).push(shareholder);
      });
      
      console.log('\n📊 按目标公司分组分析:');
      groupsByTarget.forEach((shareholders, targetId) => {
        console.log(`目标公司ID ${targetId}: ${shareholders.length} 个股东`);
        shareholders.forEach(s => {
          console.log(`  - ${s.shareholderName} (${s.percentage}%)`);
        });
        console.log('');
      });
      
    } else {
      console.log('❌ 没有找到公司数据');
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
    if (error.response) {
      console.error('响应状态:', error.response.status);
      console.error('响应数据:', error.response.data);
    }
  }
}

testEquityChartData();
