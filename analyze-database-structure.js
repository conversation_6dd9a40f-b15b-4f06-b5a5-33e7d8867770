import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function analyzeDatabaseStructure() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 1. 查看所有表
    const [tables] = await connection.query(`
      SELECT TABLE_NAME, TABLE_COMMENT 
      FROM INFORMATION_SCHEMA.TABLES 
      WHERE TABLE_SCHEMA = 'stake_management_v2'
      ORDER BY TABLE_NAME
    `);
    
    console.log('\n📊 数据库表列表:');
    tables.forEach(table => {
      console.log(`  ${table.TABLE_NAME} - ${table.TABLE_COMMENT || '无注释'}`);
    });

    // 2. 查看任职档案相关表结构
    console.log('\n👥 任职档案相关表结构:');
    
    // persons表
    const [personsColumns] = await connection.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'stake_management_v2' AND TABLE_NAME = 'persons'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('\n  persons表字段:');
    personsColumns.forEach(col => {
      console.log(`    ${col.COLUMN_NAME} (${col.DATA_TYPE}) - ${col.COLUMN_COMMENT || '无注释'}`);
    });

    // employment_records表
    const [employmentColumns] = await connection.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'stake_management_v2' AND TABLE_NAME = 'employment_records'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('\n  employment_records表字段:');
    employmentColumns.forEach(col => {
      console.log(`    ${col.COLUMN_NAME} (${col.DATA_TYPE}) - ${col.COLUMN_COMMENT || '无注释'}`);
    });

    // 3. 查看公司变更相关表结构
    console.log('\n🏢 公司变更相关表结构:');
    
    // company_change_logs表
    const [changeLogsColumns] = await connection.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'stake_management_v2' AND TABLE_NAME = 'company_change_logs'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('\n  company_change_logs表字段:');
    changeLogsColumns.forEach(col => {
      console.log(`    ${col.COLUMN_NAME} (${col.DATA_TYPE}) - ${col.COLUMN_COMMENT || '无注释'}`);
    });

    // 4. 查看现有数据
    console.log('\n📋 现有数据统计:');
    
    const [companiesCount] = await connection.query('SELECT COUNT(*) as count FROM companies');
    console.log(`  公司数量: ${companiesCount[0].count}`);
    
    const [personsCount] = await connection.query('SELECT COUNT(*) as count FROM persons');
    console.log(`  人员数量: ${personsCount[0].count}`);
    
    const [employmentCount] = await connection.query('SELECT COUNT(*) as count FROM employment_records');
    console.log(`  任职记录数量: ${employmentCount[0].count}`);
    
    const [changeLogsCount] = await connection.query('SELECT COUNT(*) as count FROM company_change_logs');
    console.log(`  变更记录数量: ${changeLogsCount[0].count}`);

    // 5. 查看现有公司列表
    const [companies] = await connection.query(`
      SELECT id, company_name_cn, company_name_en, business_segment
      FROM companies
      ORDER BY id
    `);
    
    console.log('\n🏢 现有公司列表:');
    companies.forEach(company => {
      console.log(`  ID: ${company.id}, 中文名: ${company.company_name_cn}, 英文名: ${company.company_name_en || '无'}, 业务板块: ${company.business_segment || '无'}`);
    });

    // 6. 查看现有人员列表
    const [persons] = await connection.query(`
      SELECT id, name, id_number, phone, email
      FROM persons
      ORDER BY id
    `);
    
    console.log('\n👥 现有人员列表:');
    persons.forEach(person => {
      console.log(`  ID: ${person.id}, 姓名: ${person.name}, 身份证: ${person.id_number || '无'}, 电话: ${person.phone || '无'}`);
    });

  } catch (error) {
    console.error('❌ 分析失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行分析
analyzeDatabaseStructure();
