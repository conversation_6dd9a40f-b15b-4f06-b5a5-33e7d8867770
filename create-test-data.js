import mysql from 'mysql2/promise';

const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function createTestData() {
  console.log('🔄 开始创建测试数据...');
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 创建用户和角色测试数据
    console.log('\n👥 创建用户和角色测试数据...');
    
    // 插入测试用户
    const testUsers = [
      ['admin', 'admin_hash', 1, 'active'],
      ['manager1', 'manager1_hash', 2, 'active'],
      ['manager2', 'manager2_hash', 2, 'active'],
      ['user1', 'user1_hash', 3, 'active'],
      ['user2', 'user2_hash', 3, 'active'],
      ['finance1', 'finance1_hash', 4, 'active'],
      ['finance2', 'finance2_hash', 4, 'active'],
      ['readonly1', 'readonly1_hash', 3, 'active'],
      ['readonly2', 'readonly2_hash', 3, 'active'],
      ['test_user', 'test_hash', 3, 'inactive']
    ];
    
    for (const [username, passwordHash, roleId, status] of testUsers) {
      await connection.query(
        'INSERT INTO users (username, password_hash, role_id, account_status) VALUES (?, ?, ?, ?) ON DUPLICATE KEY UPDATE username = VALUES(username)',
        [username, passwordHash, roleId, status]
      );
    }
    console.log(`✅ 创建了 ${testUsers.length} 个测试用户`);
    
    // 2. 创建人员测试数据
    console.log('\n👤 创建人员测试数据...');
    
    const testPersons = [
      ['李明', '身份证', '110101199001011001', '***********', '<EMAIL>'],
      ['王芳', '身份证', '110101199002022002', '***********', '<EMAIL>'],
      ['张伟', '身份证', '110101199003033003', '***********', '<EMAIL>'],
      ['刘强', '身份证', '110101199004044004', '***********', '<EMAIL>'],
      ['陈静', '身份证', '110101199005055005', '***********', '<EMAIL>'],
      ['杨洋', '身份证', '110101199006066006', '***********', '<EMAIL>'],
      ['赵敏', '身份证', '110101199007077007', '***********', '<EMAIL>'],
      ['孙涛', '身份证', '110101199008088008', '***********', '<EMAIL>'],
      ['周丽', '身份证', '110101199009099009', '13800138009', '<EMAIL>'],
      ['吴刚', '身份证', '110101199010101010', '13800138010', '<EMAIL>'],
      ['徐娜', '身份证', '110101199011111011', '13800138011', '<EMAIL>'],
      ['朱华', '身份证', '110101199012121012', '13800138012', '<EMAIL>'],
      ['马超', '护照', 'P123456789', '13800138013', '<EMAIL>'],
      ['林雪', '护照', 'P987654321', '13800138014', '<EMAIL>'],
      ['胡斌', '其他', 'O111222333', '13800138015', '<EMAIL>']
    ];
    
    for (const [name, idType, idNumber, phone, email] of testPersons) {
      await connection.query(
        'INSERT INTO persons (name, id_type, id_number, phone, email, created_by) VALUES (?, ?, ?, ?, ?, 1) ON DUPLICATE KEY UPDATE name = VALUES(name)',
        [name, idType, idNumber, phone, email]
      );
    }
    console.log(`✅ 创建了 ${testPersons.length} 个测试人员`);
    
    // 3. 创建公司测试数据
    console.log('\n🏢 创建公司测试数据...');
    
    const testCompanies = [
      ['深圳科技有限公司', 'Shenzhen Tech Co., Ltd.', 'SZ001', 1000.0000, '2020-01-15', 10, 9, 4, 1, 1],
      ['上海贸易有限公司', 'Shanghai Trade Co., Ltd.', 'SH001', 2000.0000, '2019-03-20', 7, 10, 6, 1, 1],
      ['北京投资有限公司', 'Beijing Investment Co., Ltd.', 'BJ001', 5000.0000, '2018-06-10', 11, 12, 5, 1, 1],
      ['广州制造有限公司', 'Guangzhou Manufacturing Co., Ltd.', 'GZ001', 3000.0000, '2021-02-28', 4, 6, 1, 1, 1],
      ['杭州电商有限公司', 'Hangzhou E-commerce Co., Ltd.', 'HZ001', 1500.0000, '2020-08-12', 1, 2, 2, 1, 1],
      ['成都软件有限公司', 'Chengdu Software Co., Ltd.', 'CD001', 800.0000, '2022-01-05', 10, 4, 3, 1, 1],
      ['西安新能源有限公司', 'Xian New Energy Co., Ltd.', 'XA001', 4000.0000, '2019-11-18', 5, 3, 7, 1, 1],
      ['青岛海运有限公司', 'Qingdao Shipping Co., Ltd.', 'QD001', 2500.0000, '2020-05-22', 8, 1, 1, 1, 1],
      ['天津金融有限公司', 'Tianjin Finance Co., Ltd.', 'TJ001', 10000.0000, '2017-09-30', 11, 3, 5, 1, 1],
      ['重庆房地产有限公司', 'Chongqing Real Estate Co., Ltd.', 'CQ001', 8000.0000, '2018-12-08', 12, 4, 3, 1, 1],
      ['南京教育有限公司', 'Nanjing Education Co., Ltd.', 'NJ001', 600.0000, '2021-07-15', 17, 2, 2, 1, 1],
      ['武汉医疗有限公司', 'Wuhan Medical Co., Ltd.', 'WH001', 3500.0000, '2019-04-25', 18, 2, 2, 1, 1]
    ];
    
    for (const [nameCn, nameEn, code, capital, date, segmentId, regionId, agencyId, annualId, operationId] of testCompanies) {
      await connection.query(
        `INSERT INTO companies (
          company_name_cn, company_name_en, company_code, registered_capital, establish_date,
          business_segment_id, region_id, agency_id, annual_update_status_id, operation_status_id,
          registered_address, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
        ON DUPLICATE KEY UPDATE company_name_cn = VALUES(company_name_cn)`,
        [nameCn, nameEn, code, capital, date, segmentId, regionId, agencyId, annualId, operationId, `${nameCn}注册地址`]
      );
    }
    console.log(`✅ 创建了 ${testCompanies.length} 个测试公司`);
    
    // 4. 创建股东关系测试数据
    console.log('\n💰 创建股东关系测试数据...');
    
    // 获取人员和公司ID
    const [persons] = await connection.query('SELECT id FROM persons ORDER BY id LIMIT 15');
    const [companies] = await connection.query('SELECT id FROM companies ORDER BY id LIMIT 12');
    
    const shareholdings = [];
    for (let i = 0; i < 15; i++) {
      const personId = persons[i % persons.length].id;
      const companyId = companies[i % companies.length].id;
      const investmentAmount = (Math.random() * 1000 + 100).toFixed(4);
      const percentage = (Math.random() * 30 + 5).toFixed(2);
      const isProxy = Math.random() > 0.8; // 20%概率为代持
      const actualShareholderId = isProxy ? persons[(i + 1) % persons.length].id : null;
      const startDate = new Date(2020 + Math.floor(Math.random() * 4), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
      
      shareholdings.push([personId, companyId, investmentAmount, percentage, isProxy, actualShareholderId, startDate]);
    }
    
    for (const [personId, companyId, amount, percentage, isProxy, actualId, startDate] of shareholdings) {
      await connection.query(
        `INSERT INTO shareholdings (
          person_id, company_id, investment_amount, percentage, is_proxy, 
          actual_shareholder_id, start_date, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 1)`,
        [personId, companyId, amount, percentage, isProxy, actualId, startDate]
      );
    }
    console.log(`✅ 创建了 ${shareholdings.length} 条股东关系记录`);
    
    // 5. 创建任职关系测试数据
    console.log('\n👔 创建任职关系测试数据...');
    
    const positions = ['董事长', '总经理', '副总经理', '财务总监', '技术总监', '市场总监', '人事总监', '董事', '监事', '经理'];
    const employments = [];
    
    for (let i = 0; i < 20; i++) {
      const personId = persons[i % persons.length].id;
      const companyId = companies[i % companies.length].id;
      const position = positions[i % positions.length];
      const startDate = new Date(2019 + Math.floor(Math.random() * 5), Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1);
      const endDate = Math.random() > 0.7 ? new Date(startDate.getTime() + Math.random() * 365 * 24 * 60 * 60 * 1000 * 2) : null;
      
      employments.push([personId, companyId, position, startDate, endDate]);
    }
    
    for (const [personId, companyId, position, startDate, endDate] of employments) {
      await connection.query(
        `INSERT INTO employments (
          person_id, company_id, position, start_date, end_date, created_by
        ) VALUES (?, ?, ?, ?, ?, 1)`,
        [personId, companyId, position, startDate, endDate]
      );
    }
    console.log(`✅ 创建了 ${employments.length} 条任职关系记录`);
    
    console.log('\n🎉 测试数据创建完成！');
    
  } catch (error) {
    console.error('❌ 创建测试数据失败:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

createTestData()
  .then(() => console.log('🎉 测试数据创建成功完成'))
  .catch(err => console.error('💥 创建失败:', err));
