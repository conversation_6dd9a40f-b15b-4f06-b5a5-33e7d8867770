import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function fixChangeLogsStatus() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 查看status字段的枚举值
    const [statusEnum] = await connection.query(`
      SELECT COLUMN_TYPE
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'stake_management_v2' 
        AND TABLE_NAME = 'company_change_logs' 
        AND COLUMN_NAME = 'status'
    `);
    
    console.log('\n📋 status字段枚举值:', statusEnum[0].COLUMN_TYPE);

    // 手动添加缺失的两条记录（使用正确的状态值）
    const missingRecords = [
      {
        company_id: 45,
        change_type: 'basic',
        change_content: '公司注册资本变更：【2000万元】→【3000万元】',
        old_value: '2000万元',
        new_value: '3000万元',
        change_date: '2024-09-05',
        status: 'pending', // 改为pending
        operator: '周九',
        operate_date: '2024-09-05 15:10:00'
      },
      {
        company_id: 49,
        change_type: 'basic',
        change_content: '公司经营范围变更：【软件开发】→【软件开发；技术咨询；系统集成】',
        old_value: '软件开发',
        new_value: '软件开发；技术咨询；系统集成',
        change_date: '2024-11-10',
        status: 'pending', // 改为pending
        operator: '王五',
        operate_date: '2024-11-10 13:45:00'
      }
    ];

    console.log('\n📝 添加缺失的变更记录...');
    
    for (const record of missingRecords) {
      try {
        const [result] = await connection.query(`
          INSERT INTO company_change_logs (
            company_id, change_type, change_content, old_value, new_value, 
            change_date, status, operator, operate_date
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        `, [
          record.company_id,
          record.change_type,
          record.change_content,
          record.old_value,
          record.new_value,
          record.change_date,
          record.status,
          record.operator,
          record.operate_date
        ]);
        
        console.log(`✅ 添加变更记录成功 - ID: ${result.insertId}`);
      } catch (error) {
        console.error(`❌ 添加变更记录失败:`, error.message);
      }
    }

    // 验证最终结果
    const [finalStats] = await connection.query(`
      SELECT status, COUNT(*) as count
      FROM company_change_logs
      GROUP BY status
      ORDER BY status
    `);
    
    console.log('\n📊 最终变更记录状态统计:');
    finalStats.forEach(stat => {
      const statusText = {
        'pending': '待确认',
        'confirmed': '已确认'
      }[stat.status] || stat.status;
      console.log(`  ${statusText}: ${stat.count} 条`);
    });

    // 显示所有变更记录
    const [allChangeLogs] = await connection.query(`
      SELECT cl.id, c.company_name_cn, cl.change_type, cl.change_content, 
             cl.change_date, cl.status, cl.operator
      FROM company_change_logs cl
      LEFT JOIN companies c ON cl.company_id = c.id
      ORDER BY cl.change_date DESC
    `);
    
    console.log(`\n📋 所有变更记录 (共${allChangeLogs.length}条):`);
    allChangeLogs.forEach(log => {
      const statusText = {
        'pending': '待确认',
        'confirmed': '已确认'
      }[log.status] || log.status;
      
      console.log(`${log.change_date.toISOString().split('T')[0]} | ${log.company_name_cn} | ${log.change_type} | ${statusText} | ${log.operator}`);
    });

  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行修复
fixChangeLogsStatus();
