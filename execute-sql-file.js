import mysql from 'mysql2/promise';
import fs from 'fs';

const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function executeSQLFile() {
  console.log('🔄 开始执行SQL文件...');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 读取SQL文件
    const sqlContent = fs.readFileSync('database-schema-complete.sql', 'utf8');
    console.log('📄 读取SQL文件成功');
    
    // 清理和分割SQL语句
    const cleanedSQL = sqlContent
      .replace(/--.*$/gm, '') // 移除单行注释
      .replace(/\/\*[\s\S]*?\*\//g, '') // 移除多行注释
      .replace(/^\s*$/gm, '') // 移除空行
      .trim();
    
    // 按分号分割，但保留完整的语句
    const statements = [];
    let currentStatement = '';
    let inString = false;
    let stringChar = '';
    
    for (let i = 0; i < cleanedSQL.length; i++) {
      const char = cleanedSQL[i];
      const prevChar = i > 0 ? cleanedSQL[i - 1] : '';
      
      if ((char === '"' || char === "'") && prevChar !== '\\') {
        if (!inString) {
          inString = true;
          stringChar = char;
        } else if (char === stringChar) {
          inString = false;
          stringChar = '';
        }
      }
      
      currentStatement += char;
      
      if (char === ';' && !inString) {
        const stmt = currentStatement.trim();
        if (stmt && 
            !stmt.toLowerCase().startsWith('use ') &&
            !stmt.toLowerCase().startsWith('create database') &&
            !stmt.toLowerCase().startsWith('set foreign_key_checks')) {
          statements.push(stmt);
        }
        currentStatement = '';
      }
    }
    
    console.log(`📋 准备执行 ${statements.length} 条SQL语句`);
    
    let successCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i];
      try {
        await connection.query(statement);
        
        if (statement.toLowerCase().includes('create table')) {
          const tableName = statement.match(/create table (?:if not exists )?`?(\w+)`?/i)?.[1];
          console.log(`✅ 创建表: ${tableName}`);
        } else if (statement.toLowerCase().includes('insert into')) {
          const tableName = statement.match(/insert into `?(\w+)`?/i)?.[1];
          console.log(`📝 插入数据: ${tableName}`);
        } else if (statement.toLowerCase().includes('alter table')) {
          const tableName = statement.match(/alter table `?(\w+)`?/i)?.[1];
          console.log(`🔧 修改表: ${tableName}`);
        } else if (statement.toLowerCase().includes('create view')) {
          const viewName = statement.match(/create view `?(\w+)`?/i)?.[1];
          console.log(`👁️ 创建视图: ${viewName}`);
        }
        
        successCount++;
      } catch (error) {
        console.error(`❌ 执行失败 (${i + 1}/${statements.length}):`, error.message);
        console.error('SQL:', statement.substring(0, 100) + '...');
        errorCount++;
      }
    }
    
    // 验证结果
    const [tables] = await connection.query('SHOW TABLES');
    console.log(`\n📊 执行结果:`);
    console.log(`  ✅ 成功: ${successCount} 条`);
    console.log(`  ❌ 失败: ${errorCount} 条`);
    console.log(`  📋 创建表数量: ${tables.length}`);
    
    console.log(`\n📋 已创建的表:`);
    tables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });
    
    await connection.end();
    console.log('\n🎉 SQL文件执行完成！');
    
  } catch (error) {
    console.error('❌ 执行SQL文件失败:', error.message);
    throw error;
  }
}

executeSQLFile()
  .then(() => console.log('🎉 数据库结构创建成功'))
  .catch(err => console.error('💥 创建失败:', err));
