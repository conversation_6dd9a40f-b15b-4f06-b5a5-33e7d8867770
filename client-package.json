{"name": "stake-equity-management-client", "version": "1.0.0", "description": "Frontend for stake and equity management system", "private": true, "type": "module", "scripts": {"dev": "vite --host 0.0.0.0 --port 5173", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "analyze": "npx vite-bundle-analyzer"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "@ant-design/charts": "^2.0.3", "@reduxjs/toolkit": "^2.0.1", "react-redux": "^9.0.4", "@tanstack/react-query": "^5.14.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "axios": "^1.6.2", "dayjs": "^1.11.10", "recharts": "^2.8.0", "framer-motion": "^10.16.16", "react-beautiful-dnd": "^13.1.1", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "lodash-es": "^4.17.21", "classnames": "^2.3.2", "react-helmet-async": "^2.0.4", "react-error-boundary": "^4.0.11", "react-hot-toast": "^2.4.1", "zustand": "^4.4.7"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/lodash-es": "^4.17.12", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "eslint-plugin-import": "^2.29.0", "eslint-plugin-jsx-a11y": "^6.8.0", "typescript": "^5.3.3", "vite": "^5.0.8", "vite-plugin-pwa": "^0.17.4", "vite-bundle-analyzer": "^0.7.0", "vitest": "^1.0.4", "@vitest/ui": "^1.0.4", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jsdom": "^23.0.1", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@storybook/react": "^7.6.6", "@storybook/react-vite": "^7.6.6", "@storybook/addon-essentials": "^7.6.6", "@storybook/addon-interactions": "^7.6.6", "@storybook/addon-links": "^7.6.6", "@storybook/blocks": "^7.6.6", "@storybook/testing-library": "^0.2.2", "prettier": "^3.1.1", "prettier-plugin-tailwindcss": "^0.5.9"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}