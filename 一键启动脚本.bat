@echo off
chcp 65001 >nul
title 股权管理系统 - 一键启动

echo ========================================
echo           股权管理系统
echo        一键启动脚本 v1.0
echo ========================================
echo.

:: 检查是否在正确的目录
if not exist "package.json" (
    echo [错误] 请将此脚本放在项目根目录下运行！
    echo 当前目录: %CD%
    echo 请确保此目录包含 package.json 文件
    pause
    exit /b 1
)

:: 检查Node.js是否安装
echo [检查] 正在检查Node.js安装状态...
node --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未检测到Node.js，请先安装Node.js！
    echo 下载地址: https://nodejs.org/zh-cn/
    pause
    exit /b 1
) else (
    for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
    echo [成功] Node.js版本: %NODE_VERSION%
)

:: 检查MySQL是否安装
echo [检查] 正在检查MySQL安装状态...
mysql --version >nul 2>&1
if errorlevel 1 (
    echo [警告] 未检测到MySQL命令行工具
    echo 请确保MySQL已正确安装并添加到系统PATH
    echo.
) else (
    for /f "tokens=*" %%i in ('mysql --version') do set MYSQL_VERSION=%%i
    echo [成功] MySQL版本: %MYSQL_VERSION%
)

:: 检查依赖是否安装
echo [检查] 正在检查项目依赖...
if not exist "node_modules" (
    echo [信息] 首次运行，正在安装依赖包...
    echo 这可能需要几分钟时间，请耐心等待...
    npm install
    if errorlevel 1 (
        echo [错误] 依赖安装失败！
        echo 请检查网络连接或尝试手动运行: npm install
        pause
        exit /b 1
    )
    echo [成功] 依赖安装完成！
) else (
    echo [成功] 依赖已安装
)

:: 检查数据库连接
echo [检查] 正在测试数据库连接...
echo 请确保MySQL服务正在运行，数据库已创建
echo.

:: 启动后端服务
echo [启动] 正在启动后端服务...
start "股权管理系统-后端服务" cmd /k "echo 后端服务启动中... && npm run server"

:: 等待后端服务启动
echo [等待] 等待后端服务启动完成...
timeout /t 8 /nobreak >nul

:: 启动前端服务
echo [启动] 正在启动前端服务...
start "股权管理系统-前端服务" cmd /k "echo 前端服务启动中... && npm run dev"

:: 等待前端服务启动
echo [等待] 等待前端服务启动完成...
timeout /t 10 /nobreak >nul

:: 打开浏览器
echo [启动] 正在打开系统页面...
start http://localhost:5173

echo.
echo ========================================
echo           启动完成！
echo ========================================
echo.
echo 系统访问地址:
echo   前端页面: http://localhost:5173
echo   后端API:  http://localhost:8080
echo.
echo 注意事项:
echo 1. 请保持两个命令行窗口运行
echo 2. 如需停止服务，关闭对应窗口即可
echo 3. 如遇问题，请查看命令行窗口的错误信息
echo.
echo 常用功能:
echo - 公司信息管理
echo - 股东信息管理  
echo - 任职档案管理
echo - 业务板块管理
echo - 变更确认处理
echo.
echo ========================================

pause
