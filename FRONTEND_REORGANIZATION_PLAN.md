# Frontend Pages Reorganization Plan

## 📋 Current State Analysis

### ✅ **Existing Pages:**
- `src/pages/AddCompany.tsx` - ✅ Working (needs modernization)
- `src/pages/CompanyInfo.tsx` - ✅ Working (needs API integration)
- `src/pages/StockList.tsx` - ✅ Basic structure
- `src/pages/company/change/BasicChange.tsx` - ✅ Basic structure
- `src/pages/company/change/ExecutiveChange.tsx` - ❓ Need to check
- `src/pages/company/change/ShareholderChange.tsx` - ❓ Need to check

### ❌ **Missing Pages (Based on Menu Structure):**
- Dashboard/Home page
- Company detail page
- Company finance page
- Company equity chart page
- Employment/Personnel management pages
- Business segment management pages
- Shareholder management pages
- Basic data management pages (regions, agencies)
- Data manual page
- File submission page

## 🎯 **Recommended File Structure:**

```
src/pages/
├── 📁 dashboard/
│   └── Dashboard.tsx                    # Main dashboard/home page
├── 📁 company/
│   ├── CompanyList.tsx                  # Company list (rename from CompanyInfo.tsx)
│   ├── CompanyAdd.tsx                   # Add company (rename from AddCompany.tsx)
│   ├── CompanyDetail.tsx                # Company detail view
│   ├── CompanyEdit.tsx                  # Edit company
│   ├── CompanyFinance.tsx               # Company finance management
│   ├── CompanyEquityChart.tsx           # Company equity visualization
│   └── 📁 change/
│       ├── BasicChange.tsx              # ✅ Existing
│       ├── ExecutiveChange.tsx          # ✅ Existing
│       └── ShareholderChange.tsx        # ✅ Existing
├── 📁 stock/
│   ├── StockList.tsx                    # ✅ Existing (needs enhancement)
│   └── StockTransfer.tsx                # Stock transfer management
├── 📁 employment/
│   ├── PersonnelList.tsx                # Personnel list
│   ├── PersonnelAdd.tsx                 # Add personnel
│   └── PersonnelDetail.tsx              # Personnel detail
├── 📁 business/
│   ├── BusinessSegmentList.tsx          # Business segment list
│   ├── BusinessSegmentAdd.tsx           # Add business segment
│   └── BusinessEquityChart.tsx          # Business equity chart
├── 📁 shareholder/
│   ├── ShareholderList.tsx              # Shareholder list
│   ├── ShareholderAdd.tsx               # Add shareholder
│   └── ShareholderDetail.tsx            # Shareholder detail
├── 📁 basic-data/
│   ├── RegionManagement.tsx             # Region management
│   └── AgencyManagement.tsx             # Agency management
├── 📁 manual/
│   └── DataManual.tsx                   # Data manual
├── 📁 submission/
│   └── FileSubmission.tsx               # File submission
└── 📁 auth/
    ├── Login.tsx                        # Login page (future)
    └── Profile.tsx                      # User profile (future)
```

## 🔧 **File Naming Standards:**

### ✅ **Naming Convention:**
- **PascalCase** for component files: `CompanyList.tsx`
- **Descriptive names** that clearly indicate purpose
- **Consistent prefixes** for related functionality
- **Folder organization** by feature/domain

### ✅ **Component Standards:**
- Each page should be a **default export**
- Use **TypeScript interfaces** for props and data types
- Include **proper error handling** and loading states
- Follow **responsive design** principles
- Use **consistent styling** approach

## 🚀 **Implementation Priority:**

### **Phase 1: Core Company Management (Week 1)**
1. ✅ **CompanyAdd.tsx** - Modernize existing AddCompany
2. ✅ **CompanyList.tsx** - Enhance existing CompanyInfo
3. 🆕 **CompanyDetail.tsx** - New detailed view
4. 🆕 **CompanyEdit.tsx** - Edit functionality

### **Phase 2: Stock & Equity Management (Week 2)**
5. ✅ **StockList.tsx** - Enhance existing
6. 🆕 **StockTransfer.tsx** - New transfer functionality
7. 🆕 **CompanyEquityChart.tsx** - Visualization

### **Phase 3: Personnel & Shareholders (Week 3)**
8. 🆕 **PersonnelAdd.tsx** - Personnel management
9. 🆕 **ShareholderAdd.tsx** - Shareholder management
10. 🆕 **ShareholderList.tsx** - Shareholder listing

### **Phase 4: Basic Data & Admin (Week 4)**
11. 🆕 **RegionManagement.tsx** - Region management
12. 🆕 **AgencyManagement.tsx** - Agency management
13. 🆕 **DataManual.tsx** - Documentation
14. 🆕 **FileSubmission.tsx** - File handling

## 📝 **Code Standards:**

### **1. Component Structure:**
```typescript
import React, { useState, useEffect } from 'react';
import { Card, Button, message } from 'antd';
import { useNavigate } from 'react-router-dom';

// Types
interface ComponentProps {
  // Define props here
}

interface DataType {
  // Define data structures here
}

// Component
const ComponentName: React.FC<ComponentProps> = (props) => {
  // State management
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  // Effects
  useEffect(() => {
    // Component initialization
  }, []);

  // Event handlers
  const handleAction = () => {
    // Handle user actions
  };

  // Render
  return (
    <div className="component-container">
      <Card title="Page Title">
        {/* Content */}
      </Card>
    </div>
  );
};

export default ComponentName;
```

### **2. API Integration:**
```typescript
// Use consistent API service
import { apiService } from '@/services/api';

// Error handling
try {
  const response = await apiService.get('/companies');
  if (response.data.success) {
    setData(response.data.data);
  }
} catch (error) {
  console.error('API Error:', error);
  message.error('操作失败，请稍后重试');
}
```

### **3. Responsive Design:**
```typescript
// Use Ant Design Grid system
import { Row, Col, Grid } from 'antd';

const { useBreakpoint } = Grid;

const Component = () => {
  const screens = useBreakpoint();
  const isMobile = !screens.md;

  return (
    <Row gutter={[16, 16]}>
      <Col xs={24} sm={12} md={8} lg={6}>
        {/* Responsive content */}
      </Col>
    </Row>
  );
};
```

## 🔄 **Migration Steps:**

### **Step 1: Rename and Reorganize Existing Files**
```bash
# Rename existing files to follow standards
mv src/pages/AddCompany.tsx src/pages/company/CompanyAdd.tsx
mv src/pages/CompanyInfo.tsx src/pages/company/CompanyList.tsx
```

### **Step 2: Update Imports and Routes**
- Update all import statements
- Update routing configuration
- Update navigation references

### **Step 3: Modernize Components**
- Add TypeScript interfaces
- Implement responsive design
- Add proper error handling
- Integrate with API services

### **Step 4: Create Missing Components**
- Create placeholder components for missing pages
- Implement core functionality
- Add proper navigation

## ✅ **Quality Checklist:**

### **For Each Component:**
- [ ] TypeScript interfaces defined
- [ ] Responsive design implemented
- [ ] Error handling included
- [ ] Loading states managed
- [ ] API integration working
- [ ] Navigation properly configured
- [ ] Consistent styling applied
- [ ] Accessibility considerations
- [ ] Performance optimized
- [ ] Documentation added

## 🎯 **Next Steps:**

1. **Start with CompanyAdd.tsx** - Modernize the existing AddCompany page
2. **Create missing components** following the established patterns
3. **Update routing configuration** to match new structure
4. **Test all navigation flows** to ensure consistency
5. **Implement responsive design** across all components

This reorganization will create a maintainable, scalable frontend structure that follows modern development standards and can be easily extended in the future.
