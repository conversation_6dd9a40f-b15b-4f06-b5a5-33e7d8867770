import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function fixAdminPerson() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    await connection.beginTransaction();

    // 为admin用户创建person记录
    console.log('🔄 为admin用户创建person记录...');
    const [personResult] = await connection.query(
      `INSERT INTO persons (
        name, email, phone, id_type, id_number,
        created_at, updated_at, created_by, updated_by
      ) VALUES (?, ?, ?, ?, ?, NOW(), NOW(), ?, ?)`,
      ['系统管理员', '<EMAIL>', '13800000001', '身份证', 'admin_id_001', 1, 1]
    );
    const personId = personResult.insertId;
    console.log(`✅ 创建person记录成功，ID: ${personId}`);

    // 更新admin用户的person_id
    await connection.query(
      'UPDATE users SET person_id = ? WHERE username = ?',
      [personId, 'admin']
    );
    console.log('✅ 更新admin用户的person_id成功');

    await connection.commit();

    // 验证结果
    console.log('\n📊 验证结果:');
    const [adminUser] = await connection.query(`
      SELECT 
        u.id,
        u.username,
        u.person_id,
        COALESCE(p.name, u.username) as realName,
        p.email,
        ur.name as roleName
      FROM users u
      LEFT JOIN persons p ON u.person_id = p.id
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      WHERE u.username = 'admin'
    `);

    if (adminUser.length > 0) {
      const user = adminUser[0];
      console.log(`admin用户信息:`);
      console.log(`  用户名: ${user.username}`);
      console.log(`  真实姓名: ${user.realName}`);
      console.log(`  邮箱: ${user.email}`);
      console.log(`  角色: ${user.roleName}`);
      console.log(`  Person ID: ${user.person_id}`);
    }

  } catch (error) {
    await connection.rollback();
    console.error('❌ 修复admin用户person记录失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行修复
fixAdminPerson().catch(console.error);
