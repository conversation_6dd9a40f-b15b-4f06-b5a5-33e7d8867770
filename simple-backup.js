import mysql from 'mysql2/promise';
import fs from 'fs';

const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management',
  socketPath: '/tmp/mysql.sock'
};

async function createBackup() {
  console.log('🔄 开始创建数据库备份...');
  
  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 获取所有表
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`📋 发现 ${tables.length} 个表`);
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19);
    const backupFile = `backup_${timestamp}.sql`;
    
    let sql = `-- 数据库备份 ${new Date().toLocaleString()}\n`;
    sql += `-- 数据库: ${dbConfig.database}\n\n`;
    
    // 备份每个表
    for (const tableRow of tables) {
      const tableName = Object.values(tableRow)[0];
      console.log(`📦 备份表: ${tableName}`);
      
      // 获取表结构
      const [createResult] = await connection.execute(`SHOW CREATE TABLE \`${tableName}\``);
      sql += `-- 表: ${tableName}\n`;
      sql += `DROP TABLE IF EXISTS \`${tableName}\`;\n`;
      sql += createResult[0]['Create Table'] + ';\n\n';
      
      // 获取数据
      const [rows] = await connection.execute(`SELECT * FROM \`${tableName}\``);
      if (rows.length > 0) {
        sql += `-- 数据: ${tableName}\n`;
        const [columns] = await connection.execute(`DESCRIBE \`${tableName}\``);
        const columnNames = columns.map(col => `\`${col.Field}\``).join(', ');
        
        sql += `INSERT INTO \`${tableName}\` (${columnNames}) VALUES\n`;
        
        const valueStrings = [];
        for (const row of rows) {
          const values = Object.values(row).map(val => {
            if (val === null) return 'NULL';
            if (typeof val === 'string') return `'${val.replace(/'/g, "''")}'`;
            if (val instanceof Date) return `'${val.toISOString().slice(0, 19).replace('T', ' ')}'`;
            return val;
          });
          valueStrings.push(`(${values.join(', ')})`);
        }
        
        sql += valueStrings.join(',\n') + ';\n\n';
      }
    }
    
    // 写入文件
    fs.writeFileSync(backupFile, sql);
    const stats = fs.statSync(backupFile);
    
    console.log(`✅ 备份完成: ${backupFile}`);
    console.log(`📊 文件大小: ${(stats.size / 1024).toFixed(2)} KB`);
    
    await connection.end();
    return backupFile;
    
  } catch (error) {
    console.error('❌ 备份失败:', error.message);
    throw error;
  }
}

createBackup()
  .then(file => console.log(`🎉 备份成功: ${file}`))
  .catch(err => console.error('💥 备份失败:', err));
