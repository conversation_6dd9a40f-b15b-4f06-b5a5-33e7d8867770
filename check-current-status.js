import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function checkCurrentStatus() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 查询所有变更记录的状态分布
    const [statusStats] = await connection.query(`
      SELECT status, change_type, COUNT(*) as count
      FROM company_change_logs
      GROUP BY status, change_type
      ORDER BY status, change_type
    `);
    
    console.log('\n📊 变更记录状态分布:');
    statusStats.forEach(stat => {
      console.log(`  状态: ${stat.status}, 类型: ${stat.change_type}, 数量: ${stat.count}`);
    });

    // 查询待确认和有异常的记录
    const [pendingAndRejected] = await connection.query(`
      SELECT status, change_type, COUNT(*) as count
      FROM company_change_logs
      WHERE status IN ('pending', 'rejected')
      GROUP BY status, change_type
      ORDER BY status, change_type
    `);
    
    console.log('\n📋 待确认和有异常记录:');
    pendingAndRejected.forEach(stat => {
      console.log(`  状态: ${stat.status}, 类型: ${stat.change_type}, 数量: ${stat.count}`);
    });

    // 查询具体的记录
    const [records] = await connection.query(`
      SELECT id, company_id, change_type, status
      FROM company_change_logs
      WHERE status IN ('pending', 'rejected')
      ORDER BY status, change_type, id
    `);
    
    console.log('\n📝 具体的待确认和有异常记录:');
    records.forEach(record => {
      console.log(`  ID: ${record.id}, 公司ID: ${record.company_id}, 类型: ${record.change_type}, 状态: ${record.status}`);
    });

  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行查询
checkCurrentStatus();
