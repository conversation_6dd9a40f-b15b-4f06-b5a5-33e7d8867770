# 剩余硬编码数据修复建议

## 🎯 **刚才完成的修改总结**

### ✅ **已修改的页面**
1. **股东登记页面** - 只显示企业股东，过滤个人股东
2. **股东信息页面** - 显示所有持股的个人和企业股东
3. **公司信息页面** - 显示真实的业务板块名称

## 📋 **剩余需要修复的硬编码数据页面**

### 🟡 **中优先级页面（有API但使用硬编码备用数据）**

#### 4. **业务板块页面** (`src/pages/business/BusinessSegmentPage.tsx`)
**当前问题**：API失败时使用硬编码备用数据

**修改建议**：
- **删除**：第49-86行模拟业务板块数据
- **修改**：第154、159行，移除硬编码备用逻辑
- **改进**：API失败时显示错误状态，提供重试功能
- **优化**：添加数据为空时的友好提示

**具体修改**：
```typescript
// 删除硬编码备用数据
// const mockBusinessSegments = [...];

// 修改错误处理
catch (error) {
  console.error('获取业务板块数据失败:', error);
  setError(`获取业务板块数据失败: ${error.message}`);
  message.error(`获取业务板块数据失败: ${error.message}`);
  setLoading(false);
  // 不使用 setBusinessSegments(mockBusinessSegments);
}
```

#### 5. **代理机构管理页面** (`src/pages/basic-data/AgencyManagement.tsx`)
**当前问题**：API失败时使用硬编码备用数据

**修改建议**：
- **删除**：第34-83行模拟代理机构数据
- **修改**：第92-111行，移除硬编码备用逻辑
- **改进**：API失败时显示错误信息，不显示模拟数据
- **添加**：网络错误重试机制

**具体修改**：
```typescript
// 删除硬编码数据
// const mockAgencyData = [...];

// 修改API调用
const fetchAgencies = async () => {
  try {
    setLoading(true);
    setError(null);
    
    const response = await axios.get('http://localhost:8080/api/agencies');
    if (response.data.success) {
      setData(response.data.data);
    } else {
      throw new Error(response.data.message || 'API返回失败');
    }
    setLoading(false);
  } catch (error) {
    setError(`数据加载失败: ${error.message}`);
    setLoading(false);
    // 不使用备用数据
  }
};
```

#### 6. **数据字典页面** (`src/pages/basic-data/DataDictionary.tsx`)
**当前问题**：API失败时使用硬编码备用数据

**修改建议**：
- **删除**：所有模拟数据常量
- **修改**：第113-143行，移除硬编码备用逻辑
- **改进**：API失败时显示具体错误类型
- **添加**：分别处理职位和出资方式的错误状态

**具体修改**：
```typescript
// 删除所有硬编码数据
// const mockPositions = [...];
// const mockInvestmentMethods = [...];

// 分别处理不同类型的数据加载
const [positionError, setPositionError] = useState<string | null>(null);
const [investmentError, setInvestmentError] = useState<string | null>(null);
```

### 🟢 **低优先级页面（部分硬编码选项）**

#### 7. **公司新增页面** (`src/pages/company/CompanyAddWorking.tsx`)
**当前问题**：部分选项使用硬编码数据

**修改建议**：
- **删除**：第91行硬编码地区选项，改为调用 `/api/regions`
- **删除**：第92行硬编码代理机构，改为调用 `/api/agencies`
- **优化**：第93-94行年审状态和经营状态，考虑从数据字典获取
- **改进**：第107、112行默认业务板块，API失败时禁用表单提交
- **添加**：选项加载失败时的错误提示

**具体修改**：
```typescript
// 添加状态管理
const [regionsLoading, setRegionsLoading] = useState(true);
const [agenciesLoading, setAgenciesLoading] = useState(true);
const [formDisabled, setFormDisabled] = useState(false);

// 加载选项数据
useEffect(() => {
  const loadOptions = async () => {
    try {
      const [regionsRes, agenciesRes] = await Promise.all([
        axios.get('/api/regions'),
        axios.get('/api/agencies')
      ]);
      
      if (regionsRes.data.success) setRegions(regionsRes.data.data);
      if (agenciesRes.data.success) setAgencies(agenciesRes.data.data);
    } catch (error) {
      message.error('加载选项数据失败，表单已禁用');
      setFormDisabled(true);
    }
  };
  loadOptions();
}, []);
```

#### 8. **公司详情页面** (`src/pages/company/CompanyDetail.tsx`)
**当前问题**：多个模块使用硬编码数据

**修改建议**：
- **删除**：第119-122行硬编码高管信息，添加 `/api/companies/{id}/executives`
- **删除**：第125-134行硬编码股东信息，添加 `/api/companies/{id}/shareholders`
- **删除**：第140-152行硬编码财务信息，添加 `/api/companies/{id}/financial`
- **添加**：各模块独立的错误处理
- **优化**：模块化加载，单个模块失败不影响其他模块

**具体修改**：
```typescript
// 模块化数据加载
const [executiveData, setExecutiveData] = useState([]);
const [shareholderData, setShareholderData] = useState([]);
const [financialData, setFinancialData] = useState([]);
const [executiveError, setExecutiveError] = useState<string | null>(null);
const [shareholderError, setShareholderError] = useState<string | null>(null);
const [financialError, setFinancialError] = useState<string | null>(null);

// 独立加载各模块数据
const loadExecutiveData = async () => {
  try {
    const response = await axios.get(`/api/companies/${id}/executives`);
    if (response.data.success) {
      setExecutiveData(response.data.data);
    }
  } catch (error) {
    setExecutiveError('高管信息加载失败');
  }
};
```

#### 9. **任务管理页面** (`src/pages/task/TaskIndex.tsx`)
**当前问题**：初始统计数据结构硬编码

**修改建议**：
- **优化**：第90-98行初始统计结构，改为从API响应动态生成
- **添加**：统计数据加载失败时的错误处理
- **改进**：空数据状态的友好提示

**具体修改**：
```typescript
// 动态生成统计结构
const generateStatsFromAPI = (apiData: any) => {
  return {
    total: apiData.total || 0,
    overdue: apiData.overdue || 0,
    pending: apiData.pending || 0,
    completed: apiData.completed || 0
  };
};

// 替换硬编码的初始值
const [stats, setStats] = useState(null); // 不设置默认值
```

### 🆕 **新发现的硬编码问题**

#### 10. **公司详情页面的标签页内容**
**问题**：股东信息、财务信息等标签页使用硬编码数据

**修改建议**：
- 为每个标签页创建独立的API调用
- 实现懒加载，只有点击标签页时才加载数据
- 添加每个标签页的独立错误处理

#### 11. **股权图页面**
**问题**：可能存在硬编码的股权关系数据

**修改建议**：
- 检查股权图数据来源
- 从 `/api/companies/{id}/equity-structure` 获取真实数据
- 实现动态股权图渲染

#### 12. **变更历史页面**
**问题**：公司变更历史可能使用模拟数据

**修改建议**：
- 从 `/api/companies/{id}/change-history` 获取真实变更记录
- 实现变更类型的动态分类
- 添加变更详情的展示

## 🛠️ **通用修复模式**

### **1. 错误处理标准化**
```typescript
// 统一的错误处理模式
try {
  setLoading(true);
  setError(null);
  
  const response = await axios.get('API_ENDPOINT');
  if (response.data.success) {
    setData(response.data.data);
  } else {
    throw new Error(response.data.message || 'API返回失败');
  }
  setLoading(false);
} catch (error) {
  console.error('API调用失败:', error);
  setError(`数据加载失败: ${error.message}`);
  message.error(`数据加载失败: ${error.message}`);
  setLoading(false);
  // 不使用硬编码备用数据
}
```

### **2. 重试机制**
```typescript
const handleRetry = () => {
  setError(null);
  fetchData(); // 重新调用数据获取函数
};

// 错误状态UI
{error && (
  <div className="error-state">
    <p>数据加载失败: {error}</p>
    <Button onClick={handleRetry}>重试</Button>
  </div>
)}
```

### **3. 空数据状态**
```typescript
{!loading && !error && data.length === 0 && (
  <div className="empty-state">
    <p>暂无数据</p>
    <Button onClick={handleRefresh}>刷新</Button>
  </div>
)}
```

## 📋 **修复优先级建议**

### **第一批（本周完成）**
4. 业务板块页面
5. 代理机构管理页面

### **第二批（下周完成）**
6. 数据字典页面
7. 公司新增页面

### **第三批（后续优化）**
8. 公司详情页面
9. 任务管理页面
10-12. 新发现的硬编码问题

## 🎯 **预期效果**

修复完成后：
- ✅ **所有页面都不使用硬编码数据**
- ✅ **API失败时用户能清楚知道问题所在**
- ✅ **提供重试机制，改善用户体验**
- ✅ **数据来源统一，保证数据一致性**
- ✅ **系统更加稳定和可维护**

## 📝 **总结**

目前还有 **6个主要页面** 需要修复硬编码数据问题。建议按照优先级逐步修复，确保每个页面都能从真实API获取数据，并在API失败时提供友好的错误处理和重试机制。
