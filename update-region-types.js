import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function updateRegionTypes() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 1. 修改regions表的type字段，添加"离岸"选项
    console.log('\n🔄 修改regions表的type字段...');
    await connection.query(`
      ALTER TABLE regions 
      MODIFY COLUMN type ENUM('国内', '离岸', '海外') NOT NULL COMMENT '地区类型'
    `);
    console.log('✅ regions表type字段修改成功');

    // 2. 修改archive_rule_regions表的region_type字段，添加"离岸"选项
    console.log('\n🔄 修改archive_rule_regions表的region_type字段...');
    try {
      await connection.query(`
        ALTER TABLE archive_rule_regions 
        MODIFY COLUMN region_type ENUM('国内', '离岸', '海外') NOT NULL COMMENT '地区类型'
      `);
      console.log('✅ archive_rule_regions表region_type字段修改成功');
    } catch (error) {
      console.log('⚠️ archive_rule_regions表不存在或修改失败:', error.message);
    }

    // 3. 检查并修改region表（如果存在）
    console.log('\n🔄 检查region表是否存在...');
    try {
      const [tables] = await connection.query(`
        SELECT COUNT(*) as count FROM information_schema.tables 
        WHERE table_schema = 'stake_management_v2' 
        AND table_name = 'region'
      `);
      
      if (tables[0].count > 0) {
        console.log('📋 region表存在，更新type字段...');
        await connection.query(`
          ALTER TABLE region 
          MODIFY COLUMN type VARCHAR(50) NOT NULL COMMENT '类型：国内/离岸/海外'
        `);
        console.log('✅ region表type字段修改成功');
      } else {
        console.log('📋 region表不存在，跳过更新');
      }
    } catch (error) {
      console.log('⚠️ region表修改失败:', error.message);
    }

    // 4. 添加离岸地区的示例数据
    console.log('\n🔄 添加离岸地区数据...');
    const offshoreRegions = [
      '开曼群岛',
      '英属维尔京群岛',
      '百慕大',
      '塞舌尔',
      '萨摩亚',
      '马绍尔群岛'
    ];

    for (const regionName of offshoreRegions) {
      try {
        await connection.query(`
          INSERT IGNORE INTO regions (type, name, is_active) 
          VALUES ('离岸', ?, TRUE)
        `, [regionName]);
        console.log(`✅ 添加离岸地区: ${regionName}`);
      } catch (error) {
        console.log(`⚠️ 添加离岸地区失败 ${regionName}:`, error.message);
      }
    }

    // 5. 更新视图
    console.log('\n🔄 更新v_companies_full视图...');
    try {
      await connection.query('DROP VIEW IF EXISTS v_companies_full');
      await connection.query(`
        CREATE VIEW v_companies_full AS
        SELECT
          c.*,
          bs.name as business_segment_name,
          r.name as region_name,
          r.type as region_type,
          a.name as agency_name,
          aus.name as annual_update_status_name,
          os.name as operation_status_name
        FROM companies c
        LEFT JOIN business_segments bs ON c.business_segment_id = bs.id
        LEFT JOIN regions r ON c.region_id = r.id
        LEFT JOIN agencies a ON c.agency_id = a.id
        LEFT JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
        LEFT JOIN operation_status os ON c.operation_status_id = os.id
      `);
      console.log('✅ v_companies_full视图更新成功');
    } catch (error) {
      console.log('⚠️ 视图更新失败:', error.message);
    }

    // 6. 验证修改结果
    console.log('\n📊 验证修改结果:');
    
    // 检查regions表的type字段
    const [regionColumns] = await connection.query(`
      SHOW COLUMNS FROM regions LIKE 'type'
    `);
    console.log('regions表type字段:', regionColumns[0]);

    // 检查离岸地区数据
    const [offshoreData] = await connection.query(`
      SELECT * FROM regions WHERE type = '离岸'
    `);
    console.log('\n📋 离岸地区数据:');
    offshoreData.forEach(region => {
      console.log(`  ID: ${region.id}, 名称: ${region.name}, 类型: ${region.type}`);
    });

    // 检查所有地区类型统计
    const [typeStats] = await connection.query(`
      SELECT type, COUNT(*) as count FROM regions GROUP BY type
    `);
    console.log('\n📊 地区类型统计:');
    typeStats.forEach(stat => {
      console.log(`  ${stat.type}: ${stat.count}个地区`);
    });

    console.log('\n🎉 地区类型更新完成！');

  } catch (error) {
    console.error('❌ 更新失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行更新
updateRegionTypes();
