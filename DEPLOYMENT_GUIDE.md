# Deployment Guide - Stake/Equity Management System

## 🎯 Overview
This guide provides step-by-step instructions for deploying the stake/equity management system on a local machine with enterprise-grade security.

## 📋 Prerequisites

### System Requirements
- **Operating System**: Ubuntu 20.04+ / CentOS 8+ / macOS 12+
- **RAM**: Minimum 8GB, Recommended 16GB
- **Storage**: Minimum 50GB SSD
- **CPU**: Minimum 4 cores, Recommended 8 cores
- **Network**: Isolated network or VPN access

### Software Requirements
- Node.js 20+ LTS
- MySQL 8.0+
- Git
- SSL Certificate (self-signed or CA-issued)
- Firewall (UFW/iptables)

## 🚀 Installation Steps

### 1. System Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git build-essential

# Install Node.js 20 LTS
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt install -y nodejs

# Verify installation
node --version  # Should be v20.x.x
npm --version   # Should be 10.x.x
```

### 2. MySQL Installation & Configuration

```bash
# Install MySQL 8.0
sudo apt install -y mysql-server-8.0

# Secure MySQL installation
sudo mysql_secure_installation

# Configure MySQL for production
sudo mysql -u root -p
```

```sql
-- Create application database and user
CREATE DATABASE stake_management_v2;
CREATE USER 'stake_app'@'localhost' IDENTIFIED BY 'YourSecurePassword123!@#';
GRANT SELECT, INSERT, UPDATE, DELETE ON stake_management_v2.* TO 'stake_app'@'localhost';
FLUSH PRIVILEGES;

-- Configure MySQL settings
SET GLOBAL max_connections = 200;
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
EXIT;
```

### 3. Application Deployment

```bash
# Create application directory
sudo mkdir -p /opt/stake-management
sudo chown $USER:$USER /opt/stake-management
cd /opt/stake-management

# Clone or copy application files
# (Assuming you have the modernized codebase)
git clone <your-repository> .

# Install dependencies
npm install

# Install client dependencies
cd client && npm install && cd ..

# Install server dependencies
cd server && npm install && cd ..
```

### 4. Environment Configuration

```bash
# Create production environment file
cp .env.example .env.production
```

Edit `.env.production`:
```bash
# Application
NODE_ENV=production
PORT=8080
HOST=0.0.0.0

# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=stake_management_v2
DB_USER=stake_app
DB_PASSWORD=YourSecurePassword123!@#
DB_SSL=true

# Security
JWT_ACCESS_SECRET=$(openssl rand -base64 64)
JWT_REFRESH_SECRET=$(openssl rand -base64 64)
ENCRYPTION_KEY=$(openssl rand -base64 32)
SESSION_SECRET=$(openssl rand -base64 64)
SALT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# CORS
CORS_ORIGIN=https://your-domain.com

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/stake-management/app.log

# SSL
SSL_CERT_PATH=/etc/ssl/certs/stake-management.crt
SSL_KEY_PATH=/etc/ssl/private/stake-management.key
```

### 5. Database Migration

```bash
# Run database migrations
cd server
npm run db:migrate

# Seed initial data
npm run db:seed
```

### 6. SSL Certificate Setup

#### Option A: Self-Signed Certificate (Development/Internal Use)
```bash
# Create SSL directory
sudo mkdir -p /etc/ssl/stake-management

# Generate private key
sudo openssl genrsa -out /etc/ssl/stake-management/private.key 2048

# Generate certificate signing request
sudo openssl req -new -key /etc/ssl/stake-management/private.key \
  -out /etc/ssl/stake-management/certificate.csr

# Generate self-signed certificate
sudo openssl x509 -req -days 365 \
  -in /etc/ssl/stake-management/certificate.csr \
  -signkey /etc/ssl/stake-management/private.key \
  -out /etc/ssl/stake-management/certificate.crt

# Set proper permissions
sudo chmod 600 /etc/ssl/stake-management/private.key
sudo chmod 644 /etc/ssl/stake-management/certificate.crt
```

#### Option B: CA-Issued Certificate
```bash
# Copy your CA-issued certificates
sudo cp your-certificate.crt /etc/ssl/stake-management/certificate.crt
sudo cp your-private.key /etc/ssl/stake-management/private.key
sudo cp ca-bundle.crt /etc/ssl/stake-management/ca-bundle.crt

# Set proper permissions
sudo chmod 600 /etc/ssl/stake-management/private.key
sudo chmod 644 /etc/ssl/stake-management/certificate.crt
sudo chmod 644 /etc/ssl/stake-management/ca-bundle.crt
```

### 7. Firewall Configuration

```bash
# Install and configure UFW
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing

# Allow SSH (adjust port if needed)
sudo ufw allow 22/tcp

# Allow HTTP and HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow MySQL only from localhost
sudo ufw allow from 127.0.0.1 to any port 3306

# Check firewall status
sudo ufw status verbose
```

### 8. Process Management with PM2

```bash
# Install PM2 globally
sudo npm install -g pm2

# Create PM2 ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [
    {
      name: 'stake-management-server',
      script: './server/dist/app.js',
      cwd: '/opt/stake-management',
      env: {
        NODE_ENV: 'production',
        PORT: 8080
      },
      instances: 'max',
      exec_mode: 'cluster',
      max_memory_restart: '1G',
      error_file: '/var/log/stake-management/pm2-error.log',
      out_file: '/var/log/stake-management/pm2-out.log',
      log_file: '/var/log/stake-management/pm2.log',
      time: true,
      autorestart: true,
      watch: false,
      max_restarts: 10,
      min_uptime: '10s'
    }
  ]
};
EOF

# Create log directory
sudo mkdir -p /var/log/stake-management
sudo chown $USER:$USER /var/log/stake-management

# Start application with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
pm2 startup
sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u $USER --hp $HOME
```

### 9. Nginx Reverse Proxy (Optional but Recommended)

```bash
# Install Nginx
sudo apt install -y nginx

# Create Nginx configuration
sudo tee /etc/nginx/sites-available/stake-management << EOF
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://\$server_name\$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/ssl/stake-management/certificate.crt;
    ssl_certificate_key /etc/ssl/stake-management/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Serve static files
    location / {
        root /opt/stake-management/client/dist;
        try_files \$uri \$uri/ /index.html;
    }

    # Proxy API requests
    location /api {
        proxy_pass http://localhost:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
    }

    # Security settings
    client_max_body_size 10M;
    client_body_timeout 60s;
    client_header_timeout 60s;
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/stake-management /etc/nginx/sites-enabled/
sudo rm /etc/nginx/sites-enabled/default

# Test and restart Nginx
sudo nginx -t
sudo systemctl restart nginx
sudo systemctl enable nginx
```

### 10. Build and Start Application

```bash
# Build the application
cd /opt/stake-management
npm run build

# Start with PM2
pm2 restart all
```

## 🔧 Post-Deployment Configuration

### 1. Create Initial Admin User

```bash
# Connect to MySQL
mysql -u stake_app -p stake_management_v2

# Insert admin user (password: Admin123!@#)
INSERT INTO users (username, email, password_hash, salt, role_id, full_name, is_active) 
VALUES (
  'admin', 
  '<EMAIL>', 
  '$2b$12$hash_here', 
  'salt_here', 
  1, 
  'System Administrator', 
  true
);
```

### 2. Configure Backup Strategy

```bash
# Create backup script
sudo tee /opt/stake-management/scripts/backup.sh << EOF
#!/bin/bash
BACKUP_DIR="/opt/backups/stake-management"
DATE=\$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p \$BACKUP_DIR

# Database backup
mysqldump -u stake_app -p'YourSecurePassword123!@#' stake_management_v2 > \$BACKUP_DIR/db_\$DATE.sql

# Application backup
tar -czf \$BACKUP_DIR/app_\$DATE.tar.gz /opt/stake-management --exclude=node_modules

# Keep only last 7 days of backups
find \$BACKUP_DIR -name "*.sql" -mtime +7 -delete
find \$BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF

# Make executable
sudo chmod +x /opt/stake-management/scripts/backup.sh

# Add to crontab (daily backup at 2 AM)
echo "0 2 * * * /opt/stake-management/scripts/backup.sh" | sudo crontab -
```

### 3. Monitoring Setup

```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Setup log rotation
sudo tee /etc/logrotate.d/stake-management << EOF
/var/log/stake-management/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        pm2 reload all
    endscript
}
EOF
```

## ✅ Verification Checklist

### Security Verification
- [ ] HTTPS is working correctly
- [ ] HTTP redirects to HTTPS
- [ ] Firewall rules are active
- [ ] Database access is restricted to localhost
- [ ] SSL certificate is valid
- [ ] Security headers are present

### Application Verification
- [ ] Application loads without errors
- [ ] Login functionality works
- [ ] Database connections are successful
- [ ] API endpoints respond correctly
- [ ] File uploads work (if applicable)
- [ ] Responsive design works on mobile

### Performance Verification
- [ ] Page load times < 2 seconds
- [ ] Database queries are optimized
- [ ] Static files are served efficiently
- [ ] Memory usage is within limits
- [ ] CPU usage is reasonable

### Backup Verification
- [ ] Automated backups are running
- [ ] Backup files are created successfully
- [ ] Restore procedure has been tested
- [ ] Backup retention policy is working

## 🚨 Troubleshooting

### Common Issues

1. **Application won't start**
   ```bash
   # Check PM2 logs
   pm2 logs
   
   # Check system logs
   sudo journalctl -u nginx
   ```

2. **Database connection errors**
   ```bash
   # Check MySQL status
   sudo systemctl status mysql
   
   # Test connection
   mysql -u stake_app -p stake_management_v2
   ```

3. **SSL certificate issues**
   ```bash
   # Test certificate
   openssl x509 -in /etc/ssl/stake-management/certificate.crt -text -noout
   
   # Check Nginx configuration
   sudo nginx -t
   ```

## 📞 Support

For technical support or issues:
1. Check application logs: `/var/log/stake-management/`
2. Check PM2 status: `pm2 status`
3. Check system resources: `htop`
4. Review security logs: `sudo tail -f /var/log/auth.log`
