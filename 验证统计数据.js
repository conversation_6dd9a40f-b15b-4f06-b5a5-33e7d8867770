// 验证待办任务统计数据的正确性
// 这个脚本用于验证修复后的统计逻辑是否正确

const verifyStatistics = async () => {
  console.log('🔍 开始验证待办任务统计数据...');
  
  try {
    // 获取统计数据
    const response = await fetch('http://localhost:8080/api/tasks/statistics');
    const result = await response.json();
    
    if (!result.success) {
      console.error('❌ 获取统计数据失败:', result.message);
      return;
    }
    
    const { typeStats, statusStats, typeStatusStats } = result.data;
    
    console.log('📊 统计数据:');
    console.log('typeStats:', typeStats);
    console.log('statusStats:', statusStats);
    console.log('typeStatusStats:', typeStatusStats);
    
    // 验证任务类型总数
    console.log('\n🧮 验证任务类型总数:');
    const calculatedTotal = typeStats.年审年报 + typeStats.地址维护 + typeStats.自定义任务;
    console.log(`年审年报(${typeStats.年审年报}) + 地址维护(${typeStats.地址维护}) + 自定义任务(${typeStats.自定义任务}) = ${calculatedTotal}`);
    console.log(`全部任务数: ${typeStats.全部}`);
    console.log(`验证结果: ${calculatedTotal === typeStats.全部 ? '✅ 正确' : '❌ 错误'}`);
    
    // 验证任务状态总数
    console.log('\n🧮 验证任务状态总数:');
    const calculatedStatusTotal = statusStats.已逾期 + statusStats.未开始 + statusStats.进行中 + statusStats.待核实 + statusStats.已完成;
    console.log(`已逾期(${statusStats.已逾期}) + 未开始(${statusStats.未开始}) + 进行中(${statusStats.进行中}) + 待核实(${statusStats.待核实}) + 已完成(${statusStats.已完成}) = ${calculatedStatusTotal}`);
    console.log(`全部任务数: ${typeStats.全部}`);
    console.log(`验证结果: ${calculatedStatusTotal === typeStats.全部 ? '✅ 正确' : '❌ 错误'}`);
    
    // 验证各类型的状态统计
    console.log('\n🧮 验证各类型的状态统计:');
    
    // 年审年报
    const annualTotal = typeStatusStats.年审年报.已逾期 + typeStatusStats.年审年报.未开始 + 
                       typeStatusStats.年审年报.进行中 + typeStatusStats.年审年报.待核实 + typeStatusStats.年审年报.已完成;
    console.log(`年审年报总数: 计算值(${annualTotal}) vs 统计值(${typeStats.年审年报}) ${annualTotal === typeStats.年审年报 ? '✅' : '❌'}`);
    
    // 地址维护
    const addressTotal = typeStatusStats.地址维护.已逾期 + typeStatusStats.地址维护.未开始 + 
                        typeStatusStats.地址维护.进行中 + typeStatusStats.地址维护.待核实 + typeStatusStats.地址维护.已完成;
    console.log(`地址维护总数: 计算值(${addressTotal}) vs 统计值(${typeStats.地址维护}) ${addressTotal === typeStats.地址维护 ? '✅' : '❌'}`);
    
    // 自定义任务
    const customTotal = typeStatusStats.自定义任务.已逾期 + typeStatusStats.自定义任务.未开始 + 
                       typeStatusStats.自定义任务.进行中 + typeStatusStats.自定义任务.待核实 + typeStatusStats.自定义任务.已完成;
    console.log(`自定义任务总数: 计算值(${customTotal}) vs 统计值(${typeStats.自定义任务}) ${customTotal === typeStats.自定义任务 ? '✅' : '❌'}`);
    
    // 验证待核实数据的加总
    console.log('\n🧮 验证待核实数据的加总:');
    const calculatedPendingTotal = typeStatusStats.年审年报.待核实 + typeStatusStats.地址维护.待核实 + typeStatusStats.自定义任务.待核实;
    console.log(`年审年报待核实(${typeStatusStats.年审年报.待核实}) + 地址维护待核实(${typeStatusStats.地址维护.待核实}) + 自定义任务待核实(${typeStatusStats.自定义任务.待核实}) = ${calculatedPendingTotal}`);
    console.log(`全部待核实: ${statusStats.待核实}`);
    console.log(`验证结果: ${calculatedPendingTotal === statusStats.待核实 ? '✅ 正确' : '❌ 错误'}`);
    
    // 验证已逾期数据的加总
    console.log('\n🧮 验证已逾期数据的加总:');
    const calculatedOverdueTotal = typeStatusStats.年审年报.已逾期 + typeStatusStats.地址维护.已逾期 + typeStatusStats.自定义任务.已逾期;
    console.log(`年审年报已逾期(${typeStatusStats.年审年报.已逾期}) + 地址维护已逾期(${typeStatusStats.地址维护.已逾期}) + 自定义任务已逾期(${typeStatusStats.自定义任务.已逾期}) = ${calculatedOverdueTotal}`);
    console.log(`全部已逾期: ${statusStats.已逾期}`);
    console.log(`验证结果: ${calculatedOverdueTotal === statusStats.已逾期 ? '✅ 正确' : '❌ 错误'}`);
    
    // 验证未完成数据的加总
    console.log('\n🧮 验证未完成数据的加总:');
    const calculatedIncompleteTotal = 
      (typeStatusStats.年审年报.已逾期 + typeStatusStats.年审年报.未开始 + typeStatusStats.年审年报.进行中) +
      (typeStatusStats.地址维护.已逾期 + typeStatusStats.地址维护.未开始 + typeStatusStats.地址维护.进行中) +
      (typeStatusStats.自定义任务.已逾期 + typeStatusStats.自定义任务.未开始 + typeStatusStats.自定义任务.进行中);
    
    const totalIncomplete = statusStats.已逾期 + statusStats.未开始 + statusStats.进行中;
    console.log(`各类型未完成总计: ${calculatedIncompleteTotal}`);
    console.log(`全部未完成(已逾期+未开始+进行中): ${totalIncomplete}`);
    console.log(`验证结果: ${calculatedIncompleteTotal === totalIncomplete ? '✅ 正确' : '❌ 错误'}`);
    
    // 总结
    console.log('\n📋 验证总结:');
    const allCorrect = 
      calculatedTotal === typeStats.全部 &&
      calculatedStatusTotal === typeStats.全部 &&
      annualTotal === typeStats.年审年报 &&
      addressTotal === typeStats.地址维护 &&
      customTotal === typeStats.自定义任务 &&
      calculatedPendingTotal === statusStats.待核实 &&
      calculatedOverdueTotal === statusStats.已逾期 &&
      calculatedIncompleteTotal === totalIncomplete;
    
    if (allCorrect) {
      console.log('🎉 所有统计数据验证通过！统计逻辑修复成功！');
    } else {
      console.log('⚠️ 部分统计数据存在问题，需要进一步检查');
    }
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
  }
  
  console.log('🏁 验证完成');
};

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  console.log('在浏览器中运行验证...');
  verifyStatistics();
} else {
  console.log('请在浏览器控制台中运行此验证脚本');
  console.log('复制以下代码到浏览器控制台:');
  console.log(verifyStatistics.toString());
  console.log('然后执行: verifyStatistics()');
}

// 导出函数供浏览器使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = verifyStatistics;
}
