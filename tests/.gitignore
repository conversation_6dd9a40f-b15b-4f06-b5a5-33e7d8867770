# Test artifacts and generated files
# These files are generated during testing and should not be committed

# Test reports
reports/
!reports/.gitkeep

# Coverage reports
coverage/
!coverage/.gitkeep

# Test logs
logs/
!logs/.gitkeep

# Test screenshots and videos (for E2E tests)
screenshots/
videos/
test-results/

# Temporary test files
*.tmp
*.temp
test-*.log
debug-*.log

# Test database dumps
*.sql.backup
test-data-*.sql

# Performance test results
performance/results/
benchmarks/results/

# Test cache files
.vitest/
.jest/
.nyc_output/

# Test environment files (may contain sensitive data)
.env.test.local
test.env.local

# Node modules for test-specific packages
node_modules/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Backup files
*.bak
*.backup

# Test artifacts from specific tools
playwright-report/
test-results/
coverage.lcov
junit.xml

# Keep directory structure but ignore contents
*/
!*/
!*.md
!*.ts
!*.js
!*.json
!*.sh
