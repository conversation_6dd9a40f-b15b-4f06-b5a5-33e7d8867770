// Example unit test for company controller
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { Request, Response } from 'express';
import { CompanyController } from '@/controllers/CompanyController';
import { CompanyService } from '@/services/CompanyService';

// Mock the service
vi.mock('@/services/CompanyService');

describe('CompanyController', () => {
  let companyController: CompanyController;
  let mockCompanyService: vi.Mocked<CompanyService>;
  let mockRequest: Partial<Request>;
  let mockResponse: Partial<Response>;

  beforeEach(() => {
    // Reset mocks
    vi.clearAllMocks();
    
    // Create mock service
    mockCompanyService = vi.mocked(new CompanyService());
    companyController = new CompanyController(mockCompanyService);
    
    // Create mock request and response
    mockRequest = {
      body: {},
      params: {},
      query: {},
      user: { id: 1, role: 'admin' }
    };
    
    mockResponse = {
      status: vi.fn().mockReturnThis(),
      json: vi.fn().mockReturnThis(),
      send: vi.fn().mockReturnThis()
    };
  });

  describe('createCompany', () => {
    it('should create a company successfully', async () => {
      // Arrange
      const companyData = {
        companyNameCn: '测试公司',
        companyNameEn: 'Test Company',
        registeredCapital: 1000000,
        establishDate: '2023-01-15',
        businessSegment: '电子商务',
        region: '华南',
        agency: '代理机构A',
        annualUpdate: '不管年审',
        registeredAddress: '测试地址',
        operationStatus: '正常经营'
      };
      
      const expectedResult = {
        id: 1,
        ...companyData,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      mockRequest.body = companyData;
      mockCompanyService.createCompany.mockResolvedValue(expectedResult);
      
      // Act
      await companyController.createCompany(
        mockRequest as Request,
        mockResponse as Response
      );
      
      // Assert
      expect(mockCompanyService.createCompany).toHaveBeenCalledWith(companyData, 1);
      expect(mockResponse.status).toHaveBeenCalledWith(201);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: '公司创建成功',
        data: expectedResult
      });
    });

    it('should handle validation errors', async () => {
      // Arrange
      const invalidData = {
        companyNameCn: '', // Invalid: empty name
        companyNameEn: 'Test Company'
      };
      
      mockRequest.body = invalidData;
      mockCompanyService.createCompany.mockRejectedValue(
        new Error('公司中文名不能为空')
      );
      
      // Act
      await companyController.createCompany(
        mockRequest as Request,
        mockResponse as Response
      );
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(400);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: '公司中文名不能为空'
      });
    });

    it('should handle duplicate company names', async () => {
      // Arrange
      const duplicateData = {
        companyNameCn: '已存在公司',
        companyNameEn: 'Existing Company',
        registeredCapital: 1000000
      };
      
      mockRequest.body = duplicateData;
      mockCompanyService.createCompany.mockRejectedValue(
        new Error('公司名称已存在')
      );
      
      // Act
      await companyController.createCompany(
        mockRequest as Request,
        mockResponse as Response
      );
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(409);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: '公司名称已存在'
      });
    });
  });

  describe('getCompanies', () => {
    it('should return paginated companies list', async () => {
      // Arrange
      const mockCompanies = [
        {
          id: 1,
          companyNameCn: '公司A',
          companyNameEn: 'Company A',
          registeredCapital: 1000000,
          operationStatus: '正常经营'
        },
        {
          id: 2,
          companyNameCn: '公司B',
          companyNameEn: 'Company B',
          registeredCapital: 2000000,
          operationStatus: '正常经营'
        }
      ];
      
      const mockResult = {
        data: mockCompanies,
        total: 2,
        page: 1,
        pageSize: 10
      };
      
      mockRequest.query = { page: '1', pageSize: '10' };
      mockCompanyService.getCompanies.mockResolvedValue(mockResult);
      
      // Act
      await companyController.getCompanies(
        mockRequest as Request,
        mockResponse as Response
      );
      
      // Assert
      expect(mockCompanyService.getCompanies).toHaveBeenCalledWith({
        page: 1,
        pageSize: 10
      });
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockResult
      });
    });

    it('should handle search queries', async () => {
      // Arrange
      const searchQuery = '测试';
      mockRequest.query = { 
        page: '1', 
        pageSize: '10',
        search: searchQuery
      };
      
      const mockResult = {
        data: [],
        total: 0,
        page: 1,
        pageSize: 10
      };
      
      mockCompanyService.getCompanies.mockResolvedValue(mockResult);
      
      // Act
      await companyController.getCompanies(
        mockRequest as Request,
        mockResponse as Response
      );
      
      // Assert
      expect(mockCompanyService.getCompanies).toHaveBeenCalledWith({
        page: 1,
        pageSize: 10,
        search: searchQuery
      });
    });
  });

  describe('getCompanyById', () => {
    it('should return company details', async () => {
      // Arrange
      const companyId = 1;
      const mockCompany = {
        id: companyId,
        companyNameCn: '测试公司',
        companyNameEn: 'Test Company',
        registeredCapital: 1000000,
        shareholders: [],
        executives: [],
        investments: []
      };
      
      mockRequest.params = { id: companyId.toString() };
      mockCompanyService.getCompanyById.mockResolvedValue(mockCompany);
      
      // Act
      await companyController.getCompanyById(
        mockRequest as Request,
        mockResponse as Response
      );
      
      // Assert
      expect(mockCompanyService.getCompanyById).toHaveBeenCalledWith(companyId);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        data: mockCompany
      });
    });

    it('should handle company not found', async () => {
      // Arrange
      const companyId = 999;
      mockRequest.params = { id: companyId.toString() };
      mockCompanyService.getCompanyById.mockResolvedValue(null);
      
      // Act
      await companyController.getCompanyById(
        mockRequest as Request,
        mockResponse as Response
      );
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: '公司不存在'
      });
    });
  });

  describe('updateCompany', () => {
    it('should update company successfully', async () => {
      // Arrange
      const companyId = 1;
      const updateData = {
        registeredCapital: 2000000,
        operationStatus: '正常经营'
      };
      
      const updatedCompany = {
        id: companyId,
        companyNameCn: '测试公司',
        companyNameEn: 'Test Company',
        ...updateData,
        updatedAt: new Date()
      };
      
      mockRequest.params = { id: companyId.toString() };
      mockRequest.body = updateData;
      mockCompanyService.updateCompany.mockResolvedValue(updatedCompany);
      
      // Act
      await companyController.updateCompany(
        mockRequest as Request,
        mockResponse as Response
      );
      
      // Assert
      expect(mockCompanyService.updateCompany).toHaveBeenCalledWith(
        companyId,
        updateData,
        1
      );
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: '公司更新成功',
        data: updatedCompany
      });
    });
  });

  describe('deleteCompany', () => {
    it('should delete company successfully', async () => {
      // Arrange
      const companyId = 1;
      mockRequest.params = { id: companyId.toString() };
      mockCompanyService.deleteCompany.mockResolvedValue(true);
      
      // Act
      await companyController.deleteCompany(
        mockRequest as Request,
        mockResponse as Response
      );
      
      // Assert
      expect(mockCompanyService.deleteCompany).toHaveBeenCalledWith(companyId, 1);
      expect(mockResponse.status).toHaveBeenCalledWith(200);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: true,
        message: '公司删除成功'
      });
    });

    it('should handle company not found for deletion', async () => {
      // Arrange
      const companyId = 999;
      mockRequest.params = { id: companyId.toString() };
      mockCompanyService.deleteCompany.mockResolvedValue(false);
      
      // Act
      await companyController.deleteCompany(
        mockRequest as Request,
        mockResponse as Response
      );
      
      // Assert
      expect(mockResponse.status).toHaveBeenCalledWith(404);
      expect(mockResponse.json).toHaveBeenCalledWith({
        success: false,
        message: '公司不存在'
      });
    });
  });
});
