#!/bin/bash

# Test cleanup script for stake/equity management system
# This script removes all test artifacts and resets the test environment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test directories
TESTS_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PROJECT_ROOT="$(cd "$TESTS_DIR/.." && pwd)"

echo -e "${BLUE}🧹 Cleaning up test environment${NC}"
echo -e "${BLUE}================================${NC}"

# Function to print section headers
print_section() {
    echo -e "\n${BLUE}$1${NC}"
    echo -e "${BLUE}$(printf '=%.0s' {1..30})${NC}"
}

# Function to safely remove directory/file
safe_remove() {
    local path="$1"
    local description="$2"
    
    if [ -e "$path" ]; then
        echo -e "🗑️  Removing $description..."
        rm -rf "$path"
        echo -e "${GREEN}✅ $description removed${NC}"
    else
        echo -e "${YELLOW}⚠️  $description not found, skipping${NC}"
    fi
}

# Change to project root
cd "$PROJECT_ROOT"

# 1. Clean test database
print_section "Database Cleanup"

if command -v mysql >/dev/null 2>&1; then
    echo -e "🗄️  Cleaning test database..."
    
    # Try to drop test database
    mysql -u txuser -ptxpassword -e "DROP DATABASE IF EXISTS stake_management_test;" 2>/dev/null || {
        echo -e "${YELLOW}⚠️  Could not connect to MySQL or database doesn't exist${NC}"
    }
    
    echo -e "${GREEN}✅ Test database cleanup complete${NC}"
else
    echo -e "${YELLOW}⚠️  MySQL not found, skipping database cleanup${NC}"
fi

# 2. Clean test reports and artifacts
print_section "Test Artifacts Cleanup"

safe_remove "$TESTS_DIR/reports" "test reports"
safe_remove "$TESTS_DIR/coverage" "coverage reports"
safe_remove "$TESTS_DIR/logs" "test logs"
safe_remove "$TESTS_DIR/screenshots" "test screenshots"
safe_remove "$TESTS_DIR/videos" "test videos"

# 3. Clean temporary test files
print_section "Temporary Files Cleanup"

safe_remove "$PROJECT_ROOT/.nyc_output" "NYC coverage cache"
safe_remove "$PROJECT_ROOT/coverage" "root coverage directory"
safe_remove "$PROJECT_ROOT/test-results" "test results"
safe_remove "$PROJECT_ROOT/playwright-report" "Playwright reports"

# 4. Clean test cache
print_section "Test Cache Cleanup"

safe_remove "$PROJECT_ROOT/node_modules/.cache" "Node modules cache"
safe_remove "$PROJECT_ROOT/.vitest" "Vitest cache"
safe_remove "$PROJECT_ROOT/.jest" "Jest cache"

# 5. Clean environment files
print_section "Environment Cleanup"

safe_remove "$PROJECT_ROOT/.env.test" "test environment file"
safe_remove "$PROJECT_ROOT/.env.test.local" "local test environment file"

# 6. Clean build artifacts from tests
print_section "Build Artifacts Cleanup"

safe_remove "$PROJECT_ROOT/dist-test" "test build directory"
safe_remove "$PROJECT_ROOT/build-test" "test build directory"

# 7. Clean log files
print_section "Log Files Cleanup"

# Find and remove test log files
find "$PROJECT_ROOT" -name "*.test.log" -type f -delete 2>/dev/null || true
find "$PROJECT_ROOT" -name "test-*.log" -type f -delete 2>/dev/null || true
find "$PROJECT_ROOT" -name "debug-*.log" -type f -delete 2>/dev/null || true

echo -e "${GREEN}✅ Log files cleaned${NC}"

# 8. Reset test directories structure
print_section "Recreating Clean Test Structure"

# Recreate essential test directories
mkdir -p "$TESTS_DIR/reports"
mkdir -p "$TESTS_DIR/coverage"
mkdir -p "$TESTS_DIR/logs"

# Create .gitkeep files to preserve directory structure
touch "$TESTS_DIR/reports/.gitkeep"
touch "$TESTS_DIR/coverage/.gitkeep"
touch "$TESTS_DIR/logs/.gitkeep"

echo -e "${GREEN}✅ Clean test structure recreated${NC}"

# 9. Clean package-lock if needed
print_section "Package Dependencies Cleanup"

if [ -f "$PROJECT_ROOT/package-lock.json" ]; then
    echo -e "📦 Checking for test-only dependencies..."
    
    # This is optional - only if you want to clean test dependencies
    # npm prune --production
    
    echo -e "${GREEN}✅ Dependencies check complete${NC}"
fi

# 10. Final verification
print_section "Cleanup Verification"

echo -e "🔍 Verifying cleanup..."

# Check if major test artifacts are gone
CLEANUP_SUCCESS=true

if [ -d "$TESTS_DIR/reports" ] && [ "$(ls -A $TESTS_DIR/reports 2>/dev/null | grep -v .gitkeep)" ]; then
    echo -e "${YELLOW}⚠️  Some test reports still exist${NC}"
    CLEANUP_SUCCESS=false
fi

if [ -d "$TESTS_DIR/coverage" ] && [ "$(ls -A $TESTS_DIR/coverage 2>/dev/null | grep -v .gitkeep)" ]; then
    echo -e "${YELLOW}⚠️  Some coverage files still exist${NC}"
    CLEANUP_SUCCESS=false
fi

# Check for any remaining test databases
if command -v mysql >/dev/null 2>&1; then
    if mysql -u txuser -ptxpassword -e "USE stake_management_test;" 2>/dev/null; then
        echo -e "${YELLOW}⚠️  Test database still exists${NC}"
        CLEANUP_SUCCESS=false
    fi
fi

# Final result
print_section "Cleanup Complete"

if [ "$CLEANUP_SUCCESS" = true ]; then
    echo -e "${GREEN}🎉 Test environment cleanup completed successfully!${NC}"
    echo -e "${GREEN}✅ All test artifacts have been removed${NC}"
    echo -e "${GREEN}✅ Test directories are clean and ready${NC}"
    exit 0
else
    echo -e "${YELLOW}⚠️  Cleanup completed with some warnings${NC}"
    echo -e "${YELLOW}📋 Please review the warnings above${NC}"
    exit 0
fi
