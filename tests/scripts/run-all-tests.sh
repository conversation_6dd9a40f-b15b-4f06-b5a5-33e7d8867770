#!/bin/bash

# Complete test suite runner for stake/equity management system
# This script runs all types of tests in the correct order

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Test directories
TESTS_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
PROJECT_ROOT="$(cd "$TESTS_DIR/.." && pwd)"

echo -e "${BLUE}🚀 Starting complete test suite for Stake/Equity Management System${NC}"
echo -e "${BLUE}================================================${NC}"

# Function to print section headers
print_section() {
    echo -e "\n${BLUE}$1${NC}"
    echo -e "${BLUE}$(printf '=%.0s' {1..50})${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to run tests with error handling
run_test_suite() {
    local test_name="$1"
    local test_command="$2"
    local required="$3"
    
    print_section "Running $test_name"
    
    if eval "$test_command"; then
        echo -e "${GREEN}✅ $test_name passed${NC}"
        return 0
    else
        echo -e "${RED}❌ $test_name failed${NC}"
        if [ "$required" = "required" ]; then
            echo -e "${RED}💥 Required test suite failed. Stopping execution.${NC}"
            exit 1
        else
            echo -e "${YELLOW}⚠️ Optional test suite failed. Continuing...${NC}"
            return 1
        fi
    fi
}

# Change to project root
cd "$PROJECT_ROOT"

# Check prerequisites
print_section "Checking Prerequisites"

if ! command_exists npm; then
    echo -e "${RED}❌ npm is not installed${NC}"
    exit 1
fi

if ! command_exists node; then
    echo -e "${RED}❌ Node.js is not installed${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites check passed${NC}"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_section "Installing Dependencies"
    npm install
fi

# Create necessary directories
mkdir -p tests/reports
mkdir -p tests/coverage
mkdir -p tests/logs

# Set test environment
export NODE_ENV=test
export CI=true

# Initialize test counters
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 1. Linting and Code Quality
print_section "Code Quality Checks"
if run_test_suite "ESLint" "npm run lint" "required"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

if run_test_suite "TypeScript Check" "npm run type-check" "required"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

# 2. Unit Tests
print_section "Unit Tests"
if run_test_suite "Unit Tests" "npm run test:unit" "required"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

# 3. Integration Tests
print_section "Integration Tests"
if run_test_suite "Integration Tests" "npm run test:integration" "required"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

# 4. Security Tests
print_section "Security Tests"
if run_test_suite "Security Audit" "npm audit --audit-level moderate" "optional"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

if run_test_suite "Security Tests" "npm run test:security" "optional"; then
    ((PASSED_TESTS++))
else
    ((FAILED_TESTS++))
fi
((TOTAL_TESTS++))

# 5. End-to-End Tests (if available)
if [ -f "tests/e2e" ]; then
    print_section "End-to-End Tests"
    if run_test_suite "E2E Tests" "npm run test:e2e" "optional"; then
        ((PASSED_TESTS++))
    else
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
fi

# 6. Performance Tests (if available)
if [ -f "tests/performance" ]; then
    print_section "Performance Tests"
    if run_test_suite "Performance Tests" "npm run test:performance" "optional"; then
        ((PASSED_TESTS++))
    else
        ((FAILED_TESTS++))
    fi
    ((TOTAL_TESTS++))
fi

# 7. Coverage Report
print_section "Generating Coverage Report"
if run_test_suite "Coverage Report" "npm run test:coverage" "optional"; then
    echo -e "${GREEN}📊 Coverage report generated in tests/coverage/${NC}"
fi

# Generate final report
print_section "Test Results Summary"

echo -e "📊 ${BLUE}Test Suite Results:${NC}"
echo -e "   Total Test Suites: $TOTAL_TESTS"
echo -e "   ${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "   ${RED}Failed: $FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All tests passed successfully!${NC}"
    echo -e "${GREEN}✅ System is ready for deployment${NC}"
    exit 0
else
    echo -e "\n${YELLOW}⚠️ Some tests failed${NC}"
    echo -e "${YELLOW}📋 Please review the test results above${NC}"
    
    if [ $PASSED_TESTS -ge $((TOTAL_TESTS * 80 / 100)) ]; then
        echo -e "${YELLOW}✅ Core functionality appears to be working (80%+ pass rate)${NC}"
        exit 0
    else
        echo -e "${RED}❌ Too many critical tests failed${NC}"
        exit 1
    fi
fi
