// Global test setup - runs once before all tests
import { beforeAll, afterAll } from 'vitest';
import mysql from 'mysql2/promise';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Test database configuration
const TEST_DB_CONFIG = {
  host: 'localhost',
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_test',
  multipleStatements: true
};

let testDbConnection: mysql.Connection;

export async function setup() {
  console.log('🚀 Setting up test environment...');
  
  try {
    // 1. Create test database if it doesn't exist
    await createTestDatabase();
    
    // 2. Run database migrations for test database
    await runTestMigrations();
    
    // 3. Seed test database with fixtures
    await seedTestDatabase();
    
    // 4. Setup test environment variables
    setupTestEnvironment();
    
    console.log('✅ Test environment setup complete');
  } catch (error) {
    console.error('❌ Test environment setup failed:', error);
    throw error;
  }
}

export async function teardown() {
  console.log('🧹 Cleaning up test environment...');
  
  try {
    // Close database connections
    if (testDbConnection) {
      await testDbConnection.end();
    }
    
    // Clean up test artifacts
    await cleanupTestArtifacts();
    
    console.log('✅ Test environment cleanup complete');
  } catch (error) {
    console.error('❌ Test environment cleanup failed:', error);
  }
}

async function createTestDatabase() {
  console.log('📊 Creating test database...');
  
  // Connect without specifying database to create it
  const connection = await mysql.createConnection({
    host: TEST_DB_CONFIG.host,
    user: TEST_DB_CONFIG.user,
    password: TEST_DB_CONFIG.password
  });
  
  try {
    // Drop existing test database if it exists
    await connection.execute('DROP DATABASE IF EXISTS stake_management_test');
    
    // Create fresh test database
    await connection.execute('CREATE DATABASE stake_management_test');
    
    console.log('✅ Test database created successfully');
  } finally {
    await connection.end();
  }
}

async function runTestMigrations() {
  console.log('🔄 Running test database migrations...');
  
  // Connect to test database
  testDbConnection = await mysql.createConnection(TEST_DB_CONFIG);
  
  // Read and execute the enhanced schema
  const fs = await import('fs/promises');
  const path = await import('path');
  
  const schemaPath = path.resolve(__dirname, '../../enhanced-database-schema.sql');
  const schemaSql = await fs.readFile(schemaPath, 'utf-8');
  
  // Remove USE statement and execute schema
  const cleanSql = schemaSql
    .replace(/CREATE DATABASE IF NOT EXISTS.*?;/gi, '')
    .replace(/USE.*?;/gi, '');
  
  await testDbConnection.execute(cleanSql);
  
  console.log('✅ Test database migrations complete');
}

async function seedTestDatabase() {
  console.log('🌱 Seeding test database...');
  
  // Insert test roles
  await testDbConnection.execute(`
    INSERT INTO roles (name, display_name, permissions, is_active) VALUES
    ('super_admin', '超级管理员', '{"all": true}', true),
    ('admin', '管理员', '{"companies": ["read", "write"], "users": ["read", "write"]}', true),
    ('manager', '经理', '{"companies": ["read", "write"]}', true),
    ('analyst', '分析师', '{"companies": ["read"]}', true),
    ('viewer', '查看者', '{"companies": ["read"]}', true)
  `);
  
  // Insert test users
  await testDbConnection.execute(`
    INSERT INTO users (username, email, password_hash, salt, role_id, full_name, is_active) VALUES
    ('testadmin', '<EMAIL>', '$2b$12$test_hash', 'test_salt', 1, 'Test Admin', true),
    ('testuser', '<EMAIL>', '$2b$12$test_hash', 'test_salt', 4, 'Test User', true)
  `);
  
  // Insert test companies
  await testDbConnection.execute(`
    INSERT INTO companies (
      company_name_cn, company_name_en, registered_capital, establish_date,
      business_segment, region, agency, annual_update, registered_address, operation_status
    ) VALUES
    ('测试公司A', 'Test Company A', 1000.0000, '2023-01-15', '电子商务', '华南', '代理机构A', '不管年审', '测试地址A', '正常经营'),
    ('测试公司B', 'Test Company B', 2000.0000, '2023-02-20', '制造业', '华东', '代理机构B', '管年审（固定周期）', '测试地址B', '正常经营')
  `);
  
  console.log('✅ Test database seeded successfully');
}

function setupTestEnvironment() {
  console.log('⚙️ Setting up test environment variables...');
  
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.DB_HOST = TEST_DB_CONFIG.host;
  process.env.DB_USER = TEST_DB_CONFIG.user;
  process.env.DB_PASSWORD = TEST_DB_CONFIG.password;
  process.env.DB_NAME = TEST_DB_CONFIG.database;
  process.env.JWT_ACCESS_SECRET = 'test_jwt_access_secret';
  process.env.JWT_REFRESH_SECRET = 'test_jwt_refresh_secret';
  process.env.ENCRYPTION_KEY = 'test_encryption_key_32_characters';
  
  console.log('✅ Test environment variables set');
}

async function cleanupTestArtifacts() {
  console.log('🗑️ Cleaning up test artifacts...');
  
  try {
    // Remove test reports directory
    await execAsync('rm -rf tests/reports/*');
    
    // Remove test coverage directory
    await execAsync('rm -rf tests/coverage/*');
    
    // Remove test logs
    await execAsync('rm -rf tests/logs/*');
    
    console.log('✅ Test artifacts cleaned up');
  } catch (error) {
    console.warn('⚠️ Some test artifacts could not be cleaned up:', error.message);
  }
}

// Export for use in global setup
export { setup as default };
