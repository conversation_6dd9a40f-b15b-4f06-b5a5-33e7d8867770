import { defineConfig } from 'vitest/config';
import path from 'path';

export default defineConfig({
  test: {
    // Test environment
    environment: 'jsdom',
    
    // Global setup and teardown
    globalSetup: ['./tests/helpers/setup.ts'],
    setupFiles: ['./tests/helpers/test-setup.ts'],
    
    // Test file patterns
    include: [
      'tests/unit/**/*.{test,spec}.{js,ts,jsx,tsx}',
      'tests/integration/**/*.{test,spec}.{js,ts,jsx,tsx}'
    ],
    exclude: [
      'node_modules',
      'dist',
      'build',
      'tests/e2e/**/*'
    ],
    
    // Coverage configuration
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      reportsDirectory: './tests/coverage',
      exclude: [
        'node_modules/',
        'tests/',
        'dist/',
        'build/',
        '**/*.d.ts',
        '**/*.config.{js,ts}',
        '**/index.{js,ts}'
      ],
      thresholds: {
        global: {
          branches: 80,
          functions: 80,
          lines: 80,
          statements: 80
        }
      }
    },
    
    // Test timeout
    testTimeout: 10000,
    hookTimeout: 10000,
    
    // Parallel execution
    threads: true,
    maxThreads: 4,
    
    // Watch mode
    watch: false,
    
    // Reporter
    reporter: ['verbose', 'json'],
    outputFile: './tests/reports/test-results.json',
    
    // Mock configuration
    deps: {
      inline: ['@testing-library/jest-dom']
    }
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': path.resolve(__dirname, '../../src'),
      '@tests': path.resolve(__dirname, '../'),
      '@fixtures': path.resolve(__dirname, '../fixtures'),
      '@mocks': path.resolve(__dirname, '../mocks')
    }
  },
  
  // Define global variables for tests
  define: {
    'import.meta.env.VITE_API_URL': JSON.stringify('http://localhost:8080'),
    'import.meta.env.VITE_APP_ENV': JSON.stringify('test')
  }
});
