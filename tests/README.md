# Testing Directory

This directory contains all test files for the stake/equity management system. This structure ensures tests are organized and can be easily identified and removed when development is finished.

## Directory Structure

```
tests/
├── README.md                    # This file
├── unit/                        # Unit tests
│   ├── backend/                 # Backend unit tests
│   │   ├── controllers/         # Controller tests
│   │   ├── services/           # Service layer tests
│   │   ├── models/             # Model tests
│   │   └── utils/              # Utility function tests
│   └── frontend/               # Frontend unit tests
│       ├── components/         # Component tests
│       ├── hooks/              # Custom hook tests
│       ├── services/           # API service tests
│       └── utils/              # Utility function tests
├── integration/                # Integration tests
│   ├── api/                    # API endpoint tests
│   ├── database/               # Database integration tests
│   └── auth/                   # Authentication flow tests
├── e2e/                        # End-to-end tests
│   ├── user-flows/             # Complete user workflow tests
│   ├── admin-flows/            # Admin workflow tests
│   └── mobile/                 # Mobile-specific tests
├── performance/                # Performance tests
│   ├── load/                   # Load testing
│   ├── stress/                 # Stress testing
│   └── benchmarks/             # Performance benchmarks
├── security/                   # Security tests
│   ├── auth/                   # Authentication security tests
│   ├── input-validation/       # Input validation tests
│   └── penetration/            # Penetration testing scripts
├── fixtures/                   # Test data and fixtures
│   ├── database/               # Database seed data for tests
│   ├── api-responses/          # Mock API responses
│   └── files/                  # Test files (images, documents)
├── mocks/                      # Mock implementations
│   ├── api/                    # API mocks
│   ├── database/               # Database mocks
│   └── external-services/      # External service mocks
├── helpers/                    # Test helper functions
│   ├── setup.ts               # Test environment setup
│   ├── teardown.ts            # Test cleanup
│   ├── database-helpers.ts    # Database test utilities
│   └── auth-helpers.ts        # Authentication test utilities
├── config/                     # Test configuration
│   ├── jest.config.js         # Jest configuration
│   ├── vitest.config.ts       # Vitest configuration
│   ├── playwright.config.ts   # Playwright E2E config
│   └── test.env               # Test environment variables
└── scripts/                   # Test automation scripts
    ├── run-all-tests.sh       # Run complete test suite
    ├── run-unit-tests.sh      # Run only unit tests
    ├── run-integration-tests.sh # Run only integration tests
    ├── run-e2e-tests.sh       # Run only E2E tests
    ├── setup-test-db.sh       # Setup test database
    └── cleanup-tests.sh       # Clean up test artifacts
```

## Test Types

### 1. Unit Tests (`tests/unit/`)
- Test individual functions, components, and modules in isolation
- Fast execution, no external dependencies
- High code coverage target: 90%+

### 2. Integration Tests (`tests/integration/`)
- Test interaction between different modules
- Database integration, API endpoint testing
- Moderate execution time

### 3. End-to-End Tests (`tests/e2e/`)
- Test complete user workflows
- Browser automation with Playwright
- Slower execution, run before releases

### 4. Performance Tests (`tests/performance/`)
- Load testing, stress testing
- Performance benchmarks
- Run periodically or before major releases

### 5. Security Tests (`tests/security/`)
- Authentication and authorization testing
- Input validation and sanitization
- Penetration testing scripts

## Test Data Management

### Fixtures (`tests/fixtures/`)
- Consistent test data across all tests
- Database seed files for different scenarios
- Mock API responses for predictable testing

### Mocks (`tests/mocks/`)
- Mock external dependencies
- Database mocks for unit tests
- API mocks for frontend testing

## Running Tests

### All Tests
```bash
npm run test
# or
./tests/scripts/run-all-tests.sh
```

### Specific Test Types
```bash
npm run test:unit          # Unit tests only
npm run test:integration   # Integration tests only
npm run test:e2e          # E2E tests only
npm run test:performance  # Performance tests only
npm run test:security     # Security tests only
```

### Test Coverage
```bash
npm run test:coverage     # Generate coverage report
```

## Test Environment

### Environment Variables
- Use `tests/config/test.env` for test-specific configuration
- Separate test database: `stake_management_test`
- Test-specific API keys and secrets

### Database Setup
```bash
# Setup test database
./tests/scripts/setup-test-db.sh

# Reset test database
./tests/scripts/cleanup-tests.sh
```

## Best Practices

### 1. Test Organization
- Group related tests in appropriate directories
- Use descriptive file names: `user-authentication.test.ts`
- Follow naming convention: `*.test.ts` or `*.spec.ts`

### 2. Test Data
- Use fixtures for consistent test data
- Clean up test data after each test
- Don't rely on external data sources

### 3. Test Independence
- Each test should be independent
- Tests should not depend on execution order
- Use setup/teardown for test isolation

### 4. Performance
- Keep unit tests fast (< 100ms each)
- Use mocks to avoid external dependencies
- Run expensive tests (E2E, performance) separately

### 5. Maintenance
- Update tests when code changes
- Remove obsolete tests
- Keep test code quality high

## Cleanup for Production

When development is finished and you want to remove all test files:

```bash
# Remove entire tests directory
rm -rf tests/

# Remove test-related dependencies from package.json
# Remove test scripts from package.json
# Remove test configuration files
```

## CI/CD Integration

Tests are integrated into the CI/CD pipeline:

1. **Pre-commit**: Run unit tests and linting
2. **Pull Request**: Run unit and integration tests
3. **Pre-deployment**: Run full test suite including E2E
4. **Post-deployment**: Run smoke tests

## Test Reporting

- Test results are saved in `tests/reports/`
- Coverage reports in `tests/coverage/`
- Performance test results in `tests/performance/reports/`

This structure ensures all test-related files are contained within the `tests/` directory and can be easily identified and removed when no longer needed.
