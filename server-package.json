{"name": "stake-equity-management-server", "version": "1.0.0", "description": "Backend API for stake and equity management system", "private": true, "type": "module", "main": "dist/app.js", "scripts": {"dev": "tsx watch src/app.ts", "build": "tsc && tsc-alias", "start": "node dist/app.js", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint . --ext .ts --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext .ts --fix", "type-check": "tsc --noEmit", "db:migrate": "tsx src/database/migrate.ts", "db:seed": "tsx src/database/seed.ts", "db:reset": "tsx src/database/reset.ts", "db:generate": "prisma generate", "db:studio": "prisma studio", "docs:generate": "swagger-jsdoc -d swaggerDef.js src/routes/*.ts -o docs/swagger.json", "security:audit": "npm audit --audit-level moderate", "logs:clear": "rm -rf logs/*.log"}, "dependencies": {"express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "express-async-errors": "^3.1.1", "helmet": "^7.1.0", "cors": "^2.8.5", "compression": "^1.7.4", "morgan": "^1.10.0", "winston": "^3.11.0", "winston-daily-rotate-file": "^4.7.1", "mysql2": "^3.6.5", "prisma": "^5.7.1", "@prisma/client": "^5.7.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "crypto": "^1.0.1", "joi": "^17.11.0", "zod": "^3.22.4", "dotenv": "^16.3.1", "cookie-parser": "^1.4.6", "express-session": "^1.17.3", "connect-redis": "^7.1.0", "redis": "^4.6.11", "nodemailer": "^6.9.7", "multer": "^1.4.5-lts.1", "sharp": "^0.33.1", "pdf-parse": "^1.1.1", "xlsx": "^0.18.5", "node-cron": "^3.0.3", "uuid": "^9.0.1", "dayjs": "^1.11.10"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.4", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/cookie-parser": "^1.4.6", "@types/express-session": "^1.17.10", "@types/nodemailer": "^6.4.14", "@types/multer": "^1.4.11", "@types/node-cron": "^3.0.11", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "eslint": "^8.55.0", "typescript": "^5.3.3", "tsx": "^4.6.2", "tsc-alias": "^1.8.8", "vitest": "^1.0.4", "@vitest/coverage-v8": "^1.0.4", "supertest": "^6.3.3", "@types/supertest": "^6.0.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "@types/swagger-ui-express": "^4.1.6", "nodemon": "^3.0.2"}, "prisma": {"schema": "src/database/schema.prisma"}, "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}}