import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function checkTaskData() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 查看所有任务
    const [tasks] = await connection.query(
      'SELECT task_type, year, COUNT(*) as count FROM pending_tasks GROUP BY task_type, year ORDER BY year, task_type'
    );
    
    console.log('\n📊 任务统计:');
    tasks.forEach(task => {
      console.log(`${task.year}年 ${task.task_type}: ${task.count}个`);
    });

    // 查看2025年的具体任务
    const [tasks2025] = await connection.query(
      'SELECT task_type, task_status, COUNT(*) as count FROM pending_tasks WHERE year = 2025 GROUP BY task_type, task_status'
    );
    
    console.log('\n📋 2025年任务详情:');
    tasks2025.forEach(task => {
      console.log(`${task.task_type} - ${task.task_status}: ${task.count}个`);
    });

    // 查看公司数量
    const [companies] = await connection.query('SELECT COUNT(*) as count FROM companies');
    console.log(`\n🏢 总公司数: ${companies[0].count}`);

    // 查看2025年年审年报任务的公司
    const [annualCompanies] = await connection.query(
      'SELECT DISTINCT company_id FROM pending_tasks WHERE task_type = "年审年报" AND year = 2025'
    );
    console.log(`📊 2025年有年审年报任务的公司: ${annualCompanies.length}个`);

    // 查看2025年地址维护任务的公司
    const [addressCompanies] = await connection.query(
      'SELECT DISTINCT company_id FROM pending_tasks WHERE task_type = "地址维护" AND year = 2025'
    );
    console.log(`🏠 2025年有地址维护任务的公司: ${addressCompanies.length}个`);

  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
checkTaskData();
