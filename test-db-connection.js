import mysql from 'mysql2/promise';

const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management',
  socketPath: '/tmp/mysql.sock'
};

async function testConnection() {
  let connection;
  
  try {
    console.log('🔄 测试数据库连接...');
    console.log('配置:', { ...dbConfig, password: '***' });
    
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 测试查询
    const [tables] = await connection.execute('SHOW TABLES');
    console.log(`📋 发现 ${tables.length} 个表:`);
    tables.forEach(table => {
      console.log(`  - ${Object.values(table)[0]}`);
    });
    
    // 测试数据查询
    const [companies] = await connection.execute('SELECT COUNT(*) as count FROM companies');
    console.log(`📊 公司数量: ${companies[0].count}`);
    
  } catch (error) {
    console.error('❌ 连接失败:', error.message);
    console.error('错误代码:', error.code);
    console.error('错误详情:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

testConnection();
