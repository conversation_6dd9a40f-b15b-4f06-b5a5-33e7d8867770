import axios from 'axios';

async function testChangeLogsAPI() {
  try {
    console.log('🔍 测试公司变更记录API...');
    
    // 1. 测试获取变更记录列表
    console.log('\n📋 测试获取变更记录列表...');
    try {
      const response = await axios.get('http://localhost:8080/api/company-change-logs');
      console.log('✅ 获取变更记录成功');
      console.log(`📊 变更记录数量: ${response.data.data.length}`);
      
      if (response.data.data.length > 0) {
        console.log('📝 前3条记录:');
        response.data.data.slice(0, 3).forEach((record, index) => {
          console.log(`  ${index + 1}. ${record.companyName} - ${record.changeTypeText} - ${record.statusText}`);
        });
      }
    } catch (error) {
      console.error('❌ 获取变更记录失败:', error.response?.data || error.message);
    }

    // 2. 测试获取统计数据
    console.log('\n📊 测试获取统计数据...');
    try {
      const response = await axios.get('http://localhost:8080/api/company-change-logs/statistics');
      console.log('✅ 获取统计数据成功');
      console.log('📈 统计结果:', response.data.data);
    } catch (error) {
      console.error('❌ 获取统计数据失败:', error.response?.data || error.message);
    }

    // 3. 检查数据库中的变更记录
    console.log('\n🔍 检查数据库中的变更记录...');
    try {
      const response = await axios.get('http://localhost:8080/api/mysql-3306-test');
      console.log('✅ 数据库连接正常');
    } catch (error) {
      console.error('❌ 数据库连接失败:', error.message);
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testChangeLogsAPI();
