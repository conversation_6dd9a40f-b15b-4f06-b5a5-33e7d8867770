// Example of responsive components for the stake/equity management system
import React, { useState } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Drawer, 
  Menu, 
  Layout, 
  Typography, 
  Space, 
  Grid,
  Dropdown,
  Avatar
} from 'antd';
import {
  MenuOutlined,
  UserOutlined,
  DashboardOutlined,
  BankOutlined,
  TeamOutlined,
  SettingOutlined,
  LogoutOutlined
} from '@ant-design/icons';

const { Header, Sider, Content } = Layout;
const { Title, Text } = Typography;
const { useBreakpoint } = Grid;

// Responsive Dashboard Layout Component
export const ResponsiveDashboard: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuVisible, setMobileMenuVisible] = useState(false);
  const screens = useBreakpoint();

  // Determine if we're on mobile
  const isMobile = !screens.md;

  // Menu items
  const menuItems = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: 'companies',
      icon: <BankOutlined />,
      label: '公司管理',
      children: [
        { key: 'company-list', label: '公司列表' },
        { key: 'company-add', label: '新增公司' },
        { key: 'company-equity', label: '股权关系' },
      ],
    },
    {
      key: 'shareholders',
      icon: <TeamOutlined />,
      label: '股东管理',
      children: [
        { key: 'shareholder-list', label: '股东列表' },
        { key: 'equity-changes', label: '股权变更' },
      ],
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ];

  // User dropdown menu
  const userMenu = (
    <Menu
      items={[
        {
          key: 'profile',
          icon: <UserOutlined />,
          label: '个人资料',
        },
        {
          key: 'logout',
          icon: <LogoutOutlined />,
          label: '退出登录',
          danger: true,
        },
      ]}
    />
  );

  return (
    <Layout className="min-h-screen">
      {/* Desktop Sidebar */}
      {!isMobile && (
        <Sider
          trigger={null}
          collapsible
          collapsed={collapsed}
          className="bg-white shadow-lg"
          width={250}
          collapsedWidth={80}
        >
          <div className="p-4 text-center border-b">
            <Title level={4} className={`m-0 ${collapsed ? 'hidden' : 'block'}`}>
              股权管理系统
            </Title>
            {collapsed && (
              <BankOutlined className="text-2xl text-primary-600" />
            )}
          </div>
          <Menu
            mode="inline"
            defaultSelectedKeys={['dashboard']}
            items={menuItems}
            className="border-r-0"
          />
        </Sider>
      )}

      {/* Mobile Drawer */}
      {isMobile && (
        <Drawer
          title="股权管理系统"
          placement="left"
          onClose={() => setMobileMenuVisible(false)}
          open={mobileMenuVisible}
          bodyStyle={{ padding: 0 }}
          width={280}
        >
          <Menu
            mode="inline"
            defaultSelectedKeys={['dashboard']}
            items={menuItems}
            onClick={() => setMobileMenuVisible(false)}
          />
        </Drawer>
      )}

      <Layout>
        {/* Header */}
        <Header className="bg-white shadow-sm px-4 flex items-center justify-between">
          <div className="flex items-center">
            <Button
              type="text"
              icon={<MenuOutlined />}
              onClick={() => {
                if (isMobile) {
                  setMobileMenuVisible(true);
                } else {
                  setCollapsed(!collapsed);
                }
              }}
              className="mr-4"
            />
            <Title level={4} className="m-0 hidden sm:block">
              仪表板
            </Title>
          </div>

          <div className="flex items-center space-x-4">
            <Dropdown overlay={userMenu} placement="bottomRight">
              <div className="flex items-center cursor-pointer hover:bg-gray-50 px-3 py-2 rounded-lg">
                <Avatar icon={<UserOutlined />} className="mr-2" />
                <span className="hidden sm:inline">管理员</span>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* Main Content */}
        <Content className="container-responsive py-6">
          <ResponsiveCompanyTable />
        </Content>
      </Layout>
    </Layout>
  );
};

// Responsive Company Table Component
export const ResponsiveCompanyTable: React.FC = () => {
  const screens = useBreakpoint();
  const isMobile = !screens.md;

  // Sample data
  const companies = [
    {
      id: 1,
      nameZh: '阿里巴巴集团',
      nameEn: 'Alibaba Group',
      capital: '1000万',
      status: '正常经营',
      region: '浙江',
      establishDate: '2023-01-15',
    },
    {
      id: 2,
      nameZh: '腾讯控股',
      nameEn: 'Tencent Holdings',
      capital: '2000万',
      status: '正常经营',
      region: '广东',
      establishDate: '2023-02-20',
    },
  ];

  // Desktop table columns
  const desktopColumns = [
    {
      title: '公司名称',
      dataIndex: 'nameZh',
      key: 'nameZh',
      render: (text: string, record: any) => (
        <div>
          <div className="font-medium">{text}</div>
          <div className="text-sm text-gray-500">{record.nameEn}</div>
        </div>
      ),
    },
    {
      title: '注册资本',
      dataIndex: 'capital',
      key: 'capital',
    },
    {
      title: '地区',
      dataIndex: 'region',
      key: 'region',
    },
    {
      title: '成立日期',
      dataIndex: 'establishDate',
      key: 'establishDate',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => (
        <span className={`px-2 py-1 rounded-full text-xs ${
          status === '正常经营' 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {status}
        </span>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      render: () => (
        <Space>
          <Button type="link" size="small">查看</Button>
          <Button type="link" size="small">编辑</Button>
        </Space>
      ),
    },
  ];

  // Mobile card view
  const MobileCompanyCards = () => (
    <div className="space-y-4">
      {companies.map((company) => (
        <Card key={company.id} className="shadow-sm">
          <div className="space-y-3">
            <div>
              <Title level={5} className="m-0">{company.nameZh}</Title>
              <Text type="secondary" className="text-sm">{company.nameEn}</Text>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <Text type="secondary">注册资本</Text>
                <div className="font-medium">{company.capital}</div>
              </div>
              <div>
                <Text type="secondary">地区</Text>
                <div className="font-medium">{company.region}</div>
              </div>
              <div>
                <Text type="secondary">成立日期</Text>
                <div className="font-medium">{company.establishDate}</div>
              </div>
              <div>
                <Text type="secondary">状态</Text>
                <span className={`px-2 py-1 rounded-full text-xs ${
                  company.status === '正常经营' 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {company.status}
                </span>
              </div>
            </div>
            
            <div className="flex space-x-2 pt-2 border-t">
              <Button type="primary" size="small" className="flex-1">
                查看详情
              </Button>
              <Button size="small" className="flex-1">
                编辑
              </Button>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <Title level={3} className="m-0">公司管理</Title>
          <Text type="secondary">管理和查看所有公司信息</Text>
        </div>
        <Button type="primary" className="w-full sm:w-auto">
          新增公司
        </Button>
      </div>

      {/* Content */}
      <Card className="shadow-sm">
        {isMobile ? (
          <MobileCompanyCards />
        ) : (
          <div className="table-responsive">
            <Table
              columns={desktopColumns}
              dataSource={companies}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total, range) =>
                  `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
              }}
            />
          </div>
        )}
      </Card>
    </div>
  );
};

// Responsive Form Component
export const ResponsiveCompanyForm: React.FC = () => {
  const screens = useBreakpoint();
  const isMobile = !screens.md;

  return (
    <Card title="新增公司" className="shadow-sm">
      <form className="space-y-6">
        {/* Basic Information */}
        <div>
          <Title level={5}>基本信息</Title>
          <div className={`grid gap-4 ${isMobile ? 'grid-cols-1' : 'grid-cols-2'}`}>
            <div>
              <label className="block text-sm font-medium mb-2">公司中文名 *</label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="请输入公司中文名"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">公司英文名 *</label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="请输入公司英文名"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">注册资本 *</label>
              <input
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="请输入注册资本"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">成立日期 *</label>
              <input
                type="date"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>
        </div>

        {/* Address Information */}
        <div>
          <Title level={5}>地址信息</Title>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">注册地址 *</label>
              <textarea
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                placeholder="请输入注册地址"
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className={`flex ${isMobile ? 'flex-col space-y-2' : 'flex-row space-x-4'} pt-6 border-t`}>
          <Button type="primary" className={isMobile ? 'w-full' : 'flex-1'}>
            保存
          </Button>
          <Button className={isMobile ? 'w-full' : 'flex-1'}>
            重置
          </Button>
          <Button className={isMobile ? 'w-full' : 'flex-1'}>
            取消
          </Button>
        </div>
      </form>
    </Card>
  );
};

export default ResponsiveDashboard;
