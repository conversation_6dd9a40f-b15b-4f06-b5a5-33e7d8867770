import axios from 'axios';

async function test2025Investigation() {
  try {
    console.log('🔍 测试2025年任务排查数据...');
    
    // 测试2025年任务排查
    const response = await axios.post('http://localhost:8080/api/task/investigation', {
      year: '2025'
    });

    if (response.data.success) {
      const data = response.data.data;
      
      console.log('\n📊 2025年任务排查结果:');
      console.log(`年度: ${data.year}`);
      console.log(`总公司数: ${data.totalCompanies}`);
      console.log(`固定周期公司数: ${data.fixedAnnualCompanies}`);
      console.log(`滚动周期公司数: ${data.rollingAnnualCompanies}`);
      console.log(`不管年审公司数: ${data.noAnnualCompanies}`);
      
      console.log('\n📋 年审年报统计:');
      console.log(`理论数: ${data.annualReport.theory}`);
      console.log(`实际数: ${data.annualReport.actual}`);
      console.log(`缺失数: ${data.annualReport.missing}`);
      
      console.log('\n📋 地址维护统计:');
      console.log(`理论数: ${data.addressMaintenance.theory}`);
      console.log(`实际数: ${data.addressMaintenance.actual}`);
      console.log(`缺失数: ${data.addressMaintenance.missing}`);
      
      // 检查是否实际数大于理论数
      console.log('\n⚠️ 数据异常检查:');
      if (data.annualReport.actual > data.annualReport.theory) {
        console.log(`❌ 年审年报实际数(${data.annualReport.actual}) > 理论数(${data.annualReport.theory})`);
      } else {
        console.log(`✅ 年审年报数据正常`);
      }
      
      if (data.addressMaintenance.actual > data.addressMaintenance.theory) {
        console.log(`❌ 地址维护实际数(${data.addressMaintenance.actual}) > 理论数(${data.addressMaintenance.theory})`);
      } else {
        console.log(`✅ 地址维护数据正常`);
      }
      
      console.log('\n📝 缺失任务列表:');
      if (data.missingTasks.length === 0) {
        console.log('🎉 没有缺失任务！');
      } else {
        data.missingTasks.forEach((task, index) => {
          console.log(`${index + 1}. ${task.companyName}`);
          console.log(`   - 缺失年审年报: ${task.missingAnnualReport ? '是' : '否'}`);
          console.log(`   - 缺失地址维护: ${task.missingAddressMaintenance ? '是' : '否'}`);
        });
      }
      
    } else {
      console.error('❌ 任务排查失败:', response.data.message);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
test2025Investigation();
