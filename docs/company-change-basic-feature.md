# 公司信息变更基础信息页面功能说明

## 📋 功能概述

公司信息变更基础信息页面是一个用于提交公司基础信息变更申请的表单页面，支持多种变更类型的提交和确认流程。

## 🎯 页面功能

### 1. 基本信息填写
- **变更类型选择**: 支持基础信息变更、高管信息变更、股东信息变更
- **变更内容选择**: 根据变更类型动态显示相应的变更内容选项
- **变更日期**: 支持日期选择器，默认为今天

### 2. 变更内容配置

#### 基础信息变更选项：
- 公司名称变更
- 注册资本变更  
- 注册地址变更
- 经营范围变更

#### 高管信息变更选项：
- 新增高管
- 删除高管
- 职位变更

#### 股东信息变更选项：
- 新增股东
- 删除股东
- 持股比例变更

### 3. 具体变更表格
- 动态显示变更前后的对比信息
- 支持在线编辑修改后的值
- 自动从数据库获取当前值作为修改前的值

### 4. 参考规范
- 提供A规范、B规范等参考文档链接
- 帮助用户了解变更要求和标准

### 5. 文件上传
- 支持C规范、D规范等文件模板下载
- 支持重新上传相关文件
- 文件类型管理和版本控制

## 🔄 操作流程

### 提交流程
1. 选择变更类型和变更内容
2. 填写变更日期
3. 在具体变更表格中填写修改后的值
4. 上传相关文件（可选）
5. 点击"提交"按钮
6. 系统验证数据完整性
7. 提交成功后跳转到变更确认页面

### 确认流程（管理员）
1. 在变更确认页面查看待确认的变更记录
2. 点击变更记录进入详情页面
3. 页面以确认模式显示，所有字段为只读状态
4. 点击"确认"按钮
5. 系统应用变更到相关数据表
6. 返回变更确认页面

## 🛠 技术实现

### 前端技术栈
- **React + TypeScript**: 主要开发框架
- **Ant Design**: UI组件库
- **React Router**: 路由管理
- **dayjs**: 日期处理

### 后端API
- **提交变更**: `POST /api/company-change-confirmation/submit`
- **确认变更**: `POST /api/company-change-confirmation/confirm/:id`
- **获取变更记录**: `GET /api/company-change-confirmation/records`

### 数据库设计
```sql
-- 公司变更记录表
CREATE TABLE company_change_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  company_id INT NOT NULL,
  change_type VARCHAR(50) NOT NULL,
  change_content TEXT NOT NULL,
  old_value TEXT,
  new_value TEXT,
  change_date DATE NOT NULL,
  operator VARCHAR(100) NOT NULL,
  status ENUM('pending', 'confirmed') DEFAULT 'pending',
  operate_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🎨 样式设计

### 按钮样式
- **提交/确认按钮**: 黑色背景，白色文字
- **取消按钮**: 白色背景，黑色边框
- **文件操作按钮**: 链接样式，蓝色文字

### 表格样式
- 居中对齐的表头和内容
- 清晰的边框分隔
- 输入框与表格单元格融合设计

### 响应式设计
- 支持移动端适配
- 表单字段在小屏幕上垂直排列
- 按钮组支持换行显示

## 🔗 页面跳转

### 入口页面
- **公司详情页面**: 右上角"变更基础信息"按钮
- **变更确认页面**: 点击变更记录进入确认模式

### 跳转逻辑
```typescript
// 从公司详情页跳转
navigate('/company-change-basic', { 
  state: { 
    companyId: companyInfo?.id, 
    companyName: companyInfo?.chineseName 
  } 
});

// 从变更确认页跳转（确认模式）
navigate('/company-change-basic', { 
  state: { 
    companyId: record.companyId,
    companyName: record.companyName,
    isConfirmMode: true,
    changeRecord: record
  } 
});
```

## ✅ 测试验证

### 功能测试
1. ✅ 页面正常加载和显示
2. ✅ 变更类型选择功能正常
3. ✅ 变更内容动态更新正常
4. ✅ 具体变更表格显示和编辑正常
5. ✅ 提交功能正常，数据成功保存
6. ✅ 跳转功能正常

### API测试
1. ✅ 提交API返回成功响应
2. ✅ 数据库正确保存变更记录
3. ✅ 变更确认页面显示新记录

### 集成测试
1. ✅ 从公司详情页面跳转正常
2. ✅ 提交后跳转到变更确认页面
3. ✅ 整个变更流程端到端测试通过

## 🚀 后续优化

### 功能增强
- [ ] 添加变更历史记录查看
- [ ] 支持批量变更操作
- [ ] 添加变更审批流程
- [ ] 支持变更模板保存和复用

### 用户体验优化
- [ ] 添加表单自动保存功能
- [ ] 优化文件上传体验
- [ ] 添加操作引导和帮助提示
- [ ] 支持快捷键操作

### 技术优化
- [ ] 添加表单验证规则
- [ ] 优化API响应速度
- [ ] 添加错误重试机制
- [ ] 实现离线缓存功能
