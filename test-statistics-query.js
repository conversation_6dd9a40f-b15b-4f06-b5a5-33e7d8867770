import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function testStatisticsQuery() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 执行和后端API相同的查询
    const [stats] = await connection.query(`
      SELECT
        change_type,
        COUNT(*) as count
      FROM company_change_logs
      WHERE status IN ('pending', 'rejected')
      GROUP BY change_type
    `);
    
    console.log('\n📊 统计查询结果:');
    stats.forEach(stat => {
      console.log(`  类型: ${stat.change_type}, 数量: ${stat.count}`);
    });

    // 模拟后端统计逻辑
    const statistics = {
      basicInfo: 0,
      executiveInfo: 0,
      shareholderInfo: 0,
      investmentInfo: 0
    };

    stats.forEach(stat => {
      switch (stat.change_type) {
        case 'basic':
          statistics.basicInfo = stat.count;
          break;
        case 'executive':
          statistics.executiveInfo = stat.count;
          break;
        case 'shareholder':
          statistics.shareholderInfo = stat.count;
          break;
        case 'investment':
          statistics.investmentInfo = stat.count;
          break;
      }
    });

    console.log('\n📋 处理后的统计结果:');
    console.log(JSON.stringify(statistics, null, 2));

  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行查询
testStatisticsQuery();
