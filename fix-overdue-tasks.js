import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function fixOverdueTasks() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 查看当前的已逾期任务
    const [overdueTasks] = await connection.query(
      'SELECT id, company_name, task_type, task_status FROM pending_tasks WHERE task_status = "已逾期"'
    );
    
    console.log(`\n📊 找到 ${overdueTasks.length} 个已逾期任务:`);
    overdueTasks.forEach(task => {
      console.log(`  ID: ${task.id}, 公司: ${task.company_name}, 类型: ${task.task_type}`);
    });

    if (overdueTasks.length === 0) {
      console.log('没有找到已逾期任务，无需修复');
      return;
    }

    // 随机将已逾期任务改为未开始或进行中
    const statuses = ['未开始', '进行中'];
    let updatedCount = 0;

    for (const task of overdueTasks) {
      const randomStatus = statuses[Math.floor(Math.random() * statuses.length)];
      
      await connection.query(
        'UPDATE pending_tasks SET task_status = ? WHERE id = ?',
        [randomStatus, task.id]
      );
      
      console.log(`  ✅ 任务 ${task.id} (${task.company_name} - ${task.task_type}) 状态从"已逾期"改为"${randomStatus}"`);
      updatedCount++;
    }

    console.log(`\n🎉 成功修复 ${updatedCount} 个任务的状态`);

    // 验证修复结果
    const [remainingOverdue] = await connection.query(
      'SELECT COUNT(*) as count FROM pending_tasks WHERE task_status = "已逾期"'
    );
    
    console.log(`\n📋 修复后剩余已逾期任务: ${remainingOverdue[0].count} 个`);

    // 显示当前任务状态统计
    const [statusStats] = await connection.query(
      'SELECT task_status, COUNT(*) as count FROM pending_tasks GROUP BY task_status ORDER BY task_status'
    );
    
    console.log('\n📊 当前任务状态统计:');
    statusStats.forEach(stat => {
      console.log(`  ${stat.task_status}: ${stat.count} 个`);
    });

  } catch (error) {
    console.error('❌ 修复失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
fixOverdueTasks();
