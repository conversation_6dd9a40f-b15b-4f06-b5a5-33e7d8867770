import mysql from 'mysql2/promise';

const oldDbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management',
  socketPath: '/tmp/mysql.sock'
};

const newDbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function migrateRelationships() {
  console.log('🔄 开始迁移关系数据...');
  
  let oldConnection, newConnection;
  
  try {
    oldConnection = await mysql.createConnection(oldDbConfig);
    newConnection = await mysql.createConnection(newDbConfig);
    console.log('✅ 数据库连接成功');
    
    // 阶段1: 迁移股东关系
    console.log('\n💰 阶段1: 迁移股东关系');
    
    // 从investment_records表迁移
    try {
      const [investmentRecords] = await oldConnection.query(`
        SELECT ir.*, se.name as shareholder_name 
        FROM investment_records ir
        LEFT JOIN shareholder_entities se ON ir.shareholder_id = se.id
      `);
      
      for (const record of investmentRecords) {
        if (!record.shareholder_name) continue;
        
        // 查找对应的person_id
        const [persons] = await newConnection.query(
          'SELECT id FROM persons WHERE name = ?', 
          [record.shareholder_name]
        );
        
        if (persons.length === 0) continue;
        const personId = persons[0].id;
        
        // 查找对应的company_id
        const [companies] = await newConnection.query(
          'SELECT id FROM companies WHERE company_name_cn LIKE ?', 
          [`%${record.companyName}%`]
        );
        
        const companyId = companies.length > 0 ? companies[0].id : null;
        
        // 处理投资金额和持股比例
        let investmentAmount = null;
        let percentage = null;
        
        if (record.investmentAmount) {
          const amountMatch = record.investmentAmount.toString().match(/[\d.]+/);
          investmentAmount = amountMatch ? parseFloat(amountMatch[0]) : null;
        }
        
        if (record.percentage) {
          const percentMatch = record.percentage.toString().match(/[\d.]+/);
          percentage = percentMatch ? parseFloat(percentMatch[0]) : null;
        }
        
        await newConnection.query(`
          INSERT INTO shareholdings (
            person_id, company_id, investment_amount, percentage, 
            start_date, created_at, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, 1)
          ON DUPLICATE KEY UPDATE investment_amount = VALUES(investment_amount)
        `, [
          personId,
          companyId,
          investmentAmount,
          percentage,
          record.startDate,
          record.created_at
        ]);
      }
      
      console.log(`✅ 从investment_records迁移了 ${investmentRecords.length} 条股东关系`);
    } catch (error) {
      console.log('⚠️ investment_records表不存在或为空');
    }
    
    // 从shareholders表迁移
    try {
      const [shareholders] = await oldConnection.query('SELECT * FROM shareholders WHERE name IS NOT NULL');
      
      for (const shareholder of shareholders) {
        // 查找对应的person_id
        const [persons] = await newConnection.query(
          'SELECT id FROM persons WHERE name = ?', 
          [shareholder.name]
        );
        
        if (persons.length === 0) continue;
        const personId = persons[0].id;
        
        // 查找对应的company_id
        let companyId = null;
        if (shareholder.company_id) {
          const [companies] = await newConnection.query(
            'SELECT id FROM companies LIMIT 1 OFFSET ?', 
            [shareholder.company_id - 1]
          );
          companyId = companies.length > 0 ? companies[0].id : null;
        }
        
        // 处理投资金额和持股比例
        let investmentAmount = null;
        let percentage = null;
        
        if (shareholder.investment_amount) {
          const amountMatch = shareholder.investment_amount.toString().match(/[\d.]+/);
          investmentAmount = amountMatch ? parseFloat(amountMatch[0]) : null;
        }
        
        if (shareholder.percentage) {
          const percentMatch = shareholder.percentage.toString().match(/[\d.]+/);
          percentage = percentMatch ? parseFloat(percentMatch[0]) : null;
        }
        
        await newConnection.query(`
          INSERT INTO shareholdings (
            person_id, company_id, investment_amount, percentage, 
            start_date, created_at, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, 1)
          ON DUPLICATE KEY UPDATE investment_amount = VALUES(investment_amount)
        `, [
          personId,
          companyId,
          investmentAmount,
          percentage,
          shareholder.start_date,
          shareholder.created_at
        ]);
      }
      
      console.log(`✅ 从shareholders迁移了 ${shareholders.length} 条股东关系`);
    } catch (error) {
      console.log('⚠️ shareholders表不存在或为空');
    }
    
    // 阶段2: 迁移任职关系
    console.log('\n👔 阶段2: 迁移任职关系');
    
    // 从employment_records表迁移
    try {
      const [employmentRecords] = await oldConnection.query('SELECT * FROM employment_records');
      
      for (const record of employmentRecords) {
        // 查找对应的person_id
        const [persons] = await newConnection.query(
          'SELECT id FROM persons LIMIT 1 OFFSET ?', 
          [record.person_id - 1]
        );
        
        if (persons.length === 0) continue;
        const personId = persons[0].id;
        
        // 查找对应的company_id
        const [companies] = await newConnection.query(
          'SELECT id FROM companies WHERE company_name_cn LIKE ?', 
          [`%${record.companyName}%`]
        );
        
        const companyId = companies.length > 0 ? companies[0].id : null;
        
        await newConnection.query(`
          INSERT INTO employments (
            person_id, company_id, position, start_date, created_at, created_by
          ) VALUES (?, ?, ?, ?, ?, 1)
          ON DUPLICATE KEY UPDATE position = VALUES(position)
        `, [
          personId,
          companyId,
          record.position || '董事',
          record.startDate,
          record.created_at
        ]);
      }
      
      console.log(`✅ 从employment_records迁移了 ${employmentRecords.length} 条任职关系`);
    } catch (error) {
      console.log('⚠️ employment_records表不存在或为空');
    }
    
    // 从executives表迁移
    try {
      const [executives] = await oldConnection.query('SELECT * FROM executives WHERE person IS NOT NULL');
      
      for (const executive of executives) {
        // 查找对应的person_id
        const [persons] = await newConnection.query(
          'SELECT id FROM persons WHERE name = ?', 
          [executive.person]
        );
        
        if (persons.length === 0) continue;
        const personId = persons[0].id;
        
        // 查找对应的company_id
        let companyId = null;
        if (executive.company_id) {
          const [companies] = await newConnection.query(
            'SELECT id FROM companies LIMIT 1 OFFSET ?', 
            [executive.company_id - 1]
          );
          companyId = companies.length > 0 ? companies[0].id : null;
        }
        
        await newConnection.query(`
          INSERT INTO employments (
            person_id, company_id, position, start_date, created_at, created_by
          ) VALUES (?, ?, ?, ?, ?, 1)
          ON DUPLICATE KEY UPDATE position = VALUES(position)
        `, [
          personId,
          companyId,
          executive.position || '董事',
          executive.start_date,
          executive.created_at
        ]);
      }
      
      console.log(`✅ 从executives迁移了 ${executives.length} 条任职关系`);
    } catch (error) {
      console.log('⚠️ executives表不存在或为空');
    }
    
    // 阶段3: 迁移财务数据
    console.log('\n💰 阶段3: 迁移财务数据');
    
    try {
      const [finances] = await oldConnection.query('SELECT * FROM company_finances');
      
      for (const finance of finances) {
        // 查找对应的company_id
        const [companies] = await newConnection.query(
          'SELECT id FROM companies LIMIT 1 OFFSET ?', 
          [finance.company_id - 1]
        );
        
        if (companies.length === 0) continue;
        const companyId = companies[0].id;
        
        await newConnection.query(`
          INSERT INTO company_finances (
            company_id, year, total_assets, total_liabilities, total_equity,
            business_income, main_business_income, profit_before_tax, net_profit, tax_payable,
            remarks, created_at, created_by
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)
          ON DUPLICATE KEY UPDATE total_assets = VALUES(total_assets)
        `, [
          companyId,
          finance.year,
          finance.total_assets || 0,
          finance.total_liabilities || 0,
          finance.total_equity || 0,
          finance.business_income || 0,
          finance.main_business_income || 0,
          finance.profit_before_tax || 0,
          finance.net_profit || 0,
          finance.tax_payable || 0,
          finance.remarks,
          finance.created_at
        ]);
      }
      
      console.log(`✅ 迁移了 ${finances.length} 条财务记录`);
    } catch (error) {
      console.log('⚠️ company_finances表不存在或为空');
    }
    
    console.log('\n🎉 关系数据迁移完成！');
    
    // 验证迁移结果
    const [shareholdingsCount] = await newConnection.query('SELECT COUNT(*) as count FROM shareholdings');
    const [employmentsCount] = await newConnection.query('SELECT COUNT(*) as count FROM employments');
    const [financesCount] = await newConnection.query('SELECT COUNT(*) as count FROM company_finances');
    
    console.log('\n📊 关系数据统计:');
    console.log(`  💰 股东关系: ${shareholdingsCount[0].count}`);
    console.log(`  👔 任职关系: ${employmentsCount[0].count}`);
    console.log(`  💰 财务记录: ${financesCount[0].count}`);
    
  } catch (error) {
    console.error('❌ 关系数据迁移失败:', error.message);
    throw error;
  } finally {
    if (oldConnection) await oldConnection.end();
    if (newConnection) await newConnection.end();
  }
}

migrateRelationships()
  .then(() => console.log('🎉 关系数据迁移成功完成'))
  .catch(err => console.error('💥 迁移失败:', err));
