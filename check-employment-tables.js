import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function checkEmploymentTables() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 1. 查看任职相关表结构
    console.log('\n👥 任职相关表结构:');
    
    // employments表
    const [employmentsColumns] = await connection.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'stake_management_v2' AND TABLE_NAME = 'employments'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('\n  employments表字段:');
    employmentsColumns.forEach(col => {
      console.log(`    ${col.COLUMN_NAME} (${col.DATA_TYPE}) - ${col.COLUMN_COMMENT || '无注释'}`);
    });

    // positions表
    const [positionsColumns] = await connection.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'stake_management_v2' AND TABLE_NAME = 'positions'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('\n  positions表字段:');
    positionsColumns.forEach(col => {
      console.log(`    ${col.COLUMN_NAME} (${col.DATA_TYPE}) - ${col.COLUMN_COMMENT || '无注释'}`);
    });

    // 2. 查看现有数据
    console.log('\n📋 现有数据统计:');
    
    const [companiesCount] = await connection.query('SELECT COUNT(*) as count FROM companies');
    console.log(`  公司数量: ${companiesCount[0].count}`);
    
    const [personsCount] = await connection.query('SELECT COUNT(*) as count FROM persons');
    console.log(`  人员数量: ${personsCount[0].count}`);
    
    const [employmentsCount] = await connection.query('SELECT COUNT(*) as count FROM employments');
    console.log(`  任职记录数量: ${employmentsCount[0].count}`);
    
    const [positionsCount] = await connection.query('SELECT COUNT(*) as count FROM positions');
    console.log(`  职位数量: ${positionsCount[0].count}`);
    
    const [changeLogsCount] = await connection.query('SELECT COUNT(*) as count FROM company_change_logs');
    console.log(`  变更记录数量: ${changeLogsCount[0].count}`);

    // 3. 查看现有公司列表
    const [companies] = await connection.query(`
      SELECT id, company_name_cn, company_name_en, business_segment
      FROM companies
      ORDER BY id
    `);
    
    console.log('\n🏢 现有公司列表:');
    companies.forEach(company => {
      console.log(`  ID: ${company.id}, 中文名: ${company.company_name_cn}, 英文名: ${company.company_name_en || '无'}, 业务板块: ${company.business_segment || '无'}`);
    });

    // 4. 查看现有人员列表
    const [persons] = await connection.query(`
      SELECT id, name, id_number, phone, email
      FROM persons
      ORDER BY id
    `);
    
    console.log('\n👥 现有人员列表:');
    persons.forEach(person => {
      console.log(`  ID: ${person.id}, 姓名: ${person.name}, 身份证: ${person.id_number || '无'}, 电话: ${person.phone || '无'}`);
    });

    // 5. 查看现有职位列表
    const [positions] = await connection.query(`
      SELECT id, name, description
      FROM positions
      ORDER BY id
    `);
    
    console.log('\n💼 现有职位列表:');
    positions.forEach(position => {
      console.log(`  ID: ${position.id}, 职位: ${position.name}, 描述: ${position.description || '无'}`);
    });

    // 6. 查看现有任职记录
    const [employments] = await connection.query(`
      SELECT e.id, p.name as person_name, c.company_name_cn, pos.name as position_name, 
             e.start_date, e.end_date, e.is_current
      FROM employments e
      LEFT JOIN persons p ON e.person_id = p.id
      LEFT JOIN companies c ON e.company_id = c.id
      LEFT JOIN positions pos ON e.position_id = pos.id
      ORDER BY e.id
    `);
    
    console.log('\n📋 现有任职记录:');
    employments.forEach(emp => {
      console.log(`  ID: ${emp.id}, 姓名: ${emp.person_name || '无'}, 公司: ${emp.company_name_cn || '无'}, 职位: ${emp.position_name || '无'}, 开始: ${emp.start_date || '无'}, 结束: ${emp.end_date || '无'}, 在职: ${emp.is_current ? '是' : '否'}`);
    });

    // 7. 查看现有变更记录
    const [changeLogs] = await connection.query(`
      SELECT cl.id, c.company_name_cn, cl.change_type, cl.change_content, 
             cl.change_date, cl.status, cl.operator
      FROM company_change_logs cl
      LEFT JOIN companies c ON cl.company_id = c.id
      ORDER BY cl.id
    `);
    
    console.log('\n📝 现有变更记录:');
    changeLogs.forEach(log => {
      console.log(`  ID: ${log.id}, 公司: ${log.company_name_cn || '无'}, 类型: ${log.change_type}, 内容: ${log.change_content}, 日期: ${log.change_date}, 状态: ${log.status}, 操作人: ${log.operator}`);
    });

  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行查询
checkEmploymentTables();
