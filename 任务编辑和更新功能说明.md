# 任务编辑和更新功能说明

## 功能概述

在待办任务提醒页面中，为每个任务提供了编辑和更新功能，通过弹窗的形式实现，提供更好的用户体验。

## 数据库确认

✅ **数据库配置已更新**
- 当前使用数据库：`stake_management_v2`
- 旧数据库可重命名为备份用途
- 所有API接口已适配新数据库结构

## 1. 编辑任务功能

### 触发方式
用户点击任务列表中的"编辑"按钮，弹出编辑任务弹窗。

### 弹窗设计
- **标题显示**：顶部显示"编辑任务"以及正在编辑的公司名和任务类型
- **表单布局**：垂直布局，字段清晰排列

### 字段说明

#### 不可编辑字段（灰色显示）
- **任务类型**：下拉选择框，显示当前任务类型，不可更改
- **年度**：下拉选择框，显示当前年度，不可更改  
- **主体**：下拉选择框，显示当前公司，不可更改

#### 可编辑字段
- **开始日期**：日期选择器，格式YYYY-MM-DD
- **结束日期**：日期选择器，格式YYYY-MM-DD
- **重复周期（年）**：仅当任务类型为"地址维护"时显示，默认值为1，只能输入正整数
- **备注**：多行文本输入框，最大500字符

### 操作按钮
- **保存**：黑色背景按钮，提交修改并更新数据库
- **取消**：默认样式按钮，关闭弹窗不保存任何数据

### 功能逻辑
1. 弹窗打开时自动填充当前任务的所有信息
2. 用户只能修改开始日期、结束日期、重复周期和备注
3. 保存成功后显示成功消息并返回任务列表页面
4. 取消操作直接关闭弹窗，不保存任何修改

## 2. 更新任务进度功能

### 触发方式
用户点击任务列表中的"更新"按钮，弹出更新进度弹窗。

### 弹窗设计
- **标题**：显示"更新进度"
- **分区布局**：根据用户选择动态显示不同区域

### 字段说明

#### 任务状态选择（必选）
- **未开始**：任务尚未开始
- **进行中**：任务正在进行中
- **已完成**：任务已完成

#### 下一周期任务选择
- **可选择条件**：仅当任务状态为"已完成"时可选择
- **不创建**：完成当前任务后不创建下一周期任务
- **创建**：完成当前任务后创建下一周期任务

#### 文件上传区域
- **上传按钮**：黑色背景的"上传文件"按钮
- **提示文字**：请上传任务完成或更新文件
- **必填条件**：当任务状态为"进行中"或"已完成"时必须上传文件

#### 任务信息区域（条件显示）
**显示条件**：当任务状态为"已完成"且选择"创建"下一周期任务时显示

**字段列表**：
- **任务类型**：下拉选择，默认为当前任务类型，不可选择
- **年度**：下拉选择，默认为当前任务年度+1，可选择
- **主体**：下拉选择，默认为当前任务的公司主体，**不可编辑**（灰色显示）
- **开始日期**：日期选择器，可选填
- **结束日期**：日期选择器，必填
- **重复周期（年）**：仅当任务类型为"地址维护"时显示，默认值为1
- **备注**：多行文本输入框，用于下一周期任务的备注

### 操作按钮
- **保存**：黑色背景按钮，提交更新并处理下一周期任务创建
- **取消**：默认样式按钮，关闭弹窗不保存任何数据

### 功能逻辑

#### 状态控制逻辑
1. **未开始/进行中**：下一周期任务选项不可选择，任务信息区域不显示
2. **已完成**：下一周期任务选项可选择
   - 选择"不创建"：任务信息区域不可编辑
   - 选择"创建"：任务信息区域可填写

#### 文件上传验证
- 当任务状态为"进行中"或"已完成"时，必须上传文件才能保存
- 未上传文件时点击保存会显示错误提示

#### 数据处理逻辑
1. **更新当前任务**：更新任务状态和上传文件信息
2. **创建下一周期任务**：如果选择创建，在数据库中插入新的任务记录
3. **事务处理**：使用数据库事务确保数据一致性

## 3. 技术实现

### 前端组件
- **EditTaskModal.tsx**：编辑任务弹窗组件
- **UpdateTaskModal.tsx**：更新任务进度弹窗组件
- **TaskIndex.tsx**：主页面，集成两个弹窗组件

### 后端API
- **PUT /api/tasks/:id**：编辑任务接口
- **PUT /api/tasks/:id/update-progress**：更新任务进度接口
- **GET /api/companies-list**：获取公司列表接口

### 数据库操作
- 任务信息更新
- 下一周期任务创建
- 事务管理确保数据一致性

## 4. 用户体验特性

### 响应式设计
- 弹窗宽度适配不同屏幕尺寸
- 表单字段合理布局

### 交互反馈
- 加载状态显示
- 成功/错误消息提示
- 表单验证错误提示

### 数据验证
- 前端表单验证
- 后端数据完整性检查
- 文件上传要求验证

## 5. 注意事项

1. **权限控制**：编辑和更新功能需要适当的用户权限
2. **数据备份**：重要操作前建议进行数据备份
3. **文件管理**：上传的文件需要适当的存储和管理机制
4. **审计日志**：所有任务修改操作都会记录操作时间和用户
5. **并发控制**：避免多用户同时编辑同一任务造成数据冲突

## 6. 最新修复内容

### 🔧 主体字段修复（2025-06-26）
**问题**：在更新任务进度弹窗中，当选择"已完成"并选择"创建"下一周期任务时，主体字段没有默认显示原有公司，且可以编辑。

**修复内容**：
- ✅ 主体字段现在默认显示原有任务的公司名称
- ✅ 主体字段设置为不可编辑（灰色显示）
- ✅ 确保即使公司不在当前公司列表中也能正确显示
- ✅ 保持数据一致性，下一周期任务使用相同的公司主体

**技术实现**：
```typescript
// 主体字段设置为disabled状态
<Select disabled>
  {/* 显示当前任务的公司，即使不在companies列表中 */}
  {task && !companies.find(c => c.id === task.companyId) && (
    <Option key={task.companyId} value={task.companyId}>
      {task.companyName}
    </Option>
  )}
</Select>
```

## 7. 后续优化建议

1. **文件存储**：实现真实的文件上传和存储功能
2. **权限管理**：根据用户角色限制编辑权限
3. **操作日志**：记录详细的任务修改历史
4. **批量操作**：支持批量更新多个任务状态
5. **通知机制**：任务状态变更时发送通知给相关人员
