# 公司变更确认页面权限测试指南

## 测试用户账户

| 用户名 | 密码 | 角色 | 角色ID | 权限说明 |
|--------|------|------|--------|----------|
| admin | admin123 | 系统管理员 | 1 | 所有权限 |
| manager | manager123 | 管理员 | 2 | 除系统设置外的所有权限 |
| operator | operator123 | 操作员 | 3 | 与管理员相同 |
| viewer | viewer123 | 查看员 | 4 | 只有查看权限 |

## 测试数据状态

我们创建了以下测试数据：

1. **待确认记录** (2条)
   - 公司名称变更；注册地址变更
   - 董事长变更；总经理变更

2. **有异常记录** (2条)
   - 股东持股比例变更
   - 对外投资信息变更

3. **已确认记录** (1条)
   - 经营范围变更

## 权限测试场景

### 1. 系统管理员 (admin) 测试
**预期行为：**
- ✅ 可以看到所有状态的变更记录
- ✅ 对"待确认"记录显示"确认"按钮
- ✅ 点击"待确认"记录或"确认"按钮弹出确认弹窗
- ✅ 确认弹窗有"取消"、"退回"、"确定"按钮
- ✅ 对"有异常"和"已确认"记录只能查看，弹窗只有"返回"按钮

### 2. 管理员 (manager) 测试
**预期行为：**
- ✅ 与系统管理员完全相同的权限

### 3. 操作员 (operator) 测试
**预期行为：**
- ✅ 可以看到所有状态的变更记录
- ✅ 对"有异常"记录显示"编辑"按钮
- ✅ 点击"有异常"记录或"编辑"按钮弹出编辑弹窗
- ✅ 编辑弹窗有"取消"、"提交"按钮，表单可编辑
- ✅ 对"待确认"记录点击弹出详情弹窗，有"返回"、"撤销变更"按钮
- ✅ 对"已确认"记录只能查看，弹窗只有"返回"按钮

### 4. 查看员 (viewer) 测试
**预期行为：**
- ✅ 可以看到所有状态的变更记录
- ✅ 对所有记录都只能查看，无操作按钮
- ✅ 点击任何记录弹出详情弹窗，只有"返回"按钮

## 测试步骤

1. **登录测试用户**
   - 访问 http://localhost:5173/login
   - 使用上述账户登录

2. **访问变更确认页面**
   - 访问 http://localhost:5173/company-change-confirmation

3. **验证页面显示**
   - 检查是否显示5条测试记录
   - 检查操作按钮是否符合权限要求

4. **测试交互功能**
   - 点击不同状态的记录
   - 验证弹窗内容和按钮
   - 测试编辑/确认/撤销功能

## 预期结果验证

### 按钮显示规则
- **系统管理员/管理员**：待确认记录显示"确认"按钮
- **操作员**：有异常记录显示"编辑"按钮
- **查看员**：无操作按钮
- **所有角色**：已确认记录显示"查看"按钮

### 弹窗按钮规则
- **系统管理员/管理员 + 待确认记录**：取消、退回、确定
- **系统管理员/管理员 + 有异常记录**：返回（只读查看）
- **操作员 + 有异常记录**：取消、提交（可编辑）
- **操作员 + 待确认记录**：返回、撤销变更
- **其他情况**：返回

### 数据显示优化
- **JSON数据格式化**：原值和新值中的JSON数据会被格式化显示，不再显示原始JSON字符串
- **多行显示**：复杂数据结构会以多行形式清晰展示
- **字段映射**：投资信息等结构化数据会显示为易读的格式：
  - 投资信息：`投资主体: 投资公司A, 投资金额: 1500万元, 持股比例: 30%`
  - 股东信息：`股东姓名: 股东A, 持股比例: 70%`
  - 高管信息：`职位: 董事长, 姓名: 赵六`
- **编辑弹窗优化**：原值和新值字段使用多行文本框，支持自动调整高度

### 功能验证
- **确认功能**：状态变为"已确认"
- **退回功能**：状态变为"有异常"
- **编辑功能**：修改后状态变为"待确认"
- **撤销功能**：删除记录
