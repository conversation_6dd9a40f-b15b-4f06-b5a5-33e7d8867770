import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function checkPendingTasks() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 检查pending_tasks表结构
    console.log('\n📋 检查pending_tasks表结构:');
    const [columns] = await connection.execute('DESCRIBE pending_tasks');
    console.log('pending_tasks表字段:');
    columns.forEach(col => {
      console.log(`  ${col.Field}: ${col.Type} ${col.Null === 'YES' ? '(可空)' : '(非空)'} ${col.Key ? `[${col.Key}]` : ''}`);
    });

    // 检查pending_tasks数据
    console.log('\n📊 检查pending_tasks数据:');
    const [taskCount] = await connection.execute('SELECT COUNT(*) as count FROM pending_tasks');
    console.log(`pending_tasks表中有 ${taskCount[0].count} 条记录`);

    if (taskCount[0].count > 0) {
      // 检查任务状态统计
      const [statusStats] = await connection.execute(`
        SELECT
          task_status,
          COUNT(*) as count
        FROM pending_tasks
        GROUP BY task_status
        ORDER BY count DESC
      `);
      
      console.log('\n任务状态统计:');
      statusStats.forEach(stat => {
        console.log(`  ${stat.task_status}: ${stat.count} 个任务`);
      });

      // 检查任务类型统计
      const [typeStats] = await connection.execute(`
        SELECT 
          task_type,
          COUNT(*) as count
        FROM pending_tasks 
        GROUP BY task_type
        ORDER BY count DESC
      `);
      
      console.log('\n任务类型统计:');
      typeStats.forEach(stat => {
        console.log(`  ${stat.task_type}: ${stat.count} 个任务`);
      });

      // 检查年度统计
      const [yearStats] = await connection.execute(`
        SELECT 
          year,
          COUNT(*) as count
        FROM pending_tasks 
        GROUP BY year
        ORDER BY year DESC
      `);
      
      console.log('\n年度统计:');
      yearStats.forEach(stat => {
        console.log(`  ${stat.year}年: ${stat.count} 个任务`);
      });

      // 查看前10条记录
      const [sampleTasks] = await connection.execute(`
        SELECT
          id,
          task_type,
          year,
          company_id,
          task_status,
          start_date,
          deadline,
          created_at
        FROM pending_tasks
        ORDER BY created_at DESC
        LIMIT 10
      `);
      
      console.log('\n最近10条任务记录:');
      sampleTasks.forEach(task => {
        console.log(`  ID:${task.id} ${task.task_type} ${task.year}年 公司ID:${task.company_id} 状态:${task.task_status} (${task.created_at})`);
      });
    }

    // 检查task_types表
    console.log('\n📋 检查task_types表:');
    const [taskTypes] = await connection.execute('SELECT * FROM task_types ORDER BY id');
    console.log('任务类型列表:');
    taskTypes.forEach(type => {
      console.log(`  ID:${type.id} ${type.name} - ${type.description || '无描述'}`);
    });

    // 检查companies表的年审状态
    console.log('\n🏢 检查公司年审状态:');
    const [companies] = await connection.execute(`
      SELECT 
        c.id,
        c.company_name_cn,
        aus.name as annual_status
      FROM companies c
      LEFT JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      ORDER BY c.id
    `);
    
    console.log('公司年审状态:');
    companies.forEach(company => {
      console.log(`  ID:${company.id} ${company.company_name_cn} - ${company.annual_status || '未设置'}`);
    });

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行检查
checkPendingTasks().catch(console.error);
