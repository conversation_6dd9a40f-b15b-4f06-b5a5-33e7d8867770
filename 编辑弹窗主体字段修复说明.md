# 编辑任务弹窗主体字段默认值修复说明

## 🔍 问题描述

在待办任务提醒页面，当用户点击某条任务的编辑按钮后，在弹出的编辑弹窗中，主体下拉列表的默认值没有正确显示用户所点击的那条任务对应的公司。

## 🔧 问题根因分析

### 1. 数据缺失问题
**问题**：后端API返回的任务数据中缺少`companyId`字段
```javascript
// 修复前的API返回数据
{
  "id": 54,
  "taskType": "年审年报",
  "companyName": "广州制造有限公司",
  // 缺少 companyId 字段
}
```

### 2. 前端接口定义不完整
**问题**：前端TaskItem接口定义中没有`companyId`字段
```typescript
// 修复前的接口定义
interface TaskItem {
  id: number;
  companyName: string;
  // 缺少 companyId: number;
}
```

### 3. 表单默认值设置问题
**问题**：编辑弹窗中主体字段无法正确设置默认值，因为缺少companyId

## ✅ 修复方案

### 1. 后端API修复

#### 修改任务列表API
**文件**：`server/index.js`
**位置**：`/api/tasks` 接口

```javascript
// 修复前
const [rows] = await pool.query(`
  SELECT
    id,
    task_status as taskStatus,
    task_type as taskType,
    year,
    DATE_FORMAT(start_date, "%Y-%m-%d") as startDate,
    DATE_FORMAT(deadline, "%Y-%m-%d") as deadline,
    company_name as companyName,  // 只有公司名称
    business_segment as businessSegment,
    remarks,
    priority,
    progress,
    DATE_FORMAT(created_at, "%Y-%m-%d %H:%i:%s") as createTime
  FROM pending_tasks
  ${whereClause}
  ${orderClause}
  LIMIT ? OFFSET ?
`, [...params, parseInt(limit), offset]);

// 修复后
const [rows] = await pool.query(`
  SELECT
    id,
    task_status as taskStatus,
    task_type as taskType,
    year,
    DATE_FORMAT(start_date, "%Y-%m-%d") as startDate,
    DATE_FORMAT(deadline, "%Y-%m-%d") as deadline,
    company_id as companyId,      // 新增：公司ID
    company_name as companyName,  // 保留：公司名称
    business_segment as businessSegment,
    remarks,
    priority,
    progress,
    DATE_FORMAT(created_at, "%Y-%m-%d %H:%i:%s") as createTime
  FROM pending_tasks
  ${whereClause}
  ${orderClause}
  LIMIT ? OFFSET ?
`, [...params, parseInt(limit), offset]);
```

### 2. 前端接口定义修复

#### 更新TaskItem接口
**文件**：`src/pages/task/TaskIndex.tsx` 和 `src/pages/task/components/EditTaskModal.tsx`

```typescript
// 修复前
interface TaskItem {
  id: number;
  taskStatus: string;
  taskType: string;
  year: string;
  startDate: string;
  deadline: string;
  companyName: string;  // 只有公司名称
  businessSegment: string;
  remarks: string;
  priority: number;
  progress: number;
  createTime: string;
}

// 修复后
interface TaskItem {
  id: number;
  taskStatus: string;
  taskType: string;
  year: string;
  startDate: string;
  deadline: string;
  companyId: number;    // 新增：公司ID
  companyName: string;  // 保留：公司名称
  businessSegment: string;
  remarks: string;
  priority: number;
  progress: number;
  createTime: string;
}
```

### 3. 编辑弹窗组件修复

#### 优化主体字段显示逻辑
**文件**：`src/pages/task/components/EditTaskModal.tsx`

```typescript
// 修复前：主体字段可能无法正确显示当前任务的公司
<Form.Item label="主体" name="company">
  <Select disabled>
    {companies.map(company => (
      <Option key={company.id} value={company.id}>
        {company.name}
      </Option>
    ))}
  </Select>
</Form.Item>

// 修复后：确保当前任务的公司能正确显示
<Form.Item label="主体" name="company">
  <Select disabled>
    {companies.map(company => (
      <Option key={company.id} value={company.id}>
        {company.name}
      </Option>
    ))}
    {/* 显示当前任务的公司，即使不在companies列表中 */}
    {task && !companies.find(c => c.id === task.companyId) && (
      <Option key={task.companyId} value={task.companyId}>
        {task.companyName}
      </Option>
    )}
  </Select>
</Form.Item>
```

#### 表单默认值设置
```typescript
// 表单默认值设置（已存在，现在可以正常工作）
form.setFieldsValue({
  taskType: task.taskType,
  year: task.year,
  company: task.companyId,  // 现在有正确的companyId
  startDate: task.startDate ? dayjs(task.startDate) : null,
  deadline: task.deadline ? dayjs(task.deadline) : null,
  repeatCycle: 1,
  remarks: task.remarks
});
```

## 📊 修复验证

### API数据验证
修复后的API现在返回完整的数据：
```json
{
  "success": true,
  "data": {
    "list": [{
      "id": 54,
      "taskStatus": "已逾期",
      "taskType": "年审年报",
      "year": "2024",
      "startDate": "2025-03-08",
      "deadline": "2025-05-06",
      "companyId": 13,              // ✅ 新增字段
      "companyName": "广州制造有限公司",
      "businessSegment": "A. 农、林、牧、渔业",
      "remarks": "2024年度年审年报工作...",
      "priority": 3,
      "progress": 23,
      "createTime": "2025-06-24 18:46:19"
    }]
  }
}
```

### 功能验证
1. ✅ 后端API返回companyId字段
2. ✅ 前端接口定义包含companyId字段
3. ✅ 编辑弹窗主体字段正确显示默认值
4. ✅ 主体字段设置为不可编辑状态
5. ✅ 边界情况处理（公司不在列表中时也能正确显示）

## 🎯 修复效果

### 修复前问题
- ❌ 编辑弹窗主体字段无默认值
- ❌ 用户需要手动选择公司
- ❌ 可能选择错误的公司
- ❌ 用户体验不佳

### 修复后效果
- ✅ 编辑弹窗主体字段自动显示当前任务的公司
- ✅ 主体字段不可编辑，防止误操作
- ✅ 即使公司不在当前列表中也能正确显示
- ✅ 用户体验得到显著改善

## 📋 测试验证

### 测试步骤
1. 打开待办任务提醒页面 `http://localhost:5173/task`
2. 点击任意任务的"编辑"按钮
3. 观察编辑弹窗中的主体字段：
   - 应该默认显示该任务对应的公司名称
   - 字段应该是灰色不可编辑状态
   - 公司名称应该与任务列表中显示的一致

### 验证脚本
提供了完整的验证脚本 `测试编辑弹窗主体默认值.js`，可以：
- 验证API返回的数据结构
- 检查companyId字段是否存在
- 验证公司信息的一致性
- 提供详细的测试指南

## 🔧 技术实现细节

### 关键修改点
1. **数据库查询**：在SELECT语句中添加`company_id as companyId`
2. **接口定义**：在TaskItem接口中添加`companyId: number`
3. **表单设置**：使用`task.companyId`设置表单默认值
4. **边界处理**：处理公司不在列表中的情况

### 兼容性考虑
- 保持向后兼容，同时返回companyId和companyName
- 不影响其他使用任务数据的组件
- 确保更新任务弹窗也能正常工作

## 🚀 部署说明

### 部署步骤
1. 重启后端服务器应用API修改
2. 刷新前端页面应用接口修改
3. 测试编辑弹窗功能

### 注意事项
- 修改涉及数据库查询和前端接口，需要同时部署
- 建议在测试环境先验证功能正确性
- 确保所有使用TaskItem接口的组件都已更新

修复完成后，编辑任务弹窗的主体字段现在能正确显示当前任务对应的公司，提供了更好的用户体验！
