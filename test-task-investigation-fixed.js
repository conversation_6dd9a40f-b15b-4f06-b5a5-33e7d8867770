import axios from 'axios';

async function testTaskInvestigationFixed() {
  try {
    console.log('🔍 测试修复后的任务排查功能...');
    
    // 测试2026年任务排查
    const response = await axios.post('http://localhost:8080/api/task/investigation', {
      year: '2026'
    });

    if (response.data.success) {
      const data = response.data.data;
      
      console.log('\n📊 任务排查结果:');
      console.log(`年度: ${data.year}`);
      console.log(`总公司数: ${data.totalCompanies}`);
      console.log(`固定周期公司数: ${data.fixedAnnualCompanies}`);
      console.log(`滚动周期公司数: ${data.rollingAnnualCompanies}`);
      console.log(`不管年审公司数: ${data.noAnnualCompanies}`);
      
      console.log('\n📋 年审年报统计:');
      console.log(`理论数: ${data.annualReport.theory}`);
      console.log(`实际数: ${data.annualReport.actual}`);
      console.log(`缺失数: ${data.annualReport.missing}`);
      
      console.log('\n📋 地址维护统计:');
      console.log(`理论数: ${data.addressMaintenance.theory}`);
      console.log(`实际数: ${data.addressMaintenance.actual}`);
      console.log(`缺失数: ${data.addressMaintenance.missing}`);
      
      console.log('\n📝 缺失任务列表:');
      if (data.missingTasks.length === 0) {
        console.log('🎉 没有缺失任务！');
      } else {
        data.missingTasks.forEach((task, index) => {
          console.log(`${index + 1}. ${task.companyName}`);
          console.log(`   - 缺失年审年报: ${task.missingAnnualReport ? '是' : '否'}`);
          console.log(`   - 缺失地址维护: ${task.missingAddressMaintenance ? '是' : '否'}`);
        });
      }
      
      // 验证数据一致性
      console.log('\n✅ 数据一致性验证:');
      const annualReportConsistent = data.annualReport.actual + data.annualReport.missing === data.annualReport.theory;
      const addressMaintenanceConsistent = data.addressMaintenance.actual + data.addressMaintenance.missing === data.addressMaintenance.theory;
      
      console.log(`年审年报数据一致性: ${annualReportConsistent ? '✅ 通过' : '❌ 失败'}`);
      console.log(`  实际数(${data.annualReport.actual}) + 缺失数(${data.annualReport.missing}) = 理论数(${data.annualReport.theory})`);
      
      console.log(`地址维护数据一致性: ${addressMaintenanceConsistent ? '✅ 通过' : '❌ 失败'}`);
      console.log(`  实际数(${data.addressMaintenance.actual}) + 缺失数(${data.addressMaintenance.missing}) = 理论数(${data.addressMaintenance.theory})`);
      
      // 验证缺失任务数量
      const annualReportMissingCount = data.missingTasks.filter(task => task.missingAnnualReport).length;
      const addressMaintenanceMissingCount = data.missingTasks.filter(task => task.missingAddressMaintenance).length;
      
      console.log(`\n📊 缺失任务数量验证:`);
      console.log(`年审年报缺失任务统计: ${data.annualReport.missing}, 实际列表: ${annualReportMissingCount} ${data.annualReport.missing === annualReportMissingCount ? '✅' : '❌'}`);
      console.log(`地址维护缺失任务统计: ${data.addressMaintenance.missing}, 实际列表: ${addressMaintenanceMissingCount} ${data.addressMaintenance.missing === addressMaintenanceMissingCount ? '✅' : '❌'}`);
      
      // 验证公司分类逻辑
      console.log(`\n🏢 公司分类验证:`);
      const totalCalculated = data.fixedAnnualCompanies + data.rollingAnnualCompanies + data.noAnnualCompanies;
      console.log(`固定周期(${data.fixedAnnualCompanies}) + 滚动周期(${data.rollingAnnualCompanies}) + 不管年审(${data.noAnnualCompanies}) = ${totalCalculated}`);
      console.log(`总公司数: ${data.totalCompanies} ${totalCalculated === data.totalCompanies ? '✅' : '❌'}`);
      
      // 验证理论数计算
      console.log(`\n🧮 理论数计算验证:`);
      const expectedTheory = data.fixedAnnualCompanies + data.rollingAnnualCompanies;
      console.log(`年审年报理论数: ${data.annualReport.theory}, 预期: ${expectedTheory} ${data.annualReport.theory === expectedTheory ? '✅' : '❌'}`);
      console.log(`地址维护理论数: ${data.addressMaintenance.theory}, 预期: ${expectedTheory} ${data.addressMaintenance.theory === expectedTheory ? '✅' : '❌'}`);
      
    } else {
      console.error('❌ 任务排查失败:', response.data.message);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testTaskInvestigationFixed();
