import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function createTestUsers() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    await connection.beginTransaction();
    console.log('🔄 开始创建测试用户...');

    // 测试用户数据
    const testUsers = [
      {
        username: 'admin',
        password: '123456',
        realName: '系统管理员',
        email: '<EMAIL>',
        phone: '13800000001',
        roleId: 1 // 系统管理员
      },
      {
        username: 'manager',
        password: '123456',
        realName: '管理员',
        email: '<EMAIL>',
        phone: '13800000002',
        roleId: 2 // 管理员
      },
      {
        username: 'operator',
        password: '123456',
        realName: '操作员',
        email: '<EMAIL>',
        phone: '13800000003',
        roleId: 3 // 操作员
      },
      {
        username: 'viewer',
        password: '123456',
        realName: '查看员',
        email: '<EMAIL>',
        phone: '13800000004',
        roleId: 4 // 查看员
      }
    ];

    for (const userData of testUsers) {
      // 检查用户是否已存在
      const [existingUser] = await connection.query(
        'SELECT id FROM users WHERE username = ?',
        [userData.username]
      );

      if (existingUser.length > 0) {
        console.log(`⚠️ 用户 ${userData.username} 已存在，跳过创建`);
        continue;
      }

      // 1. 创建person记录
      const [personResult] = await connection.query(
        `INSERT INTO persons (
          name, email, phone, id_type, id_number,
          created_at, updated_at, created_by, updated_by
        ) VALUES (?, ?, ?, ?, ?, NOW(), NOW(), ?, ?)`,
        [userData.realName, userData.email, userData.phone, '身份证', `test_${Date.now()}`, 1, 1]
      );
      const personId = personResult.insertId;

      // 2. 创建用户记录
      const passwordHash = Buffer.from(userData.password).toString('base64');
      const [userResult] = await connection.query(
        `INSERT INTO users (
          username, password_hash, person_id, role_id, account_status,
          created_at, updated_at, created_by
        ) VALUES (?, ?, ?, ?, ?, NOW(), NOW(), ?)`,
        [userData.username, passwordHash, personId, userData.roleId, 'active', 1]
      );

      console.log(`✅ 创建用户成功: ${userData.username} (${userData.realName}) - 角色ID: ${userData.roleId}`);
    }

    await connection.commit();
    console.log('🎉 所有测试用户创建完成！');

    // 验证创建结果
    console.log('\n📊 验证创建结果:');
    const [users] = await connection.query(`
      SELECT 
        u.id,
        u.username,
        p.name as realName,
        ur.name as roleName,
        u.account_status
      FROM users u
      LEFT JOIN persons p ON u.person_id = p.id
      LEFT JOIN user_roles ur ON u.role_id = ur.id
      WHERE u.username IN ('admin', 'manager', 'operator', 'viewer')
      ORDER BY u.role_id
    `);

    console.log('创建的测试用户:');
    users.forEach(user => {
      console.log(`  ${user.username} - ${user.realName} (${user.roleName}) - ${user.account_status}`);
    });

  } catch (error) {
    await connection.rollback();
    console.error('❌ 创建测试用户失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行创建
createTestUsers().catch(console.error);
