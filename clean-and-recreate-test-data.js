import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function cleanAndRecreateTestData() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 清理现有测试数据
    console.log('🧹 清理现有测试数据...');
    
    // 删除股东关系
    await connection.query('DELETE FROM shareholdings WHERE created_by = 1');
    console.log('✅ 清理股东关系数据');
    
    // 删除投资关系
    await connection.query('DELETE FROM investments WHERE created_by = 1');
    console.log('✅ 清理投资关系数据');
    
    // 删除测试人员
    await connection.query('DELETE FROM persons WHERE created_by = 1');
    console.log('✅ 清理人员数据');
    
    // 删除测试公司
    await connection.query('DELETE FROM companies WHERE created_by = 1');
    console.log('✅ 清理公司数据');

    console.log('🎉 数据清理完成！');
    console.log('现在可以重新运行 create-equity-chart-test-data.js 来创建正确的测试数据');

  } catch (error) {
    console.error('❌ 清理数据失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行脚本
cleanAndRecreateTestData();
