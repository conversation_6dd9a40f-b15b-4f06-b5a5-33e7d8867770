import mysql from 'mysql2/promise';

const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function createTaskTables() {
  console.log('🔄 开始创建待办任务相关数据表...');
  
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');
    
    // 1. 创建任务类型表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS task_types (
        id INT AUTO_INCREMENT PRIMARY KEY,
        name VARCHAR(50) UNIQUE NOT NULL COMMENT '任务类型名称',
        description TEXT COMMENT '任务类型描述',
        is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        
        INDEX idx_task_type_name (name),
        INDEX idx_task_type_active (is_active)
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务类型表'
    `);
    console.log('✅ 创建任务类型表成功');
    
    // 2. 创建待办任务主表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS pending_tasks (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_status ENUM('已逾期', '未开始', '进行中', '待核实', '已完成') NOT NULL DEFAULT '未开始' COMMENT '任务状态',
        task_type VARCHAR(50) NOT NULL COMMENT '任务类型',
        year VARCHAR(4) NOT NULL COMMENT '年度',
        start_date DATE COMMENT '开始日期',
        deadline DATE NOT NULL COMMENT '截止日期',
        company_id INT COMMENT '关联公司ID',
        company_name VARCHAR(255) NOT NULL COMMENT '主体名称',
        business_segment VARCHAR(100) COMMENT '业务板块',
        remarks TEXT COMMENT '备注',
        priority INT DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
        progress INT DEFAULT 0 COMMENT '进度百分比（0-100）',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        created_by INT COMMENT '创建用户ID',
        updated_by INT COMMENT '更新用户ID',
        
        INDEX idx_task_status (task_status),
        INDEX idx_task_type (task_type),
        INDEX idx_task_year (year),
        INDEX idx_task_deadline (deadline),
        INDEX idx_task_company (company_id),
        INDEX idx_task_priority (priority),
        FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE SET NULL,
        FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
        FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='待办任务表'
    `);
    console.log('✅ 创建待办任务表成功');
    
    // 3. 创建任务操作日志表
    await connection.query(`
      CREATE TABLE IF NOT EXISTS task_operation_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL COMMENT '任务ID',
        operation_type ENUM('创建', '更新', '删除', '核实', '状态变更') NOT NULL COMMENT '操作类型',
        old_status VARCHAR(50) COMMENT '原状态',
        new_status VARCHAR(50) COMMENT '新状态',
        operation_content TEXT COMMENT '操作内容',
        operation_reason TEXT COMMENT '操作原因',
        operated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        operated_by INT COMMENT '操作用户ID',
        
        INDEX idx_task_log_task (task_id),
        INDEX idx_task_log_type (operation_type),
        INDEX idx_task_log_time (operated_at),
        FOREIGN KEY (task_id) REFERENCES pending_tasks(id) ON DELETE CASCADE,
        FOREIGN KEY (operated_by) REFERENCES users(id) ON DELETE SET NULL
      ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务操作日志表'
    `);
    console.log('✅ 创建任务操作日志表成功');
    
    // 4. 插入基础任务类型数据
    const taskTypes = [
      ['年审年报', '公司年审和年报相关任务'],
      ['地址维护', '公司注册地址维护相关任务'],
      ['自定义任务', '用户自定义的其他类型任务'],
      ['全部', '所有类型任务的统称']
    ];
    
    for (const [name, description] of taskTypes) {
      await connection.query(
        'INSERT INTO task_types (name, description) VALUES (?, ?) ON DUPLICATE KEY UPDATE description = VALUES(description)',
        [name, description]
      );
    }
    console.log('✅ 插入基础任务类型数据成功');
    
    // 5. 插入测试任务数据
    console.log('📝 开始插入测试任务数据...');
    
    const [companies] = await connection.query('SELECT id, company_name_cn FROM companies ORDER BY id LIMIT 10');
    const [businessSegments] = await connection.query('SELECT name FROM business_segments ORDER BY id LIMIT 5');
    
    const testTasks = [];
    const taskStatuses = ['已逾期', '未开始', '进行中', '待核实', '已完成'];
    const taskTypesList = ['年审年报', '地址维护', '自定义任务'];
    const years = ['2024', '2023', '2022'];
    
    for (let i = 0; i < 20; i++) {
      const status = taskStatuses[i % taskStatuses.length];
      const taskType = taskTypesList[i % taskTypesList.length];
      const year = years[i % years.length];
      const company = companies[i % companies.length];
      const businessSegment = businessSegments[i % businessSegments.length]?.name || 'IT服务';
      
      // 根据状态设置日期
      let startDate, deadline;
      const now = new Date();
      
      if (status === '已逾期') {
        deadline = new Date(now.getTime() - (Math.random() * 30 + 1) * 24 * 60 * 60 * 1000); // 1-30天前
        startDate = new Date(deadline.getTime() - 30 * 24 * 60 * 60 * 1000); // 截止日期前30天
      } else if (status === '未开始') {
        startDate = new Date(now.getTime() + (Math.random() * 10 + 1) * 24 * 60 * 60 * 1000); // 1-10天后
        deadline = new Date(startDate.getTime() + (Math.random() * 30 + 30) * 24 * 60 * 60 * 1000); // 开始后30-60天
      } else {
        startDate = new Date(now.getTime() - (Math.random() * 10 + 1) * 24 * 60 * 60 * 1000); // 1-10天前
        deadline = new Date(now.getTime() + (Math.random() * 30 + 10) * 24 * 60 * 60 * 1000); // 10-40天后
      }
      
      const progress = status === '已完成' ? 100 : 
                      status === '进行中' ? Math.floor(Math.random() * 80 + 10) :
                      status === '待核实' ? Math.floor(Math.random() * 30 + 70) : 0;
      
      const priority = Math.floor(Math.random() * 5) + 1; // 1-5优先级
      const remarks = `${taskType}相关任务，当前状态：${status}`;
      
      testTasks.push([
        status, taskType, year, startDate, deadline, 
        company.id, company.company_name_cn, businessSegment, 
        remarks, priority, progress
      ]);
    }
    
    for (const [status, type, year, startDate, deadline, companyId, companyName, segment, remarks, priority, progress] of testTasks) {
      await connection.query(
        `INSERT INTO pending_tasks (
          task_status, task_type, year, start_date, deadline,
          company_id, company_name, business_segment, remarks, priority, progress, created_by
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)`,
        [status, type, year, startDate, deadline, companyId, companyName, segment, remarks, priority, progress]
      );
    }
    
    console.log(`✅ 插入了 ${testTasks.length} 条测试任务数据`);
    
    // 6. 验证创建结果
    const [taskCount] = await connection.query('SELECT COUNT(*) as count FROM pending_tasks');
    const [typeCount] = await connection.query('SELECT COUNT(*) as count FROM task_types');
    
    console.log('\n📊 数据表创建结果统计:');
    console.log(`  📋 任务类型: ${typeCount[0].count} 条记录`);
    console.log(`  📝 待办任务: ${taskCount[0].count} 条记录`);
    
    console.log('\n🎉 待办任务数据表创建完成！');
    
  } catch (error) {
    console.error('❌ 创建待办任务数据表失败:', error.message);
    throw error;
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

createTaskTables()
  .then(() => console.log('🎉 待办任务数据表创建成功完成'))
  .catch(err => console.error('💥 创建失败:', err));
