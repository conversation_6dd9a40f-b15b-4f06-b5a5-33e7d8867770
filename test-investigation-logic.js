import axios from 'axios';

async function testInvestigationLogic() {
  try {
    console.log('🧪 测试任务排查逻辑...');
    
    // 测试2025年的任务排查
    const response = await axios.post('http://localhost:8080/api/task/investigation', {
      year: '2025'
    });

    if (response.data.success) {
      const data = response.data.data;
      
      console.log('\n📊 排查结果:');
      console.log(`年度: ${data.year}`);
      console.log(`存续公司: ${data.totalCompanies}`);
      console.log(`不管年审: ${data.noAnnualCompanies}`);
      console.log(`管年审（固定周期）: ${data.fixedAnnualCompanies}`);
      console.log(`管年审（滚动周期）: ${data.rollingAnnualCompanies}`);
      
      console.log('\n🔍 验证计算逻辑:');
      
      // 验证1: 不管年审公司数不应该是负数
      console.log(`1. 不管年审公司数: ${data.noAnnualCompanies} ${data.noAnnualCompanies >= 0 ? '✅' : '❌'}`);
      
      // 验证2: 存续公司数量 = 不管年审 + 管年审（固定） + 管年审（滚动）
      const calculatedTotal = data.noAnnualCompanies + data.fixedAnnualCompanies + data.rollingAnnualCompanies;
      console.log(`2. 公司数量验证: ${data.noAnnualCompanies} + ${data.fixedAnnualCompanies} + ${data.rollingAnnualCompanies} = ${calculatedTotal}`);
      console.log(`   存续公司数: ${data.totalCompanies} ${calculatedTotal === data.totalCompanies ? '✅' : '❌'}`);
      
      // 验证3: 年审年报理论数不应该超过存续公司数
      console.log(`3. 年审年报理论数: ${data.annualReport.theory} <= ${data.totalCompanies} ${data.annualReport.theory <= data.totalCompanies ? '✅' : '❌'}`);
      
      // 验证4: 实际数 + 缺失数 = 理论数
      const annualCalculated = data.annualReport.actual + data.annualReport.missing;
      console.log(`4. 年审年报: ${data.annualReport.actual} + ${data.annualReport.missing} = ${annualCalculated}`);
      console.log(`   理论数: ${data.annualReport.theory} ${annualCalculated === data.annualReport.theory ? '✅' : '❌'}`);
      
      // 验证5: 地址维护理论数不应该超过存续公司数
      console.log(`5. 地址维护理论数: ${data.addressMaintenance.theory} <= ${data.totalCompanies} ${data.addressMaintenance.theory <= data.totalCompanies ? '✅' : '❌'}`);
      
      // 验证6: 地址维护实际数 + 缺失数 = 理论数
      const addressCalculated = data.addressMaintenance.actual + data.addressMaintenance.missing;
      console.log(`6. 地址维护: ${data.addressMaintenance.actual} + ${data.addressMaintenance.missing} = ${addressCalculated}`);
      console.log(`   理论数: ${data.addressMaintenance.theory} ${addressCalculated === data.addressMaintenance.theory ? '✅' : '❌'}`);
      
      console.log('\n📋 缺失任务统计:');
      console.log(`总缺失任务: ${data.missingTasks.length}`);
      console.log(`年审年报缺失: ${data.missingTasks.filter(t => t.missingAnnualReport).length}`);
      console.log(`地址维护缺失: ${data.missingTasks.filter(t => t.missingAddressMaintenance).length}`);
      
    } else {
      console.error('❌ 任务排查失败:', response.data.message);
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testInvestigationLogic();
