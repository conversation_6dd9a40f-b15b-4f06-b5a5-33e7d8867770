import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'SKiP-MBP.local',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2',
  socketPath: '/tmp/mysql.sock'
};

async function checkAnnualStatus() {
  let connection;
  
  try {
    console.log('🔄 连接数据库...');
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 检查annual_update_status表
    console.log('\n📋 检查annual_update_status表:');
    const [statusList] = await connection.execute('SELECT * FROM annual_update_status ORDER BY id');
    console.log('年审状态列表:');
    statusList.forEach(status => {
      console.log(`  ID:${status.id} ${status.name} - ${status.description || '无描述'}`);
    });

    // 检查公司的年审状态分布
    console.log('\n🏢 检查公司年审状态分布:');
    const [companyStatusStats] = await connection.execute(`
      SELECT 
        COALESCE(aus.name, '未设置') as status_name,
        COUNT(*) as count
      FROM companies c
      LEFT JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      GROUP BY c.annual_update_status_id, aus.name
      ORDER BY count DESC
    `);
    
    console.log('公司年审状态统计:');
    companyStatusStats.forEach(stat => {
      console.log(`  ${stat.status_name}: ${stat.count} 个公司`);
    });

    // 检查具体的公司年审状态
    console.log('\n📊 检查具体公司年审状态:');
    const [companyDetails] = await connection.execute(`
      SELECT 
        c.id,
        c.company_name_cn,
        c.annual_update_status_id,
        COALESCE(aus.name, '未设置') as status_name
      FROM companies c
      LEFT JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
      ORDER BY c.id
    `);
    
    console.log('公司详细年审状态:');
    companyDetails.forEach(company => {
      console.log(`  ID:${company.id} ${company.company_name_cn} - 状态ID:${company.annual_update_status_id || 'NULL'} (${company.status_name})`);
    });

    // 如果所有公司都是未设置状态，我们来设置一些测试数据
    if (companyStatusStats.length === 1 && companyStatusStats[0].status_name === '未设置') {
      console.log('\n🔧 所有公司都是未设置状态，设置一些测试数据...');
      
      // 随机分配年审状态
      const companies = companyDetails;
      for (let i = 0; i < companies.length; i++) {
        const company = companies[i];
        let statusId;
        
        // 按比例分配：30%不管年审，40%固定周期，30%滚动周期
        const rand = Math.random();
        if (rand < 0.3) {
          statusId = 5; // 不管年审
        } else if (rand < 0.7) {
          statusId = 6; // 管年审（固定周期）
        } else {
          statusId = 7; // 管年审（滚动周期）
        }
        
        await connection.execute(
          'UPDATE companies SET annual_update_status_id = ? WHERE id = ?',
          [statusId, company.id]
        );
        
        console.log(`  更新公司 ${company.company_name_cn} 年审状态为: ${statusId}`);
      }
      
      console.log('✅ 测试数据设置完成');
      
      // 重新检查分布
      console.log('\n📊 重新检查公司年审状态分布:');
      const [newStats] = await connection.execute(`
        SELECT 
          aus.name as status_name,
          COUNT(*) as count
        FROM companies c
        JOIN annual_update_status aus ON c.annual_update_status_id = aus.id
        GROUP BY c.annual_update_status_id, aus.name
        ORDER BY count DESC
      `);
      
      console.log('更新后的公司年审状态统计:');
      newStats.forEach(stat => {
        console.log(`  ${stat.status_name}: ${stat.count} 个公司`);
      });
    }

  } catch (error) {
    console.error('❌ 检查失败:', error);
  } finally {
    if (connection) {
      await connection.end();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

// 执行检查
checkAnnualStatus().catch(console.error);
