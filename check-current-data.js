import mysql from 'mysql2/promise';

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3306,
  user: 'txuser',
  password: 'txpassword',
  database: 'stake_management_v2'
};

async function checkCurrentData() {
  let connection;
  
  try {
    connection = await mysql.createConnection(dbConfig);
    console.log('✅ 数据库连接成功');

    // 1. 查看companies表结构
    const [companiesColumns] = await connection.query(`
      SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE, COLUMN_DEFAULT, COLUMN_COMMENT
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_SCHEMA = 'stake_management_v2' AND TABLE_NAME = 'companies'
      ORDER BY ORDINAL_POSITION
    `);
    
    console.log('\n🏢 companies表字段:');
    companiesColumns.forEach(col => {
      console.log(`    ${col.COLUMN_NAME} (${col.DATA_TYPE}) - ${col.COLUMN_COMMENT || '无注释'}`);
    });

    // 2. 查看现有公司列表
    const [companies] = await connection.query(`
      SELECT id, company_name_cn, company_name_en
      FROM companies
      ORDER BY id
    `);
    
    console.log('\n🏢 现有公司列表:');
    companies.forEach(company => {
      console.log(`  ID: ${company.id}, 中文名: ${company.company_name_cn}, 英文名: ${company.company_name_en || '无'}`);
    });

    // 3. 查看现有人员列表
    const [persons] = await connection.query(`
      SELECT id, name, id_number, phone, email
      FROM persons
      ORDER BY id
    `);
    
    console.log('\n👥 现有人员列表:');
    persons.forEach(person => {
      console.log(`  ID: ${person.id}, 姓名: ${person.name}, 身份证: ${person.id_number || '无'}, 电话: ${person.phone || '无'}`);
    });

    // 4. 查看现有职位列表
    const [positions] = await connection.query(`
      SELECT id, name
      FROM positions
      ORDER BY sort, id
    `);
    
    console.log('\n💼 现有职位列表:');
    positions.forEach(position => {
      console.log(`  ID: ${position.id}, 职位: ${position.name}`);
    });

    // 5. 查看现有任职记录
    const [employments] = await connection.query(`
      SELECT e.id, p.name as person_name, c.company_name_cn, e.position, 
             e.start_date, e.end_date, e.is_active
      FROM employments e
      LEFT JOIN persons p ON e.person_id = p.id
      LEFT JOIN companies c ON e.company_id = c.id
      ORDER BY e.id
    `);
    
    console.log('\n📋 现有任职记录:');
    if (employments.length === 0) {
      console.log('  暂无任职记录');
    } else {
      employments.forEach(emp => {
        console.log(`  ID: ${emp.id}, 姓名: ${emp.person_name || '无'}, 公司: ${emp.company_name_cn || '无'}, 职位: ${emp.position || '无'}, 开始: ${emp.start_date || '无'}, 结束: ${emp.end_date || '无'}, 在职: ${emp.is_active ? '是' : '否'}`);
      });
    }

    // 6. 查看现有变更记录
    const [changeLogs] = await connection.query(`
      SELECT cl.id, c.company_name_cn, cl.change_type, cl.change_content, 
             cl.change_date, cl.status, cl.operator
      FROM company_change_logs cl
      LEFT JOIN companies c ON cl.company_id = c.id
      ORDER BY cl.id
    `);
    
    console.log('\n📝 现有变更记录:');
    if (changeLogs.length === 0) {
      console.log('  暂无变更记录');
    } else {
      changeLogs.forEach(log => {
        console.log(`  ID: ${log.id}, 公司: ${log.company_name_cn || '无'}, 类型: ${log.change_type}, 内容: ${log.change_content}, 日期: ${log.change_date}, 状态: ${log.status}, 操作人: ${log.operator}`);
      });
    }

    // 7. 查看业务板块
    const [businessSegments] = await connection.query(`
      SELECT id, name
      FROM business_segments
      ORDER BY id
    `);
    
    console.log('\n🏭 业务板块列表:');
    businessSegments.forEach(segment => {
      console.log(`  ID: ${segment.id}, 名称: ${segment.name}`);
    });

  } catch (error) {
    console.error('❌ 查询失败:', error);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 运行查询
checkCurrentData();
